# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/
.sdfx/
coverage/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VS Code related
.vscode/
.classpath
.project
.settings/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio build artifacts
/android/app/debug
/android/app/profile
/android/app/release

# Exclude .env files
*.env

# iOS build environment
/ios/Podfile.lock
*.ipa
*.dSYM.zip

# fastlane specific
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output
*.p8
/coverage/

# Android
.gradle/
build/
local.properties
captures/
.externalNativeBuild/
.cxx/
*.apk
output.json

# IntelliJ
misc.xml
deploymentTargetDropDown.xml
render.experimental.xml

# Keystore files
*.jks
*.keystore

# Google Services
google-services.json

# Android Profiling
*.hprof

# Android Patch
gen-external-apklibs

# Flutter
.fvm/flutter_sdk
lib/generated_plugin_registrant.dart

# iOS/XCode
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# Exceptions
!**/ios/**/default.mode1v3
!**/ios/**/default.mode2v3
!**/ios/**/default.pbxuser
!**/ios/**/default.perspectivev3
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages

# AndroidStudio
*.ap_
*.aab
*.dex
bin/
gen/
out/
.signing/
.proguard/
obj/
.idea/caches/
.idea/libraries/
.idea/shelf/
.idea/workspace.xml
.idea/tasks.xml
.idea/.name
.idea/compiler.xml
.idea/copyright/profiles_settings.xml
.idea/encodings.xml
.idea/modules.xml
.idea/scopes/scope_settings.xml
.idea/dictionaries
.idea/vcs.xml
.idea/jsLibraryMappings.xml
.idea/datasources.xml
.idea/dataSources.ids
.idea/sqlDataSources.xml
.idea/dynamic.xml
.idea/uiDesigner.xml
.idea/assetWizardSettings.xml
.idea/gradle.xml
.idea/jarRepositories.xml
.idea/navEditor.xml

# Legacy Eclipse project files
.cproject

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.war
*.ear

# VM crash logs
hs_err_pid*

# Plugin-specific files
.idea_modules/
atlassian-ide-plugin.xml
.idea/mongoSettings.xml
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# AndroidStudio Patch
!/gradle/wrapper/gradle-wrapper.jar
/default.isar
/default.isar.lock
