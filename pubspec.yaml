name: x1440
description: 1440 Messaging Studio
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
######### IMPORTANT: when you update the version number, create a tag; Android Studio doesn't push these to git nicely; you may need to push the tag manually with `git push origin --tags`
# TAG EXAMPLE: 1.2.3+4-1234-circleci-setup-validation
# TAG EXAMPLE: a.b.c+d-eeee-tag-description
# a = major version [required]
# b = minor version [required]
# c = patch version [required]
# d = build number [required] *** TODO: auto-set this based on latest from Apple & Google stores
# MS-eeee is the Jira ticket number for this build [optional]
# tag-description is a short description of the tag [optional]
version: 2.3.0

environment:
  sdk: ">=3.6.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: 1.0.8
  http: 1.2.2 #1.1.0
  flutter_dotenv: 5.2.1
  url_launcher: 6.3.1

  provider: 6.1.2
  intl: 0.20.2
 
  date_format: 2.0.9
  go_router: 14.3.0
  top_snackbar_flutter: 3.1.0
  flutter_spinkit: 5.2.1
  google_fonts: 6.2.1
  image_picker: 1.1.2
  uuid: 4.5.1
  file_picker: 8.1.3
  accordion: 2.6.0
  video_thumbnail: 0.5.3
  video_player: 2.9.2
  photo_view: 0.15.0
  path: 1.9.1
  open_file: 3.5.9
  emoji_regex: 0.0.5
  loggy: 2.0.3
  flutter_svg: any
#  live_activities: 1.9.1+1
  #        git:
  #            url: https://github.com/repstudio/flutter_live_activities
  #            ref: withAlert
  open_settings: 2.0.2
  flutter_image_compress: 2.3.0
  package_info_plus: 8.1.0

  grpc: 4.0.1
  stream_channel: 2.1.4
  protobuf: 3.1.0
  html_unescape: 2.0.0
  cached_network_image: 3.4.1
  app_links: 6.3.2
  html: 0.15.4
  country_picker: 2.0.26
  device_info_plus: 11.1.0
  path_provider: 2.1.4
  firebase_core: 3.10.0
  firebase_crashlytics: 4.3.0
  firebase_remote_config: 5.3.0
  firebase_analytics: 11.4.0
  push: 2.3.0
#  flutter_local_notifications: 17.0.0
  share_plus: 10.1.1
  dio: 5.7.0
  pretty_dio_logger: null
  json_annotation: 4.9.0
  flutter_secure_storage: 9.2.4
  injectable: 2.5.0
  get_it: 8.0.1
  retrofit: 4.4.1
  flutter_bloc: 8.1.6
  freezed_annotation: 2.4.4
  web_socket_client: 0.1.5
  web_socket_channel: 3.0.1
#  battery_info: 1.1.1
  connectivity_plus: 6.1.0
  gal: 2.3.0
  easy_image_viewer: 1.5.1
  # LiveAgentKit
  universal_io: 2.2.2 # TODO: try to remove after internal package
  synchronized: 3.3.0+3
  file_saver: 0.2.14
  isar: 3.1.0+1
  isar_flutter_libs: 3.1.0+1
  dio_smart_retry: 6.0.0
  fluttertoast: 8.2.8

  # Supports the quick actions
  flutter_inappwebview: 6.1.5
  material_symbols_icons: 4.2789.0
  flutter_web_auth_2: 4.0.1

  collection: 1.19.1

  android_intent_plus: 5.2.0

  # For Hmac implementation
  crypto: 3.0.6
  encrypt: 5.0.3
  pointycastle: 3.9.1
  ntp: 2.0.0

#  stack: 0.2.2

generate: true

dev_dependencies:
 
  flutter_driver:
    sdk: flutter

  flutter_test:
    sdk: flutter

  test: any

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: 5.0.0
  build_runner: 2.4.13
  mocktail: 1.0.4
  bloc_test: 9.1.7
  pigeon: 17.1.3
  injectable_generator: 2.4.2
  freezed: 2.5.2
  retrofit_generator: 8.2.1
  json_serializable: 6.8.0


#dependency_overrides:
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/
    - assets/images/
    - lib/config/
flutter_intl: 
  enabled: true
