<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>1440</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>1440</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>io.1440</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>x1440</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<true/>
	<key>FirebaseMessagingAutoInitEnabled</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Take photos to include with messages to customers </string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Automatically update your status and assign work based on when you arrive at and leave your retail work location</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Automatically update your status and assign work based on when you arrive at and leave your retail work location</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>1440 would like to use your microphone</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Select photos from your gallery to include with messages to customers </string>
	<key>NSSupportsLiveActivities</key>
	<true/>
	<key>NSUserActivityTypes</key>
	<array>
		<string>INSendMessageIntent</string>
	</array>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>location</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>pushActions</key>
	<array>
		<dict>
			<key>action</key>
			<string>WORK_ACTION</string>
			<key>entries</key>
			<array>
				<dict>
					<key>action</key>
					<string>ACCEPT_ACTION</string>
					<key>destructive</key>
					<false/>
					<key>displayName</key>
					<dict>
						<key>de</key>
						<string>Akzeptieren</string>
						<key>en</key>
						<string>Accept</string>
						<key>en_BV</key>
						<string>***Accept</string>
						<key>es</key>
						<string>Aceptar</string>
						<key>fr</key>
						<string>Accepter</string>
					</dict>
					<key>foreground</key>
					<true/>
				</dict>
				<dict>
					<key>action</key>
					<string>DECLINE_ACTION</string>
					<key>destructive</key>
					<true/>
					<key>displayName</key>
					<dict>
						<key>de</key>
						<string>Ablehnen</string>
						<key>en</key>
						<string>Decline</string>
						<key>en_BV</key>
						<string>***Decline</string>
						<key>es</key>
						<string>Rechazar</string>
						<key>fr</key>
						<string>Refuser</string>
					</dict>
					<key>foreground</key>
					<true/>
				</dict>
			</array>
		</dict>
		<dict>
			<key>action</key>
			<string>PING_ACTION</string>
			<key>entries</key>
			<array>
				<dict>
					<key>foreground</key>
					<true/>
					<key>action</key>
					<string>CONTINUE_ACTION</string>
					<key>destructive</key>
					<false/>
					<key>displayName</key>
					<dict>
						<key>de</key>
						<string>Fortsetzen</string>
						<key>en</key>
						<string>Continue</string>
						<key>en_BV</key>
						<string>***Continue</string>
						<key>es</key>
						<string>Continuar</string>
						<key>fr</key>
						<string>Continuer</string>
					</dict>
				</dict>
				<dict>
					<key>foreground</key>
					<true/>
					<key>action</key>
					<string>DISCONNECT_ACTION</string>
					<key>destructive</key>
					<true/>
					<key>displayName</key>
					<dict>
						<key>de</key>
						<string>Trennen</string>
						<key>en</key>
						<string>Disconnect</string>
						<key>en_BV</key>
						<string>***Disconnect</string>
						<key>es</key>
						<string>Desconectar</string>
						<key>fr</key>
						<string>Déconnecter</string>
					</dict>
				</dict>
			</array>
		</dict>
	</array>
</dict>
</plist>
