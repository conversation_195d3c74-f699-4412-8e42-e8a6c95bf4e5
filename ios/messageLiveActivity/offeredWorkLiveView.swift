//
//  conversationLiveMessage.swift
//  messageLiveActivityExtension
//
//  Created by <PERSON> on 10/30/23.
//

import Foundation
import SwiftUI
import AppIntents

struct OfferedWorkLiveView: View {
    let activeLiveMessage: ActiveLiveMessage;
    
    var body: some View {
        if (activeLiveMessage.acceptMessageLink != nil && activeLiveMessage.declineMessageLink != nil) {                
                HStack(alignment: .top) {
                    MessageLiveView(activeLiveMessage: activeLiveMessage)
                    
                    if !activeLiveMessage.acceptOpensApp, #available(iOSApplicationExtension 17.0, *) {
                        Button(intent: AcceptWorkIntent(workId: activeLiveMessage.id!)) {
                            ZStack {
                                Circle()
                                    .fill(Color(red: 0.376, green: 0.827, blue: 0.329))
                                    .frame(width: 48, height: 48)
                                Image(systemName: "checkmark")
                                    .foregroundColor(.white)
                                    .font(.system(size: 22, weight: .semibold))
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.top, 32)
                        .padding(.bottom, 32)
                    } else if (activeLiveMessage.acceptMessageLink != nil) {
                        Link(destination: URL(string: activeLiveMessage.acceptMessageLink!)!) {
                            ZStack {
                                Circle()
                                    .fill(Color(red: 0.376, green: 0.827, blue: 0.329))
                                    .frame(width: 48, height: 48)
                                Image(systemName: "checkmark")
                                    .foregroundColor(.white)
                                    .font(.system(size: 22, weight: .semibold))
                            }
                        }
                        .padding(.top, 32)
                        .padding(.bottom, 32)
                    }
                    if #available(iOSApplicationExtension 17.0, *), activeLiveMessage.id != nil {
                        Button(intent: DeclineWorkIntent(workId: activeLiveMessage.id!)) {
                            ZStack {
                                Circle()
                                    .fill(Color(red: 0.76, green: 0.224, blue: 0.204))
                                    .frame(width: 48, height: 48)
                                Image(systemName: "xmark")
                                    .foregroundColor(.white)
                                    .font(.system(size: 22, weight: .semibold))
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.top, 32)
                        .padding(.bottom, 32)
                        .padding(.trailing, 16)
                    } else if (activeLiveMessage.declineMessageLink != nil) {
                        Link(destination: URL(string: activeLiveMessage.declineMessageLink!)!) {
                            ZStack {
                                Circle()
                                    .fill(Color(red: 0.76, green: 0.224, blue: 0.204))
                                    .frame(width: 48, height: 48)
                                Image(systemName: "xmark")
                                    .foregroundColor(.white)
                                    .font(.system(size: 22, weight: .semibold))
                            }
                        }
                        .padding(.top, 32)
                        .padding(.bottom, 32)
                        .padding(.trailing, 16)
                    }
            }
        } else {
            MessageLiveView(activeLiveMessage: activeLiveMessage)
        }
    }
}
