//
//  File.swift
//  messageLiveActivityExtension
//
//  Created by <PERSON> on 10/30/23.
//

import Foundation
import SwiftUI

let fourteen40backgroundColor = Color(red: 0.149, green: 0.149, blue: 0.149); // fourteen40backgroundColor
let backgroundColor = Color.black; // fourteen40backgroundColor

let grey = Color(red: 0.31, green: 0.31, blue: 0.31); // border: 1px solid rgba(79, 79, 79, 1)
let availableColor = Color(red: 0.376, green: 0.827, blue: 0.004);
let busyColor = Color(red: 1.0, green: 0.647, blue: 0.0);
let offlineColor = Color(red: 0.627, green: 0.627, blue: 0.627);
