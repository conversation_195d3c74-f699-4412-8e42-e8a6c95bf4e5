//
//  messageLiveView.swift
//  messageLiveActivityExtension
//
//  Created by <PERSON> on 11/11/23.
//

import Foundation
import SwiftUI

struct MessageLiveView: View {
    let activeLiveMessage: ActiveLiveMessage;
    
    var innerView: some View {
        HStack(alignment: .top) {
            ConsumerPhotoAndChannelView(activeLiveMessage: activeLiveMessage)
                .padding(.trailing, 8)
            
            VStack(alignment: .leading) {
                
                Text(activeLiveMessage.title ?? "")
                    .lineLimit(1)
                    .font(.headline)
                    .foregroundStyle(.white)
                    .fontWeight(.bold)
                    .padding(.bottom, 2.0)
                
                Text(activeLiveMessage.body ?? "")
                    .lineLimit(3)
                    .font(.subheadline)
                    .foregroundStyle(.white)
                    .multilineTextAlignment(.leading)
                
                Spacer()
                
                if (activeLiveMessage.footer != nil) {
                    Text(activeLiveMessage.footer!)
                        .lineLimit(1)
                        .font(.footnote)
                        .foregroundColor(Color(red: 0.592, green: 0.592, blue: 0.592))//background: rgba(151, 151, 151, 1);
                        .multilineTextAlignment(.leading)
                }
            }
            .padding(.bottom, 8)
            .padding(.trailing, 8)
            .padding(.top, 12.0) /// note: this gets the alignment ~where we want it, aligning with the profile pic padded 16 from top :shrug:
            Spacer()
        }
    }
    
    var body: some View {
        if (activeLiveMessage.openConversationLink != nil) {
            Link(destination: URL(string: activeLiveMessage.openConversationLink ?? "")!) {
                innerView
            }
        } else if #available(iOSApplicationExtension 17.0, *) {
                Button(intent: DoNothingIntent()) {
                    innerView
                }
                .buttonStyle(PlainButtonStyle())

        } else {
            innerView
        }
    }
}
