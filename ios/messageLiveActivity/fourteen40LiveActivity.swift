//
//  liveMessage.swift
//  messageLiveActivityExtension
//
//  Created by <PERSON> on 10/30/23.
//

import Foundation
import SwiftUI
import WidgetKit

// TODO: change this into a LiveActivity class w/ messages, baseMessage getter, activeMessage getter
/// LiveMessage has entire live activity payload from the Flutter app; the activeLiveMessage refers only to the current active message
struct Fourteen40LiveActivity {
    let statusMessage:StatusLiveMessage?;
    
    let messages:Array<Any>;
    
    let topMessage:Any?;
    let topMessageDictionary:[String: Any]?;
    let topMessageType:String?;
    let topMessageId:String?;
    
    let deepLinkBasePath:String?;
    
    // TODO: figure out handling these at a specific message/view level
    let appIcon: String?;
    let chatBubbleIcon: String?;
    let endUserPhotoUrl: String?;
    let channelImg: String?;
    let qrCodeIcon: String?;
    let newConversationIcon: String?;
    
    let _numOfferedWork:Int;

    init(fromSharedDefaults sharedDefaults: UserDefaults, context: ActivityViewContext<LiveActivitiesAppAttributes>) {
        messages = (sharedDefault.array(forKey: context.attributes.prefixedKey("messages")) ?? []) as Array<Any>;
        topMessage = messages.first;
        topMessageDictionary = topMessage as? [String:Any];
        topMessageType = topMessageDictionary?["type"] as? String ?? "";
        topMessageId = topMessageDictionary?["id"] as? String ?? "";
        deepLinkBasePath = sharedDefault.string(forKey: context.attributes.prefixedKey("deepLinkBasePath")); // TODO: remove this from the message classes
        
        // TODO: figure out handling these at a specific message/view level
        /// status view images
        appIcon = topMessageId == nil ? nil : sharedDefaults.string(forKey: context.attributes.prefixedKey(topMessageId!+"_appIcon")) ?? sharedDefaults.string(forKey: context.attributes.prefixedKey("base_appIcon"));
        chatBubbleIcon = topMessageId == nil ? nil : sharedDefaults.string(forKey: context.attributes.prefixedKey(topMessageId!+"_chatBubbleIcon")) ?? sharedDefaults.string(forKey: context.attributes.prefixedKey("base_chatBubbleIcon"));
        qrCodeIcon = topMessageId == nil ? nil : sharedDefaults.string(forKey: context.attributes.prefixedKey(topMessageId!+"_qrCodeIcon")) ?? sharedDefaults.string(forKey: context.attributes.prefixedKey("base_qrCodeIcon"));
        newConversationIcon = topMessageId == nil ? nil : sharedDefaults.string(forKey: context.attributes.prefixedKey(topMessageId!+"_newConversationIcon")) ?? sharedDefaults.string(forKey: context.attributes.prefixedKey("base_newConversationIcon")); // TODO: this should be 'base_newConversationIcon' I think? But this is working

        /// conversation view images
        endUserPhotoUrl = topMessageId == nil ? nil : sharedDefaults.string(forKey: context.attributes.prefixedKey(topMessageId!+"_endUserPhotoUrl"));
        channelImg = topMessageId == nil ? nil : sharedDefaults.string(forKey: context.attributes.prefixedKey(topMessageId!+"_channelImg"));
        
        var numOfferedWork:Int = 0;
        
        var settingStatusMessage: StatusLiveMessage?;
        for thisMessage in messages {
            let thisMessageDictionary = thisMessage as? [String:Any];
            
            if thisMessageDictionary?["type"] as? String == "newConversation" {
                numOfferedWork += 1
            } else if thisMessageDictionary?["type"] as? String == "status" {
                settingStatusMessage = settingStatusMessage ?? StatusLiveMessage(
                    id: thisMessageDictionary?["id"] as? String,
                    deepLinkBasePath: deepLinkBasePath,
                    statusType: thisMessageDictionary?["statusType"] as? String,
                    statusText: thisMessageDictionary?["statusText"] as? String,
                    numActiveConversations: thisMessageDictionary?["numActiveConversations"] as? String,
                    appIcon: appIcon,
                    chatBubbleIcon: chatBubbleIcon,
                    qrCodeIcon: qrCodeIcon,
                    newConversationIcon: newConversationIcon
                );
            }
        }
        statusMessage = settingStatusMessage;
        _numOfferedWork = numOfferedWork;
    }
    
    var view: some View {
        ZStack() {
            // TODO: handle this more elegantly (esp last two using the same input ... they are separate View objects to alert the Live Activity the height could be different
            if topMessageType == "status" {
                StatusLiveView(statusLiveMessage: StatusLiveMessage(
                    id: topMessageId,
                    deepLinkBasePath: deepLinkBasePath,
                    statusType: topMessageDictionary!["statusType"] as? String,
                    statusText: topMessageDictionary!["statusText"] as? String,
                    numActiveConversations: topMessageDictionary!["numActiveConversations"] as? String,
                    appIcon: appIcon,
                    chatBubbleIcon: chatBubbleIcon,
                    qrCodeIcon: qrCodeIcon,
                    newConversationIcon: newConversationIcon
                ));
            } else if topMessageType == "newConversation", deepLinkBasePath != nil, topMessageId != nil {
                OfferedWorkLiveView(activeLiveMessage: ActiveLiveMessage(
                    deepLinkBasePath: deepLinkBasePath,
                    type: topMessageDictionary?["type"] as? String ?? "",
                    endUserPhotoUrl: endUserPhotoUrl,
                    channelImg: channelImg,
                    id: topMessageId,
                    title: topMessageDictionary?["title"] as? String,
                    body: topMessageDictionary?["body"] as? String,
                    footer: topMessageDictionary?["footer"] as? String,
                    numOfferedWork: _numOfferedWork,
                    acceptOpensApp: _numOfferedWork < 2));
            } else {
                MessageLiveView(activeLiveMessage: ActiveLiveMessage(
                    deepLinkBasePath: deepLinkBasePath,
                    type: topMessageDictionary?["type"] as? String ?? "",
                    endUserPhotoUrl: endUserPhotoUrl,
                    channelImg: channelImg,
                    id: topMessageId,
                    title: topMessageDictionary?["title"] as? String,
                    body: topMessageDictionary?["body"] as? String,
                    footer: topMessageDictionary?["footer"] as? String,
                    numOfferedWork: 0,
                    acceptOpensApp: false)) // TODO: shouldn't need to set anything to do with accepting/declining conversation for these basic views
            }
        }.fixedSize(horizontal: false, vertical: true)
    }
    
    var dynamicIsland: DynamicIsland {
        let statusColor = statusMessage?.statusColor ?? offlineColor;
        let statusText = statusMessage?.statusText ?? "Offline";
        return DynamicIsland {
            DynamicIslandExpandedRegion(.center) {
                view;
            }
        } compactLeading: {
            // TODO: make this a separate view used by this and the Status live message view
            HStack() {
                Circle()
                    .fill(statusColor)
                    .frame(width: 18, height: 18)
                    .padding(4)
                Text(statusText)
                    .font(.system(size: 14, weight: .medium, design: .default))
                    .foregroundStyle(.white)
                    .lineLimit(1)
                    .padding(.trailing, 16)
            }
            .overlay(
                RoundedRectangle(cornerRadius: 60)
                    .stroke(grey, lineWidth: 1)
                    .padding(.trailing, 1)
            )
        } compactTrailing: {
            HStack {
                
                if statusMessage?.newConversationIcon != nil, let newConversationIcon = UIImage(contentsOfFile: statusMessage!.newConversationIcon!)
                {
                    Image(uiImage: newConversationIcon)
                        .resizable()
                        .frame(width: 20, height: 20)
                        .padding(.leading, 8)
                        .padding(.trailing, 8)
                }
                
                let numActiveConversations = statusMessage?.numActiveConversations as? String ?? "0";
                if statusMessage?.chatBubbleIcon != nil, let chatBubbleIconWidget = UIImage(contentsOfFile: statusMessage!.chatBubbleIcon!)
                {
                    ZStack {
                        Image(uiImage: chatBubbleIconWidget)
                            .resizable()
                            .frame(width: 26, height: 24) // 42
                        Text(numActiveConversations) // TODO: get real number
                            .lineLimit(1)
                            .font(.system(size: 14, weight: .medium, design: .default))
                            .foregroundStyle(.white)
                    }
                } else {
                    EmptyView();
                }
            }
        } minimal: {
            let numActiveConversations = statusMessage?.numActiveConversations as? String ?? "0";
            if statusMessage?.chatBubbleIcon != nil, let chatBubbleIconWidget = UIImage(contentsOfFile: statusMessage!.chatBubbleIcon!)
            {
                ZStack {
                    Image(uiImage: chatBubbleIconWidget)
                        .resizable()
                        .frame(width: 24, height: 24) // 42
                    Text(numActiveConversations) // TODO: get real number
                        .lineLimit(1)
                        .font(.system(size: 14, weight: .medium, design: .default))
                        .foregroundStyle(.white)
                }
                .padding(4)
            } else {
                EmptyView();
            }
        }
    }
}
