PODS:
  - app_links (1.0.0):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - desktop_webview_window (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_saver (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - Firebase/Analytics (11.6.0):
    - Firebase/Core
  - Firebase/Core (11.6.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.6.0)
  - Firebase/CoreOnly (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - Firebase/Crashlytics (11.6.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.6.0)
  - Firebase/RemoteConfig (11.6.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.6.0)
  - firebase_analytics (11.4.0):
    - Firebase/Analytics (= 11.6.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (3.10.0):
    - Firebase/CoreOnly (~> 11.6.0)
    - FlutterMacOS
  - firebase_crashlytics (4.3.0):
    - Firebase/CoreOnly (~> 11.6.0)
    - Firebase/Crashlytics (~> 11.6.0)
    - firebase_core
    - FlutterMacOS
  - firebase_remote_config (5.3.0):
    - Firebase/CoreOnly (~> 11.6.0)
    - Firebase/RemoteConfig (~> 11.6.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseABTesting (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - FirebaseAnalytics (11.6.0):
    - FirebaseAnalytics/AdIdSupport (= 11.6.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.6.0):
    - FirebaseCoreInternal (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - FirebaseCoreInternal (11.6.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfig (11.6.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseCoreExtension (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.15.0)
  - flutter_image_compress_macos (1.0.0):
    - FlutterMacOS
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 6.0.3)
  - flutter_secure_storage_macos (6.1.3):
    - FlutterMacOS
  - flutter_web_auth_2 (3.0.0):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - GoogleAppMeasurement (11.6.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.6.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.6.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - isar_flutter_libs (1.0.0):
    - FlutterMacOS
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_file_mac (0.0.1):
    - FlutterMacOS
  - OrderedSet (6.0.3)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - share_plus (0.0.1):
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - window_to_front (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `Flutter/ephemeral/.symlinks/plugins/app_links/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin`)
  - desktop_webview_window (from `Flutter/ephemeral/.symlinks/plugins/desktop_webview_window/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_saver (from `Flutter/ephemeral/.symlinks/plugins/file_saver/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - firebase_analytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_crashlytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos`)
  - firebase_remote_config (from `Flutter/ephemeral/.symlinks/plugins/firebase_remote_config/macos`)
  - flutter_image_compress_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_image_compress_macos/macos`)
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - flutter_web_auth_2 (from `Flutter/ephemeral/.symlinks/plugins/flutter_web_auth_2/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - gal (from `Flutter/ephemeral/.symlinks/plugins/gal/darwin`)
  - isar_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/isar_flutter_libs/macos`)
  - open_file_mac (from `Flutter/ephemeral/.symlinks/plugins/open_file_mac/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)
  - window_to_front (from `Flutter/ephemeral/.symlinks/plugins/window_to_front/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift

EXTERNAL SOURCES:
  app_links:
    :path: Flutter/ephemeral/.symlinks/plugins/app_links/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin
  desktop_webview_window:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_webview_window/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_saver:
    :path: Flutter/ephemeral/.symlinks/plugins/file_saver/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  firebase_analytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_crashlytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos
  firebase_remote_config:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_remote_config/macos
  flutter_image_compress_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_image_compress_macos/macos
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  flutter_web_auth_2:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_web_auth_2/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  gal:
    :path: Flutter/ephemeral/.symlinks/plugins/gal/darwin
  isar_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/isar_flutter_libs/macos
  open_file_mac:
    :path: Flutter/ephemeral/.symlinks/plugins/open_file_mac/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin
  window_to_front:
    :path: Flutter/ephemeral/.symlinks/plugins/window_to_front/macos

SPEC CHECKSUMS:
  app_links: 9028728e32c83a0831d9db8cf91c526d16cc5468
  connectivity_plus: b21496ab28d1324eb59885d888a4d83b98531f01
  desktop_webview_window: 7e37af677d6d19294cb433d9b1d878ef78dffa4d
  device_info_plus: 724ebb83a99d517430554a1810445ff28fe71fa3
  file_saver: e35bd97de451dde55ff8c38862ed7ad0f3418d0f
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  Firebase: 374a441a91ead896215703a674d58cdb3e9d772b
  firebase_analytics: 4a9ea994dad50015bc4e408e76d1e072a3c18d99
  firebase_core: 74979c022826e0f704c32008f9b132b5f3f4fd16
  firebase_crashlytics: f0d090cf0b47a695119676453550ebb16695c2a3
  firebase_remote_config: 181268ca1603965a3bf30cc6591ab96ea6ba7d23
  FirebaseABTesting: 663ece168d2d65a31f71603d71937e326020a887
  FirebaseAnalytics: 7114c698cac995602e3b1b96663473e50d54d6e7
  FirebaseCore: 48b0dd707581cf9c1a1220da68223fb0a562afaa
  FirebaseCoreExtension: 2d77d6430c16cf43ca2b04608302ed02b3598361
  FirebaseCoreInternal: d98ab91e2d80a56d7b246856a8885443b302c0c2
  FirebaseCrashlytics: b21c665fb50138766480bce73ebdb1aa30f7f300
  FirebaseInstallations: efc0946fc756e4d22d8113f7c761948120322e8c
  FirebaseRemoteConfig: ee5161282c4e857ad81c0197cd8baec9d5dfef0e
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: 9529d14180868e29a8da164b3a729c036204918b
  FirebaseSharedSwift: e17c654ef1f1a616b0b33054e663ad1035c8fd40
  flutter_image_compress_macos: e68daf54bb4bf2144c580fd4d151c949cbf492f0
  flutter_inappwebview_macos: c2d68649f9f8f1831bfcd98d73fd6256366d9d1d
  flutter_secure_storage_macos: 7f45e30f838cf2659862a4e4e3ee1c347c2b3b54
  flutter_web_auth_2: 62b08da29f15a20fa63f144234622a1488d45b65
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  gal: 44e5b10dbd347c8247a2851acee6c1fbe282c1d3
  GoogleAppMeasurement: 6a9e6317b6a6d810ad03d4a66564ca6c4c5818a3
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  isar_flutter_libs: a65381780401f81ad6bf3f2e7cd0de5698fb98c4
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_file_mac: 01874b6d6a2c1485ac9b126d7105b99102dea2cf
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: 8147fd5037235202932580f35ebe195f7a55e3cd
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  share_plus: b818fd6bf0e537317735186d977a68a125703488
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  window_to_front: 9e76fd432e36700a197dac86a0011e49c89abe0a

PODFILE CHECKSUM: c2e95c8c0fe03c5c57e438583cae4cc732296009

COCOAPODS: 1.16.2
