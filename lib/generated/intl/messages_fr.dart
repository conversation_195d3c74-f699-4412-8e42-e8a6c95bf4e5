// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a fr locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'fr';

  static String m0(destinationName) =>
      "Conversation transférée avec succès à ${destinationName}";

  static String m1(error) => "Échec de fetchAiSuggestions : ${error}";

  static String m2(error) =>
      "Échec de fetchAiSuggestions : Erreur lors de l\'\'analyse de la réponse JSON - ${error}";

  static String m3(error) =>
      "Exception lors de l\'\'opération de contact : ${error}";

  static String m4(contactId) =>
      "createOrUpdateContactAndMEU : erreur dans la réponse : ${contactId}";

  static String m5(error) =>
      "createOrUpdateContactAndMEU : erreur dans la réponse : ${error}";

  static String m6(error) => "Exception lors de l\'\'opération MEU : ${error}";

  static String m7(error) =>
      "fetchContactById : Erreur lors de la récupération du contact : ${error}";

  static String m8(error) =>
      "fetchContactDetails : Erreur lors de la récupération des détails du contact : ${error}";

  static String m9(error) =>
      "fetchContactLayout : Erreur lors de la récupération de la mise en page du contact : ${error}";

  static String m10(error) =>
      "fetchContactRelatedLists : Erreur lors de la récupération des listes de contacts associées : ${error}";

  static String m11(error) => "Échec de fetchConversationEntries : ${error}";

  static String m12(error) => "Exception GraphQL : ${error}";

  static String m13(error) => "Erreur dans la réponse : ${error}";

  static String m14(error) => "erreur de connexion liveagent : ${error}";

  static String m15(conversationId) =>
      "Échec de la recherche de messagingSessionIdsByConversationId par conversationId : ${conversationId}";

  static String m16(error) => "Erreur lors de la requête MEU : ${error}";

  static String m17(error) =>
      "Vous avez perdu la connexion à Salesforce : ${error}";

  static String m18(error) => "Échec de sendFileToSFAndGetID : ${error}";

  static String m19(error) => "Échec de sendMessage : ${error}";

  static String m20(error) =>
      "erreur de shim_service initialization : ${error}";

  static String m21(error) =>
      "erreur de shim_service getNotificationMessage : ${error}";

  static String m22(error) => "erreur de shim_service startSession : ${error}";

  static String m23(actionType) => "Type d\'\'action inconnu : ${actionType}";

  static String m24(attachmentCount) =>
      "${attachmentCount} ${Intl.plural(attachmentCount, one: 'pièce jointe', other: 'pièces jointes')}";

  static String m25(howMany) => "${Intl.plural(howMany, one: 'à', other: 'à')}";

  static String m26(fileName) => "Fichier : ${fileName}";

  static String m27(activeConversations) =>
      "Vous allez actuellement hors ligne avec ${activeConversations} ${Intl.plural(activeConversations, one: 'conversation', other: 'conversations')} actives. Terminer ${Intl.plural(activeConversations, one: 'celle-ci', other: 'toutes')}?";

  static String m28(limit) =>
      "La taille de l\'\'image dépasse la limite du canal : ${limit} KB.";

  static String m29(username) =>
      "Marquer la conversation avec ${username} comme terminée ?";

  static String m30(recipient) =>
      "Êtes-vous sûr de vouloir transférer cette conversation à : ${recipient} ?";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "agent_transfer_success_label": m0,
    "agent_transfer_title": MessageLookupByLibrary.simpleMessage(
      "Transfert à un agent",
    ),
    "ai_recommended_response_label": MessageLookupByLibrary.simpleMessage(
      "RÉPONSE RECOMMANDÉE PAR L\'\'IA",
    ),
    "allow_notification_cta": MessageLookupByLibrary.simpleMessage(
      "Autoriser les Notifications",
    ),
    "app_error_1440_conversation_fetch_label":
        MessageLookupByLibrary.simpleMessage(
          "fetch1440Conversations : Aucune donnée trouvée.",
        ),
    "app_error_accept_actiontype_null_conversation_id_label":
        MessageLookupByLibrary.simpleMessage(
          "Accepter actionType avec conversationId nul",
        ),
    "app_error_accept_work_failed_auto_decline_timeout":
        MessageLookupByLibrary.simpleMessage(
          "Échec de l\'\'acceptation: la conversation a été refusée en raison d\'\'un délai d\'\'attente",
        ),
    "app_error_ai_suggestions_data_invalid_label":
        MessageLookupByLibrary.simpleMessage(
          "Échec de fetchAiSuggestions : Les données sont invalides ou vides",
        ),
    "app_error_ai_suggestions_failure_label": m1,
    "app_error_ai_suggestions_parse_response_label": m2,
    "app_error_contact_creation_update_label":
        MessageLookupByLibrary.simpleMessage(
          "Erreur lors de la création ou de la mise à jour du contact.",
        ),
    "app_error_contact_exception_label": m3,
    "app_error_conversation_transfer_label":
        MessageLookupByLibrary.simpleMessage(
          "Erreur lors du transfert de la conversation.",
        ),
    "app_error_create_update_contact_duplicate_label":
        MessageLookupByLibrary.simpleMessage(
          "Erreur lors de la mise à jour du MEU en double : ID de MEU en double introuvable",
        ),
    "app_error_create_update_contact_duplicate_no_error_code_label":
        MessageLookupByLibrary.simpleMessage(
          "createOrUpdateContactAndMEU : aucun code d\'\'erreur DUPLICATE_VALUE trouvé dans la réponse",
        ),
    "app_error_create_update_contact_id_label": m4,
    "app_error_create_update_contact_label": m5,
    "app_error_creating_updating_messaging_end_user_label":
        MessageLookupByLibrary.simpleMessage(
          "Erreur lors de la création ou de la mise à jour de l\'\'utilisateur final de messagerie.",
        ),
    "app_error_decline_actiontype_null_conversation_id_label":
        MessageLookupByLibrary.simpleMessage(
          "Refuser actionType avec conversationId nul",
        ),
    "app_error_device_token_retrieval_label": MessageLookupByLibrary.simpleMessage(
      "Impossible d\'\'obtenir le jeton de l\'\'appareil. Veuillez redémarrer l\'\'application et réessayer.",
    ),
    "app_error_exception_meu_label": m6,
    "app_error_fetch_contact_by_id_label": m7,
    "app_error_fetch_contact_details_label": m8,
    "app_error_fetch_contact_layout_label": m9,
    "app_error_fetch_contact_related_lists_label": m10,
    "app_error_fetch_conversation_entries_label": m11,
    "app_error_graphql_exception_label": m12,
    "app_error_graphql_refresh_token_label":
        MessageLookupByLibrary.simpleMessage(
          "Échec du renouvellement du jeton graphQL",
        ),
    "app_error_graphql_unknown_label": MessageLookupByLibrary.simpleMessage(
      "Erreur indéterminée de graphQL",
    ),
    "app_error_in_response_label": m13,
    "app_error_liveagent_connection_label": m14,
    "app_error_login_failed_label": MessageLookupByLibrary.simpleMessage(
      "Échec de la connexion. Veuillez réessayer.",
    ),
    "app_error_messaging_session_by_conversation_label": m15,
    "app_error_presence_status_fetch_label": MessageLookupByLibrary.simpleMessage(
      "Impossible de récupérer les statuts de présence. Veuillez vous reconnecter.",
    ),
    "app_error_presence_status_label": MessageLookupByLibrary.simpleMessage(
      "Échec de la définition du statut de présence. Veuillez réessayer.",
    ),
    "app_error_query_meu_label": m16,
    "app_error_received_empty_null_message_label":
        MessageLookupByLibrary.simpleMessage(
          "Message nul vide reçu du Service de Notification",
        ),
    "app_error_salesforce_existing_login_label":
        MessageLookupByLibrary.simpleMessage(
          "Vous vous êtes connecté depuis un autre emplacement.",
        ),
    "app_error_salesforce_lost_connection_gglt_label":
        MessageLookupByLibrary.simpleMessage(
          "Vous avez perdu la connexion à Salesforce (gglt).",
        ),
    "app_error_salesforce_lost_connection_label": m17,
    "app_error_salesforce_lost_connection_laks_label":
        MessageLookupByLibrary.simpleMessage(
          "Vous avez perdu la connexion à Salesforce (laks).",
        ),
    "app_error_salesforce_lost_connection_lakt_label":
        MessageLookupByLibrary.simpleMessage(
          "Vous avez perdu la connexion à Salesforce (lakt).",
        ),
    "app_error_salesforce_lost_connection_usfce_label":
        MessageLookupByLibrary.simpleMessage(
          "Vous avez perdu la connexion à Salesforce (code usfce).",
        ),
    "app_error_send_file_to_salesforce_label": m18,
    "app_error_send_message_label": m19,
    "app_error_shim_initialization_label": m20,
    "app_error_shim_notification_label": m21,
    "app_error_shim_session_expired_label":
        MessageLookupByLibrary.simpleMessage(
          "Votre session a expiré. Veuillez vous reconnecter.",
        ),
    "app_error_shim_start_session_label": m22,
    "app_error_uncaught_listener": MessageLookupByLibrary.simpleMessage(
      "Exception non interceptée lors de l\'\'appel du listener",
    ),
    "app_error_uncaught_streamer_label": MessageLookupByLibrary.simpleMessage(
      "Exception inattendue dans streamer()",
    ),
    "app_error_uncaught_streaming_label": MessageLookupByLibrary.simpleMessage(
      "Exception non interceptée lors du streaming()",
    ),
    "app_error_unexpected_auth_label": MessageLookupByLibrary.simpleMessage(
      "Exception inattendue lors de l\'\'authentification",
    ),
    "app_error_unexpected_streaming_label":
        MessageLookupByLibrary.simpleMessage(
          "Erreur inattendue lors du streaming",
        ),
    "app_error_unknown_actiontype_label": m23,
    "app_error_update_duplicate_contact_contact_id_not_found_label":
        MessageLookupByLibrary.simpleMessage(
          "Erreur lors de la mise à jour du contact en double, ID de contact en double introuvable.",
        ),
    "app_error_update_duplicate_contact_label":
        MessageLookupByLibrary.simpleMessage(
          "Erreur lors de la mise à jour du contact en double.",
        ),
    "app_permissions_notification_detailed_grant_label":
        MessageLookupByLibrary.simpleMessage(
          "1. Dans la fenêtre des Paramètres système, localisez 1440 Mobile et cliquez dessus.\n\n2. Localisez l\'élément \'\'Autorisations\'\' ou \'\'Autorisations d\'application\'\' dans le menu, et appuyez dessus.\n\n3. Vous trouverez l\'élément correctement intitulé. Assurez-vous qu\'il soit activé ou réglé sur \'‘Toujours autoriser\'’.",
        ),
    "app_permissions_notification_label": MessageLookupByLibrary.simpleMessage(
      "Application",
    ),
    "attachments_label": m24,
    "channel_label": MessageLookupByLibrary.simpleMessage(
      "Canal de communication",
    ),
    "channel_text_label": MessageLookupByLibrary.simpleMessage("SMS"),
    "channel_unknown_label": MessageLookupByLibrary.simpleMessage("Inconnue"),
    "channel_whatsapp_label": MessageLookupByLibrary.simpleMessage("WhatsApp"),
    "chat_at_label": m25,
    "chat_bubble_file_attachment_label": m26,
    "chat_bubble_message_not_sent_title": MessageLookupByLibrary.simpleMessage(
      "Votre message n\'\'a pas été envoyé. Appuyez sur \"Réessayer\" pour envoyer ce message",
    ),
    "chat_bubble_message_not_sent_try_again_cta":
        MessageLookupByLibrary.simpleMessage("Réessayer"),
    "chat_field_disabled_already_in_session":
        MessageLookupByLibrary.simpleMessage(
          "Le client est déjà dans une autre session de messagerie.",
        ),
    "chat_field_disabled_label": MessageLookupByLibrary.simpleMessage(
      "La messagerie est désactivée jusqu\'\'à ce que le client réponde.",
    ),
    "chat_load_error_message_label": MessageLookupByLibrary.simpleMessage(
      "Échec du chargement du message",
    ),
    "chat_loading_label": MessageLookupByLibrary.simpleMessage("Chargement..."),
    "chat_now_label": MessageLookupByLibrary.simpleMessage("Maintenant"),
    "chat_today_label": MessageLookupByLibrary.simpleMessage("Aujourd\'\'hui"),
    "chat_yesterday_label": MessageLookupByLibrary.simpleMessage("Hier"),
    "contact_detail_no_name_label": MessageLookupByLibrary.simpleMessage(
      "Pas de Nom",
    ),
    "contact_no_contact_info_label": MessageLookupByLibrary.simpleMessage(
      "Aucune information de contact disponible",
    ),
    "contact_search_hint": MessageLookupByLibrary.simpleMessage(
      "Commencez à taper pour rechercher un pays",
    ),
    "contact_search_label": MessageLookupByLibrary.simpleMessage("Recherche"),
    "conversation_date_time_now_label": MessageLookupByLibrary.simpleMessage(
      "Maintenant",
    ),
    "conversation_username_unknown_label": MessageLookupByLibrary.simpleMessage(
      "Inconnu",
    ),
    "conversations_screen_title": MessageLookupByLibrary.simpleMessage(
      "Messages",
    ),
    "current_status_title": MessageLookupByLibrary.simpleMessage(
      "Statut Actuel",
    ),
    "demo_company_name_label": MessageLookupByLibrary.simpleMessage(
      "Nom de l\'\'Entreprise",
    ),
    "demo_email_label": MessageLookupByLibrary.simpleMessage("Email"),
    "demo_name_label": MessageLookupByLibrary.simpleMessage("Nom"),
    "demo_phone_label": MessageLookupByLibrary.simpleMessage("Téléphone"),
    "demo_start_cta": MessageLookupByLibrary.simpleMessage("Commencer la Démo"),
    "demo_title": MessageLookupByLibrary.simpleMessage(
      "Découvrez comment Messaging Studio par 1440 peut aider votre organisation !",
    ),
    "display_templates_cta": MessageLookupByLibrary.simpleMessage("Modèles"),
    "file_save_file_options_cancel_cta": MessageLookupByLibrary.simpleMessage(
      "Annuler",
    ),
    "file_save_file_options_save_cta": MessageLookupByLibrary.simpleMessage(
      "Enregistrer",
    ),
    "form_error_label": MessageLookupByLibrary.simpleMessage(
      "Veuillez corriger les erreurs du formulaire.",
    ),
    "going_offline_modal_title": m27,
    "image_file_size_error_label": m28,
    "knowledge_article_is_latest_version_label":
        MessageLookupByLibrary.simpleMessage("Dernière Version"),
    "knowledge_article_is_latest_version_no_label":
        MessageLookupByLibrary.simpleMessage("Non"),
    "knowledge_article_is_latest_version_yes_label":
        MessageLookupByLibrary.simpleMessage("Oui"),
    "knowledge_article_language_label": MessageLookupByLibrary.simpleMessage(
      "Langue",
    ),
    "knowledge_article_number_label": MessageLookupByLibrary.simpleMessage(
      "Numéro d\'\'Article",
    ),
    "knowledge_article_publication_status_label":
        MessageLookupByLibrary.simpleMessage("Statut de la Publication"),
    "knowledge_article_record_type_label": MessageLookupByLibrary.simpleMessage(
      "Type d\'\'Enregistrement",
    ),
    "knowledge_article_version_number_label":
        MessageLookupByLibrary.simpleMessage("Numéro de Version"),
    "knowledge_articles_no_results_label": MessageLookupByLibrary.simpleMessage(
      "Aucun Article de Connaissance Trouvé",
    ),
    "knowledge_articles_no_summary_available_label":
        MessageLookupByLibrary.simpleMessage("Aucun résumé disponible"),
    "knowledge_articles_no_title_available_label":
        MessageLookupByLibrary.simpleMessage("Aucun titre disponible"),
    "knowledge_articles_screen_title": MessageLookupByLibrary.simpleMessage(
      "Articles de Connaissance",
    ),
    "knowledge_articles_search_hint_text": MessageLookupByLibrary.simpleMessage(
      "Rechercher des Articles de Connaissance",
    ),
    "knowledge_articles_send_cta": MessageLookupByLibrary.simpleMessage(
      "Envoyer",
    ),
    "knowledge_no_article_is_latest_version_label":
        MessageLookupByLibrary.simpleMessage(
          "Aucune Dernière Version Disponible",
        ),
    "knowledge_no_article_language_label": MessageLookupByLibrary.simpleMessage(
      "Aucune Langue Disponible",
    ),
    "knowledge_no_article_number_label": MessageLookupByLibrary.simpleMessage(
      "Aucun Numéro d\'\'Article Disponible",
    ),
    "knowledge_no_article_publication_status_label":
        MessageLookupByLibrary.simpleMessage(
          "Aucun Statut de Publication Disponible",
        ),
    "knowledge_no_article_record_type_label":
        MessageLookupByLibrary.simpleMessage(
          "Aucun Type d\'\'Enregistrement Disponible",
        ),
    "knowledge_no_article_version_number_label":
        MessageLookupByLibrary.simpleMessage(
          "Aucun Numéro de Version Disponible",
        ),
    "local_media_cannot_open_label": MessageLookupByLibrary.simpleMessage(
      "Impossible d\'\'ouvrir le fichier",
    ),
    "login_demo_mode_cta": MessageLookupByLibrary.simpleMessage(
      "Mode Démo de Connexion",
    ),
    "login_screen_subtitle": MessageLookupByLibrary.simpleMessage(
      "Révolutionner l\'\'expérience client, une minute à la fois.",
    ),
    "login_screen_title": MessageLookupByLibrary.simpleMessage("Bienvenue sur"),
    "login_with_salesforce_cta": MessageLookupByLibrary.simpleMessage(
      "Connexion avec Salesforce",
    ),
    "login_with_sandbox_cta": MessageLookupByLibrary.simpleMessage(
      "Connexion au Sandbox",
    ),
    "logout_menu_label": MessageLookupByLibrary.simpleMessage("Déconnexion"),
    "mark_conversation_as_done_title": m29,
    "message_filtering_show_ended_label": MessageLookupByLibrary.simpleMessage(
      "Afficher Terminés",
    ),
    "message_sorting_alphabetical_label": MessageLookupByLibrary.simpleMessage(
      "Alphabétique",
    ),
    "message_sorting_by_channel_label": MessageLookupByLibrary.simpleMessage(
      "Par Canal",
    ),
    "message_sorting_chronological_label": MessageLookupByLibrary.simpleMessage(
      "Chronologique",
    ),
    "modal_accept_cta": MessageLookupByLibrary.simpleMessage("Accepter"),
    "modal_access_location_subtitle": MessageLookupByLibrary.simpleMessage(
      "Mettre à jour automatiquement le statut et assigner le travail en fonction de votre arrivée et de votre départ du lieu de travail, même lorsque l\'\'application n\'\'est pas utilisée.",
    ),
    "modal_access_location_title": MessageLookupByLibrary.simpleMessage(
      "Autoriser 1440 à Accéder à Votre Position ?",
    ),
    "modal_cancel_cta": MessageLookupByLibrary.simpleMessage("Annuler"),
    "modal_confirm_cta": MessageLookupByLibrary.simpleMessage("Confirmer"),
    "modal_end_cta": MessageLookupByLibrary.simpleMessage("Terminer"),
    "modal_ok_cta": MessageLookupByLibrary.simpleMessage("Ok"),
    "new_contact_company_error_label": MessageLookupByLibrary.simpleMessage(
      "Le Nom de l\'\'Entreprise est requis",
    ),
    "new_contact_cta": MessageLookupByLibrary.simpleMessage("Nouveau Contact"),
    "new_contact_email_error_label": MessageLookupByLibrary.simpleMessage(
      "Email invalide. Format : <EMAIL>",
    ),
    "new_contact_first_name_error_label": MessageLookupByLibrary.simpleMessage(
      "Le Prénom est requis",
    ),
    "new_contact_international_format_error_label":
        MessageLookupByLibrary.simpleMessage(
          "Utilisez uniquement des chiffres",
        ),
    "new_contact_last_name_error_label": MessageLookupByLibrary.simpleMessage(
      "Le Nom est requis",
    ),
    "new_contact_opening_message_error_label":
        MessageLookupByLibrary.simpleMessage(
          "Le Message d\'\'ouverture est requis",
        ),
    "new_contact_screen_country_code_label":
        MessageLookupByLibrary.simpleMessage("Indicatif du Pays"),
    "new_contact_screen_email_label": MessageLookupByLibrary.simpleMessage(
      "Email",
    ),
    "new_contact_screen_first_name_label": MessageLookupByLibrary.simpleMessage(
      "Prénom",
    ),
    "new_contact_screen_from_channel_label":
        MessageLookupByLibrary.simpleMessage("Depuis la chaîne"),
    "new_contact_screen_last_name_label": MessageLookupByLibrary.simpleMessage(
      "Nom",
    ),
    "new_contact_screen_mobile_label": MessageLookupByLibrary.simpleMessage(
      "Mobile",
    ),
    "new_contact_screen_opening_message_label":
        MessageLookupByLibrary.simpleMessage("Message d\'\'Ouverture"),
    "new_contact_screen_sms_label": MessageLookupByLibrary.simpleMessage("SMS"),
    "new_contact_screen_sms_main_store_label":
        MessageLookupByLibrary.simpleMessage("SMS (magasin principal)"),
    "new_contact_screen_start_conversation_cta":
        MessageLookupByLibrary.simpleMessage("Commencer la Conversation"),
    "new_contact_screen_title": MessageLookupByLibrary.simpleMessage(
      "Nouvelle Conversation",
    ),
    "new_contact_screen_whatsapp_label": MessageLookupByLibrary.simpleMessage(
      "WhatsApp",
    ),
    "new_contact_us_format_error_label": MessageLookupByLibrary.simpleMessage(
      "Format : (XXX) XXX-XXXX",
    ),
    "new_conversation_screen_title": MessageLookupByLibrary.simpleMessage(
      "Nouvelle Conversation",
    ),
    "no_ai_suggested_title": MessageLookupByLibrary.simpleMessage(
      "Veuillez vous assurer que le package Messaging Studio est installé dans votre organisation et que les suggestions IA sont correctement configurées pour activer cette fonctionnalité.",
    ),
    "no_channels_defined_for_customer_label": MessageLookupByLibrary.simpleMessage(
      "Avis : Aucun canal de communication n\'\'a été défini dans Salesforce pour ce utilisateur.",
    ),
    "no_templates_available_label": MessageLookupByLibrary.simpleMessage(
      "Avis : Aucun modèle n\'\'est disponible pour la sélection. Veuillez contacter votre administrateur informatique",
    ),
    "no_templates_found_label": MessageLookupByLibrary.simpleMessage(
      "Aucun modèle correspondant trouvé.",
    ),
    "not_available_label": MessageLookupByLibrary.simpleMessage("N/D"),
    "notification_description_part1": MessageLookupByLibrary.simpleMessage(
      "Vous devez ",
    ),
    "notification_description_part2": MessageLookupByLibrary.simpleMessage(
      "Autoriser les Notifications",
    ),
    "notification_description_part3": MessageLookupByLibrary.simpleMessage(
      " pour utiliser cette application.",
    ),
    "notifications_disabled_label": MessageLookupByLibrary.simpleMessage(
      "NOTIFICATIONS DESACTIVÉES",
    ),
    "org_not_provisioned_subtitle": MessageLookupByLibrary.simpleMessage(
      "Organisation non provisionnée. Veuillez contacter votre administrateur système pour provisionner votre organisation.",
    ),
    "org_not_provisioned_title": MessageLookupByLibrary.simpleMessage(
      "Échec de la connexion",
    ),
    "other_form_error_label": MessageLookupByLibrary.simpleMessage(
      "Une erreur s\'\'est produite lors de la soumission du formulaire. Veuillez réessayer.",
    ),
    "permissions_notification_detailed_grant_label":
        MessageLookupByLibrary.simpleMessage(
          "1. Dans la fenêtre des Paramètres système, localisez 1440 Mobile et cliquez dessus.\n\n2. Localisez l\'élément \'\'Autorisations de notification\'\' ou \'\'Autorisations d\'application\'\' dans le menu, et appuyez dessus.\n\n3. Vous trouverez l\'élément correctement intitulé. Assurez-vous qu\'il soit activé ou réglé sur \'‘Toujours autoriser\'’.",
        ),
    "permissions_notification_label": MessageLookupByLibrary.simpleMessage(
      "Notification",
    ),
    "permissions_notification_message_label": MessageLookupByLibrary.simpleMessage(
      "Les notifications sont utilisées pour vous avertir lorsque de nouveaux travaux et messages ont été reçus.",
    ),
    "presence_status_offline_label": MessageLookupByLibrary.simpleMessage(
      "Hors ligne",
    ),
    "quick_action_configuration_error_title":
        MessageLookupByLibrary.simpleMessage(
          "Erreur de Configuration de l\'\'Action Rapide",
        ),
    "quick_text_copy_cta": MessageLookupByLibrary.simpleMessage(
      "Copier Texte Rapide",
    ),
    "quick_text_modal_search_hint": MessageLookupByLibrary.simpleMessage(
      "Rechercher Texte Rapide",
    ),
    "quick_text_modal_search_not_found_label":
        MessageLookupByLibrary.simpleMessage("Aucun Texte Rapide trouvé."),
    "quick_text_modal_title": MessageLookupByLibrary.simpleMessage(
      "Texte Rapide",
    ),
    "quick_text_no_name_available_label": MessageLookupByLibrary.simpleMessage(
      "Aucun nom disponible.",
    ),
    "search_contacts_cancel_label": MessageLookupByLibrary.simpleMessage(
      "Annuler",
    ),
    "search_contacts_found_label": MessageLookupByLibrary.simpleMessage(
      "Contacts Trouvés",
    ),
    "search_contacts_hint_text": MessageLookupByLibrary.simpleMessage(
      "Rechercher des Contacts",
    ),
    "search_messages_hint_text": MessageLookupByLibrary.simpleMessage(
      "Rechercher par Nom ou Canal",
    ),
    "search_no_contacts_found_label": MessageLookupByLibrary.simpleMessage(
      "Aucun Contact Trouvé",
    ),
    "search_recent_contacts_label": MessageLookupByLibrary.simpleMessage(
      "Contacts Récents",
    ),
    "search_templates_hint": MessageLookupByLibrary.simpleMessage(
      "Rechercher...",
    ),
    "send_template_cta": MessageLookupByLibrary.simpleMessage("Envoyer"),
    "settings_menu_label": MessageLookupByLibrary.simpleMessage("Paramètres"),
    "settings_screen_ai_suggestions_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Activer ou désactiver les Suggestions IA",
        ),
    "settings_screen_ai_suggestions_title":
        MessageLookupByLibrary.simpleMessage("Suggestions IA"),
    "settings_screen_connection_state_internet_subtitle":
        MessageLookupByLibrary.simpleMessage("Internet"),
    "settings_screen_connection_state_title":
        MessageLookupByLibrary.simpleMessage("État de la Connexion"),
    "settings_screen_connection_state_web_socket_subtitle":
        MessageLookupByLibrary.simpleMessage("Web Socket"),
    "settings_screen_copy_device_token_failure_label":
        MessageLookupByLibrary.simpleMessage(
          "Aucun jeton de l\'\'appareil à copier",
        ),
    "settings_screen_copy_device_token_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Copier le jeton APNS/FCM dans le presse-papiers",
        ),
    "settings_screen_copy_device_token_success_label":
        MessageLookupByLibrary.simpleMessage(
          "Jeton de l\'\'appareil copié dans le presse-papiers",
        ),
    "settings_screen_copy_device_token_title":
        MessageLookupByLibrary.simpleMessage(
          "Copier le jeton de l\'\'appareil",
        ),
    "settings_screen_crash_app_subtitle": MessageLookupByLibrary.simpleMessage(
      "Envoyer un Événement de Crashlytics",
    ),
    "settings_screen_crash_app_title": MessageLookupByLibrary.simpleMessage(
      "Crasher l\'\'Application",
    ),
    "settings_screen_demo_mode_subtitle": MessageLookupByLibrary.simpleMessage(
      "Activez le mode démonstration pour avoir un aperçu du fonctionnement de l\'application 1440",
    ),
    "settings_screen_demo_mode_title": MessageLookupByLibrary.simpleMessage(
      "Mode démonstration",
    ),
    "settings_screen_end_session_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Tester la Reconnexion Automatique",
        ),
    "settings_screen_end_session_title": MessageLookupByLibrary.simpleMessage(
      "Terminer la Session",
    ),
    "settings_screen_fail_next_message_send_title":
        MessageLookupByLibrary.simpleMessage(
          "Échouer l\'\'envoi du prochain message",
        ),
    "settings_screen_login_custom_domain_error":
        MessageLookupByLibrary.simpleMessage(
          "Ajoutez simplement votre domaine",
        ),
    "settings_screen_login_with_custom_domain_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Activer la Connexion avec un Domaine Personnalisé",
        ),
    "settings_screen_login_with_custom_domain_title":
        MessageLookupByLibrary.simpleMessage(
          "Connexion avec un Domaine Personnalisé",
        ),
    "settings_screen_login_with_sandbox_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Activer la connexion avec le Sandbox à des fins de test",
        ),
    "settings_screen_login_with_sandbox_title":
        MessageLookupByLibrary.simpleMessage("Connexion avec le Sandbox"),
    "settings_screen_mobile_environment_label":
        MessageLookupByLibrary.simpleMessage("Environnement"),
    "settings_screen_mobile_version_title":
        MessageLookupByLibrary.simpleMessage("Version Mobile 1440"),
    "settings_screen_show_all_app_text_styles_title":
        MessageLookupByLibrary.simpleMessage(
          "Afficher Tous les Styles de Texte de l\'\'Application",
        ),
    "settings_screen_show_dev_options_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Activer ou désactiver les éléments des paramètres de développement",
        ),
    "settings_screen_show_dev_options_title":
        MessageLookupByLibrary.simpleMessage("Afficher Options Dev"),
    "settings_screen_spoof_connection_error_title":
        MessageLookupByLibrary.simpleMessage(
          "Falsifier l\'\'Erreur de Connexion",
        ),
    "settings_screen_spoof_critical_error_subtitle":
        MessageLookupByLibrary.simpleMessage("Cela vous déconnectera"),
    "settings_screen_spoof_critical_error_title":
        MessageLookupByLibrary.simpleMessage("Falsifier l\'\'Erreur Critique"),
    "settings_screen_spoof_error_message_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Afficher une Erreur d\'\'Application Falsifiée",
        ),
    "settings_screen_spoof_error_message_title":
        MessageLookupByLibrary.simpleMessage("Message d\'\'Erreur Falsifié"),
    "settings_screen_spoof_no_ux_error_report_title":
        MessageLookupByLibrary.simpleMessage(
          "Falsifier le Rapport d\'\'Erreur sans UX",
        ),
    "settings_screen_spoof_warning_error_title":
        MessageLookupByLibrary.simpleMessage(
          "Falsifier l\'\'Avertissement d\'\'Erreur",
        ),
    "settings_screen_title": MessageLookupByLibrary.simpleMessage("Paramètres"),
    "starting_outbound_conversation_label":
        MessageLookupByLibrary.simpleMessage(
          "Démarrage de la conversation sortante...",
        ),
    "templates_error_label": MessageLookupByLibrary.simpleMessage(
      "Échec du chargement des modèles",
    ),
    "templates_selection_title": MessageLookupByLibrary.simpleMessage(
      "Modèles",
    ),
    "templates_tooltip_label": MessageLookupByLibrary.simpleMessage(
      "Plus de 24 heures se sont écoulées depuis le dernier message de ce contact. Seules les réponses par modèle sont autorisées.",
    ),
    "transfer_conversation_cta": MessageLookupByLibrary.simpleMessage(
      "Transférer",
    ),
    "transfer_dialog_cancel_cta": MessageLookupByLibrary.simpleMessage(
      "Annuler",
    ),
    "transfer_dialog_description": m30,
    "transfer_dialog_title": MessageLookupByLibrary.simpleMessage("Transférer"),
    "transfer_dialog_transfer_cta": MessageLookupByLibrary.simpleMessage(
      "Transférer",
    ),
    "transfer_error_label": MessageLookupByLibrary.simpleMessage(
      "Salesforce n\'a pas pu initier le transfert.",
    ),
    "transfer_error_title": MessageLookupByLibrary.simpleMessage("Erreur !"),
    "transfer_no_agents_available_label": MessageLookupByLibrary.simpleMessage(
      "Aucun agent disponible pour le moment.",
    ),
    "transfer_no_matching_people_label": MessageLookupByLibrary.simpleMessage(
      "Aucune personne correspondante trouvée.",
    ),
    "transfer_no_matching_queues_label": MessageLookupByLibrary.simpleMessage(
      "Aucune file d\'attente correspondante trouvée.",
    ),
    "transfer_no_queues_available_label": MessageLookupByLibrary.simpleMessage(
      "Aucune file d\'attente disponible pour le moment.",
    ),
    "transfer_people_tab_label": MessageLookupByLibrary.simpleMessage(
      "Personnes",
    ),
    "transfer_queue_tab_label": MessageLookupByLibrary.simpleMessage(
      "Files d\'attente",
    ),
    "transfer_search_hint": MessageLookupByLibrary.simpleMessage(
      "Rechercher...",
    ),
    "transfer_success_title": MessageLookupByLibrary.simpleMessage("Succès !"),
    "transfer_title": MessageLookupByLibrary.simpleMessage("Transférer"),
    "video_preview_error_label": MessageLookupByLibrary.simpleMessage(
      "Aucune vidéo sélectionnée ou erreur d\'\'affichage de la vidéo.",
    ),
    "view_contact_cancel_cta": MessageLookupByLibrary.simpleMessage("Annuler"),
    "view_contact_edit_cta": MessageLookupByLibrary.simpleMessage("Modifier"),
    "view_contact_message_cta": MessageLookupByLibrary.simpleMessage("Message"),
    "view_contact_save_cta": MessageLookupByLibrary.simpleMessage(
      "Enregistrer",
    ),
    "warning_title": MessageLookupByLibrary.simpleMessage("Avertissement"),
  };
}
