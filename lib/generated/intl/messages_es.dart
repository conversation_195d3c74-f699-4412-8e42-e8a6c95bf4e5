// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a es locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'es';

  static String m0(destinationName) =>
      "Conversación transferida con éxito a ${destinationName}";

  static String m1(error) => "Error en fetchAiSuggestions: ${error}";

  static String m2(error) =>
      "Error en fetchAiSuggestions: Error al analizar la respuesta JSON - ${error}";

  static String m3(error) =>
      "Excepción durante la operación de contacto: ${error}";

  static String m4(contactId) =>
      "createOrUpdateContactAndMEU: error en la respuesta: ${contactId}";

  static String m5(error) =>
      "createOrUpdateContactAndMEU: error en la respuesta: ${error}";

  static String m6(error) => "Excepción durante la operación MEU: ${error}";

  static String m7(error) =>
      "fetchContactById: Error al obtener el contacto: ${error}";

  static String m8(error) =>
      "fetchContactDetails: Error al obtener los detalles del contacto: ${error}";

  static String m9(error) =>
      "fetchContactLayout: Error al obtener el diseño del contacto: ${error}";

  static String m10(error) =>
      "fetchContactRelatedLists: Error al obtener las listas relacionadas con el contacto: ${error}";

  static String m11(error) => "Error al obtener conversationEntries: ${error}";

  static String m12(error) => "Excepción de GraphQL: ${error}";

  static String m13(error) => "Error en la respuesta: ${error}";

  static String m14(error) => "error en la conexión de liveagent: ${error}";

  static String m15(conversationId) =>
      "Error al encontrar messagingSessionIdsByConversationId por conversationId: ${conversationId}";

  static String m16(error) => "Error al consultar MEU: ${error}";

  static String m17(error) => "Perdiste la conexión con Salesforce: ${error}";

  static String m18(error) =>
      "Error al enviar el archivo a SF y obtener ID: ${error}";

  static String m19(error) => "Error al enviar el mensaje: ${error}";

  static String m20(error) =>
      "error del servicio shim_service initialization: ${error}";

  static String m21(error) =>
      "error del servicio shim_service getNotificationMessage: ${error}";

  static String m22(error) =>
      "error del servicio shim_service startSession: ${error}";

  static String m23(actionType) => "Tipo de acción desconocido: ${actionType}";

  static String m24(attachmentCount) =>
      "${attachmentCount} ${Intl.plural(attachmentCount, one: 'adjunto', other: 'adjuntos')}";

  static String m25(howMany) =>
      "${Intl.plural(howMany, one: 'a la', other: 'a las')}";

  static String m26(fileName) => "Archivo: ${fileName}";

  static String m27(activeConversations) =>
      "Actualmente vas a desconectarte con ${activeConversations} ${Intl.plural(activeConversations, one: 'conversación', other: 'conversaciones')} ${Intl.plural(activeConversations, one: 'activa', other: 'activas')}. ¿${Intl.plural(activeConversations, one: 'Finalizarla', other: 'Finalizar todas')}?";

  static String m28(limit) =>
      "El tamaño de la imagen supera el límite del canal: ${limit} KB.";

  static String m29(username) =>
      "¿Marcar la conversación con ${username} como terminada?";

  static String m30(recipient) =>
      "¿Está seguro que desea transferir esta conversación a: ${recipient}?";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "agent_transfer_success_label": m0,
    "agent_transfer_title": MessageLookupByLibrary.simpleMessage(
      "Transferencia a un agente",
    ),
    "ai_recommended_response_label": MessageLookupByLibrary.simpleMessage(
      "RESPUESTA RECOMENDADA POR IA",
    ),
    "allow_notification_cta": MessageLookupByLibrary.simpleMessage(
      "Permitir Notificaciones",
    ),
    "app_error_1440_conversation_fetch_label":
        MessageLookupByLibrary.simpleMessage(
          "fetch1440Conversations: No se encontraron datos.",
        ),
    "app_error_accept_actiontype_null_conversation_id_label":
        MessageLookupByLibrary.simpleMessage(
          "Aceptar actionType con conversationId nulo",
        ),
    "app_error_accept_work_failed_auto_decline_timeout":
        MessageLookupByLibrary.simpleMessage(
          "No se pudo aceptar: la conversación se rechazó debido al tiempo de espera",
        ),
    "app_error_ai_suggestions_data_invalid_label":
        MessageLookupByLibrary.simpleMessage(
          "Error en fetchAiSuggestions: Los datos son inválidos o están vacíos",
        ),
    "app_error_ai_suggestions_failure_label": m1,
    "app_error_ai_suggestions_parse_response_label": m2,
    "app_error_contact_creation_update_label":
        MessageLookupByLibrary.simpleMessage(
          "Error al crear o actualizar el contacto.",
        ),
    "app_error_contact_exception_label": m3,
    "app_error_conversation_transfer_label":
        MessageLookupByLibrary.simpleMessage(
          "Error al transferir la conversación.",
        ),
    "app_error_create_update_contact_duplicate_label":
        MessageLookupByLibrary.simpleMessage(
          "Error al actualizar el MEU duplicado: ID de MEU duplicado no encontrado",
        ),
    "app_error_create_update_contact_duplicate_no_error_code_label":
        MessageLookupByLibrary.simpleMessage(
          "createOrUpdateContactAndMEU: no se encontró el código de error DUPLICATE_VALUE en la respuesta",
        ),
    "app_error_create_update_contact_id_label": m4,
    "app_error_create_update_contact_label": m5,
    "app_error_creating_updating_messaging_end_user_label":
        MessageLookupByLibrary.simpleMessage(
          "Error al crear o actualizar el usuario final de mensajería.",
        ),
    "app_error_decline_actiontype_null_conversation_id_label":
        MessageLookupByLibrary.simpleMessage(
          "Rechazar actionType con conversationId nulo",
        ),
    "app_error_device_token_retrieval_label": MessageLookupByLibrary.simpleMessage(
      "No se pudo obtener el token del dispositivo. Por favor, reinicia la aplicación e inténtalo de nuevo.",
    ),
    "app_error_exception_meu_label": m6,
    "app_error_fetch_contact_by_id_label": m7,
    "app_error_fetch_contact_details_label": m8,
    "app_error_fetch_contact_layout_label": m9,
    "app_error_fetch_contact_related_lists_label": m10,
    "app_error_fetch_conversation_entries_label": m11,
    "app_error_graphql_exception_label": m12,
    "app_error_graphql_refresh_token_label":
        MessageLookupByLibrary.simpleMessage(
          "Error al actualizar el token de graphQL",
        ),
    "app_error_graphql_unknown_label": MessageLookupByLibrary.simpleMessage(
      "Error no determinado de graphQL",
    ),
    "app_error_in_response_label": m13,
    "app_error_liveagent_connection_label": m14,
    "app_error_login_failed_label": MessageLookupByLibrary.simpleMessage(
      "Inicio de sesión fallido. Por favor, inténtalo de nuevo.",
    ),
    "app_error_messaging_session_by_conversation_label": m15,
    "app_error_presence_status_fetch_label": MessageLookupByLibrary.simpleMessage(
      "No se pudieron obtener los estados de presencia. Por favor, inicia sesión nuevamente.",
    ),
    "app_error_presence_status_label": MessageLookupByLibrary.simpleMessage(
      "Error al establecer el estado de presencia. Por favor, inténtalo de nuevo.",
    ),
    "app_error_query_meu_label": m16,
    "app_error_received_empty_null_message_label":
        MessageLookupByLibrary.simpleMessage(
          "Recibido un mensaje nulo vacío del Servicio de Notificaciones",
        ),
    "app_error_salesforce_existing_login_label":
        MessageLookupByLibrary.simpleMessage(
          "Has iniciado sesión desde otra ubicación.",
        ),
    "app_error_salesforce_lost_connection_gglt_label":
        MessageLookupByLibrary.simpleMessage(
          "Perdiste la conexión con Salesforce (gglt).",
        ),
    "app_error_salesforce_lost_connection_label": m17,
    "app_error_salesforce_lost_connection_laks_label":
        MessageLookupByLibrary.simpleMessage(
          "Perdiste la conexión con Salesforce (laks).",
        ),
    "app_error_salesforce_lost_connection_lakt_label":
        MessageLookupByLibrary.simpleMessage(
          "Perdiste la conexión con Salesforce (lakt).",
        ),
    "app_error_salesforce_lost_connection_usfce_label":
        MessageLookupByLibrary.simpleMessage(
          "Perdiste la conexión con Salesforce (código usfce).",
        ),
    "app_error_send_file_to_salesforce_label": m18,
    "app_error_send_message_label": m19,
    "app_error_shim_initialization_label": m20,
    "app_error_shim_notification_label": m21,
    "app_error_shim_session_expired_label":
        MessageLookupByLibrary.simpleMessage(
          "Tu sesión ha expirado. Por favor, inicia sesión nuevamente.",
        ),
    "app_error_shim_start_session_label": m22,
    "app_error_uncaught_listener": MessageLookupByLibrary.simpleMessage(
      "Excepción no capturada al llamar al oyente",
    ),
    "app_error_uncaught_streamer_label": MessageLookupByLibrary.simpleMessage(
      "Excepción inesperada en streamer()",
    ),
    "app_error_uncaught_streaming_label": MessageLookupByLibrary.simpleMessage(
      "Excepción no capturada durante la transmisión()",
    ),
    "app_error_unexpected_auth_label": MessageLookupByLibrary.simpleMessage(
      "Excepción inesperada durante la autenticación",
    ),
    "app_error_unexpected_streaming_label":
        MessageLookupByLibrary.simpleMessage(
          "Error inesperado durante la transmisión",
        ),
    "app_error_unknown_actiontype_label": m23,
    "app_error_update_duplicate_contact_contact_id_not_found_label":
        MessageLookupByLibrary.simpleMessage(
          "Error al actualizar el contacto duplicado, ID de contacto duplicado no encontrado.",
        ),
    "app_error_update_duplicate_contact_label":
        MessageLookupByLibrary.simpleMessage(
          "Error al actualizar el contacto duplicado.",
        ),
    "app_permissions_notification_detailed_grant_label":
        MessageLookupByLibrary.simpleMessage(
          "1. En la ventana de Configuración del sistema, localice 1440 Mobile y haga clic en ella.\n\n2. Localice el elemento \'\'Permisos\'\' o \'\'Permisos de la aplicación\'\' en el menú, y tóquelo.\n\n3. Encontrará el elemento correctamente titulado. Asegúrese de que esté activado o configurado en \'‘Permitir siempre\'’.",
        ),
    "app_permissions_notification_label": MessageLookupByLibrary.simpleMessage(
      "Aplicación",
    ),
    "attachments_label": m24,
    "channel_label": MessageLookupByLibrary.simpleMessage(
      "Canal de comunicación",
    ),
    "channel_text_label": MessageLookupByLibrary.simpleMessage("SMS"),
    "channel_unknown_label": MessageLookupByLibrary.simpleMessage(
      "Desconocido",
    ),
    "channel_whatsapp_label": MessageLookupByLibrary.simpleMessage("WhatsApp"),
    "chat_at_label": m25,
    "chat_bubble_file_attachment_label": m26,
    "chat_bubble_message_not_sent_title": MessageLookupByLibrary.simpleMessage(
      "Tu mensaje no fue enviado. Toca \"Reintentar\" para enviar este mensaje",
    ),
    "chat_bubble_message_not_sent_try_again_cta":
        MessageLookupByLibrary.simpleMessage("Reintentar"),
    "chat_field_disabled_already_in_session":
        MessageLookupByLibrary.simpleMessage(
          "El cliente ya está en otra sesión de mensajería.",
        ),
    "chat_field_disabled_label": MessageLookupByLibrary.simpleMessage(
      "La mensajería está desactivada hasta que el cliente responda.",
    ),
    "chat_load_error_message_label": MessageLookupByLibrary.simpleMessage(
      "Error al cargar el mensaje",
    ),
    "chat_loading_label": MessageLookupByLibrary.simpleMessage("Cargando..."),
    "chat_now_label": MessageLookupByLibrary.simpleMessage("Ahora"),
    "chat_today_label": MessageLookupByLibrary.simpleMessage("Hoy"),
    "chat_yesterday_label": MessageLookupByLibrary.simpleMessage("Ayer"),
    "contact_detail_no_name_label": MessageLookupByLibrary.simpleMessage(
      "Sin Nombre",
    ),
    "contact_no_contact_info_label": MessageLookupByLibrary.simpleMessage(
      "No hay información de contacto disponible",
    ),
    "contact_search_hint": MessageLookupByLibrary.simpleMessage(
      "Empieza a escribir para buscar país",
    ),
    "contact_search_label": MessageLookupByLibrary.simpleMessage("Buscar"),
    "conversation_date_time_now_label": MessageLookupByLibrary.simpleMessage(
      "Ahora",
    ),
    "conversation_username_unknown_label": MessageLookupByLibrary.simpleMessage(
      "Desconocido",
    ),
    "conversations_screen_title": MessageLookupByLibrary.simpleMessage(
      "Mensajes",
    ),
    "current_status_title": MessageLookupByLibrary.simpleMessage(
      "Estado Actual",
    ),
    "demo_company_name_label": MessageLookupByLibrary.simpleMessage(
      "Nombre de la Empresa",
    ),
    "demo_email_label": MessageLookupByLibrary.simpleMessage(
      "Correo Electrónico",
    ),
    "demo_name_label": MessageLookupByLibrary.simpleMessage("Nombre"),
    "demo_phone_label": MessageLookupByLibrary.simpleMessage("Teléfono"),
    "demo_start_cta": MessageLookupByLibrary.simpleMessage(
      "Iniciar Demostración",
    ),
    "demo_title": MessageLookupByLibrary.simpleMessage(
      "¡Mira cómo Messaging Studio de 1440 puede ayudar a tu organización!",
    ),
    "display_templates_cta": MessageLookupByLibrary.simpleMessage("Plantillas"),
    "file_save_file_options_cancel_cta": MessageLookupByLibrary.simpleMessage(
      "Cancelar",
    ),
    "file_save_file_options_save_cta": MessageLookupByLibrary.simpleMessage(
      "Guardar",
    ),
    "form_error_label": MessageLookupByLibrary.simpleMessage(
      "Por favor, corrige los errores del formulario.",
    ),
    "going_offline_modal_title": m27,
    "image_file_size_error_label": m28,
    "knowledge_article_is_latest_version_label":
        MessageLookupByLibrary.simpleMessage("Es la Última Versión"),
    "knowledge_article_is_latest_version_no_label":
        MessageLookupByLibrary.simpleMessage("No"),
    "knowledge_article_is_latest_version_yes_label":
        MessageLookupByLibrary.simpleMessage("Sí"),
    "knowledge_article_language_label": MessageLookupByLibrary.simpleMessage(
      "Idioma",
    ),
    "knowledge_article_number_label": MessageLookupByLibrary.simpleMessage(
      "Número de Artículo",
    ),
    "knowledge_article_publication_status_label":
        MessageLookupByLibrary.simpleMessage("Estado de Publicación"),
    "knowledge_article_record_type_label": MessageLookupByLibrary.simpleMessage(
      "Tipo de Registro",
    ),
    "knowledge_article_version_number_label":
        MessageLookupByLibrary.simpleMessage("Número de Versión"),
    "knowledge_articles_no_results_label": MessageLookupByLibrary.simpleMessage(
      "No se encontraron Artículos de Conocimiento",
    ),
    "knowledge_articles_no_summary_available_label":
        MessageLookupByLibrary.simpleMessage("No hay resumen disponible"),
    "knowledge_articles_no_title_available_label":
        MessageLookupByLibrary.simpleMessage("No hay título disponible"),
    "knowledge_articles_screen_title": MessageLookupByLibrary.simpleMessage(
      "Artículos de Conocimiento",
    ),
    "knowledge_articles_search_hint_text": MessageLookupByLibrary.simpleMessage(
      "Buscar Artículos de Conocimiento",
    ),
    "knowledge_articles_send_cta": MessageLookupByLibrary.simpleMessage(
      "Enviar",
    ),
    "knowledge_no_article_is_latest_version_label":
        MessageLookupByLibrary.simpleMessage(
          "No hay Última Versión Disponible",
        ),
    "knowledge_no_article_language_label": MessageLookupByLibrary.simpleMessage(
      "No hay Idioma Disponible",
    ),
    "knowledge_no_article_number_label": MessageLookupByLibrary.simpleMessage(
      "No hay Número de Artículo Disponible",
    ),
    "knowledge_no_article_publication_status_label":
        MessageLookupByLibrary.simpleMessage(
          "No hay Estado de Publicación Disponible",
        ),
    "knowledge_no_article_record_type_label":
        MessageLookupByLibrary.simpleMessage(
          "No hay Tipo de Registro Disponible",
        ),
    "knowledge_no_article_version_number_label":
        MessageLookupByLibrary.simpleMessage(
          "No hay Número de Versión Disponible",
        ),
    "local_media_cannot_open_label": MessageLookupByLibrary.simpleMessage(
      "No se puede abrir el archivo",
    ),
    "login_demo_mode_cta": MessageLookupByLibrary.simpleMessage(
      "Modo de demostración de inicio de sesión",
    ),
    "login_screen_subtitle": MessageLookupByLibrary.simpleMessage(
      "Revoluciona la experiencia del cliente, un minuto a la vez.",
    ),
    "login_screen_title": MessageLookupByLibrary.simpleMessage("Bienvenido a"),
    "login_with_salesforce_cta": MessageLookupByLibrary.simpleMessage(
      "Iniciar sesión con Salesforce",
    ),
    "login_with_sandbox_cta": MessageLookupByLibrary.simpleMessage(
      "Iniciar sesión con Sandbox",
    ),
    "logout_menu_label": MessageLookupByLibrary.simpleMessage("Cerrar sesión"),
    "mark_conversation_as_done_title": m29,
    "message_filtering_show_ended_label": MessageLookupByLibrary.simpleMessage(
      "Mostrar Terminados",
    ),
    "message_sorting_alphabetical_label": MessageLookupByLibrary.simpleMessage(
      "Alfabético",
    ),
    "message_sorting_by_channel_label": MessageLookupByLibrary.simpleMessage(
      "Por Canal",
    ),
    "message_sorting_chronological_label": MessageLookupByLibrary.simpleMessage(
      "Cronológico",
    ),
    "modal_accept_cta": MessageLookupByLibrary.simpleMessage("Aceptar"),
    "modal_access_location_subtitle": MessageLookupByLibrary.simpleMessage(
      "Actualizar automáticamente el estado y asignar trabajo basado en cuando llegues y te vayas del lugar de trabajo, incluso cuando la aplicación no esté en uso.",
    ),
    "modal_access_location_title": MessageLookupByLibrary.simpleMessage(
      "¿Permitir que 1440 acceda a tu ubicación?",
    ),
    "modal_cancel_cta": MessageLookupByLibrary.simpleMessage("Cancelar"),
    "modal_confirm_cta": MessageLookupByLibrary.simpleMessage("Confirmar"),
    "modal_end_cta": MessageLookupByLibrary.simpleMessage("Finalizar"),
    "modal_ok_cta": MessageLookupByLibrary.simpleMessage("Ok"),
    "new_contact_company_error_label": MessageLookupByLibrary.simpleMessage(
      "El Nombre de la Empresa es obligatorio",
    ),
    "new_contact_cta": MessageLookupByLibrary.simpleMessage("Nuevo Contacto"),
    "new_contact_email_error_label": MessageLookupByLibrary.simpleMessage(
      "Correo electrónico inválido. Formato: <EMAIL>",
    ),
    "new_contact_first_name_error_label": MessageLookupByLibrary.simpleMessage(
      "El Nombre es obligatorio",
    ),
    "new_contact_international_format_error_label":
        MessageLookupByLibrary.simpleMessage("Usa solo números"),
    "new_contact_last_name_error_label": MessageLookupByLibrary.simpleMessage(
      "El Apellido es obligatorio",
    ),
    "new_contact_opening_message_error_label":
        MessageLookupByLibrary.simpleMessage(
          "El Mensaje Inicial es obligatorio",
        ),
    "new_contact_screen_country_code_label":
        MessageLookupByLibrary.simpleMessage("Código de País"),
    "new_contact_screen_email_label": MessageLookupByLibrary.simpleMessage(
      "Correo Electrónico",
    ),
    "new_contact_screen_first_name_label": MessageLookupByLibrary.simpleMessage(
      "Nombre",
    ),
    "new_contact_screen_from_channel_label":
        MessageLookupByLibrary.simpleMessage("Del canal"),
    "new_contact_screen_last_name_label": MessageLookupByLibrary.simpleMessage(
      "Apellido",
    ),
    "new_contact_screen_mobile_label": MessageLookupByLibrary.simpleMessage(
      "Móvil",
    ),
    "new_contact_screen_opening_message_label":
        MessageLookupByLibrary.simpleMessage("Mensaje Inicial"),
    "new_contact_screen_sms_label": MessageLookupByLibrary.simpleMessage("SMS"),
    "new_contact_screen_sms_main_store_label":
        MessageLookupByLibrary.simpleMessage("SMS (Tienda principal)"),
    "new_contact_screen_start_conversation_cta":
        MessageLookupByLibrary.simpleMessage("Iniciar Conversación"),
    "new_contact_screen_title": MessageLookupByLibrary.simpleMessage(
      "Nueva Conversación",
    ),
    "new_contact_screen_whatsapp_label": MessageLookupByLibrary.simpleMessage(
      "WhatsApp",
    ),
    "new_contact_us_format_error_label": MessageLookupByLibrary.simpleMessage(
      "Formato: (XXX) XXX-XXXX",
    ),
    "new_conversation_screen_title": MessageLookupByLibrary.simpleMessage(
      "Nueva Conversación",
    ),
    "no_ai_suggested_title": MessageLookupByLibrary.simpleMessage(
      "Por favor, asegúrate de que el paquete de Messaging Studio esté instalado en tu organización y que las sugerencias de IA estén correctamente configuradas para habilitar esta función.",
    ),
    "no_channels_defined_for_customer_label": MessageLookupByLibrary.simpleMessage(
      "Aviso: No se han definido canales de comunicación en Salesforce para este usuario.",
    ),
    "no_templates_available_label": MessageLookupByLibrary.simpleMessage(
      "Aviso: No hay plantillas disponibles para seleccionar. Por favor, contacte a su administrador de TI",
    ),
    "no_templates_found_label": MessageLookupByLibrary.simpleMessage(
      "No se encontraron plantillas coincidentes.",
    ),
    "not_available_label": MessageLookupByLibrary.simpleMessage("N/D"),
    "notification_description_part1": MessageLookupByLibrary.simpleMessage(
      "Debes ",
    ),
    "notification_description_part2": MessageLookupByLibrary.simpleMessage(
      "Permitir Notificaciones",
    ),
    "notification_description_part3": MessageLookupByLibrary.simpleMessage(
      " para usar esta aplicación.",
    ),
    "notifications_disabled_label": MessageLookupByLibrary.simpleMessage(
      "NOTIFICACIONES DESHABILITADAS",
    ),
    "org_not_provisioned_subtitle": MessageLookupByLibrary.simpleMessage(
      "Organización no provisionada. Póngase en contacto con su administrador del sistema para provisionar su organización.",
    ),
    "org_not_provisioned_title": MessageLookupByLibrary.simpleMessage(
      "Error al iniciar sesión",
    ),
    "other_form_error_label": MessageLookupByLibrary.simpleMessage(
      "Hubo un error al enviar el formulario. Por favor, intenta de nuevo.",
    ),
    "permissions_notification_detailed_grant_label":
        MessageLookupByLibrary.simpleMessage(
          "1. En la ventana de Configuración del sistema, localice 1440 Mobile y haga clic en ella.\n\n2. Localice el elemento \'\'Permisos de notificación\'\' o \'\'Permisos de la aplicación\'\' en el menú, y tóquelo.\n\n3. Encontrará el elemento correctamente titulado. Asegúrese de que esté activado o configurado en \'‘Permitir siempre\'’.",
        ),
    "permissions_notification_label": MessageLookupByLibrary.simpleMessage(
      "Notificación",
    ),
    "permissions_notification_message_label": MessageLookupByLibrary.simpleMessage(
      "Las notificaciones se utilizan para informarle cuando se han recibido nuevos trabajos y mensajes.",
    ),
    "presence_status_offline_label": MessageLookupByLibrary.simpleMessage(
      "Desconectado",
    ),
    "quick_action_configuration_error_title":
        MessageLookupByLibrary.simpleMessage(
          "Error de Configuración de la Acción Rápida",
        ),
    "quick_text_copy_cta": MessageLookupByLibrary.simpleMessage(
      "Copiar Texto Rápido",
    ),
    "quick_text_modal_search_hint": MessageLookupByLibrary.simpleMessage(
      "Buscar Texto Rápido",
    ),
    "quick_text_modal_search_not_found_label":
        MessageLookupByLibrary.simpleMessage(
          "No se encontraron Textos Rápidos.",
        ),
    "quick_text_modal_title": MessageLookupByLibrary.simpleMessage(
      "Texto Rápido",
    ),
    "quick_text_no_name_available_label": MessageLookupByLibrary.simpleMessage(
      "Sin nombre disponible.",
    ),
    "search_contacts_cancel_label": MessageLookupByLibrary.simpleMessage(
      "Cancelar",
    ),
    "search_contacts_found_label": MessageLookupByLibrary.simpleMessage(
      "Contactos Encontrados",
    ),
    "search_contacts_hint_text": MessageLookupByLibrary.simpleMessage(
      "Buscar Contactos",
    ),
    "search_messages_hint_text": MessageLookupByLibrary.simpleMessage(
      "Buscar por Nombre o Canal",
    ),
    "search_no_contacts_found_label": MessageLookupByLibrary.simpleMessage(
      "No se encontraron contactos",
    ),
    "search_recent_contacts_label": MessageLookupByLibrary.simpleMessage(
      "Contactos Recientes",
    ),
    "search_templates_hint": MessageLookupByLibrary.simpleMessage("Buscar..."),
    "send_template_cta": MessageLookupByLibrary.simpleMessage("Enviar"),
    "settings_menu_label": MessageLookupByLibrary.simpleMessage(
      "Configuración",
    ),
    "settings_screen_ai_suggestions_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Habilitar o deshabilitar Sugerencias de IA",
        ),
    "settings_screen_ai_suggestions_title":
        MessageLookupByLibrary.simpleMessage("Sugerencias de IA"),
    "settings_screen_connection_state_internet_subtitle":
        MessageLookupByLibrary.simpleMessage("Internet"),
    "settings_screen_connection_state_title":
        MessageLookupByLibrary.simpleMessage("Estado de la Conexión"),
    "settings_screen_connection_state_web_socket_subtitle":
        MessageLookupByLibrary.simpleMessage("Web Socket"),
    "settings_screen_copy_device_token_failure_label":
        MessageLookupByLibrary.simpleMessage(
          "No hay token del dispositivo para copiar",
        ),
    "settings_screen_copy_device_token_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Copiar token APNS/FCM al portapapeles",
        ),
    "settings_screen_copy_device_token_success_label":
        MessageLookupByLibrary.simpleMessage(
          "Token del dispositivo copiado al portapapeles",
        ),
    "settings_screen_copy_device_token_title":
        MessageLookupByLibrary.simpleMessage("Copiar token del dispositivo"),
    "settings_screen_crash_app_subtitle": MessageLookupByLibrary.simpleMessage(
      "Enviar Evento de Fallo a Crashlytics",
    ),
    "settings_screen_crash_app_title": MessageLookupByLibrary.simpleMessage(
      "Fallar App",
    ),
    "settings_screen_demo_mode_subtitle": MessageLookupByLibrary.simpleMessage(
      "Habilite el modo de demostración para obtener una visión general de cómo funciona la aplicación 1440",
    ),
    "settings_screen_demo_mode_title": MessageLookupByLibrary.simpleMessage(
      "Modo de demostración",
    ),
    "settings_screen_end_session_subtitle":
        MessageLookupByLibrary.simpleMessage("Probar Reconexión Automática"),
    "settings_screen_end_session_title": MessageLookupByLibrary.simpleMessage(
      "Terminar Sesión",
    ),
    "settings_screen_fail_next_message_send_title":
        MessageLookupByLibrary.simpleMessage("Fallar Próximo Envío de Mensaje"),
    "settings_screen_login_custom_domain_error":
        MessageLookupByLibrary.simpleMessage("Solo añade tu dominio"),
    "settings_screen_login_with_custom_domain_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Habilitar el Inicio de Sesión con Dominio Personalizado",
        ),
    "settings_screen_login_with_custom_domain_title":
        MessageLookupByLibrary.simpleMessage(
          "Iniciar sesión con Dominio Personalizado",
        ),
    "settings_screen_login_with_sandbox_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Habilitar el inicio de sesión de Sandbox para propósitos de prueba",
        ),
    "settings_screen_login_with_sandbox_title":
        MessageLookupByLibrary.simpleMessage("Iniciar sesión con Sandbox"),
    "settings_screen_mobile_environment_label":
        MessageLookupByLibrary.simpleMessage("Entorno"),
    "settings_screen_mobile_version_title":
        MessageLookupByLibrary.simpleMessage("Versión Móvil 1440"),
    "settings_screen_show_all_app_text_styles_title":
        MessageLookupByLibrary.simpleMessage(
          "Mostrar Todos los Estilos de Texto de la App",
        ),
    "settings_screen_show_dev_options_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Habilitar o deshabilitar elementos de configuración de desarrollo",
        ),
    "settings_screen_show_dev_options_title":
        MessageLookupByLibrary.simpleMessage("Mostrar Opciones de Desarrollo"),
    "settings_screen_spoof_connection_error_title":
        MessageLookupByLibrary.simpleMessage("Falsificar Error de Conexión"),
    "settings_screen_spoof_critical_error_subtitle":
        MessageLookupByLibrary.simpleMessage("Esto te desconectará"),
    "settings_screen_spoof_critical_error_title":
        MessageLookupByLibrary.simpleMessage("Falsificar Error Crítico"),
    "settings_screen_spoof_error_message_subtitle":
        MessageLookupByLibrary.simpleMessage(
          "Mostrar Error Falsificado de la App",
        ),
    "settings_screen_spoof_error_message_title":
        MessageLookupByLibrary.simpleMessage("Falsificar Mensaje de Error"),
    "settings_screen_spoof_no_ux_error_report_title":
        MessageLookupByLibrary.simpleMessage(
          "Falsificar Informe de Error Sin UX",
        ),
    "settings_screen_spoof_warning_error_title":
        MessageLookupByLibrary.simpleMessage("Falsificar Error de Advertencia"),
    "settings_screen_title": MessageLookupByLibrary.simpleMessage(
      "Configuración",
    ),
    "starting_outbound_conversation_label":
        MessageLookupByLibrary.simpleMessage(
          "Iniciando conversación saliente...",
        ),
    "templates_error_label": MessageLookupByLibrary.simpleMessage(
      "Error al cargar las plantillas",
    ),
    "templates_selection_title": MessageLookupByLibrary.simpleMessage(
      "Plantillas",
    ),
    "templates_tooltip_label": MessageLookupByLibrary.simpleMessage(
      "Han pasado más de 24 horas desde el último mensaje de este contacto. Solo se permiten respuestas de plantillas.",
    ),
    "transfer_conversation_cta": MessageLookupByLibrary.simpleMessage(
      "Transferir",
    ),
    "transfer_dialog_cancel_cta": MessageLookupByLibrary.simpleMessage(
      "Cancelar",
    ),
    "transfer_dialog_description": m30,
    "transfer_dialog_title": MessageLookupByLibrary.simpleMessage("Transferir"),
    "transfer_dialog_transfer_cta": MessageLookupByLibrary.simpleMessage(
      "Transferir",
    ),
    "transfer_error_label": MessageLookupByLibrary.simpleMessage(
      "Salesforce no pudo iniciar la transferencia.",
    ),
    "transfer_error_title": MessageLookupByLibrary.simpleMessage("¡Error!"),
    "transfer_no_agents_available_label": MessageLookupByLibrary.simpleMessage(
      "No hay agentes disponibles en este momento.",
    ),
    "transfer_no_matching_people_label": MessageLookupByLibrary.simpleMessage(
      "No se encontraron personas coincidentes.",
    ),
    "transfer_no_matching_queues_label": MessageLookupByLibrary.simpleMessage(
      "No se encontraron colas coincidentes.",
    ),
    "transfer_no_queues_available_label": MessageLookupByLibrary.simpleMessage(
      "No hay colas disponibles en este momento.",
    ),
    "transfer_people_tab_label": MessageLookupByLibrary.simpleMessage(
      "Personas",
    ),
    "transfer_queue_tab_label": MessageLookupByLibrary.simpleMessage("Colas"),
    "transfer_search_hint": MessageLookupByLibrary.simpleMessage("Buscar..."),
    "transfer_success_title": MessageLookupByLibrary.simpleMessage("¡Éxito!"),
    "transfer_title": MessageLookupByLibrary.simpleMessage("Transferir"),
    "video_preview_error_label": MessageLookupByLibrary.simpleMessage(
      "No se seleccionó video o error al mostrar el video.",
    ),
    "view_contact_cancel_cta": MessageLookupByLibrary.simpleMessage("Cancelar"),
    "view_contact_edit_cta": MessageLookupByLibrary.simpleMessage("Editar"),
    "view_contact_message_cta": MessageLookupByLibrary.simpleMessage("Mensaje"),
    "view_contact_save_cta": MessageLookupByLibrary.simpleMessage("Guardar"),
    "warning_title": MessageLookupByLibrary.simpleMessage("Advertencia"),
  };
}
