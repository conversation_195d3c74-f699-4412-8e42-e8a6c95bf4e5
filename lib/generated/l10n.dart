// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `accept actionType with null conversationId`
  String get app_error_accept_actiontype_null_conversation_id_label {
    return Intl.message(
      'accept actionType with null conversationId',
      name: 'app_error_accept_actiontype_null_conversation_id_label',
      desc: '',
      args: [],
    );
  }

  /// `declineConversation actionType with null conversationId`
  String get app_error_decline_actiontype_null_conversation_id_label {
    return Intl.message(
      'declineConversation actionType with null conversationId',
      name: 'app_error_decline_actiontype_null_conversation_id_label',
      desc: '',
      args: [],
    );
  }

  /// `Unknown actionType: {actionType}`
  String app_error_unknown_actiontype_label(Object actionType) {
    return Intl.message(
      'Unknown actionType: $actionType',
      name: 'app_error_unknown_actiontype_label',
      desc: '',
      args: [actionType],
    );
  }

  /// `Login failed. Please try again.`
  String get app_error_login_failed_label {
    return Intl.message(
      'Login failed. Please try again.',
      name: 'app_error_login_failed_label',
      desc: '',
      args: [],
    );
  }

  /// `Unexpected exception during auth`
  String get app_error_unexpected_auth_label {
    return Intl.message(
      'Unexpected exception during auth',
      name: 'app_error_unexpected_auth_label',
      desc: '',
      args: [],
    );
  }

  /// `Unexpected error while streaming`
  String get app_error_unexpected_streaming_label {
    return Intl.message(
      'Unexpected error while streaming',
      name: 'app_error_unexpected_streaming_label',
      desc: '',
      args: [],
    );
  }

  /// `Uncaught exception during stream()`
  String get app_error_uncaught_streaming_label {
    return Intl.message(
      'Uncaught exception during stream()',
      name: 'app_error_uncaught_streaming_label',
      desc: '',
      args: [],
    );
  }

  /// `Unexpected exception in streamer()`
  String get app_error_uncaught_streamer_label {
    return Intl.message(
      'Unexpected exception in streamer()',
      name: 'app_error_uncaught_streamer_label',
      desc: '',
      args: [],
    );
  }

  /// `fetchContactById: Error fetching contact: {error}`
  String app_error_fetch_contact_by_id_label(Object error) {
    return Intl.message(
      'fetchContactById: Error fetching contact: $error',
      name: 'app_error_fetch_contact_by_id_label',
      desc: '',
      args: [error],
    );
  }

  /// `fetchContactDetails: Error fetching contact: {error}`
  String app_error_fetch_contact_details_label(Object error) {
    return Intl.message(
      'fetchContactDetails: Error fetching contact: $error',
      name: 'app_error_fetch_contact_details_label',
      desc: '',
      args: [error],
    );
  }

  /// `Uncaught exception calling listener`
  String get app_error_uncaught_listener {
    return Intl.message(
      'Uncaught exception calling listener',
      name: 'app_error_uncaught_listener',
      desc: '',
      args: [],
    );
  }

  /// `fetchContactLayout: Error fetching contact layout: {error}`
  String app_error_fetch_contact_layout_label(Object error) {
    return Intl.message(
      'fetchContactLayout: Error fetching contact layout: $error',
      name: 'app_error_fetch_contact_layout_label',
      desc: '',
      args: [error],
    );
  }

  /// `fetchContactRelatedLists: Error fetching contact related lists: {error}`
  String app_error_fetch_contact_related_lists_label(Object error) {
    return Intl.message(
      'fetchContactRelatedLists: Error fetching contact related lists: $error',
      name: 'app_error_fetch_contact_related_lists_label',
      desc: '',
      args: [error],
    );
  }

  /// `Error updating duplicate contact.`
  String get app_error_update_duplicate_contact_label {
    return Intl.message(
      'Error updating duplicate contact.',
      name: 'app_error_update_duplicate_contact_label',
      desc: '',
      args: [],
    );
  }

  /// `Exception during contact operation: {error}`
  String app_error_contact_exception_label(Object error) {
    return Intl.message(
      'Exception during contact operation: $error',
      name: 'app_error_contact_exception_label',
      desc: '',
      args: [error],
    );
  }

  /// `Error creating or updating contact.`
  String get app_error_contact_creation_update_label {
    return Intl.message(
      'Error creating or updating contact.',
      name: 'app_error_contact_creation_update_label',
      desc: '',
      args: [],
    );
  }

  /// `Error updating duplicate contact, duplicate contact ID not found.`
  String get app_error_update_duplicate_contact_contact_id_not_found_label {
    return Intl.message(
      'Error updating duplicate contact, duplicate contact ID not found.',
      name: 'app_error_update_duplicate_contact_contact_id_not_found_label',
      desc: '',
      args: [],
    );
  }

  /// `Error in response: {error}`
  String app_error_in_response_label(Object error) {
    return Intl.message(
      'Error in response: $error',
      name: 'app_error_in_response_label',
      desc: '',
      args: [error],
    );
  }

  /// `Error creating or updating messaging end user.`
  String get app_error_creating_updating_messaging_end_user_label {
    return Intl.message(
      'Error creating or updating messaging end user.',
      name: 'app_error_creating_updating_messaging_end_user_label',
      desc: '',
      args: [],
    );
  }

  /// `Error querying MEU: {error}`
  String app_error_query_meu_label(Object error) {
    return Intl.message(
      'Error querying MEU: $error',
      name: 'app_error_query_meu_label',
      desc: '',
      args: [error],
    );
  }

  /// `Exception during MEU operation: {error}`
  String app_error_exception_meu_label(Object error) {
    return Intl.message(
      'Exception during MEU operation: $error',
      name: 'app_error_exception_meu_label',
      desc: '',
      args: [error],
    );
  }

  /// `createOrUpdateContactAndMEU: error in response: {error}`
  String app_error_create_update_contact_label(Object error) {
    return Intl.message(
      'createOrUpdateContactAndMEU: error in response: $error',
      name: 'app_error_create_update_contact_label',
      desc: '',
      args: [error],
    );
  }

  /// `Error updating duplicate MEU: Duplicate MEU ID not found`
  String get app_error_create_update_contact_duplicate_label {
    return Intl.message(
      'Error updating duplicate MEU: Duplicate MEU ID not found',
      name: 'app_error_create_update_contact_duplicate_label',
      desc: '',
      args: [],
    );
  }

  /// `createOrUpdateContactAndMEU: no DUPLICATE_VALUE error code found in response`
  String get app_error_create_update_contact_duplicate_no_error_code_label {
    return Intl.message(
      'createOrUpdateContactAndMEU: no DUPLICATE_VALUE error code found in response',
      name: 'app_error_create_update_contact_duplicate_no_error_code_label',
      desc: '',
      args: [],
    );
  }

  /// `createOrUpdateContactAndMEU: error in response: {contactId}`
  String app_error_create_update_contact_id_label(Object contactId) {
    return Intl.message(
      'createOrUpdateContactAndMEU: error in response: $contactId',
      name: 'app_error_create_update_contact_id_label',
      desc: '',
      args: [contactId],
    );
  }

  /// `error from shim_service startSession: {error}`
  String app_error_shim_start_session_label(Object error) {
    return Intl.message(
      'error from shim_service startSession: $error',
      name: 'app_error_shim_start_session_label',
      desc: '',
      args: [error],
    );
  }

  /// `error from shim_service initialization: {error}`
  String app_error_shim_initialization_label(Object error) {
    return Intl.message(
      'error from shim_service initialization: $error',
      name: 'app_error_shim_initialization_label',
      desc: '',
      args: [error],
    );
  }

  /// `error from liveagent connection: {error}`
  String app_error_liveagent_connection_label(Object error) {
    return Intl.message(
      'error from liveagent connection: $error',
      name: 'app_error_liveagent_connection_label',
      desc: '',
      args: [error],
    );
  }

  /// `error from shim_service getNotificationMessage: {error}`
  String app_error_shim_notification_label(Object error) {
    return Intl.message(
      'error from shim_service getNotificationMessage: $error',
      name: 'app_error_shim_notification_label',
      desc: '',
      args: [error],
    );
  }

  /// `Your session has expired. Please log in again.`
  String get app_error_shim_session_expired_label {
    return Intl.message(
      'Your session has expired. Please log in again.',
      name: 'app_error_shim_session_expired_label',
      desc: '',
      args: [],
    );
  }

  /// `Received an empty null message from Notification Service`
  String get app_error_received_empty_null_message_label {
    return Intl.message(
      'Received an empty null message from Notification Service',
      name: 'app_error_received_empty_null_message_label',
      desc: '',
      args: [],
    );
  }

  /// `Could not get device token. Please restart the app and try again.`
  String get app_error_device_token_retrieval_label {
    return Intl.message(
      'Could not get device token. Please restart the app and try again.',
      name: 'app_error_device_token_retrieval_label',
      desc: '',
      args: [],
    );
  }

  /// `undetermined graphQL error`
  String get app_error_graphql_unknown_label {
    return Intl.message(
      'undetermined graphQL error',
      name: 'app_error_graphql_unknown_label',
      desc: '',
      args: [],
    );
  }

  /// `failed to refresh graphQL token`
  String get app_error_graphql_refresh_token_label {
    return Intl.message(
      'failed to refresh graphQL token',
      name: 'app_error_graphql_refresh_token_label',
      desc: '',
      args: [],
    );
  }

  /// `Fetch conversationEntries failed: {error}`
  String app_error_fetch_conversation_entries_label(Object error) {
    return Intl.message(
      'Fetch conversationEntries failed: $error',
      name: 'app_error_fetch_conversation_entries_label',
      desc: '',
      args: [error],
    );
  }

  /// `failed to find messagingSessionIdsByConversationId by conversationId: {conversationId}`
  String app_error_messaging_session_by_conversation_label(
    Object conversationId,
  ) {
    return Intl.message(
      'failed to find messagingSessionIdsByConversationId by conversationId: $conversationId',
      name: 'app_error_messaging_session_by_conversation_label',
      desc: '',
      args: [conversationId],
    );
  }

  /// `GraphQL Exception: {error}`
  String app_error_graphql_exception_label(Object error) {
    return Intl.message(
      'GraphQL Exception: $error',
      name: 'app_error_graphql_exception_label',
      desc: '',
      args: [error],
    );
  }

  /// `sendMessage Failure: {error}`
  String app_error_send_message_label(Object error) {
    return Intl.message(
      'sendMessage Failure: $error',
      name: 'app_error_send_message_label',
      desc: '',
      args: [error],
    );
  }

  /// `sendFileToSFAndGetID Failure: {error}`
  String app_error_send_file_to_salesforce_label(Object error) {
    return Intl.message(
      'sendFileToSFAndGetID Failure: $error',
      name: 'app_error_send_file_to_salesforce_label',
      desc: '',
      args: [error],
    );
  }

  /// `fetch1440Conversations: No data found.`
  String get app_error_1440_conversation_fetch_label {
    return Intl.message(
      'fetch1440Conversations: No data found.',
      name: 'app_error_1440_conversation_fetch_label',
      desc: '',
      args: [],
    );
  }

  /// `fetchAiSuggestions Failure: Data is invalid or empty`
  String get app_error_ai_suggestions_data_invalid_label {
    return Intl.message(
      'fetchAiSuggestions Failure: Data is invalid or empty',
      name: 'app_error_ai_suggestions_data_invalid_label',
      desc: '',
      args: [],
    );
  }

  /// `fetchAiSuggestions Failure: Error parsing JSON response - {error}`
  String app_error_ai_suggestions_parse_response_label(Object error) {
    return Intl.message(
      'fetchAiSuggestions Failure: Error parsing JSON response - $error',
      name: 'app_error_ai_suggestions_parse_response_label',
      desc: '',
      args: [error],
    );
  }

  /// `fetchAiSuggestions Failure: {error}`
  String app_error_ai_suggestions_failure_label(Object error) {
    return Intl.message(
      'fetchAiSuggestions Failure: $error',
      name: 'app_error_ai_suggestions_failure_label',
      desc: '',
      args: [error],
    );
  }

  /// `Failed to set presence status. Please try again.`
  String get app_error_presence_status_label {
    return Intl.message(
      'Failed to set presence status. Please try again.',
      name: 'app_error_presence_status_label',
      desc: '',
      args: [],
    );
  }

  /// `Sorry! There was a problem with transferring this conversation.`
  String get app_error_conversation_transfer_label {
    return Intl.message(
      'Sorry! There was a problem with transferring this conversation.',
      name: 'app_error_conversation_transfer_label',
      desc: '',
      args: [],
    );
  }

  /// `You have logged in from another location.`
  String get app_error_salesforce_existing_login_label {
    return Intl.message(
      'You have logged in from another location.',
      name: 'app_error_salesforce_existing_login_label',
      desc: '',
      args: [],
    );
  }

  /// `You have lost connection to Salesforce: {error}`
  String app_error_salesforce_lost_connection_label(Object error) {
    return Intl.message(
      'You have lost connection to Salesforce: $error',
      name: 'app_error_salesforce_lost_connection_label',
      desc: '',
      args: [error],
    );
  }

  /// `You have lost connection to Salesforce (code usfce).`
  String get app_error_salesforce_lost_connection_usfce_label {
    return Intl.message(
      'You have lost connection to Salesforce (code usfce).',
      name: 'app_error_salesforce_lost_connection_usfce_label',
      desc: '',
      args: [],
    );
  }

  /// `You have lost connection to Salesforce (laks).`
  String get app_error_salesforce_lost_connection_laks_label {
    return Intl.message(
      'You have lost connection to Salesforce (laks).',
      name: 'app_error_salesforce_lost_connection_laks_label',
      desc: '',
      args: [],
    );
  }

  /// `You have lost connection to Salesforce (lakt).`
  String get app_error_salesforce_lost_connection_lakt_label {
    return Intl.message(
      'You have lost connection to Salesforce (lakt).',
      name: 'app_error_salesforce_lost_connection_lakt_label',
      desc: '',
      args: [],
    );
  }

  /// `You have lost connection to Salesforce (gglt).`
  String get app_error_salesforce_lost_connection_gglt_label {
    return Intl.message(
      'You have lost connection to Salesforce (gglt).',
      name: 'app_error_salesforce_lost_connection_gglt_label',
      desc: '',
      args: [],
    );
  }

  /// `Could not fetch presence statuses. Please login again.`
  String get app_error_presence_status_fetch_label {
    return Intl.message(
      'Could not fetch presence statuses. Please login again.',
      name: 'app_error_presence_status_fetch_label',
      desc: '',
      args: [],
    );
  }

  /// `Notification`
  String get permissions_notification_label {
    return Intl.message(
      'Notification',
      name: 'permissions_notification_label',
      desc: '',
      args: [],
    );
  }

  /// `Notifications are used to notify you when new work and messages have been received.`
  String get permissions_notification_message_label {
    return Intl.message(
      'Notifications are used to notify you when new work and messages have been received.',
      name: 'permissions_notification_message_label',
      desc: '',
      args: [],
    );
  }

  /// `1. In the System Settings window, locate 1440 Mobile and click on it.\n\n2. Locate the ''Notification Permissions'' or ''App permissions'' item in the menu, and tap it.\n\n3. You''ll find the correctly titled item. Make sure it is turned on or set to '‘Allow all the time'’.`
  String get permissions_notification_detailed_grant_label {
    return Intl.message(
      '1. In the System Settings window, locate 1440 Mobile and click on it.\n\n2. Locate the \'\'Notification Permissions\'\' or \'\'App permissions\'\' item in the menu, and tap it.\n\n3. You\'\'ll find the correctly titled item. Make sure it is turned on or set to \'‘Allow all the time\'’.',
      name: 'permissions_notification_detailed_grant_label',
      desc: '',
      args: [],
    );
  }

  /// `App`
  String get app_permissions_notification_label {
    return Intl.message(
      'App',
      name: 'app_permissions_notification_label',
      desc: '',
      args: [],
    );
  }

  /// `1. In the System Settings window, locate 1440 Mobile and click on it.\n\n2. Locate the ''Permissions'' or ''App permissions'' item in the menu, and tap it.\n\n3. You''ll find the correctly titled item. Make sure it is turned on or set to '‘Allow all the time'’.`
  String get app_permissions_notification_detailed_grant_label {
    return Intl.message(
      '1. In the System Settings window, locate 1440 Mobile and click on it.\n\n2. Locate the \'\'Permissions\'\' or \'\'App permissions\'\' item in the menu, and tap it.\n\n3. You\'\'ll find the correctly titled item. Make sure it is turned on or set to \'‘Allow all the time\'’.',
      name: 'app_permissions_notification_detailed_grant_label',
      desc: '',
      args: [],
    );
  }

  /// `Failed to Login`
  String get org_not_provisioned_title {
    return Intl.message(
      'Failed to Login',
      name: 'org_not_provisioned_title',
      desc: '',
      args: [],
    );
  }

  /// `Organization not provisioned. Please contact your system administrator to provision your organization.`
  String get org_not_provisioned_subtitle {
    return Intl.message(
      'Organization not provisioned. Please contact your system administrator to provision your organization.',
      name: 'org_not_provisioned_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `AI RECOMMENDED RESPONSE`
  String get ai_recommended_response_label {
    return Intl.message(
      'AI RECOMMENDED RESPONSE',
      name: 'ai_recommended_response_label',
      desc: '',
      args: [],
    );
  }

  /// `Login with Salesforce`
  String get login_with_salesforce_cta {
    return Intl.message(
      'Login with Salesforce',
      name: 'login_with_salesforce_cta',
      desc: '',
      args: [],
    );
  }

  /// `Login to Sandbox`
  String get login_with_sandbox_cta {
    return Intl.message(
      'Login to Sandbox',
      name: 'login_with_sandbox_cta',
      desc: '',
      args: [],
    );
  }

  /// `Login Demo Mode`
  String get login_demo_mode_cta {
    return Intl.message(
      'Login Demo Mode',
      name: 'login_demo_mode_cta',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to`
  String get login_screen_title {
    return Intl.message(
      'Welcome to',
      name: 'login_screen_title',
      desc: '',
      args: [],
    );
  }

  /// `Revolutionize the customer experience, one minute at a time.`
  String get login_screen_subtitle {
    return Intl.message(
      'Revolutionize the customer experience, one minute at a time.',
      name: 'login_screen_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Current Status`
  String get current_status_title {
    return Intl.message(
      'Current Status',
      name: 'current_status_title',
      desc: '',
      args: [],
    );
  }

  /// `Search by Name or Channel`
  String get search_messages_hint_text {
    return Intl.message(
      'Search by Name or Channel',
      name: 'search_messages_hint_text',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get settings_menu_label {
    return Intl.message(
      'Settings',
      name: 'settings_menu_label',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout_menu_label {
    return Intl.message(
      'Logout',
      name: 'logout_menu_label',
      desc: '',
      args: [],
    );
  }

  /// `Messages`
  String get conversations_screen_title {
    return Intl.message(
      'Messages',
      name: 'conversations_screen_title',
      desc: '',
      args: [],
    );
  }

  /// `Alphabetical`
  String get message_sorting_alphabetical_label {
    return Intl.message(
      'Alphabetical',
      name: 'message_sorting_alphabetical_label',
      desc: '',
      args: [],
    );
  }

  /// `Chronological`
  String get message_sorting_chronological_label {
    return Intl.message(
      'Chronological',
      name: 'message_sorting_chronological_label',
      desc: '',
      args: [],
    );
  }

  /// `By Channel`
  String get message_sorting_by_channel_label {
    return Intl.message(
      'By Channel',
      name: 'message_sorting_by_channel_label',
      desc: '',
      args: [],
    );
  }

  /// `Show Ended`
  String get message_filtering_show_ended_label {
    return Intl.message(
      'Show Ended',
      name: 'message_filtering_show_ended_label',
      desc: '',
      args: [],
    );
  }

  /// `Contact`
  String get new_contact_screen_title {
    return Intl.message(
      'Contact',
      name: 'new_contact_screen_title',
      desc: '',
      args: [],
    );
  }

  /// `Search Contacts`
  String get search_contacts_hint_text {
    return Intl.message(
      'Search Contacts',
      name: 'search_contacts_hint_text',
      desc: '',
      args: [],
    );
  }

  /// `No Contacts Found`
  String get search_no_contacts_found_label {
    return Intl.message(
      'No Contacts Found',
      name: 'search_no_contacts_found_label',
      desc: '',
      args: [],
    );
  }

  /// `Contacts Found`
  String get search_contacts_found_label {
    return Intl.message(
      'Contacts Found',
      name: 'search_contacts_found_label',
      desc: '',
      args: [],
    );
  }

  /// `Recent Contacts`
  String get search_recent_contacts_label {
    return Intl.message(
      'Recent Contacts',
      name: 'search_recent_contacts_label',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get search_contacts_cancel_label {
    return Intl.message(
      'Cancel',
      name: 'search_contacts_cancel_label',
      desc: '',
      args: [],
    );
  }

  /// `New Contact`
  String get new_contact_cta {
    return Intl.message(
      'New Contact',
      name: 'new_contact_cta',
      desc: '',
      args: [],
    );
  }

  /// `New Conversation`
  String get new_conversation_screen_title {
    return Intl.message(
      'New Conversation',
      name: 'new_conversation_screen_title',
      desc: '',
      args: [],
    );
  }

  /// `SMS`
  String get new_contact_screen_sms_label {
    return Intl.message(
      'SMS',
      name: 'new_contact_screen_sms_label',
      desc: '',
      args: [],
    );
  }

  /// `WhatsApp`
  String get new_contact_screen_whatsapp_label {
    return Intl.message(
      'WhatsApp',
      name: 'new_contact_screen_whatsapp_label',
      desc: '',
      args: [],
    );
  }

  /// `First Name`
  String get new_contact_screen_first_name_label {
    return Intl.message(
      'First Name',
      name: 'new_contact_screen_first_name_label',
      desc: '',
      args: [],
    );
  }

  /// `Last Name`
  String get new_contact_screen_last_name_label {
    return Intl.message(
      'Last Name',
      name: 'new_contact_screen_last_name_label',
      desc: '',
      args: [],
    );
  }

  /// `Country Code`
  String get new_contact_screen_country_code_label {
    return Intl.message(
      'Country Code',
      name: 'new_contact_screen_country_code_label',
      desc: '',
      args: [],
    );
  }

  /// `Mobile`
  String get new_contact_screen_mobile_label {
    return Intl.message(
      'Mobile',
      name: 'new_contact_screen_mobile_label',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get new_contact_screen_email_label {
    return Intl.message(
      'Email',
      name: 'new_contact_screen_email_label',
      desc: '',
      args: [],
    );
  }

  /// `Opening Message`
  String get new_contact_screen_opening_message_label {
    return Intl.message(
      'Opening Message',
      name: 'new_contact_screen_opening_message_label',
      desc: '',
      args: [],
    );
  }

  /// `Start Conversation`
  String get new_contact_screen_start_conversation_cta {
    return Intl.message(
      'Start Conversation',
      name: 'new_contact_screen_start_conversation_cta',
      desc: '',
      args: [],
    );
  }

  /// `First Name is required`
  String get new_contact_first_name_error_label {
    return Intl.message(
      'First Name is required',
      name: 'new_contact_first_name_error_label',
      desc: '',
      args: [],
    );
  }

  /// `Last Name is required`
  String get new_contact_last_name_error_label {
    return Intl.message(
      'Last Name is required',
      name: 'new_contact_last_name_error_label',
      desc: '',
      args: [],
    );
  }

  /// `Invalid email. Format: <EMAIL>`
  String get new_contact_email_error_label {
    return Intl.message(
      'Invalid email. Format: <EMAIL>',
      name: 'new_contact_email_error_label',
      desc: '',
      args: [],
    );
  }

  /// `Company Name is required`
  String get new_contact_company_error_label {
    return Intl.message(
      'Company Name is required',
      name: 'new_contact_company_error_label',
      desc: '',
      args: [],
    );
  }

  /// `Opening Message is required`
  String get new_contact_opening_message_error_label {
    return Intl.message(
      'Opening Message is required',
      name: 'new_contact_opening_message_error_label',
      desc: '',
      args: [],
    );
  }

  /// `Format: (XXX) XXX-XXXX`
  String get new_contact_us_format_error_label {
    return Intl.message(
      'Format: (XXX) XXX-XXXX',
      name: 'new_contact_us_format_error_label',
      desc: '',
      args: [],
    );
  }

  /// `Use only numbers`
  String get new_contact_international_format_error_label {
    return Intl.message(
      'Use only numbers',
      name: 'new_contact_international_format_error_label',
      desc: '',
      args: [],
    );
  }

  /// `From Channel`
  String get new_contact_screen_from_channel_label {
    return Intl.message(
      'From Channel',
      name: 'new_contact_screen_from_channel_label',
      desc: '',
      args: [],
    );
  }

  /// `SMS (Main Store)`
  String get new_contact_screen_sms_main_store_label {
    return Intl.message(
      'SMS (Main Store)',
      name: 'new_contact_screen_sms_main_store_label',
      desc: '',
      args: [],
    );
  }

  /// `Quick Text`
  String get quick_text_modal_title {
    return Intl.message(
      'Quick Text',
      name: 'quick_text_modal_title',
      desc: '',
      args: [],
    );
  }

  /// `Search Quick Text`
  String get quick_text_modal_search_hint {
    return Intl.message(
      'Search Quick Text',
      name: 'quick_text_modal_search_hint',
      desc: '',
      args: [],
    );
  }

  /// `No Quick Texts found.`
  String get quick_text_modal_search_not_found_label {
    return Intl.message(
      'No Quick Texts found.',
      name: 'quick_text_modal_search_not_found_label',
      desc: '',
      args: [],
    );
  }

  /// `No name available.`
  String get quick_text_no_name_available_label {
    return Intl.message(
      'No name available.',
      name: 'quick_text_no_name_available_label',
      desc: '',
      args: [],
    );
  }

  /// `Copy Quick Text`
  String get quick_text_copy_cta {
    return Intl.message(
      'Copy Quick Text',
      name: 'quick_text_copy_cta',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get settings_screen_title {
    return Intl.message(
      'Settings',
      name: 'settings_screen_title',
      desc: '',
      args: [],
    );
  }

  /// `AI Suggestions`
  String get settings_screen_ai_suggestions_title {
    return Intl.message(
      'AI Suggestions',
      name: 'settings_screen_ai_suggestions_title',
      desc: '',
      args: [],
    );
  }

  /// `Enable or disable AI Suggestions`
  String get settings_screen_ai_suggestions_subtitle {
    return Intl.message(
      'Enable or disable AI Suggestions',
      name: 'settings_screen_ai_suggestions_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Show Dev Options`
  String get settings_screen_show_dev_options_title {
    return Intl.message(
      'Show Dev Options',
      name: 'settings_screen_show_dev_options_title',
      desc: '',
      args: [],
    );
  }

  /// `Enable or disable dev settings items`
  String get settings_screen_show_dev_options_subtitle {
    return Intl.message(
      'Enable or disable dev settings items',
      name: 'settings_screen_show_dev_options_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Connection State`
  String get settings_screen_connection_state_title {
    return Intl.message(
      'Connection State',
      name: 'settings_screen_connection_state_title',
      desc: '',
      args: [],
    );
  }

  /// `Internet`
  String get settings_screen_connection_state_internet_subtitle {
    return Intl.message(
      'Internet',
      name: 'settings_screen_connection_state_internet_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Web Socket`
  String get settings_screen_connection_state_web_socket_subtitle {
    return Intl.message(
      'Web Socket',
      name: 'settings_screen_connection_state_web_socket_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Spoof Error Message`
  String get settings_screen_spoof_error_message_title {
    return Intl.message(
      'Spoof Error Message',
      name: 'settings_screen_spoof_error_message_title',
      desc: '',
      args: [],
    );
  }

  /// `Show Spoofed App Error`
  String get settings_screen_spoof_error_message_subtitle {
    return Intl.message(
      'Show Spoofed App Error',
      name: 'settings_screen_spoof_error_message_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Crash App`
  String get settings_screen_crash_app_title {
    return Intl.message(
      'Crash App',
      name: 'settings_screen_crash_app_title',
      desc: '',
      args: [],
    );
  }

  /// `Send Crashlytics Crash Event`
  String get settings_screen_crash_app_subtitle {
    return Intl.message(
      'Send Crashlytics Crash Event',
      name: 'settings_screen_crash_app_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `End Session`
  String get settings_screen_end_session_title {
    return Intl.message(
      'End Session',
      name: 'settings_screen_end_session_title',
      desc: '',
      args: [],
    );
  }

  /// `Test Auto Reconnect`
  String get settings_screen_end_session_subtitle {
    return Intl.message(
      'Test Auto Reconnect',
      name: 'settings_screen_end_session_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Copy device token`
  String get settings_screen_copy_device_token_title {
    return Intl.message(
      'Copy device token',
      name: 'settings_screen_copy_device_token_title',
      desc: '',
      args: [],
    );
  }

  /// `Device token copied to clipboard`
  String get settings_screen_copy_device_token_success_label {
    return Intl.message(
      'Device token copied to clipboard',
      name: 'settings_screen_copy_device_token_success_label',
      desc: '',
      args: [],
    );
  }

  /// `No device token to copy`
  String get settings_screen_copy_device_token_failure_label {
    return Intl.message(
      'No device token to copy',
      name: 'settings_screen_copy_device_token_failure_label',
      desc: '',
      args: [],
    );
  }

  /// `Copy APNS/FCM topken to the clipboard`
  String get settings_screen_copy_device_token_subtitle {
    return Intl.message(
      'Copy APNS/FCM topken to the clipboard',
      name: 'settings_screen_copy_device_token_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `1440 Mobile Version`
  String get settings_screen_mobile_version_title {
    return Intl.message(
      '1440 Mobile Version',
      name: 'settings_screen_mobile_version_title',
      desc: '',
      args: [],
    );
  }

  /// `Environment`
  String get settings_screen_mobile_environment_label {
    return Intl.message(
      'Environment',
      name: 'settings_screen_mobile_environment_label',
      desc: '',
      args: [],
    );
  }

  /// `Fail Next Message Send`
  String get settings_screen_fail_next_message_send_title {
    return Intl.message(
      'Fail Next Message Send',
      name: 'settings_screen_fail_next_message_send_title',
      desc: '',
      args: [],
    );
  }

  /// `Show All App Text Styles`
  String get settings_screen_show_all_app_text_styles_title {
    return Intl.message(
      'Show All App Text Styles',
      name: 'settings_screen_show_all_app_text_styles_title',
      desc: '',
      args: [],
    );
  }

  /// `Spoof No UX Error Report`
  String get settings_screen_spoof_no_ux_error_report_title {
    return Intl.message(
      'Spoof No UX Error Report',
      name: 'settings_screen_spoof_no_ux_error_report_title',
      desc: '',
      args: [],
    );
  }

  /// `Spoof Warning Error`
  String get settings_screen_spoof_warning_error_title {
    return Intl.message(
      'Spoof Warning Error',
      name: 'settings_screen_spoof_warning_error_title',
      desc: '',
      args: [],
    );
  }

  /// `Spoof Connection Error`
  String get settings_screen_spoof_connection_error_title {
    return Intl.message(
      'Spoof Connection Error',
      name: 'settings_screen_spoof_connection_error_title',
      desc: '',
      args: [],
    );
  }

  /// `Spoof Critical Error`
  String get settings_screen_spoof_critical_error_title {
    return Intl.message(
      'Spoof Critical Error',
      name: 'settings_screen_spoof_critical_error_title',
      desc: '',
      args: [],
    );
  }

  /// `This will log you out`
  String get settings_screen_spoof_critical_error_subtitle {
    return Intl.message(
      'This will log you out',
      name: 'settings_screen_spoof_critical_error_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Login with Sandbox`
  String get settings_screen_login_with_sandbox_title {
    return Intl.message(
      'Login with Sandbox',
      name: 'settings_screen_login_with_sandbox_title',
      desc: '',
      args: [],
    );
  }

  /// `Enable sandbox login for testing purposes`
  String get settings_screen_login_with_sandbox_subtitle {
    return Intl.message(
      'Enable sandbox login for testing purposes',
      name: 'settings_screen_login_with_sandbox_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Login with Custom Domain`
  String get settings_screen_login_with_custom_domain_title {
    return Intl.message(
      'Login with Custom Domain',
      name: 'settings_screen_login_with_custom_domain_title',
      desc: '',
      args: [],
    );
  }

  /// `Enable Custom Domain Login`
  String get settings_screen_login_with_custom_domain_subtitle {
    return Intl.message(
      'Enable Custom Domain Login',
      name: 'settings_screen_login_with_custom_domain_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Demo Mode`
  String get settings_screen_demo_mode_title {
    return Intl.message(
      'Demo Mode',
      name: 'settings_screen_demo_mode_title',
      desc: '',
      args: [],
    );
  }

  /// `Enable demo mode for an overview on how the 1440 app works`
  String get settings_screen_demo_mode_subtitle {
    return Intl.message(
      'Enable demo mode for an overview on how the 1440 app works',
      name: 'settings_screen_demo_mode_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Just add your domain`
  String get settings_screen_login_custom_domain_error {
    return Intl.message(
      'Just add your domain',
      name: 'settings_screen_login_custom_domain_error',
      desc: '',
      args: [],
    );
  }

  /// `Mark the conversation with {username} as done?`
  String mark_conversation_as_done_title(Object username) {
    return Intl.message(
      'Mark the conversation with $username as done?',
      name: 'mark_conversation_as_done_title',
      desc: '',
      args: [username],
    );
  }

  /// `Confirm`
  String get modal_confirm_cta {
    return Intl.message(
      'Confirm',
      name: 'modal_confirm_cta',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get modal_cancel_cta {
    return Intl.message('Cancel', name: 'modal_cancel_cta', desc: '', args: []);
  }

  /// `End`
  String get modal_end_cta {
    return Intl.message('End', name: 'modal_end_cta', desc: '', args: []);
  }

  /// `Ok`
  String get modal_ok_cta {
    return Intl.message('Ok', name: 'modal_ok_cta', desc: '', args: []);
  }

  /// `Accept`
  String get modal_accept_cta {
    return Intl.message('Accept', name: 'modal_accept_cta', desc: '', args: []);
  }

  /// `Please ensure the Messaging Studio package is installed in your org and AI suggestions are properly set up to enable this feature.`
  String get no_ai_suggested_title {
    return Intl.message(
      'Please ensure the Messaging Studio package is installed in your org and AI suggestions are properly set up to enable this feature.',
      name: 'no_ai_suggested_title',
      desc: '',
      args: [],
    );
  }

  /// `Your message was not sent. Tap "Try Again" to send this message`
  String get chat_bubble_message_not_sent_title {
    return Intl.message(
      'Your message was not sent. Tap "Try Again" to send this message',
      name: 'chat_bubble_message_not_sent_title',
      desc: '',
      args: [],
    );
  }

  /// `Try Again`
  String get chat_bubble_message_not_sent_try_again_cta {
    return Intl.message(
      'Try Again',
      name: 'chat_bubble_message_not_sent_try_again_cta',
      desc: '',
      args: [],
    );
  }

  /// `Allow 1440 to Access Your Location?`
  String get modal_access_location_title {
    return Intl.message(
      'Allow 1440 to Access Your Location?',
      name: 'modal_access_location_title',
      desc: '',
      args: [],
    );
  }

  /// `Automatically update status and assign work based on when you arrive at and leave retail work location, even when the app is not in use.`
  String get modal_access_location_subtitle {
    return Intl.message(
      'Automatically update status and assign work based on when you arrive at and leave retail work location, even when the app is not in use.',
      name: 'modal_access_location_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `File: {fileName}`
  String chat_bubble_file_attachment_label(Object fileName) {
    return Intl.message(
      'File: $fileName',
      name: 'chat_bubble_file_attachment_label',
      desc: '',
      args: [fileName],
    );
  }

  /// `Starting outbound conversation...`
  String get starting_outbound_conversation_label {
    return Intl.message(
      'Starting outbound conversation...',
      name: 'starting_outbound_conversation_label',
      desc: '',
      args: [],
    );
  }

  /// `Please correct the form errors.`
  String get form_error_label {
    return Intl.message(
      'Please correct the form errors.',
      name: 'form_error_label',
      desc: '',
      args: [],
    );
  }

  /// `There was an error submitting the form. Please try again.`
  String get other_form_error_label {
    return Intl.message(
      'There was an error submitting the form. Please try again.',
      name: 'other_form_error_label',
      desc: '',
      args: [],
    );
  }

  /// `See how Messaging Studio by 1440 can help your organization!`
  String get demo_title {
    return Intl.message(
      'See how Messaging Studio by 1440 can help your organization!',
      name: 'demo_title',
      desc: '',
      args: [],
    );
  }

  /// `First Name`
  String get demo_name_label {
    return Intl.message(
      'First Name',
      name: 'demo_name_label',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get demo_email_label {
    return Intl.message('Email', name: 'demo_email_label', desc: '', args: []);
  }

  /// `Company Name`
  String get demo_company_name_label {
    return Intl.message(
      'Company Name',
      name: 'demo_company_name_label',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get demo_phone_label {
    return Intl.message('Phone', name: 'demo_phone_label', desc: '', args: []);
  }

  /// `Start Demo`
  String get demo_start_cta {
    return Intl.message(
      'Start Demo',
      name: 'demo_start_cta',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get demo_submit_button {
    return Intl.message(
      'Submit',
      name: 'demo_submit_button',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get contact_search_label {
    return Intl.message(
      'Search',
      name: 'contact_search_label',
      desc: '',
      args: [],
    );
  }

  /// `Start typing to search country`
  String get contact_search_hint {
    return Intl.message(
      'Start typing to search country',
      name: 'contact_search_hint',
      desc: '',
      args: [],
    );
  }

  /// `No contact information available`
  String get contact_no_contact_info_label {
    return Intl.message(
      'No contact information available',
      name: 'contact_no_contact_info_label',
      desc: '',
      args: [],
    );
  }

  /// `Knowledge Articles`
  String get knowledge_articles_screen_title {
    return Intl.message(
      'Knowledge Articles',
      name: 'knowledge_articles_screen_title',
      desc: '',
      args: [],
    );
  }

  /// `Search Knowledge Articles`
  String get knowledge_articles_search_hint_text {
    return Intl.message(
      'Search Knowledge Articles',
      name: 'knowledge_articles_search_hint_text',
      desc: '',
      args: [],
    );
  }

  /// `No Knowledge Articles Found`
  String get knowledge_articles_no_results_label {
    return Intl.message(
      'No Knowledge Articles Found',
      name: 'knowledge_articles_no_results_label',
      desc: '',
      args: [],
    );
  }

  /// `No title available`
  String get knowledge_articles_no_title_available_label {
    return Intl.message(
      'No title available',
      name: 'knowledge_articles_no_title_available_label',
      desc: '',
      args: [],
    );
  }

  /// `No summary available`
  String get knowledge_articles_no_summary_available_label {
    return Intl.message(
      'No summary available',
      name: 'knowledge_articles_no_summary_available_label',
      desc: '',
      args: [],
    );
  }

  /// `Send`
  String get knowledge_articles_send_cta {
    return Intl.message(
      'Send',
      name: 'knowledge_articles_send_cta',
      desc: '',
      args: [],
    );
  }

  /// `Article Number`
  String get knowledge_article_number_label {
    return Intl.message(
      'Article Number',
      name: 'knowledge_article_number_label',
      desc: '',
      args: [],
    );
  }

  /// `No Article Number Available`
  String get knowledge_no_article_number_label {
    return Intl.message(
      'No Article Number Available',
      name: 'knowledge_no_article_number_label',
      desc: '',
      args: [],
    );
  }

  /// `Record Type`
  String get knowledge_article_record_type_label {
    return Intl.message(
      'Record Type',
      name: 'knowledge_article_record_type_label',
      desc: '',
      args: [],
    );
  }

  /// `No Record Type Available`
  String get knowledge_no_article_record_type_label {
    return Intl.message(
      'No Record Type Available',
      name: 'knowledge_no_article_record_type_label',
      desc: '',
      args: [],
    );
  }

  /// `Version Number`
  String get knowledge_article_version_number_label {
    return Intl.message(
      'Version Number',
      name: 'knowledge_article_version_number_label',
      desc: '',
      args: [],
    );
  }

  /// `No Version Number Available`
  String get knowledge_no_article_version_number_label {
    return Intl.message(
      'No Version Number Available',
      name: 'knowledge_no_article_version_number_label',
      desc: '',
      args: [],
    );
  }

  /// `Is Latest Version`
  String get knowledge_article_is_latest_version_label {
    return Intl.message(
      'Is Latest Version',
      name: 'knowledge_article_is_latest_version_label',
      desc: '',
      args: [],
    );
  }

  /// `No Latest Version Available`
  String get knowledge_no_article_is_latest_version_label {
    return Intl.message(
      'No Latest Version Available',
      name: 'knowledge_no_article_is_latest_version_label',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get knowledge_article_is_latest_version_yes_label {
    return Intl.message(
      'Yes',
      name: 'knowledge_article_is_latest_version_yes_label',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get knowledge_article_is_latest_version_no_label {
    return Intl.message(
      'No',
      name: 'knowledge_article_is_latest_version_no_label',
      desc: '',
      args: [],
    );
  }

  /// `Publication Status`
  String get knowledge_article_publication_status_label {
    return Intl.message(
      'Publication Status',
      name: 'knowledge_article_publication_status_label',
      desc: '',
      args: [],
    );
  }

  /// `No Publication Status Available`
  String get knowledge_no_article_publication_status_label {
    return Intl.message(
      'No Publication Status Available',
      name: 'knowledge_no_article_publication_status_label',
      desc: '',
      args: [],
    );
  }

  /// `Language`
  String get knowledge_article_language_label {
    return Intl.message(
      'Language',
      name: 'knowledge_article_language_label',
      desc: '',
      args: [],
    );
  }

  /// `No Language Available`
  String get knowledge_no_article_language_label {
    return Intl.message(
      'No Language Available',
      name: 'knowledge_no_article_language_label',
      desc: '',
      args: [],
    );
  }

  /// `Unknown`
  String get conversation_username_unknown_label {
    return Intl.message(
      'Unknown',
      name: 'conversation_username_unknown_label',
      desc: '',
      args: [],
    );
  }

  /// `Now`
  String get conversation_date_time_now_label {
    return Intl.message(
      'Now',
      name: 'conversation_date_time_now_label',
      desc: '',
      args: [],
    );
  }

  /// `Transfer`
  String get transfer_conversation_cta {
    return Intl.message(
      'Transfer',
      name: 'transfer_conversation_cta',
      desc: '',
      args: [],
    );
  }

  /// `No agents available at the moment.`
  String get transfer_no_agents_available_label {
    return Intl.message(
      'No agents available at the moment.',
      name: 'transfer_no_agents_available_label',
      desc: '',
      args: [],
    );
  }

  /// `No video selected or error showing video.`
  String get video_preview_error_label {
    return Intl.message(
      'No video selected or error showing video.',
      name: 'video_preview_error_label',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get file_save_file_options_save_cta {
    return Intl.message(
      'Save',
      name: 'file_save_file_options_save_cta',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get file_save_file_options_cancel_cta {
    return Intl.message(
      'Cancel',
      name: 'file_save_file_options_cancel_cta',
      desc: '',
      args: [],
    );
  }

  /// `Cannot open file`
  String get local_media_cannot_open_label {
    return Intl.message(
      'Cannot open file',
      name: 'local_media_cannot_open_label',
      desc: '',
      args: [],
    );
  }

  /// `Loading...`
  String get chat_loading_label {
    return Intl.message(
      'Loading...',
      name: 'chat_loading_label',
      desc: '',
      args: [],
    );
  }

  /// `Yesterday`
  String get chat_yesterday_label {
    return Intl.message(
      'Yesterday',
      name: 'chat_yesterday_label',
      desc: '',
      args: [],
    );
  }

  /// `Today`
  String get chat_today_label {
    return Intl.message('Today', name: 'chat_today_label', desc: '', args: []);
  }

  /// `Now`
  String get chat_now_label {
    return Intl.message('Now', name: 'chat_now_label', desc: '', args: []);
  }

  /// `{howMany, plural, one{at} other{at}}`
  String chat_at_label(num howMany) {
    return Intl.plural(
      howMany,
      one: 'at',
      other: 'at',
      name: 'chat_at_label',
      desc: '',
      args: [howMany],
    );
  }

  /// `failed to load message`
  String get chat_load_error_message_label {
    return Intl.message(
      'failed to load message',
      name: 'chat_load_error_message_label',
      desc: '',
      args: [],
    );
  }

  /// `Offline`
  String get presence_status_offline_label {
    return Intl.message(
      'Offline',
      name: 'presence_status_offline_label',
      desc: '',
      args: [],
    );
  }

  /// `You currently are going offline with {activeConversations} active {activeConversations, plural, one{conversation} other{conversations}}. End {activeConversations, plural, one{it} other{all}}?`
  String going_offline_modal_title(num activeConversations) {
    return Intl.message(
      'You currently are going offline with $activeConversations active ${Intl.plural(activeConversations, one: 'conversation', other: 'conversations')}. End ${Intl.plural(activeConversations, one: 'it', other: 'all')}?',
      name: 'going_offline_modal_title',
      desc: '',
      args: [activeConversations],
    );
  }

  /// `{attachmentCount} {attachmentCount, plural, one{attachment} other{attachments}}`
  String attachments_label(num attachmentCount) {
    return Intl.message(
      '$attachmentCount ${Intl.plural(attachmentCount, one: 'attachment', other: 'attachments')}',
      name: 'attachments_label',
      desc: '',
      args: [attachmentCount],
    );
  }

  /// `Cancel`
  String get view_contact_cancel_cta {
    return Intl.message(
      'Cancel',
      name: 'view_contact_cancel_cta',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get view_contact_edit_cta {
    return Intl.message(
      'Edit',
      name: 'view_contact_edit_cta',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get view_contact_save_cta {
    return Intl.message(
      'Save',
      name: 'view_contact_save_cta',
      desc: '',
      args: [],
    );
  }

  /// `Message`
  String get view_contact_message_cta {
    return Intl.message(
      'Message',
      name: 'view_contact_message_cta',
      desc: '',
      args: [],
    );
  }

  /// `No Name`
  String get contact_detail_no_name_label {
    return Intl.message(
      'No Name',
      name: 'contact_detail_no_name_label',
      desc: '',
      args: [],
    );
  }

  /// `Transfer Agent`
  String get agent_transfer_title {
    return Intl.message(
      'Transfer Agent',
      name: 'agent_transfer_title',
      desc: '',
      args: [],
    );
  }

  /// `The conversation was transferred to {destinationName}.`
  String agent_transfer_success_label(Object destinationName) {
    return Intl.message(
      'The conversation was transferred to $destinationName.',
      name: 'agent_transfer_success_label',
      desc: '',
      args: [destinationName],
    );
  }

  /// `Image size exceeds channel limitation: {limit} KB.`
  String image_file_size_error_label(Object limit) {
    return Intl.message(
      'Image size exceeds channel limitation: $limit KB.',
      name: 'image_file_size_error_label',
      desc: '',
      args: [limit],
    );
  }

  /// `Failed to accept: conversation declined due to timeout`
  String get app_error_accept_work_failed_auto_decline_timeout {
    return Intl.message(
      'Failed to accept: conversation declined due to timeout',
      name: 'app_error_accept_work_failed_auto_decline_timeout',
      desc: '',
      args: [],
    );
  }

  /// `Templates`
  String get templates_selection_title {
    return Intl.message(
      'Templates',
      name: 'templates_selection_title',
      desc: '',
      args: [],
    );
  }

  /// `Notice: No templates have been defined in Salesforce for this user.`
  String get no_templates_available_label {
    return Intl.message(
      'Notice: No templates have been defined in Salesforce for this user.',
      name: 'no_templates_available_label',
      desc: '',
      args: [],
    );
  }

  /// `No matching templates found.`
  String get no_templates_found_label {
    return Intl.message(
      'No matching templates found.',
      name: 'no_templates_found_label',
      desc: '',
      args: [],
    );
  }

  /// `Send`
  String get send_template_cta {
    return Intl.message('Send', name: 'send_template_cta', desc: '', args: []);
  }

  /// `Search...`
  String get search_templates_hint {
    return Intl.message(
      'Search...',
      name: 'search_templates_hint',
      desc: '',
      args: [],
    );
  }

  /// `Templates`
  String get display_templates_cta {
    return Intl.message(
      'Templates',
      name: 'display_templates_cta',
      desc: '',
      args: [],
    );
  }

  /// `It has been more than 24 hours since the last message from this contact. Only template responses are allowed.`
  String get templates_tooltip_label {
    return Intl.message(
      'It has been more than 24 hours since the last message from this contact. Only template responses are allowed.',
      name: 'templates_tooltip_label',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load templates`
  String get templates_error_label {
    return Intl.message(
      'Failed to load templates',
      name: 'templates_error_label',
      desc: '',
      args: [],
    );
  }

  /// `N/A`
  String get not_available_label {
    return Intl.message('N/A', name: 'not_available_label', desc: '', args: []);
  }

  /// `Notice: No channels have been defined in Salesforce for this user.`
  String get no_channels_defined_for_customer_label {
    return Intl.message(
      'Notice: No channels have been defined in Salesforce for this user.',
      name: 'no_channels_defined_for_customer_label',
      desc: '',
      args: [],
    );
  }

  /// `Channel`
  String get channel_label {
    return Intl.message('Channel', name: 'channel_label', desc: '', args: []);
  }

  /// `SMS`
  String get channel_text_label {
    return Intl.message('SMS', name: 'channel_text_label', desc: '', args: []);
  }

  /// `WhatsApp`
  String get channel_whatsapp_label {
    return Intl.message(
      'WhatsApp',
      name: 'channel_whatsapp_label',
      desc: '',
      args: [],
    );
  }

  /// `Unknown`
  String get channel_unknown_label {
    return Intl.message(
      'Unknown',
      name: 'channel_unknown_label',
      desc: '',
      args: [],
    );
  }

  /// `Messaging is disabled until customer replies.`
  String get chat_field_disabled_label {
    return Intl.message(
      'Messaging is disabled until customer replies.',
      name: 'chat_field_disabled_label',
      desc: '',
      args: [],
    );
  }

  /// `Customer is already in another messaging session.`
  String get chat_field_disabled_already_in_session {
    return Intl.message(
      'Customer is already in another messaging session.',
      name: 'chat_field_disabled_already_in_session',
      desc: '',
      args: [],
    );
  }

  /// `Transfer`
  String get transfer_title {
    return Intl.message('Transfer', name: 'transfer_title', desc: '', args: []);
  }

  /// `Search...`
  String get transfer_search_hint {
    return Intl.message(
      'Search...',
      name: 'transfer_search_hint',
      desc: '',
      args: [],
    );
  }

  /// `People`
  String get transfer_people_tab_label {
    return Intl.message(
      'People',
      name: 'transfer_people_tab_label',
      desc: '',
      args: [],
    );
  }

  /// `Queues`
  String get transfer_queue_tab_label {
    return Intl.message(
      'Queues',
      name: 'transfer_queue_tab_label',
      desc: '',
      args: [],
    );
  }

  /// `No queues available at the moment.`
  String get transfer_no_queues_available_label {
    return Intl.message(
      'No queues available at the moment.',
      name: 'transfer_no_queues_available_label',
      desc: '',
      args: [],
    );
  }

  /// `No matching queues found.`
  String get transfer_no_matching_queues_label {
    return Intl.message(
      'No matching queues found.',
      name: 'transfer_no_matching_queues_label',
      desc: '',
      args: [],
    );
  }

  /// `No matching people found.`
  String get transfer_no_matching_people_label {
    return Intl.message(
      'No matching people found.',
      name: 'transfer_no_matching_people_label',
      desc: '',
      args: [],
    );
  }

  /// `Transfer`
  String get transfer_dialog_title {
    return Intl.message(
      'Transfer',
      name: 'transfer_dialog_title',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to transfer this conversation to: {recipient}?`
  String transfer_dialog_description(Object recipient) {
    return Intl.message(
      'Are you sure you want to transfer this conversation to: $recipient?',
      name: 'transfer_dialog_description',
      desc: '',
      args: [recipient],
    );
  }

  /// `Cancel`
  String get transfer_dialog_cancel_cta {
    return Intl.message(
      'Cancel',
      name: 'transfer_dialog_cancel_cta',
      desc: '',
      args: [],
    );
  }

  /// `Transfer`
  String get transfer_dialog_transfer_cta {
    return Intl.message(
      'Transfer',
      name: 'transfer_dialog_transfer_cta',
      desc: '',
      args: [],
    );
  }

  /// `Success!`
  String get transfer_success_title {
    return Intl.message(
      'Success!',
      name: 'transfer_success_title',
      desc: '',
      args: [],
    );
  }

  /// `Error!`
  String get transfer_error_title {
    return Intl.message(
      'Error!',
      name: 'transfer_error_title',
      desc: '',
      args: [],
    );
  }

  /// `Salesforce could not initiate the transfer.`
  String get transfer_error_label {
    return Intl.message(
      'Salesforce could not initiate the transfer.',
      name: 'transfer_error_label',
      desc: '',
      args: [],
    );
  }

  /// `Quick Action Configuration Error`
  String get quick_action_configuration_error_title {
    return Intl.message(
      'Quick Action Configuration Error',
      name: 'quick_action_configuration_error_title',
      desc: '',
      args: [],
    );
  }

  /// `Allow Notifications`
  String get allow_notification_cta {
    return Intl.message(
      'Allow Notifications',
      name: 'allow_notification_cta',
      desc: '',
      args: [],
    );
  }

  /// `NOTIFICATIONS DISABLED`
  String get notifications_disabled_label {
    return Intl.message(
      'NOTIFICATIONS DISABLED',
      name: 'notifications_disabled_label',
      desc: '',
      args: [],
    );
  }

  /// `You must `
  String get notification_description_part1 {
    return Intl.message(
      'You must ',
      name: 'notification_description_part1',
      desc: '',
      args: [],
    );
  }

  /// `Allow Notifications`
  String get notification_description_part2 {
    return Intl.message(
      'Allow Notifications',
      name: 'notification_description_part2',
      desc: '',
      args: [],
    );
  }

  /// ` to use this app.`
  String get notification_description_part3 {
    return Intl.message(
      ' to use this app.',
      name: 'notification_description_part3',
      desc: '',
      args: [],
    );
  }

  /// `Warning`
  String get warning_title {
    return Intl.message('Warning', name: 'warning_title', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'de'),
      Locale.fromSubtags(languageCode: 'en', countryCode: 'BV'),
      Locale.fromSubtags(languageCode: 'es'),
      Locale.fromSubtags(languageCode: 'fr'),
      Locale.fromSubtags(languageCode: 'it'),
      Locale.fromSubtags(languageCode: 'pt'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
