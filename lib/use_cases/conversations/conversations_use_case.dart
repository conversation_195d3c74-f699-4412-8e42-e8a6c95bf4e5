import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:universal_io/io.dart';
import 'package:uuid/uuid.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/converters/parse_phone_number_converter.dart';
import 'package:x1440/api/dtos/close_work_body.dart';
import 'package:x1440/api/dtos/decline_work_body.dart';
import 'package:x1440/api/dtos/messaging_channel_entry.dart';
import 'package:x1440/api/dtos/transfer_destination.dart';
import 'package:x1440/api/dtos/transfer_messaging_session_body.dart';
import 'package:x1440/api/dtos/transfer_option.dart';
import 'package:x1440/api/dtos/transfer_options_response.dart';
import 'package:x1440/api/dtos/work_body.dart';
import 'package:x1440/api/dtos/work_response.dart';
import 'package:x1440/api/salesforce/dtos/conversation_entries.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/create_messaging_end_user_body.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/update_messaging_end_user_body.dart';
import 'package:x1440/api/salesforce/dtos/salesforce_soql_query_response.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/frameworks/notifications/models/in_app_message_notification.dart';
import 'package:x1440/frameworks/notifications/notifications_event.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/frameworks/typing_indicator/typing_indicator_event.dart';
import 'package:x1440/frameworks/typing_indicator/typing_indicator_manager.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/conversation_entry_model.dart';
import 'package:x1440/models/conversation_model.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/models/legacy/legacy_utils.dart';
import 'package:x1440/api/dtos/messaging_end_user_status_response.dart';
import 'package:x1440/models/messaging_session_model.dart';
import 'package:x1440/models/pending_message.dart';
import 'package:x1440/models/polling_response.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/conversation/conversation_repository.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/query/query_repository.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/repositories/transfer/transfer_repository.dart';
import 'package:x1440/services/de_conversations_service.dart';
import 'package:x1440/services/shim_service/shim_service.dart';
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_bloc.dart';
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_event.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/ui/blocs/messaging/messaging_bloc.dart';
import 'package:x1440/use_cases/contacts/contacts_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/models/agent_work.dart';
import 'package:x1440/use_cases/models/contact.dart';
import 'package:x1440/use_cases/models/messaging_end_user.dart';
import 'package:x1440/use_cases/models/work_capabilities.dart';
import 'package:x1440/use_cases/salesforce_data/salesforce_data_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/utils/constants.dart';
import 'package:x1440/utils/json_utils.dart';
import 'package:x1440/utils/messaging_utils.dart';
import 'package:x1440/viewmodels/conversations_viewmodel.dart';

/// typedef PollingCallback = void Function(String type, Map<String, dynamic> json);
typedef CachedMessage = (String, List<File>);

class TransferStatus {
  final String? error;
  final bool isError;

  const TransferStatus(this.isError, this.error);
}

class ConversationsUseCase {
  final QueryRepository _queryRepository;
  final ConversationRepository _conversationRepository;
  final TransferRepository _transferRepository;
  final LocalStorageRepository _localStorageRepository;
  final TypingIndicatorManager _typingIndicatorManager;
  final RemoteLogger _logger;

  @Deprecated('from legacy code')
  DeConversationsService get _deConversationsService =>
      GetIt.I<DeConversationsService>();

  @Deprecated('partial refactor; need to refine')
  AiSuggestionsBloc get _aiSuggestionsBloc => GetIt.I<AiSuggestionsBloc>();

  @Deprecated('partial refactor; need to refine')
  Future<NotificationManager> get _notificationManager async {
    await GetIt.I.allReady();
    return GetIt.I<NotificationManager>();
  }

  ConversationsUseCase(
    this._queryRepository,
    this._conversationRepository,
    this._transferRepository,
    this._localStorageRepository,
    this._typingIndicatorManager,
    this._logger,
    // {
    // @visibleForTesting Map<SfId, AgentWork>? agentWorks,
    // }
  )   : _agentWorkIdForWorkTargetIds = {},
        _initialFetchStarted = false,
        _initialFetchCompleter = null;

  /// *************************************************
  /// Messaging Sessions
  /// *************************************************
  /// sessionId -> MessagingSession
  final Map<SfId, MessagingSession> _messagingSessions = {};

  static List<TransferDestination> searchTransferDestinations(
    List<TransferDestination> destinations,
    String searchTerm,
  ) {
    searchTerm = searchTerm.toLowerCase();

    final list = destinations.where((destination) {
      final nameMatch = destination.label.toLowerCase().contains(searchTerm);

      return nameMatch;
    }).toList();

    return list;
  }

  Future<SfId?> getMEUIdForMessagingSession(SfId? messagingSessionId,
      [bool fetchIfNotFound = true]) async {
    if (messagingSessionId == null) {
      return null;
    }
    final localSession = _messagingSessions.values.firstWhereOrNull(
        (messagingSession) => messagingSession.id == messagingSessionId);
    if (localSession != null) {
      return localSession.messagingEndUser?.id;
    }
    if (!fetchIfNotFound) {
      return null;
    }
    final result =
        await _queryRepository.queryMEUByMessagingSessionId(messagingSessionId);
    if (result is Success) {
      final records = (result as Success).data.records;
      if (records.isNotEmpty) {
        final meuId = records.first['MessagingEndUserId'] as String?;
        if (meuId == null) {
          _logger
              .warn('getMEUIdForMessagingSession: MessagingEndUserId is null');
          return null;
        }
        final SfId meuSfId = SfId(meuId);
        // _messagingSessions.putIfAbsent(
        //     messagingSessionId,
        //     () => MessagingSession(
        //         id: messagingSessionId,
        //         messagingEndUser: MessagingEndUser(id: meuSfId)));

        return meuSfId;
      }
    }
    return null;
  }

  /// NOTE: by the time 'fetchAgentWork' happens, we don't have all the sessions in the correct state for some reason. Save them here on initial load. // TODO: better handle this!
  List<SfId> _messagingSessionIdsToFetch = [];

  @Deprecated('to support legacy viewmodel')
  Future<void> updateMessagingSessions(
      Set<MessagingSession> messagingSessions) async {
    _logger.info(
        'updating messaging sessions: ${messagingSessions.length}: ids: ${messagingSessions.map((e) => e.id).toList()}');
    for (var messagingSession in messagingSessions) {
      _messagingSessions[messagingSession.id] = messagingSession;

      if (messagingSession.status == MessagingSessionStatus.active ||
          messagingSession.status == MessagingSessionStatus.waiting) {
        _messagingSessionIdsToFetch.add(messagingSession.id);
      }
    }
  }

  // TODO: update this to use the messagingSession statuses at this point -- see note on updateMessagingSessions above

  Future<void> fetchOpenedAgentWork() async {
    await _waitForInitialFetch();
    if (_messagingSessionIdsToFetch.isNotEmpty) {
      final creds = await _localStorageRepository.getCredentials();
      if (creds.userId == null) {
        _logger.warn('fetchOpenedAgentWork: creds.userId is null');
        return;
      }
      final agentWorkResult =
          await _queryRepository.queryAgentWork(
            userId: creds.userId!,
              idsString: _messagingSessionIdsToFetch.map((id) => "'$id'").join(','));

      if (agentWorkResult is Success) {
        _messagingSessionIdsToFetch = [];
        for (final record in ((agentWorkResult as Success).data
                as SalesforceSoqlQueryResponse)
            .records) {
          _logger.info('setting agent work: $record');
          final workId = (record['Id'] as String?)?.toSfId();
          final workItemId = (record['WorkItemId'] as String?)?.toSfId();
          final status = record['Status'] as String?;
          if (workItemId != null && workId != null) {
            if (status == 'Opened') {
              _logger.info('setting work opened: $workId');
              _agentWorkIdForWorkTargetIds[workItemId] = workId;
              await updateMessagingSessionStatus(
                  workItemId, ConversationStatus.opened);
            }
            // TODO: remove these? or better handle
            else if (false && status == 'Assigned') {
              _logger.info('offering work assigned: $workId');

              _agentWorkIdForWorkTargetIds[workItemId] = workId;
              // _agentWorkIdForWorkTargetIds[workItemId] =
              //     AgentWork(id: workId, workTargetId: workItemId);

              // TODO: CLEAN this up (probably move this workAssigned handler into this useCase?)
              // await sendQueueReceiveMessageNotification(message, fetchedConversation.id,
              //     isWorkOffered: isAutoAcceptEnabled ? false : true);

              GetIt.I<ConversationsViewmodel>().workAssignedCallback(
                  WorkAssignedResponse(
                      workId: workId,
                      workTargetId: workItemId,
                      channelName: 'Channel'));
            }
          }
        }
      }
    }
  }

  List<MessagingSession> _getMessagingSessionsForMessagingEndUserId(
          SfId meuId) =>
      _messagingSessions.values
          .where((messagingSession) =>
              messagingSession.messagingEndUser?.id == meuId)
          .toList();

  (MessagingSession?, AgentWork?) getLatestSessionAndWorkForMessagingEndUser(
      SfId? messagingEndUserId) {
    final List<MessagingSession> messagingSessions = [];
    if (messagingEndUserId != null) {
      for (final messagingSession
          in _getMessagingSessionsForMessagingEndUserId(messagingEndUserId)) {
        if (messagingSession.messagingEndUser?.id == messagingEndUserId) {
          messagingSessions.add(messagingSession);
        }
      }
      if (messagingSessions.isEmpty) {
        messagingSessions.addAll(
            _getMessagingSessionsForMessagingEndUserId(messagingEndUserId));
      }
    }
    messagingSessions.sort((a, b) {
      if (a.createdDate == null && b.createdDate == null) return 0;
      if (a.createdDate == null) return 1;
      if (b.createdDate == null) return -1;
      return a.createdDate!.compareTo(b.createdDate!);
    });
    for (final messagingSession in messagingSessions.reversed) {
      final agentWork = _getAgentWorkByMessagingSessionId(messagingSession.id);
      if (agentWork != null) {
        return (messagingSession, agentWork);
      }
    }
    return (
      messagingSessions.lastOrNull,
      _getAgentWorkByMessagingSessionId(messagingSessions.lastOrNull?.id)
    );
  }

  /// *************************************************
  /// NOTE: this is 'broadcast' b/c the otherwise we'd lose the listener in the DeConversationsService on logout/login
  /// Probably a better way to handle ... and not relevant once we ditch the old service
  final StreamController<QueueReceiveMessage>
      _messageReceiveLegacyQueueController =
      StreamController<QueueReceiveMessage>();

  Stream<QueueReceiveMessage> get messageReceiveQueueLegacyStream =>
      _messageReceiveLegacyQueueController.stream.asBroadcastStream(
        onCancel: (controller) {
          controller.pause();
        },
        onListen: (controller) async {
          if (controller.isPaused) {
            controller.resume();
          }
        },
      );

  int get numActiveConversations =>
      _conversations.values.where((convo) => convo.isOpened).length;

  int get numActiveStandardConversations => _activeStandardConversations.length;

  List<Conversation> get _activeStandardConversations => _conversations.values
      .where(
          (convo) => convo.isOpened && !convo.isEnhanced && !convo.isOutbound)
      .toList();

  Map<SfId, Conversation> _conversations = {};

  Map<SfId, Conversation> get conversations => _conversations;

  @Deprecated('supporting legacy code')
  Map<String, LakConversation> get lakConversations => _conversations
      .map((key, value) => MapEntry(key.toString(), value.lakConversation!));

  Conversation? getConversation(SfId? conversationId) => _conversations.values
      .firstWhereOrNull((convo) => convo.id == conversationId);

  Future<Conversation?> getConversationByMessagingSessionId(
      SfId? messagingSessionId,
      [bool fetchIfNotFound = true]) async {
    await _waitForInitialFetch();
    return getConversation((await getMEUIdForMessagingSession(
        messagingSessionId, fetchIfNotFound)));
  }

  LakConversation? getConversationByScrtUuid(String? uuid) {
    if (uuid == null) return null;
    return lakConversations.values
        .firstWhereOrNull((conversation) => conversation.scrtUUID == uuid);
  }

  void setConversations(Map<SfId, Conversation> conversations) =>
      _conversations = conversations;

  final Map<String, PendingMessage> _locallyCachedPendingMessages = {};

  PendingMessage? getLocallyCachedPendingMessage(String id) =>
      _locallyCachedPendingMessages[id];

  /// NOTE: copypasta'd from ConversationsViewmodel
  @Deprecated('needs refactoring')
  Future<List<ConversationEntryAttachment>> _fetchContentEntriesFromVersionIds(
      Set<String> contentVersionIds) async {
    List<ConversationEntryAttachment> updatedAttachments = [];
    for (var contentVersionId in contentVersionIds) {
      SalesforceDataUseCase salesforceDataUseCase =
          GetIt.instance.get<SalesforceDataUseCase>();

      ConversationEntryAttachment updatedAttachment =
          await salesforceDataUseCase
              .getConversationEntryAttachmentFromContentVersionId(
                  contentVersionId);
      updatedAttachments.add(updatedAttachment);
    }
    return updatedAttachments;
  }

  final List<SfId> _conversationsPendingAccepted = [];

  bool conversationIsPendingAccepted(SfId? conversationId) =>
      _conversationsPendingAccepted.contains(conversationId);

  /// handling the possibility that we're already handling this workAssigned event based on the UI Push notification user action tap, but receive a second "work Assigned" from the websocket notifications fetch // TODO: refactor
  final List<SfId> _workAssignedEventsHandled = [];

  @Deprecated('only to support last CVM call; dont use!!! Remove ASAP')
  void addWorkAssignedEventHandled(SfId workTargetId) {
    _workAssignedEventsHandled.add(workTargetId);
  }

  bool isWorkAssignedEventHandled(SfId workTargetId) {
    return _workAssignedEventsHandled.contains(workTargetId);
  }

  void removeWorkAssignedEventHandled(SfId workTargetId) {
    _workAssignedEventsHandled.remove(workTargetId);
    _agentWorkIdForWorkTargetIds.remove(workTargetId);
  }

  Future<bool> _handleWorkAssigned(QueueReceiveMessage message) async {
    final workTargetId = message.workTargetId;
    final workId = message.workId;
    if (workTargetId == null || workId == null) {
      _logger
          .warn('FAILED _handleWorkAssigned - workTargetId or workId is null');
      throw Exception(
          'FAILED _handleWorkAssigned - workTargetId or workId is null');
      return false;
    }
    _workAssignedEventsHandled.add(workTargetId);

    bool isOutboundResponse = GetIt.I<MessagingUseCase>()
        .removeOutboundMessagingSessionNotRespondedTo(workTargetId);

    final autoAccept = message.notificationAction == 'ACCEPT_ACTION';
    final autoDecline = message.notificationAction == 'DECLINE_ACTION';

    final cvm = GetIt.I<ConversationsViewmodel>();
    final isAutoAcceptEnabled = cvm.isAutoAcceptEnabled;

    bool shouldAutoAccept =
        isOutboundResponse || autoAccept || isAutoAcceptEnabled;

    _logger.info(
        'workAssignedCallback: workId: $workId; workTargetId: $workTargetId; autoAccept: $autoAccept; autoDecline: $autoDecline; shouldAutoAccept: $shouldAutoAccept; isOutboundResponse: $isOutboundResponse');

    if (autoDecline) {
      await declineWork(workId, workTargetId);
      return true;
    }

    LakConversation? fetchedConversation = await cvm
        .fetchConversationInfoIfNeeded(workTargetId.toString(), force: true);

    if (fetchedConversation == null) {
      _logger.error('work assigned - Error: fetchedConversation is null');
      return true;
    }

    setLakConversations([fetchedConversation]);

    if (!shouldAutoAccept) {
      await sendQueueReceiveMessageNotification(
          message, fetchedConversation.sfId,
          isWorkOffered: isAutoAcceptEnabled ? false : true);
    } else {
      _logger.info(
          'Auto-accepting work: $workId; workTargetId: $workTargetId; autoAccept: $autoAccept; autoDecline: $autoDecline; id: ${fetchedConversation.id}');

      final success = await _acceptWork(workId, workTargetId);

      /// previously used this but would cause an issue on work timeout re-assign and accept from a push notification where the WorkTargetId/MessagingSessionId used by 'acceptConversation' would be the old one and not the new one
          // acceptConversation(
          //   fetchedConversation.sfId,
          // ));
      _logger.info('Auto-accepting work: success: $success');
      if (success) {
        if (autoAccept) {
          _logger.info(
              'goToChatScreen from workAssignedCallback to: ${fetchedConversation.id}');
          GetIt.I<RoutingManager>()
              .goToChatScreen(fetchedConversation.sfId, null);
        } else {
          _logger.info(
              'sendQueueReceiveMessageNotification from workAssignedCallback to: ${fetchedConversation.id}; workTargetId: $workTargetId');
          await sendQueueReceiveMessageNotification(
              message, fetchedConversation.sfId,
              isWorkOffered: false);
        }
      }
    }
    removeWorkAssignedEventHandled(workId);
    GetIt.I<ConversationsBloc>().add(RefreshConversationsEvent());
    return true;
  }

  /// NOTE: copypasta'd from ConversationsViewmodel
  @Deprecated('needs refactoring')
  Future<bool> acceptConversation(SfId conversationId) async {
    _logger.info('acceptConversation: $conversationId');

    final (messagingSession, agentWork) =
        getLatestSessionAndWorkForMessagingEndUser(conversationId);

    if (messagingSession == null || agentWork == null) {
      final messagingBloc = GetIt.I<MessagingBloc>();
      _logger.warn(
          'FAILED acceptConversation - ${messagingSession == null ? 'messagingSession is null' : ''}; ${agentWork == null ? 'agentWork is null' : ''}; websocket connection status: ${messagingBloc.state.connectivityState}');
      return false;
    }

    final workTargetId = agentWork.workTargetId; // messagingSession.id;

    LakConversation? conversationToAccept =
        getConversation(conversationId)?.lakConversation;

    final workId = agentWork.id;

    if (conversationToAccept == null) {
      _logger.warn('FAILED acceptConversation - workId is null');
      return false;
    }

    return _acceptWork(workId, workTargetId);
  }

  Future<bool> _acceptWork(SfId workId, SfId workTargetId) async {
    _conversationsPendingAccepted.add(workTargetId);

    final acceptWorkResponse = await acceptWork(workId, workTargetId);
    _conversationsPendingAccepted.remove(workTargetId);

    _logger.info(
        'acceptConversation: acceptWorkResponse: ${acceptWorkResponse?.toJson()}');

    return acceptWorkResponse != null;
  }

  Future<void> sendQueueReceiveMessageNotification(
      QueueReceiveMessage message, final SfId conversationId,
      {final bool isWorkOffered = false}) async {
    if (message.workTargetId == null) {
      throw Exception(
          'sendQueueReceiveMessageNotification: message.workTargetId is null');
    }
    _notificationIdsForWorkTargetIdOffered[message.workTargetId!] =
        message.notificationId;

    final shimServiceMessage = message.shimServiceMessage;
    final conversation = getConversation(conversationId);
    final latestMessage = conversation?.lakConversation?.messages
        .firstWhereOrNull((msg) => msg.actorType != MessageActorType.System);
    final messageBody = message.notificationBody ??
        (latestMessage != null
            ? MessagingUtils.formatMessageBody(latestMessage)
            : null) ??
        latestMessage?.messageBody ??
        'No Message Body';

    final contactsUseCase = GetIt.I<ContactsUseCase>();
    Contact? contact =
        (contactsUseCase.getSavedContactByMessagingEndUserId(conversationId) ??
                (await contactsUseCase
                    .getContactForMessagingEndUserId(conversationId)))
            ?.value;

    _logger.info(
        'sendQueueReceiveMessageNotification to: $conversationId; workTargetId: ${message.workTargetId}; isWorkOffered: $isWorkOffered; message: $message; conversation: $conversation; latestMessage: $latestMessage; contact: $contact; messageBody: $messageBody; scrtUUID: ${conversation?.scrtUUID}\nmessageId: ${safeJsonDecode(message.shimServiceMessage['channelData'])['eventId'] as String?}');

    (await _notificationManager).add(
        ReportMessageNotificationNeedsDisplayEvent(InAppMessageNotification(
      conversationId: conversationId,
      conversationScrtUuid: conversation?.scrtUUID,
      workTargetId: message.workTargetId,
      messageId:
          safeJsonDecode(message.shimServiceMessage['channelData'])['eventId']
                  as String? ??
              Uuid().v4(),
      title: message.notificationTitle ??
          contact?.name ??
          latestMessage?.participant?.name ??
          conversation?.lakConversation?.title ??
          'No Title',
      body: messageBody,
      userPhotoUrl: message.notificationAvatarUrl,
      messageType: isWorkOffered ? MessageType.work : MessageType.message,
      pushNotificationId: message.notificationId,
      username: conversation?.lakConversation?.username ??
          latestMessage?.participant?.name,
      channelType:
          conversation?.lakConversation?.channelType ?? ChannelType.sfdcSms,
      lastMessageTime: DateTime.fromMillisecondsSinceEpoch(
              shimServiceMessage['psrDateAdded'] ??
                  shimServiceMessage['dateAssigned'] ??
                  message.timestamp)
          .toIso8601String(),
    )));
  }

  /// NOTE: copypasta'd from ConversationsViewmodel
  @Deprecated('needs refactoring, REMOVE ASAP')
  Future<void> sendNewMessageNotification(
    LakMessage message,
    SfId conversationId, {
    bool isWorkOffered = false,
  }) async {
    LakConversation? conversation =
        getConversation(conversationId)?.lakConversation;

    String messageBody = MessagingUtils.formatMessageBody(message);

    if (conversation == null) {
      if (kDebugMode) {
        print('sendNewMessageNotification: conversation is null; returning');
      }
      return;
    }
    if (messageBody.isNotEmpty != true) {
      if (kDebugMode) {
        print('sendNewMessageNotification: message is empty; returning');
      }
      return;
    }

    // TODO: refactor ... this should be in a service?
    final contactsUseCase = GetIt.I<ContactsUseCase>();
    Contact? contact = (contactsUseCase
                .getSavedContactByMessagingEndUserId(conversation.sfId) ??
            (await contactsUseCase
                .getContactForMessagingEndUserId(conversation.sfId)))
        ?.value;

    final (messagingSession, agentWork) =
        getLatestSessionAndWorkForMessagingEndUser(conversation.sfId);
    SfId? workTargetId = agentWork?.workTargetId;

    if (workTargetId != null && message.pushNotificationId != null) {
      _notificationIdsForWorkTargetIdOffered[workTargetId] =
          message.pushNotificationId!;
    }

    (await _notificationManager).add(
        ReportMessageNotificationNeedsDisplayEvent(InAppMessageNotification(
      // TODO: better handle these types/conversions
      conversationId: conversation.sfId,
      conversationScrtUuid: conversation.scrtUUID,
      workTargetId: workTargetId,
      messageId: message.id,
      title: contact?.name ?? message.participant?.name ?? conversation.title,
      body: messageBody,
      userPhotoUrl: message.participant?.photoUrl,
      messageType: isWorkOffered ? MessageType.work : MessageType.message,
      pushNotificationId: message.pushNotificationId,
      username: conversation.username ?? message.participant?.name,
      channelType: conversation.channelType,
      lastMessageTime: conversation.lastMessageTime,
    )));
  }

  final Map<SfId, String> _notificationIdsForWorkTargetIdOffered = {};

  Future<void> addNewMessageToConversationIdentifier(
      LakMessage message, String conversationIdentifier) {
    _logger
        .info('addNewMessageToConversationIdentifier: $conversationIdentifier');
    final latestMessagingSessionId =
        getConversationByScrtUuid(conversationIdentifier)?.sfId;
    final (messagingSession, agentWork) =
        getLatestSessionAndWorkForMessagingEndUser(latestMessagingSessionId);
    if (messagingSession == null) {
      _logger.warn(
          'addNewMessageToConversationIdentifier: messagingSession or agentWork is null; returning');
      return Future.value();
    }
    return addNewMessage(message, messagingSession.id);
  }

  /// NOTE: copypasta'd from ConversationsViewmodel
  @Deprecated('needs refactoring')
  Future<void> addNewMessage(
    LakMessage message,
    SfId messagingSessionId, {
    bool shouldSendNotification = true,
  }) async {
    await _waitForInitialFetch();
    if (message is PendingMessage) {
      _locallyCachedPendingMessages[message.id] = message;
    }

    Conversation? conversationForMessage =
        await getConversationByMessagingSessionId(messagingSessionId);

    if (conversationForMessage?.lakConversation == null) {
      _logger.warn('addNewMessage: conversationForMessage is null; returning');
      Utils.showToast('Error: failed to send message', type: ToastType.error);
      return;
    }

    /// fetch any contentDocs needed (length > 18 => SCRT UUID)
    Set<String> contentDocIdsToFetch = {
      ...message.attachments
          .map((attachment) => attachment['contentVersionId'])
          .whereType<String>(),
      ...message.conversationEntryAttachments
          .map((attachment) => attachment.id)
          .whereType<String>(),
    };

    message.conversationEntryAttachments =
        await _fetchContentEntriesFromVersionIds(contentDocIdsToFetch);

    _conversations[conversationForMessage!.id!]!.addMessage(message);

    _aiSuggestionsBloc.add(GetAiSuggestionEvent(conversationForMessage.id!));

    if (conversationForMessage.scrtUUID != null) {
      _typingIndicatorManager
          .add(EndUserTypingEndedEvent(conversationForMessage.scrtUUID!));
    }

    if (shouldSendNotification && !message.isNotEndUser) {
      return sendNewMessageNotification(message, conversationForMessage.id!);
    }
  }

  /// NOTE: copypasta'd from ConversationsViewmodel
  @Deprecated('needs refactoring')
  Future<void> newConversationCallback(
      NewConversationResponse parsedResponse) async {
    _logger.info('newConversationCallback: $parsedResponse');
    LakConversation newConversation = (parsedResponse).conversation;

    var conversation = getConversation(newConversation.sfId);
    if (conversation != null) {
      setLakConversations([newConversation]);

      /// *************************************************
      //// check for new messages & send as new message events /// this is for enhanced outbound responses (we don't get the messages directly, the DEConversation service is doing an extra fetch & passing the conversation through this method) /// duplicated in _workAssignedCallbackForOutboundConversations
      List<LakMessage> newMessages = newConversation.messages
          .where((message) => (_conversations[conversation.id]
                  ?.lakConversation
                  ?.messages
                  .contains(message) !=
              true))
          .toList();

      if (newMessages.isEmpty) return;

      newMessages.sort((a, b) => a.timeStamp.compareTo(b.timeStamp));

      LakMessage lastMessage = newMessages.removeLast();

      final (messagingSession, agentWork) =
          getLatestSessionAndWorkForMessagingEndUser(conversation.id);
      if (messagingSession?.id == null) {
        _logger.warn(
            'newConversationCallback: messagingSession for convo ID: ${conversation.id} is null; returning');
        return;
      }
      for (LakMessage newMessage in newMessages) {
        addNewMessage(newMessage, messagingSession!.id,
            shouldSendNotification:
                parsedResponse.shouldSendNewMessageNotifications);
      }
      addNewMessage(lastMessage, messagingSession!.id);
      return;
    } else {
      // If it doesn't exist, add the new conversation to _conversations
      setLakConversations([newConversation]);
    }
    setLakConversations([newConversation]);
  }

  void setLakConversations(List<LakConversation> lakConversations) {
    _conversations = LegacyUtils.updateConversationsFromLakConversations(
        _conversations, lakConversations);
  }

  /// *************************************************
  /// Agent Work
  /// *************************************************
  final Map<SfId, SfId> _agentWorkIdForWorkTargetIds;
  final Map<SfId, WorkCapabilities> _agentWorkCapabilities = {};

  AgentWork? _getAgentWorkByMessagingSessionId(SfId? messagingSessionId) {
    final workId = _agentWorkIdForWorkTargetIds[messagingSessionId];
    if (workId != null && messagingSessionId != null) {
      return AgentWork(id: workId, workTargetId: messagingSessionId);
    }
    return null;
  }

  void onMessageQueueReceive(List<QueueReceiveMessage> messages) async {
    for (QueueReceiveMessage message in messages) {
      // check if already handled
      // try new message parser
      // if that fails, fallback to legacy
      if (!(await handleReceivedMessage(message))) {
        _logger.info(
            'adding message to legacy queue: ${message.notificationId}; $message');
        _messageReceiveLegacyQueueController.add(message);
      }
    }
  }

  Future<bool> handleReceivedMessage(QueueReceiveMessage? message) async {
    final messageType = message?.shimServicePayload['messageType'];
    _logger.info(
        'handleReceivedMessage: ${message?.notificationId} type: $messageType');
    if (message == null) {
      return false;
    }
    if (message.payload["deviceTokenTest"] != null) {
      _logger.info('Ignoring deviceTokenTest: ${message.notificationId}');
      return true;
    }
    if (message.payload['x1440Ping'] != null) {
      _logger.info('message is a ping: ${message.notificationId}');
      final creds = await _localStorageRepository.getCredentials();
      if (message.sessionId != creds.sessionId) {
        _logger.warn('received ping from different session; removing');
        return true;
      }
      _logger.info('sending keep alive event from session ${creds.sessionId}');
      GetIt.I.get<AuthBloc>().add(KeepSessionAliveEvent());
      return true;
    }

    _setAgentWorkFromMessage(message);

    switch (messageType) {
      case 'Presence/WorkAssigned':
        return _handleWorkAssigned(message);
      case '1440/WorksOpened':
        return _handleWorksOpened(message);
      case '1440/WorkUnpaused':
        return _handleWorkUnpaused(message);
      case '1440/WorkAutoDeclined':
        return _handleWorkAutoDeclined(message);
      default:
    }

    if (message.notificationAction == "DECLINE_ACTION") {
      if (message.workId != null && message.workTargetId != null) {
        await declineWork(message.workId!, message.workTargetId!);
        return true;
      }
    }
    return false;
  }

  void _setAgentWorkFromMessage(QueueReceiveMessage message) {
    if (message.workId != null &&
        message.shimServiceMessage['workTargetId'] != null) {
      _agentWorkIdForWorkTargetIds[
          SfId(message.shimServiceMessage['workTargetId'])] = message.workId!;
    }
  }

  /// NOTE: these WorksOpened also have a ChannelAddressIdentifier & ConversationIdentifier ... when refactoring conversation loading, we may be able to use them instead of loading all conversations
  Future<bool> _handleWorksOpened(QueueReceiveMessage message) async {
    final worksOpened = message.shimServiceMessage['works'];
    if (worksOpened is List) {
      _logger.info('handling WorksOpened with ${worksOpened.length} works');
      for (final workOpened in worksOpened) {
        final workId = (workOpened['workId'] as String?)?.toSfId();
        final workTargetId = (workOpened['workTargetId'] as String?)?.toSfId();

        if (workId != null && workTargetId != null) {
          final cvm = GetIt.I<ConversationsViewmodel>();

          LakConversation? fetchedConversation = await cvm
              .fetchConversationInfoIfNeeded(workOpened['workTargetId'],
                  force: true);

          if (fetchedConversation == null) {
            _logger.error('work assigned - Error: fetchedConversation is null');
            return true;
          }

          setLakConversations([fetchedConversation]);

          _agentWorkIdForWorkTargetIds[workTargetId] = workId;

          /// update conversation statuses
          await updateMessagingSessionStatus(
              workTargetId, ConversationStatus.opened);
        }
      }
    }

    /// NOTE: using this as a 'RefreshScreen' event ... the initial message handling/dispatching should probably be in a Manager? And then we can set the update from there.
    GetIt.I<ConversationsBloc>().add(RefreshConversationsEvent());
    return true;
  }

  Future<bool> _handleWorkUnpaused(QueueReceiveMessage message) async {
    final workId = message.workId;
    final workTargetId = message.workTargetId;
    if (workId == null || workTargetId == null) {
      _logger
          .warn('FAILED _handleWorkUnpaused - workId or workTargetId is null');
      return false;
    }

    final cvm = GetIt.I<ConversationsViewmodel>();

    LakConversation? fetchedConversation = await cvm
        .fetchConversationInfoIfNeeded(workTargetId.toString(), force: true);

    if (fetchedConversation == null) {
      _logger.error('work assigned - Error: fetchedConversation is null');
      return true;
    }

    setLakConversations([fetchedConversation]);

    _agentWorkIdForWorkTargetIds[workTargetId] = workId;
    await updateMessagingSessionStatus(workTargetId, ConversationStatus.opened);

    GetIt.I<ConversationsBloc>().add(RefreshConversationsEvent());

    sendQueueReceiveMessageNotification(message, fetchedConversation.sfId,
        isWorkOffered: false);
    return true;
  }

  Future<bool> _handleWorkAutoDeclined(QueueReceiveMessage message) async {
    final workTargetId = message.workTargetId;
    final conversationId =
        (await getConversationByMessagingSessionId(workTargetId, false))?.id;
    if (conversationId == null) {
      _logger.info(
          'FAILED _handleWorkAutoDeclined - workId: ${message.workId}; workTargetId: $workTargetId; conversationId is null');
      return false;
    }

    (await _notificationManager)
        .add(RemoveNotificationByConversationIdEvent(conversationId));
    return true;
  }

  Future<WorkResponse?> acceptWork(SfId workId, SfId workTargetId) async {
    final response = await _conversationRepository.acceptWork(WorkBody(
      requestId: const Uuid().v4(),
      workId: workId,
      workTargetId: workTargetId,
    ));
    WorkResponse? acceptWorkResponse;
    if (response is Success) {
      await updateMessagingSessionStatus(
          workTargetId, ConversationStatus.opened);

      _agentWorkCapabilities[workId] = WorkCapabilities.fromWorkResponse(
          ((response as Success).data as WorkResponse));

      acceptWorkResponse = (response as Success).data;
    } else if (response is Error) {
      _logger.warn(
          'FAILED acceptWork workId: $workId, workTargetId: $workTargetId - ${(response as Error).error}');
    }

    var conversation = await getConversationByMessagingSessionId(workTargetId);

    if (acceptWorkResponse == null || conversation == null) {
      _logger.warn(
          'FAILED acceptWork - ${acceptWorkResponse == null ? 'acceptWorkResponse is null' : ''}; ${conversation == null ? 'conversation is null' : ''}');
      (await _notificationManager)
          .add(RemoveNotificationByWorkTargetIdEvent(workTargetId));
      if (response is Error &&
          (response as Error).error.code == 'WorkAlreadyOpened') {
        _logger.warn(
            'FAILED acceptWork with WorkAlreadyOpened - message: ${(response as Error).error.message} - code: ${(response as Error).error.code}');
      } else {
        Utils.showToast(
            S.current.app_error_accept_work_failed_auto_decline_timeout,
            type: ToastType.warning);
      }

      GetIt.I<RoutingManager>().router.go('/');

      return null;
    }

    if (acceptWorkResponse.conversationIdentifier != null) {
      conversation.lakConversation?.scrtUUID =
          acceptWorkResponse.conversationIdentifier!;
    }

    _workAssignedEventsHandled.remove(workTargetId);

    addMessages() async {
      _logger.info(
          'acceptConversation: adding ${acceptWorkResponse?.messages?.length ?? '0'} messages');
      if (acceptWorkResponse?.messages is! List ||
          acceptWorkResponse!.messages!.isEmpty) {
        _aiSuggestionsBloc.add(GetAiSuggestionEvent(conversation.id!));
      } else {
        for (var message in acceptWorkResponse.messages!) {
          _logger
              .info('acceptConversation: adding message: ${message.messageId}');
          final Set<String> contentVersionIds = (message.attachments ?? [])
              .map((attachment) => attachment.contentVersionId)
              .toSet();
          final lakMessage = LakMessage(
              messageBody: message.content,
              actorType: MessageActorType.EndUser,
              entryType: MessageEntryType.text,
              id: message.messageId ?? const Uuid().v4(),
              timeStamp: message.timestamp,
              conversationEntryAttachments:
                  await _fetchContentEntriesFromVersionIds(contentVersionIds));
          await addNewMessage(lakMessage, workTargetId,
              shouldSendNotification: false);
        }
      }
    }

    setLakConversations([conversation.lakConversation!]);

    await Future.wait([
      addMessages(),
      GetIt.I<ConversationsViewmodel>().fetchConvoFunc(workTargetId.toString())
    ]);
    return acceptWorkResponse;
  }

  @Deprecated('supporting direct calls from CVM')
  @visibleForTesting
  Future<bool> declineWork(SfId workId, SfId workTargetId) async {
    final response = await _conversationRepository.declineWork(DeclineWorkBody(
      requestId: const Uuid().v4(),
      workId: workId,
      workTargetId: workTargetId,
    ));
    // removeWorkAssignedEventHandled(workId);
    if (response is Success) {
      return true;
    }
    return false;
  }

  Future<bool> declineConversation(SfId conversationId) async {
    final (messagingSession, agentWork) =
        getLatestSessionAndWorkForMessagingEndUser(conversationId);
    if (agentWork == null) {
      return false;
    }

    final success = await declineWork(agentWork.id, agentWork.workTargetId);
    if (success) {
      _agentWorkIdForWorkTargetIds.remove(agentWork.workTargetId);
      await updateMessagingSessionStatus(
          agentWork.workTargetId, ConversationStatus.closed);
    }
    return success;
  }

  Future<Map<SfId, Conversation>> endConversation(SfId conversationId) async {
    final (messagingSession, agentWork) =
        getLatestSessionAndWorkForMessagingEndUser(conversationId);
    if (agentWork == null) {
      return {};
    }

    final success = await closeWork(agentWork.workTargetId);
    if (success) {
      _agentWorkIdForWorkTargetIds.remove(agentWork.workTargetId);
      return updateMessagingSessionStatus(
          agentWork.workTargetId, ConversationStatus.closed);
    }
    return {};
  }

  Future<void> handlePresenceOffline() async {
    _logger.info('handlePresenceOffline');
    _initialFetchStarted = false;
    _initialFetchCompleter = null;
    await _endAllStandardConversations();

    /// set all statuses to closed
    _conversations.forEach((key, value) {
      if (value.isOpened) {
        _conversations[key] = value.copyWith(status: ConversationStatus.closed);
      }
    });
  }

  Future<void> _endAllStandardConversations() async {
    _logger.info(
        'ending AllStandardConversations with ${_activeStandardConversations.length} active standard conversations');
    final List<SfId> workTargetIds = _activeStandardConversations
        .map((convo) => convo.id)
        .whereType<SfId>()
        .toList();
    await Future.forEach(workTargetIds, endConversation);
  }

  @visibleForTesting
  Future<bool> closeWork(SfId workTargetId) async {
    final response = await _conversationRepository.closeWork(CloseWorkBody(
      requestId: const Uuid().v4(),
      workTargetId: workTargetId,
    ));
    if (response is Success) {
      GetIt.I<ShimService>()
          .workTargetIdsByWorkId
          .removeWhere((key, value) => value == workTargetId);
      return true;
    }
    return false;
  }

  Future<Conversation?> prepareOutboundConversation(Conversation? conversation,
      Contact contact, MessagingChannelEntry messagingChannelEntry) async {
    _logger.info(
        'prepareOutboundConversation: $conversation, $contact, $messagingChannelEntry');
    SfId? messagingChannelId;
    SfId? messagingEndUserId;

    if (conversation?.id != null && contact.mobilePhone == null) {
      _logger.warn(
          'prepareOutboundConversation: conversation id is not null, but contact mobilePhone is null');
      return conversation;
    }

    // As Outbound is always initiated by the user, we can use the messagingChannelEntry to get the channelId
    messagingChannelId = messagingChannelEntry.channelId;
    messagingEndUserId = messagingChannelEntry.messagingEndUserId;

    List<MessagingEndUser> associatedMessagingEndUsers =
        await getMessagingEndUsers(
            messagingChannelId: messagingChannelId,
            messagingPlatformKey: _phoneNumberForChannel(
                contact.mobilePhone!, messagingChannelEntry));

    _logger.info(
        'prepareOutboundConversation: associatedMessagingEndUsers: $associatedMessagingEndUsers');
    if (associatedMessagingEndUsers.isNotEmpty) {
      _logger.info(
          'found associated messaging end users: ${associatedMessagingEndUsers.map((meu) => 'meu: ${meu.id}, messagingChannelId: ${meu.messagingChannelId} ')}');
      MessagingEndUser? messagingEndUser =
          associatedMessagingEndUsers.firstWhereOrNull((messagingEndUser) =>

                  /// NOTE: if there a different matching contact, it will be updated as part of the 'createMessagingEndUser' call (we use the SF error response to get the needed meuId, which is otherwise not straightforward to fix)
                  messagingEndUser.contactId == contact.id) ??
              associatedMessagingEndUsers.firstOrNull;

      /// only use an existing messagingEndUser if it's associated with the correct channel; otherwise we'll create a new one below
      _logger.info(
          'prepareOutboundConversation: messagingEndUser: $messagingEndUser');
      if (messagingEndUser != null) {
        messagingEndUserId = messagingEndUser.id;
      }

      _logger.info(
          'prepareOutboundConversation: messagingEndUserId: $messagingEndUserId; messagingEndUser: $messagingEndUser; contact.id: ${contact.id}; messagingEndUser?.contactId: ${messagingEndUser?.contactId} ');
      if (messagingEndUserId != null &&
          contact.id != null &&
          messagingEndUser?.contactId != contact.id) {
        _logger.info(
            'prepareOutboundConversation: updating messagingEndUser with new contactId');
        var updateResponse =
            await _conversationRepository.updateMessagingEndUser(
                messagingEndUserId.toString(),
                UpdateMessagingEndUserBody(
                  contactId: contact.id!,
                ));

        if (updateResponse is Error) {
          throw Exception(
              'Could not update MEU ID after duplicate error: ${(updateResponse as Error).error.message}');
        }
      }
    }

    messagingEndUserId = messagingEndUserId?.isNotEmpty == true
        ? messagingEndUserId
        : await createMessagingEndUser(
            contactId: contact.id!,
            messagingChannelId: messagingChannelId,
            messageType: messagingChannelEntry.messageType,
            messagingPlatformKey: _phoneNumberForChannel(
                contact.mobilePhone!, messagingChannelEntry));

    if (messagingEndUserId == null) {
      return conversation;
    }

    final existingConvo = getConversation(messagingEndUserId);

    _logger.info(
        'prepareOutboundConversation: existingConvo: ${existingConvo?.id}');
    LakConversation? lakConvo;

    if (existingConvo == null) {
      await updateOutboundConversation(
          messagingEndUserId: messagingEndUserId,
          contact: contact.toParticipant(messagingEndUserId.toString()),
          messagingChannelEntry: messagingChannelEntry);
    }

    final newConversation = getConversation(messagingEndUserId) ??
        Conversation(
            lakConversation: lakConvo ??
                LakConversation(
                    id: messagingEndUserId.toString(),
                    channel: messagingChannelEntry.messageType,
                    channelType: messagingChannelEntry.messageType == 'Text'
                        ? ChannelType.sfdcSms
                        : ChannelType.sfdcWhatsappEnhanced));

    _logger.info(
        'prepareOutboundConversation: for messagingEndUserId: $messagingEndUserId; newConversation: $newConversation');

    _conversations[messagingEndUserId] = newConversation;

    return newConversation;
  }

  @Deprecated('copypasta from ConversationsViewmodel; needs refactor')
  Future<void> updateOutboundConversation(
      {required SfId messagingEndUserId,
      Participant? contact,
      MessagingChannelEntry? messagingChannelEntry}) async {
    ChannelType? channelType;

    // Set the proper channel. Note for now we assume only Text or WhatApp
    if (messagingChannelEntry != null) {
      channelType = messagingChannelEntry.messageType == 'Text'
          ? ChannelType.sfdcSms
          : ChannelType.sfdcWhatsappEnhanced;
    } else {
      channelType = ChannelType.sfdcSms;
    }

    String? pictureUrl;

    // Set the proper picture. Note for now we assume only Text or WhatApp
    if (messagingChannelEntry != null) {
      pictureUrl = messagingChannelEntry.messageType == 'Text'
          ? Constants.LOGO_SMS
          : Constants.LOGO_WHATSAPP;
    } else {
      pictureUrl = Constants.LOGO_SMS;
    }

    LakConversation? newConversation =
        getConversation(messagingEndUserId)?.lakConversation;
    newConversation ??= LakConversation(
      // conversationType: ConversationType.deStandard,
      id: messagingEndUserId.toString(),
      username: 'Username',
      channel: messagingChannelEntry?.messageType,
      channelType: channelType,
      pictureUrl: pictureUrl,
    );

    setLakConversations([newConversation]);
    LakConversation? fetchedConvo =
        await _deConversationsService.createConversation(newConversation.id);

    if (fetchedConvo != null) {
      setLakConversations([fetchedConvo.copyWith(username: contact?.username)]);
    }
  }

  Future<SfId?> createMessagingEndUser({
    required SfId contactId,
    required SfId messagingChannelId,
    required String messagingPlatformKey,
    required String messageType,
    String? name,
  }) async {
    CreateMessagingEndUserBody createMessagingEndUserBody =
        CreateMessagingEndUserBody(
            contactId: contactId,
            messagingChannelId: messagingChannelId,
            messagingPlatformKey: messagingPlatformKey,
            messageType: messageType,
            name: name ?? messagingPlatformKey);
    final response = await _conversationRepository
        .createMessagingEndUser(createMessagingEndUserBody);
    if (response is Success) {
      Success result = response as Success;
      return SfId(result.data.id);
    }
    return null;
  }

  Future<List<MessagingEndUser>> getMessagingEndUsers(
      {SfId? contactId,
      SfId? messagingChannelId,
      String? messagingPlatformKey,
      SfId? id}) async {
    final response = await _queryRepository.queryMessagingEndUsers(
        contactId: contactId,
        messagingChannelId: messagingChannelId,
        messagingPlatformKey: messagingPlatformKey,
        id: id);
    if (response is Success) {
      Success result = response as Success;
      return (result.data as SalesforceSoqlQueryResponse)
          .records
          .map(MessagingEndUser.fromJson)
          .toList();
    }
    return [];
  }

  Future<MessagingEndUserStatusResponse> fetchMessagingEndUserStatus(
      SfId messagingEndUserId) async {
    final response = await _conversationRepository
        .fetchMessagingEndUserStatus(messagingEndUserId);
    if (response is Success) {
      Success result = response as Success;
      return result.data as MessagingEndUserStatusResponse;
    }
    return MessagingEndUserStatusResponse(canSendOutbound: false);
  }

  Future<List<TransferOption>> getTransferOptions() async {
    final response = await _transferRepository.getTransferOptions();
    if (response is Success) {
      Success result = response as Success;
      final transferOptions = (result.data as TransferOptionsResponse).options;
      return transferOptions;
    }
    return [];
  }

  Future<List<TransferDestination>> getTransferDestinations(
      List<TransferOption> transferOptions,
      TransferDestinationType transferDestinationType) async {
    // retrieve the agent destinationType
    final transferOption = transferOptions.firstWhereOrNull((transferOption) =>
        transferOption.destinationType == transferDestinationType);

    if (transferOption == null || transferOption.destinations.isEmpty) {
      return [];
    }

    final sortedDestinations =
        List<TransferDestination>.from(transferOption.destinations);
    sortedDestinations.sort((a, b) => a.label.compareTo(b.label));

    return sortedDestinations;
  }

  Future<TransferStatus> transferConversation(
    SfId conversationId,
    SfId destinationId,
    TransferDestinationType destinationType,
  ) async {
    final (messagingSession, agentWork) =
        getLatestSessionAndWorkForMessagingEndUser(conversationId);
    if (messagingSession?.id == null) {
      _logger.warn(
          'transferConversation: messagingSession is null for conversationId: $conversationId');
      return TransferStatus(true, 'Messaging session is null');
    }
    _logger.info(
        'transferConversation: destinationId: $destinationId destinationType: $destinationType');
    final messagingSessionId = messagingSession!.id;
    final response = await _transferRepository.transferMessagingSession(
        messagingSession.id,
        TransferMessagingSessionBody(
          destinationId: destinationId,
          destinationType: destinationType,
        ));
    if (response is Success) {
      updateMessagingSessionStatus(
          messagingSessionId, ConversationStatus.closed);
      return TransferStatus(false, null);
    } else {
      Error error = response as Error;
      return TransferStatus(true, error.error.errorMessage);
    }
  }

  Completer<void>? _initialFetchCompleter;
  bool _initialFetchStarted = false;

  Future<List<LakConversation>> fetchConversations() async {
    _logger.info(
        'fetchConversations called with _initialFetchStarted: $_initialFetchStarted; _initialFetchCompleter: ${_initialFetchCompleter?.isCompleted}');
    if (_initialFetchStarted) {
      // If fetch has already started, wait for it to complete and return current conversations
      await _waitForInitialFetch();
      _logger.info(
          'fetchConversations: returning  ${_conversations.values.where((convo) => convo.isOpened).length} Opened conversations');
      return _conversations.values.map((c) => c.lakConversation!).toList();
    }

    _initialFetchStarted = true;
    _initialFetchCompleter = Completer<void>();

    try {
      final lakConvos =
          await _deConversationsService.fetchConversationsAndMessages();
      setLakConversations(lakConvos);
      if (_initialFetchCompleter?.isCompleted == false) {
        _initialFetchCompleter?.complete();
      }
      return lakConvos;
    } catch (e) {
      if (_initialFetchCompleter?.isCompleted == false) {
        _initialFetchCompleter?.completeError(e);
      }
      rethrow;
    }
  }

  Future<void> _waitForInitialFetch() async {
    if (!_initialFetchStarted) {
      await fetchConversations();
    } else {
      await _initialFetchCompleter?.future;
    }
  }

  Future<Map<SfId, Conversation>> updateMessagingSessionStatus(
      SfId messagingSessionId, ConversationStatus status) async {
    await _waitForInitialFetch();

    final conversation =
        await getConversationByMessagingSessionId(messagingSessionId);
    if (conversation?.id != null) {
      _logger
          .info('updateMessagingSessionStatus: $messagingSessionId to $status');
      _conversations[conversation!.id!] = conversation.copyWith(status: status);
      if (status == ConversationStatus.opened) {
        _aiSuggestionsBloc.add(GetAiSuggestionEvent(conversation.id!));
      }
    } else {
      _logger.warn(
          'updateMessagingSessionStatus: conversation is null for messagingSessionId: $messagingSessionId');
    }
    return _conversations;
  }

  Map<SfId, Conversation> updateConversationStatus(
      SfId conversationId, ConversationStatus status) {
    final conversation = getConversation(conversationId);
    if (conversation?.id != null) {
      _conversations[conversation!.id!] = conversation.copyWith(status: status);
    }
    return _conversations;
  }

  (AgentWork?, WorkCapabilities?) getAgentWorkForMessagingEndUserId(
      SfId? messagingEndUserId) {
    final (messagingSession, agentWork) =
        getLatestSessionAndWorkForMessagingEndUser(messagingEndUserId);
    return (agentWork, _agentWorkCapabilities[agentWork?.id]);
  }

  void clear() {
    _conversations = {};
    _agentWorkIdForWorkTargetIds.clear();
    _agentWorkCapabilities.clear();
    _messagingSessions.clear();
    _locallyCachedPendingMessages.clear();
    _conversationsPendingAccepted.clear();
    _workAssignedEventsHandled.clear();
    _notificationIdsForWorkTargetIdOffered.clear();
    _cachedMessages.clear();
    _messagingSessionIdsToFetch.clear();
  }

  void removePendingAcceptedConversation(String id) =>
      _conversationsPendingAccepted.remove(id);

  /// *************************************************
  /// Cached Messages
  // final Map<String, (String, List<File>)> _cachedMessages = {};
  // saved message for conversation.id
  final Map<SfId, CachedMessage> _cachedMessages = {};

  void cacheMessage(SfId id, String message, List<File> attachments) =>
      _cachedMessages[id] = (message, attachments);

  CachedMessage? getCachedMessage(SfId id) => _cachedMessages[id];

  String _phoneNumberForChannel(
      String mobilePhone, MessagingChannelEntry messagingChannelEntry) {
    final phoneNumber = const ParsePhoneNumberConverter().fromJson(mobilePhone);

    switch (messagingChannelEntry.messageType.toLowerCase()) {
      case 'whatsapp':
        return phoneNumber;
      default:
        return "+$phoneNumber";
    }
  }

  Future<List<ConversationEntries>> getConversationEntries(
      List<String> conversationIds) async {
    final queryRepository = GetIt.instance<QueryRepository>();
    final result = await queryRepository.conversationEntries(conversationIds);
    if (result is Success) {
      SalesforceSoqlQueryResponse response = (result as Success).data;

      if (response.records.isNotEmpty) {
        List<ConversationEntries> conversationEntries = response.records
            .map((record) => ConversationEntries.fromJson(record))
            .toList();
        return conversationEntries;
      }
    }
    return [];
  }

  void removeMessageFromConversation(
      SfId messagingEndUserId, String messageId) {
    final conversation = getConversation(messagingEndUserId);
    if (conversation != null) {
      final messages = conversation.lakConversation?.messages;
      if (messages != null) {
        messages.removeWhere((message) => message.id == messageId);
      }
    }
  }
}
