// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_channel.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingChannel _$MessagingChannelFromJson(Map<String, dynamic> json) {
  return _MessagingChannel.fromJson(json);
}

/// @nodoc
mixin _$MessagingChannel {
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  SfId get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'MessageType')
  String? get messageType => throw _privateConstructorUsedError;
  @JsonKey(name: 'DeveloperName')
  String? get developerName => throw _privateConstructorUsedError;
  @JsonKey(name: 'MasterLabel')
  String? get masterLabel => throw _privateConstructorUsedError;
  @JsonKey(name: 'MessagingPlatformKey')
  String? get messagingPlatformKey => throw _privateConstructorUsedError;
  @JsonKey(name: 'PlatformType')
  String? get platformType => throw _privateConstructorUsedError;
  @JsonKey(name: 'Language')
  String? get language => throw _privateConstructorUsedError;
  @JsonKey(name: 'IsoCountryCode')
  String? get isoCountryCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'IsActive')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: 'isDeleted')
  bool? get isDeleted => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingChannelCopyWith<MessagingChannel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingChannelCopyWith<$Res> {
  factory $MessagingChannelCopyWith(
          MessagingChannel value, $Res Function(MessagingChannel) then) =
      _$MessagingChannelCopyWithImpl<$Res, MessagingChannel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
      @JsonKey(name: 'MessageType') String? messageType,
      @JsonKey(name: 'DeveloperName') String? developerName,
      @JsonKey(name: 'MasterLabel') String? masterLabel,
      @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
      @JsonKey(name: 'PlatformType') String? platformType,
      @JsonKey(name: 'Language') String? language,
      @JsonKey(name: 'IsoCountryCode') String? isoCountryCode,
      @JsonKey(name: 'IsActive') bool? isActive,
      @JsonKey(name: 'isDeleted') bool? isDeleted});

  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class _$MessagingChannelCopyWithImpl<$Res, $Val extends MessagingChannel>
    implements $MessagingChannelCopyWith<$Res> {
  _$MessagingChannelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageType = freezed,
    Object? developerName = freezed,
    Object? masterLabel = freezed,
    Object? messagingPlatformKey = freezed,
    Object? platformType = freezed,
    Object? language = freezed,
    Object? isoCountryCode = freezed,
    Object? isActive = freezed,
    Object? isDeleted = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageType: freezed == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String?,
      developerName: freezed == developerName
          ? _value.developerName
          : developerName // ignore: cast_nullable_to_non_nullable
              as String?,
      masterLabel: freezed == masterLabel
          ? _value.masterLabel
          : masterLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingPlatformKey: freezed == messagingPlatformKey
          ? _value.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String?,
      platformType: freezed == platformType
          ? _value.platformType
          : platformType // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      isoCountryCode: freezed == isoCountryCode
          ? _value.isoCountryCode
          : isoCountryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isDeleted: freezed == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_value.id, (value) {
      return _then(_value.copyWith(id: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingChannelImplCopyWith<$Res>
    implements $MessagingChannelCopyWith<$Res> {
  factory _$$MessagingChannelImplCopyWith(_$MessagingChannelImpl value,
          $Res Function(_$MessagingChannelImpl) then) =
      __$$MessagingChannelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
      @JsonKey(name: 'MessageType') String? messageType,
      @JsonKey(name: 'DeveloperName') String? developerName,
      @JsonKey(name: 'MasterLabel') String? masterLabel,
      @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
      @JsonKey(name: 'PlatformType') String? platformType,
      @JsonKey(name: 'Language') String? language,
      @JsonKey(name: 'IsoCountryCode') String? isoCountryCode,
      @JsonKey(name: 'IsActive') bool? isActive,
      @JsonKey(name: 'isDeleted') bool? isDeleted});

  @override
  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class __$$MessagingChannelImplCopyWithImpl<$Res>
    extends _$MessagingChannelCopyWithImpl<$Res, _$MessagingChannelImpl>
    implements _$$MessagingChannelImplCopyWith<$Res> {
  __$$MessagingChannelImplCopyWithImpl(_$MessagingChannelImpl _value,
      $Res Function(_$MessagingChannelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? messageType = freezed,
    Object? developerName = freezed,
    Object? masterLabel = freezed,
    Object? messagingPlatformKey = freezed,
    Object? platformType = freezed,
    Object? language = freezed,
    Object? isoCountryCode = freezed,
    Object? isActive = freezed,
    Object? isDeleted = freezed,
  }) {
    return _then(_$MessagingChannelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageType: freezed == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String?,
      developerName: freezed == developerName
          ? _value.developerName
          : developerName // ignore: cast_nullable_to_non_nullable
              as String?,
      masterLabel: freezed == masterLabel
          ? _value.masterLabel
          : masterLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingPlatformKey: freezed == messagingPlatformKey
          ? _value.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String?,
      platformType: freezed == platformType
          ? _value.platformType
          : platformType // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      isoCountryCode: freezed == isoCountryCode
          ? _value.isoCountryCode
          : isoCountryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isDeleted: freezed == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingChannelImpl extends _MessagingChannel {
  const _$MessagingChannelImpl(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() required this.id,
      @JsonKey(name: 'MessageType') this.messageType,
      @JsonKey(name: 'DeveloperName') this.developerName,
      @JsonKey(name: 'MasterLabel') this.masterLabel,
      @JsonKey(name: 'MessagingPlatformKey') this.messagingPlatformKey,
      @JsonKey(name: 'PlatformType') this.platformType,
      @JsonKey(name: 'Language') this.language,
      @JsonKey(name: 'IsoCountryCode') this.isoCountryCode,
      @JsonKey(name: 'IsActive') this.isActive,
      @JsonKey(name: 'isDeleted') this.isDeleted})
      : super._();

  factory _$MessagingChannelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingChannelImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  final SfId id;
  @override
  @JsonKey(name: 'MessageType')
  final String? messageType;
  @override
  @JsonKey(name: 'DeveloperName')
  final String? developerName;
  @override
  @JsonKey(name: 'MasterLabel')
  final String? masterLabel;
  @override
  @JsonKey(name: 'MessagingPlatformKey')
  final String? messagingPlatformKey;
  @override
  @JsonKey(name: 'PlatformType')
  final String? platformType;
  @override
  @JsonKey(name: 'Language')
  final String? language;
  @override
  @JsonKey(name: 'IsoCountryCode')
  final String? isoCountryCode;
  @override
  @JsonKey(name: 'IsActive')
  final bool? isActive;
  @override
  @JsonKey(name: 'isDeleted')
  final bool? isDeleted;

  @override
  String toString() {
    return 'MessagingChannel(id: $id, messageType: $messageType, developerName: $developerName, masterLabel: $masterLabel, messagingPlatformKey: $messagingPlatformKey, platformType: $platformType, language: $language, isoCountryCode: $isoCountryCode, isActive: $isActive, isDeleted: $isDeleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingChannelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.developerName, developerName) ||
                other.developerName == developerName) &&
            (identical(other.masterLabel, masterLabel) ||
                other.masterLabel == masterLabel) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.platformType, platformType) ||
                other.platformType == platformType) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.isoCountryCode, isoCountryCode) ||
                other.isoCountryCode == isoCountryCode) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      messageType,
      developerName,
      masterLabel,
      messagingPlatformKey,
      platformType,
      language,
      isoCountryCode,
      isActive,
      isDeleted);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingChannelImplCopyWith<_$MessagingChannelImpl> get copyWith =>
      __$$MessagingChannelImplCopyWithImpl<_$MessagingChannelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingChannelImplToJson(
      this,
    );
  }
}

abstract class _MessagingChannel extends MessagingChannel {
  const factory _MessagingChannel(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() required final SfId id,
      @JsonKey(name: 'MessageType') final String? messageType,
      @JsonKey(name: 'DeveloperName') final String? developerName,
      @JsonKey(name: 'MasterLabel') final String? masterLabel,
      @JsonKey(name: 'MessagingPlatformKey') final String? messagingPlatformKey,
      @JsonKey(name: 'PlatformType') final String? platformType,
      @JsonKey(name: 'Language') final String? language,
      @JsonKey(name: 'IsoCountryCode') final String? isoCountryCode,
      @JsonKey(name: 'IsActive') final bool? isActive,
      @JsonKey(name: 'isDeleted')
      final bool? isDeleted}) = _$MessagingChannelImpl;
  const _MessagingChannel._() : super._();

  factory _MessagingChannel.fromJson(Map<String, dynamic> json) =
      _$MessagingChannelImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  SfId get id;
  @override
  @JsonKey(name: 'MessageType')
  String? get messageType;
  @override
  @JsonKey(name: 'DeveloperName')
  String? get developerName;
  @override
  @JsonKey(name: 'MasterLabel')
  String? get masterLabel;
  @override
  @JsonKey(name: 'MessagingPlatformKey')
  String? get messagingPlatformKey;
  @override
  @JsonKey(name: 'PlatformType')
  String? get platformType;
  @override
  @JsonKey(name: 'Language')
  String? get language;
  @override
  @JsonKey(name: 'IsoCountryCode')
  String? get isoCountryCode;
  @override
  @JsonKey(name: 'IsActive')
  bool? get isActive;
  @override
  @JsonKey(name: 'isDeleted')
  bool? get isDeleted;
  @override
  @JsonKey(ignore: true)
  _$$MessagingChannelImplCopyWith<_$MessagingChannelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
