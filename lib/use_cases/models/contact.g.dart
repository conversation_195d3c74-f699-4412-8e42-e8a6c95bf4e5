// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContactImpl _$$ContactImplFromJson(Map<String, dynamic> json) =>
    _$ContactImpl(
      id: json['Id'] == null
          ? null
          : SfId.fromJson(json['Id'] as Map<String, dynamic>),
      firstName: json['FirstName'] as String?,
      lastName: json['LastName'] as String?,
      title: json['Title'] as String?,
      photoUrl: json['PhotoUrl'] as String?,
      mobilePhone: json['MobilePhone'] as String?,
      email: json['Email'] as String?,
      consumerId: json['ConsumerId'] == null
          ? null
          : SfId.fromJson(json['ConsumerId'] as Map<String, dynamic>),
      userDetails: json['UserDetails'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$ContactImplToJson(_$ContactImpl instance) =>
    <String, dynamic>{
      'Id': instance.id?.toJson(),
      'FirstName': instance.firstName,
      'LastName': instance.lastName,
      'Title': instance.title,
      'PhotoUrl': instance.photoUrl,
      'MobilePhone': instance.mobilePhone,
      'Email': instance.email,
      'ConsumerId': instance.consumerId?.toJson(),
      'UserDetails': instance.userDetails,
    };
