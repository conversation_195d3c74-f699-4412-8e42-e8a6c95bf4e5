import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'scrt_credentials.freezed.dart';
part 'scrt_credentials.g.dart';

@freezed
@collectionOnFreezed
class ScrtCredentials with _$ScrtCredentials {
  /// Isar ID for indexing — Setting this one to '0' for singleton item in collection; otherwise default use here is (can also use custom indices -- see isar docs):
  final Id id = 0;
  const ScrtCredentials._();

  const factory ScrtCredentials({
    String? scrtAccessToken,
    String? scrtHost,
  }) = _ScrtCredentials;

  factory ScrtCredentials.fromJson(Map<String, dynamic> json) =>
      _$ScrtCredentialsFromJson(json);
}