// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_end_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingEndUserImpl _$$MessagingEndUserImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingEndUserImpl(
      id: const ParseSfIdConverter().fromJson(json['Id']),
      contactId: const ParseSfIdConverter().from<PERSON><PERSON>(json['ContactId']),
      messagingChannelId:
          const ParseSfIdConverter().from<PERSON>son(json['MessagingChannelId']),
      messagingPlatformKey: json['MessagingPlatformKey'] as String?,
      name: json['Name'] as String?,
      profilePictureUrl: json['ProfilePictureUrl'] as String?,
      contactPictureUrl: json['contactPictureUrl'] as String?,
      contactEmail: json['contactEmail'] as String?,
      contactName: json['contactName'] as String?,
      contactMobilePhone: json['contactMobilePhone'] as String?,
      userFullPhotoUrl: json['userFullPhotoUrl'] as String?,
    );

Map<String, dynamic> _$$MessagingEndUserImplToJson(
        _$MessagingEndUserImpl instance) =>
    <String, dynamic>{
      'Id': const ParseSfIdConverter().toJson(instance.id),
      'ContactId': _$JsonConverterToJson<Object?, SfId>(
          instance.contactId, const ParseSfIdConverter().toJson),
      'MessagingChannelId': _$JsonConverterToJson<Object?, SfId>(
          instance.messagingChannelId, const ParseSfIdConverter().toJson),
      'MessagingPlatformKey': instance.messagingPlatformKey,
      'Name': instance.name,
      'ProfilePictureUrl': instance.profilePictureUrl,
      'contactPictureUrl': instance.contactPictureUrl,
      'contactEmail': instance.contactEmail,
      'contactName': instance.contactName,
      'contactMobilePhone': instance.contactMobilePhone,
      'userFullPhotoUrl': instance.userFullPhotoUrl,
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
