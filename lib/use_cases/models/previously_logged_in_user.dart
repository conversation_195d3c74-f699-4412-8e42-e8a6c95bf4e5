import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'previously_logged_in_user.freezed.dart';
part 'previously_logged_in_user.g.dart';

@freezed
@collectionOnFreezed
class PreviouslyLoggedInUser with _$PreviouslyLoggedInUser {
  /// Isar ID for indexing — Setting this one to '0' for singleton item in collection; otherwise default use here is (can also use custom indices -- see isar docs):
  final Id id = 0;
  const PreviouslyLoggedInUser._();

  const factory PreviouslyLoggedInUser({
    String? userId,
    String? orgId,
    String? presenceId,
  }) = _PreviouslyLoggedInUser;

  factory PreviouslyLoggedInUser.fromJson(Map<String, dynamic> json) =>
      _$PreviouslyLoggedInUserFromJson(json);
}
