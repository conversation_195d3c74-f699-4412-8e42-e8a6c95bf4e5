// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credentials.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CredentialsImpl _$$CredentialsImplFromJson(Map<String, dynamic> json) =>
    _$CredentialsImpl(
      userId: json['userId'] as String?,
      instanceUrl: json['instanceUrl'] as String?,
      accessToken: json['accessToken'] as String?,
      orgId: json['orgId'] as String?,
      authorizationToken: json['authorizationToken'] as String?,
      webSocketUrl: json['webSocketUrl'] as String?,
      sessionToken: json['sessionToken'] as String?,
      sessionId: json['sessionId'] as String?,
      refreshToken: json['refreshToken'] as String?,
      devicePushToken: json['devicePushToken'] as String?,
      authorizationTokenExpirationTime:
          (json['authorizationTokenExpirationTime'] as num?)?.toInt(),
      sessionExpirationTime: (json['sessionExpirationTime'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$CredentialsImplToJson(_$CredentialsImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'instanceUrl': instance.instanceUrl,
      'accessToken': instance.accessToken,
      'orgId': instance.orgId,
      'authorizationToken': instance.authorizationToken,
      'webSocketUrl': instance.webSocketUrl,
      'sessionToken': instance.sessionToken,
      'sessionId': instance.sessionId,
      'refreshToken': instance.refreshToken,
      'devicePushToken': instance.devicePushToken,
      'authorizationTokenExpirationTime':
          instance.authorizationTokenExpirationTime,
      'sessionExpirationTime': instance.sessionExpirationTime,
    };
