import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

import 'messaging_channel.dart';

part 'messaging_end_user.freezed.dart';
part 'messaging_end_user.g.dart';

@freezed
class MessagingEndUser with _$MessagingEndUser {
  const MessagingEndUser._();

  const factory MessagingEndUser({
      @Json<PERSON>ey(name: 'Id') @ParseSfIdConverter() required SfId  id,
      // @J<PERSON><PERSON><PERSON>(name: 'AccountId') String?  accountId,
      @J<PERSON><PERSON><PERSON>(name: 'ContactId') @ParseSfIdConverter() SfId?  contactId,
      // @<PERSON><PERSON><PERSON><PERSON>(name: 'HasInitialResponseSent') bool?  hasInitialResponseSent,
      // @J<PERSON><PERSON><PERSON>(name: 'IsFullyOptedIn') bool?  isFullyOptedIn,
      // @<PERSON><PERSON><PERSON><PERSON>(name: 'IsOptedOut') bool?  isOptedOut,
      // @<PERSON><PERSON><PERSON><PERSON>(name: 'IsoCountryCode') String?  isoCountryCode,
      // @<PERSON><PERSON><PERSON><PERSON>(name: 'MessageType') String?  messageType,
      @<PERSON><PERSON><PERSON><PERSON>(name: 'MessagingChannelId') @ParseSfIdConverter() SfId?  messagingChannelId,
      // @JsonKey(name: 'MessagingConsentStatus') String?  messagingConsentStatus,
      @JsonKey(name: 'MessagingPlatformKey') String?  messagingPlatformKey,
      @JsonKey(name: 'Name') String?  name,
      // @JsonKey(name: 'OwnerId') String?  ownerId,
      @JsonKey(name: 'ProfilePictureUrl') String?  profilePictureUrl,

    /// items below are from the 'contact' object but were previously combined with this as such, these are added to support legacy code
    @Deprecated('legacy')
    String? contactPictureUrl,
    @Deprecated('legacy')
    String? contactEmail,
    @Deprecated('legacy')
      String?  contactName,
    @Deprecated('legacy')
    String? contactMobilePhone,
    @Deprecated('legacy')
    String? userFullPhotoUrl,
    // MessagingChannel? messagingChannel,
  }) = _MessagingEndUser;

  factory MessagingEndUser.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserFromJson(json);

  factory MessagingEndUser.fromGraphQlNode(Map<String, dynamic> json) {
    return MessagingEndUser(
        id: SfId(json['Id']),
        name: json['Name']?['value'],
        profilePictureUrl: json['ProfilePictureUrl']?['value'],
        contactId: SfId(json['Contact']?['Id'] ?? ''),
        contactPictureUrl: json['Contact']?['PhotoUrl']?['value'],
        contactEmail: json['Contact']?['Email']?['value'],
        contactName: json['Contact']?['Name']?['value'],
        contactMobilePhone: json['Contact']?['MobilePhone']?['value'],
        userFullPhotoUrl: json['Contact']?['Owner']?['FullPhotoUrl']?['value'],
        messagingChannelId: json['MessagingChannel'] != null
            ? MessagingChannel.fromJson(json['MessagingChannel']).id
            : null);
  }
}