// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'salesforce_environment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SalesforceEnvironmentImpl _$$SalesforceEnvironmentImplFromJson(
        Map<String, dynamic> json) =>
    _$SalesforceEnvironmentImpl(
      type: $enumDecodeNullable(
              _$SalesforceEnvironmentTypeEnumMap, json['type']) ??
          SalesforceEnvironmentType.production,
      customDomainBase: json['customDomainBase'] as String?,
    );

Map<String, dynamic> _$$SalesforceEnvironmentImplToJson(
        _$SalesforceEnvironmentImpl instance) =>
    <String, dynamic>{
      'type': _$SalesforceEnvironmentTypeEnumMap[instance.type]!,
      'customDomainBase': instance.customDomainBase,
    };

const _$SalesforceEnvironmentTypeEnumMap = {
  SalesforceEnvironmentType.production: 'production',
  SalesforceEnvironmentType.sandbox: 'sandbox',
  SalesforceEnvironmentType.custom: 'custom',
  SalesforceEnvironmentType.demo: 'demo',
};
