// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_messaging_session.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OutboundMessagingSession _$OutboundMessagingSessionFromJson(
    Map<String, dynamic> json) {
  return _OutboundMessagingSession.fromJson(json);
}

/// @nodoc
mixin _$OutboundMessagingSession {
  String get messagingEndUserId => throw _privateConstructorUsedError;
  String get messagingChannelId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OutboundMessagingSessionCopyWith<OutboundMessagingSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutboundMessagingSessionCopyWith<$Res> {
  factory $OutboundMessagingSessionCopyWith(OutboundMessagingSession value,
          $Res Function(OutboundMessagingSession) then) =
      _$OutboundMessagingSessionCopyWithImpl<$Res, OutboundMessagingSession>;
  @useResult
  $Res call({String messagingEndUserId, String messagingChannelId});
}

/// @nodoc
class _$OutboundMessagingSessionCopyWithImpl<$Res,
        $Val extends OutboundMessagingSession>
    implements $OutboundMessagingSessionCopyWith<$Res> {
  _$OutboundMessagingSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingEndUserId = null,
    Object? messagingChannelId = null,
  }) {
    return _then(_value.copyWith(
      messagingEndUserId: null == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingChannelId: null == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OutboundMessagingSessionImplCopyWith<$Res>
    implements $OutboundMessagingSessionCopyWith<$Res> {
  factory _$$OutboundMessagingSessionImplCopyWith(
          _$OutboundMessagingSessionImpl value,
          $Res Function(_$OutboundMessagingSessionImpl) then) =
      __$$OutboundMessagingSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String messagingEndUserId, String messagingChannelId});
}

/// @nodoc
class __$$OutboundMessagingSessionImplCopyWithImpl<$Res>
    extends _$OutboundMessagingSessionCopyWithImpl<$Res,
        _$OutboundMessagingSessionImpl>
    implements _$$OutboundMessagingSessionImplCopyWith<$Res> {
  __$$OutboundMessagingSessionImplCopyWithImpl(
      _$OutboundMessagingSessionImpl _value,
      $Res Function(_$OutboundMessagingSessionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingEndUserId = null,
    Object? messagingChannelId = null,
  }) {
    return _then(_$OutboundMessagingSessionImpl(
      messagingEndUserId: null == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingChannelId: null == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OutboundMessagingSessionImpl implements _OutboundMessagingSession {
  const _$OutboundMessagingSessionImpl(
      {required this.messagingEndUserId, required this.messagingChannelId});

  factory _$OutboundMessagingSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$OutboundMessagingSessionImplFromJson(json);

  @override
  final String messagingEndUserId;
  @override
  final String messagingChannelId;

  @override
  String toString() {
    return 'OutboundMessagingSession(messagingEndUserId: $messagingEndUserId, messagingChannelId: $messagingChannelId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OutboundMessagingSessionImpl &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, messagingEndUserId, messagingChannelId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OutboundMessagingSessionImplCopyWith<_$OutboundMessagingSessionImpl>
      get copyWith => __$$OutboundMessagingSessionImplCopyWithImpl<
          _$OutboundMessagingSessionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OutboundMessagingSessionImplToJson(
      this,
    );
  }
}

abstract class _OutboundMessagingSession implements OutboundMessagingSession {
  const factory _OutboundMessagingSession(
          {required final String messagingEndUserId,
          required final String messagingChannelId}) =
      _$OutboundMessagingSessionImpl;

  factory _OutboundMessagingSession.fromJson(Map<String, dynamic> json) =
      _$OutboundMessagingSessionImpl.fromJson;

  @override
  String get messagingEndUserId;
  @override
  String get messagingChannelId;
  @override
  @JsonKey(ignore: true)
  _$$OutboundMessagingSessionImplCopyWith<_$OutboundMessagingSessionImpl>
      get copyWith => throw _privateConstructorUsedError;
}
