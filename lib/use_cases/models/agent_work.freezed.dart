// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'agent_work.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AgentWork {
  SfId get id => throw _privateConstructorUsedError;
  SfId get workTargetId => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AgentWorkCopyWith<AgentWork> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AgentWorkCopyWith<$Res> {
  factory $AgentWorkCopyWith(AgentWork value, $Res Function(AgentWork) then) =
      _$AgentWorkCopyWithImpl<$Res, AgentWork>;
  @useResult
  $Res call({SfId id, SfId workTargetId});

  $SfIdCopyWith<$Res> get id;
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class _$AgentWorkCopyWithImpl<$Res, $Val extends AgentWork>
    implements $AgentWorkCopyWith<$Res> {
  _$AgentWorkCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? workTargetId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_value.id, (value) {
      return _then(_value.copyWith(id: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_value.workTargetId, (value) {
      return _then(_value.copyWith(workTargetId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AgentWorkImplCopyWith<$Res>
    implements $AgentWorkCopyWith<$Res> {
  factory _$$AgentWorkImplCopyWith(
          _$AgentWorkImpl value, $Res Function(_$AgentWorkImpl) then) =
      __$$AgentWorkImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({SfId id, SfId workTargetId});

  @override
  $SfIdCopyWith<$Res> get id;
  @override
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class __$$AgentWorkImplCopyWithImpl<$Res>
    extends _$AgentWorkCopyWithImpl<$Res, _$AgentWorkImpl>
    implements _$$AgentWorkImplCopyWith<$Res> {
  __$$AgentWorkImplCopyWithImpl(
      _$AgentWorkImpl _value, $Res Function(_$AgentWorkImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? workTargetId = null,
  }) {
    return _then(_$AgentWorkImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }
}

/// @nodoc

class _$AgentWorkImpl implements _AgentWork {
  const _$AgentWorkImpl({required this.id, required this.workTargetId});

  @override
  final SfId id;
  @override
  final SfId workTargetId;

  @override
  String toString() {
    return 'AgentWork(id: $id, workTargetId: $workTargetId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AgentWorkImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, workTargetId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AgentWorkImplCopyWith<_$AgentWorkImpl> get copyWith =>
      __$$AgentWorkImplCopyWithImpl<_$AgentWorkImpl>(this, _$identity);
}

abstract class _AgentWork implements AgentWork {
  const factory _AgentWork(
      {required final SfId id,
      required final SfId workTargetId}) = _$AgentWorkImpl;

  @override
  SfId get id;
  @override
  SfId get workTargetId;
  @override
  @JsonKey(ignore: true)
  _$$AgentWorkImplCopyWith<_$AgentWorkImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
