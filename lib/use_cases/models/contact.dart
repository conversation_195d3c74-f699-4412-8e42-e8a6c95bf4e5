import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/salesforce/dtos/contact_response.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/utils/phone_number_formatter.dart';

part 'contact.freezed.dart';
part 'contact.g.dart';
@freezed
class Contact with _$Contact {

  const Contact._();

  String? get name => firstName == null && lastName == null ? null : '$firstName $lastName';

  String? get formattedMobilePhone => mobilePhone == null ? null : PhoneNumberFormatter.formatUsPhoneNumber(
  mobilePhone!);

  const factory Contact({
    @JsonKey(name: 'Id') SfId? id,
    @JsonKey(name: 'FirstName') String? firstName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'LastName') String? lastName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Title') String? title,
    @Json<PERSON>ey(name: 'PhotoUrl') String? photoUrl,
    @J<PERSON><PERSON><PERSON>(name: 'MobilePhone') String? mobilePhone,
    @JsonKey(name: 'Email') String? email,
    /// fields to support Lak Participant model
    @JsonKey(name: 'ConsumerId') SfId? consumerId,
    @JsonKey(name: 'UserDetails') Map<String, dynamic>? userDetails,
  }) = _Contact;

  // static (String, String) _getName({String? firstName, String? lastName, String? name}) {
  //   if (name != null) {
  //     return name.split(' ').length == 2
  //         ? (name.split(' ')[0], name.split(' ')[1])
  //         : (name, '');
  //   }
  //   return (firstName ?? '', lastName ?? '');
  // }

  static (String?, String?) parseNames({String? firstName, String? lastName, String? fullName}) {
    // If both firstName and lastName are provided, use them
    if (firstName != null && lastName != null) {
      return (firstName.trim(), lastName.trim());
    }

    // If fullName is provided, parse it
    if (fullName != null) {
      fullName = fullName.trim();
      final parts = fullName.split(' ');

      if (parts.length > 1) {
        // If there are multiple parts, assume the last part is the lastName
        final lastName = parts.last;
        final firstName = parts.sublist(0, parts.length - 1).join(' ');
        return (firstName, lastName);
      } else if (parts.length == 1) {
        // If there's only one part, assume it's the firstName
        return (parts[0], null);
      }
    }

    // If only firstName is provided
    if (firstName != null) {
      return (firstName.trim(), null);
    }

    // If only lastName is provided
    if (lastName != null) {
      return (null, lastName.trim());
    }

    // If no valid input is provided
    return (null, null);
  }

  factory Contact.fromJson(Map<String, dynamic> json) =>
      _$ContactFromJson(json);

  factory Contact.fromParticipant(Participant participant) {
    var (firstName, lastName) = parseNames(firstName: participant.firstName, lastName: participant.lastName, fullName: participant.name);
    return Contact(
    id: participant.id?.toSfId(),
    firstName: firstName,
    lastName: lastName,
    title: participant.title,
    photoUrl: participant.photoUrl,
    mobilePhone: participant.mobilePhone,
    email: participant.email,
      consumerId: participant.consumerId?.toSfId(),
      userDetails: participant.userDetails,
  );
  }

  factory Contact.fromContactFetchResponse(ContactResponse response) {
    var (firstName, lastName) = parseNames(firstName: response.firstName, lastName: response.lastName, fullName: response.name);
    return Contact(
    id: response.id?.toSfId(),
    title: response.title,
    photoUrl: response.photoUrl,
    mobilePhone: response.mobilePhone,
    email: response.email,
    firstName: firstName,
    lastName: lastName
  );
  }

  @Deprecated('to support legacy methods')
  Participant toParticipant(String? messagingEndUserId,
      {ParticipantType type = ParticipantType.Visitor,
        Map<String, dynamic>? layout,
        Map<String, dynamic>? userDetails,
        Map<String, dynamic>? relatedLists
      }) => Participant(
    id: id?.toString(),
    firstName: firstName,
    lastName: lastName,
    name: name,
    title: title,
    photoUrl: photoUrl,
    mobilePhone: mobilePhone,
    email: email,
    messagingEndUserId: messagingEndUserId,
    type: type,
    consumerId: consumerId?.toString(),
    userDetails: userDetails ?? this.userDetails,
    layout: layout,
    relatedLists: relatedLists,
  );
}