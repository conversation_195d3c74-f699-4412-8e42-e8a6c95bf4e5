// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'scrt_credentials.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ScrtCredentials _$ScrtCredentialsFromJson(Map<String, dynamic> json) {
  return _ScrtCredentials.fromJson(json);
}

/// @nodoc
mixin _$ScrtCredentials {
  String? get scrtAccessToken => throw _privateConstructorUsedError;
  String? get scrtHost => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ScrtCredentialsCopyWith<ScrtCredentials> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScrtCredentialsCopyWith<$Res> {
  factory $ScrtCredentialsCopyWith(
          ScrtCredentials value, $Res Function(ScrtCredentials) then) =
      _$ScrtCredentialsCopyWithImpl<$Res, ScrtCredentials>;
  @useResult
  $Res call({String? scrtAccessToken, String? scrtHost});
}

/// @nodoc
class _$ScrtCredentialsCopyWithImpl<$Res, $Val extends ScrtCredentials>
    implements $ScrtCredentialsCopyWith<$Res> {
  _$ScrtCredentialsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scrtAccessToken = freezed,
    Object? scrtHost = freezed,
  }) {
    return _then(_value.copyWith(
      scrtAccessToken: freezed == scrtAccessToken
          ? _value.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtHost: freezed == scrtHost
          ? _value.scrtHost
          : scrtHost // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ScrtCredentialsImplCopyWith<$Res>
    implements $ScrtCredentialsCopyWith<$Res> {
  factory _$$ScrtCredentialsImplCopyWith(_$ScrtCredentialsImpl value,
          $Res Function(_$ScrtCredentialsImpl) then) =
      __$$ScrtCredentialsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? scrtAccessToken, String? scrtHost});
}

/// @nodoc
class __$$ScrtCredentialsImplCopyWithImpl<$Res>
    extends _$ScrtCredentialsCopyWithImpl<$Res, _$ScrtCredentialsImpl>
    implements _$$ScrtCredentialsImplCopyWith<$Res> {
  __$$ScrtCredentialsImplCopyWithImpl(
      _$ScrtCredentialsImpl _value, $Res Function(_$ScrtCredentialsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scrtAccessToken = freezed,
    Object? scrtHost = freezed,
  }) {
    return _then(_$ScrtCredentialsImpl(
      scrtAccessToken: freezed == scrtAccessToken
          ? _value.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtHost: freezed == scrtHost
          ? _value.scrtHost
          : scrtHost // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ScrtCredentialsImpl extends _ScrtCredentials {
  const _$ScrtCredentialsImpl({this.scrtAccessToken, this.scrtHost})
      : super._();

  factory _$ScrtCredentialsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ScrtCredentialsImplFromJson(json);

  @override
  final String? scrtAccessToken;
  @override
  final String? scrtHost;

  @override
  String toString() {
    return 'ScrtCredentials(scrtAccessToken: $scrtAccessToken, scrtHost: $scrtHost)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScrtCredentialsImpl &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken) &&
            (identical(other.scrtHost, scrtHost) ||
                other.scrtHost == scrtHost));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, scrtAccessToken, scrtHost);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ScrtCredentialsImplCopyWith<_$ScrtCredentialsImpl> get copyWith =>
      __$$ScrtCredentialsImplCopyWithImpl<_$ScrtCredentialsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ScrtCredentialsImplToJson(
      this,
    );
  }
}

abstract class _ScrtCredentials extends ScrtCredentials {
  const factory _ScrtCredentials(
      {final String? scrtAccessToken,
      final String? scrtHost}) = _$ScrtCredentialsImpl;
  const _ScrtCredentials._() : super._();

  factory _ScrtCredentials.fromJson(Map<String, dynamic> json) =
      _$ScrtCredentialsImpl.fromJson;

  @override
  String? get scrtAccessToken;
  @override
  String? get scrtHost;
  @override
  @JsonKey(ignore: true)
  _$$ScrtCredentialsImplCopyWith<_$ScrtCredentialsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
