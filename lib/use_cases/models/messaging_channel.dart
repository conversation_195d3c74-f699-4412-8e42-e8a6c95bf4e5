import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'messaging_channel.freezed.dart';
part 'messaging_channel.g.dart';

@freezed
class MessagingChannel with _$MessagingChannel {
  const MessagingChannel._();

  const factory MessagingChannel({
    @Json<PERSON><PERSON>(name: 'Id')
    @ParseSfIdConverter() required SfId id,
    @<PERSON>son<PERSON>ey(name: 'MessageType')
    String? messageType,
    @Json<PERSON>ey(name: 'DeveloperName')
    String? developerName,
    @Json<PERSON>ey(name: 'MasterLabel')
    String? masterLabel,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'MessagingPlatformKey')
    String? messagingPlatformKey,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'PlatformType')
    String? platformType,
    @Json<PERSON>ey(name: 'Language')
    String? language,
    @<PERSON>son<PERSON>ey(name: 'IsoCountryCode')
  String? isoCountryCode,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'IsActive')
  bool? isActive,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isDeleted')
  bool? isDeleted
  }) = _MessagingChannel;

  factory MessagingChannel.fromJson(Map<String, dynamic> json) =>
      _$MessagingChannelFromJson(json);

  get isEnhanced => platformType == 'Enhanced';
}