// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'salesforce_environment.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SalesforceEnvironment _$SalesforceEnvironmentFromJson(
    Map<String, dynamic> json) {
  return _SalesforceEnvironment.fromJson(json);
}

/// @nodoc
mixin _$SalesforceEnvironment {
  @enumerated
  SalesforceEnvironmentType get type => throw _privateConstructorUsedError;
  String? get customDomainBase => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SalesforceEnvironmentCopyWith<SalesforceEnvironment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SalesforceEnvironmentCopyWith<$Res> {
  factory $SalesforceEnvironmentCopyWith(SalesforceEnvironment value,
          $Res Function(SalesforceEnvironment) then) =
      _$SalesforceEnvironmentCopyWithImpl<$Res, SalesforceEnvironment>;
  @useResult
  $Res call(
      {@enumerated SalesforceEnvironmentType type, String? customDomainBase});
}

/// @nodoc
class _$SalesforceEnvironmentCopyWithImpl<$Res,
        $Val extends SalesforceEnvironment>
    implements $SalesforceEnvironmentCopyWith<$Res> {
  _$SalesforceEnvironmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? customDomainBase = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as SalesforceEnvironmentType,
      customDomainBase: freezed == customDomainBase
          ? _value.customDomainBase
          : customDomainBase // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SalesforceEnvironmentImplCopyWith<$Res>
    implements $SalesforceEnvironmentCopyWith<$Res> {
  factory _$$SalesforceEnvironmentImplCopyWith(
          _$SalesforceEnvironmentImpl value,
          $Res Function(_$SalesforceEnvironmentImpl) then) =
      __$$SalesforceEnvironmentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@enumerated SalesforceEnvironmentType type, String? customDomainBase});
}

/// @nodoc
class __$$SalesforceEnvironmentImplCopyWithImpl<$Res>
    extends _$SalesforceEnvironmentCopyWithImpl<$Res,
        _$SalesforceEnvironmentImpl>
    implements _$$SalesforceEnvironmentImplCopyWith<$Res> {
  __$$SalesforceEnvironmentImplCopyWithImpl(_$SalesforceEnvironmentImpl _value,
      $Res Function(_$SalesforceEnvironmentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? customDomainBase = freezed,
  }) {
    return _then(_$SalesforceEnvironmentImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as SalesforceEnvironmentType,
      customDomainBase: freezed == customDomainBase
          ? _value.customDomainBase
          : customDomainBase // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SalesforceEnvironmentImpl extends _SalesforceEnvironment {
  const _$SalesforceEnvironmentImpl(
      {@enumerated this.type = SalesforceEnvironmentType.production,
      this.customDomainBase})
      : super._();

  factory _$SalesforceEnvironmentImpl.fromJson(Map<String, dynamic> json) =>
      _$$SalesforceEnvironmentImplFromJson(json);

  @override
  @JsonKey()
  @enumerated
  final SalesforceEnvironmentType type;
  @override
  final String? customDomainBase;

  @override
  String toString() {
    return 'SalesforceEnvironment(type: $type, customDomainBase: $customDomainBase)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SalesforceEnvironmentImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.customDomainBase, customDomainBase) ||
                other.customDomainBase == customDomainBase));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, customDomainBase);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SalesforceEnvironmentImplCopyWith<_$SalesforceEnvironmentImpl>
      get copyWith => __$$SalesforceEnvironmentImplCopyWithImpl<
          _$SalesforceEnvironmentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SalesforceEnvironmentImplToJson(
      this,
    );
  }
}

abstract class _SalesforceEnvironment extends SalesforceEnvironment {
  const factory _SalesforceEnvironment(
      {@enumerated final SalesforceEnvironmentType type,
      final String? customDomainBase}) = _$SalesforceEnvironmentImpl;
  const _SalesforceEnvironment._() : super._();

  factory _SalesforceEnvironment.fromJson(Map<String, dynamic> json) =
      _$SalesforceEnvironmentImpl.fromJson;

  @override
  @enumerated
  SalesforceEnvironmentType get type;
  @override
  String? get customDomainBase;
  @override
  @JsonKey(ignore: true)
  _$$SalesforceEnvironmentImplCopyWith<_$SalesforceEnvironmentImpl>
      get copyWith => throw _privateConstructorUsedError;
}
