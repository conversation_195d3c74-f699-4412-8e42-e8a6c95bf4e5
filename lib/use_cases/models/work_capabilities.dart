import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/work_response.dart';

part 'work_capabilities.freezed.dart';
part 'work_capabilities.g.dart';

@freezed
class WorkCapabilities with _$WorkCapabilities {
  const WorkCapabilities._();

  const factory WorkCapabilities({
    /// NOTE: removing this as it had issues*; shim service was just checking whether the conversation was enhanced (whether it had a 'ConversationIdentifier') [CRM, 10/3/2024]
    /// * issues if the work was available b/c of WorksOpened (upon login) or hard close & re-open
    // @Default(false) bool canTransfer,
    @Default(false) canRaiseFlag,
  }) = _WorkCapabilities;

  factory WorkCapabilities.fromJson(Map<String, dynamic> json) => _$WorkCapabilitiesFromJson(json);

  static WorkCapabilities fromWorkResponse(WorkResponse response) => WorkCapabilities(
    // canTransfer: response.canTransfer,
    canRaiseFlag: response.canRaiseFlag,
  );
}