import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'salesforce_environment.freezed.dart';
part 'salesforce_environment.g.dart';

@freezed
@embeddedOnFreezed
class SalesforceEnvironment with _$SalesforceEnvironment {
  const SalesforceEnvironment._();

  @ignore
  bool get isProduction => type == SalesforceEnvironmentType.production;
  @ignore
  bool get isSandbox => type == SalesforceEnvironmentType.sandbox;
  @ignore
  bool get isCustom => type == SalesforceEnvironmentType.custom;
  @ignore
  bool get isDemo => type == SalesforceEnvironmentType.demo;

  @ignore
  String get domain => "https://${customDomainBase ?? ''}.my.salesforce.com";

  const factory SalesforceEnvironment({
    @enumerated
    @Default(SalesforceEnvironmentType.production) SalesforceEnvironmentType type,
    String? customDomainBase
  }) = _SalesforceEnvironment;

  factory SalesforceEnvironment.fromJson(Map<String, dynamic> json) => _$SalesforceEnvironmentFromJson(json);
}