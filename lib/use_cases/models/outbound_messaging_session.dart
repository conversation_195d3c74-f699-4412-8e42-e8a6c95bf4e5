import 'package:freezed_annotation/freezed_annotation.dart';

part 'outbound_messaging_session.freezed.dart';
part 'outbound_messaging_session.g.dart';
@freezed
class OutboundMessagingSession with _$OutboundMessagingSession {
  const factory OutboundMessagingSession({
    required String messagingEndUserId,
    required String messagingChannelId
  }) = _OutboundMessagingSession;

  factory OutboundMessagingSession.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessagingSessionFromJson(json);
}