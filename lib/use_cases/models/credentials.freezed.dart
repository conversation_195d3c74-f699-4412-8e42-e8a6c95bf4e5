// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credentials.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Credentials _$CredentialsFromJson(Map<String, dynamic> json) {
  return _Credentials.fromJson(json);
}

/// @nodoc
mixin _$Credentials {
  String? get userId => throw _privateConstructorUsedError;
  String? get instanceUrl => throw _privateConstructorUsedError;
  String? get accessToken =>
      throw _privateConstructorUsedError; // This is the SalesForce access token
  String? get orgId => throw _privateConstructorUsedError;
  String? get authorizationToken =>
      throw _privateConstructorUsedError; //This is the Shim Service access token
  String? get webSocketUrl => throw _privateConstructorUsedError;
  String? get sessionToken => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  String? get refreshToken => throw _privateConstructorUsedError;
  String? get devicePushToken => throw _privateConstructorUsedError;
  int? get authorizationTokenExpirationTime =>
      throw _privateConstructorUsedError;
  int? get sessionExpirationTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CredentialsCopyWith<Credentials> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CredentialsCopyWith<$Res> {
  factory $CredentialsCopyWith(
          Credentials value, $Res Function(Credentials) then) =
      _$CredentialsCopyWithImpl<$Res, Credentials>;
  @useResult
  $Res call(
      {String? userId,
      String? instanceUrl,
      String? accessToken,
      String? orgId,
      String? authorizationToken,
      String? webSocketUrl,
      String? sessionToken,
      String? sessionId,
      String? refreshToken,
      String? devicePushToken,
      int? authorizationTokenExpirationTime,
      int? sessionExpirationTime});
}

/// @nodoc
class _$CredentialsCopyWithImpl<$Res, $Val extends Credentials>
    implements $CredentialsCopyWith<$Res> {
  _$CredentialsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? instanceUrl = freezed,
    Object? accessToken = freezed,
    Object? orgId = freezed,
    Object? authorizationToken = freezed,
    Object? webSocketUrl = freezed,
    Object? sessionToken = freezed,
    Object? sessionId = freezed,
    Object? refreshToken = freezed,
    Object? devicePushToken = freezed,
    Object? authorizationTokenExpirationTime = freezed,
    Object? sessionExpirationTime = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _value.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationToken: freezed == authorizationToken
          ? _value.authorizationToken
          : authorizationToken // ignore: cast_nullable_to_non_nullable
              as String?,
      webSocketUrl: freezed == webSocketUrl
          ? _value.webSocketUrl
          : webSocketUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionToken: freezed == sessionToken
          ? _value.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      devicePushToken: freezed == devicePushToken
          ? _value.devicePushToken
          : devicePushToken // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationTokenExpirationTime: freezed ==
              authorizationTokenExpirationTime
          ? _value.authorizationTokenExpirationTime
          : authorizationTokenExpirationTime // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionExpirationTime: freezed == sessionExpirationTime
          ? _value.sessionExpirationTime
          : sessionExpirationTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CredentialsImplCopyWith<$Res>
    implements $CredentialsCopyWith<$Res> {
  factory _$$CredentialsImplCopyWith(
          _$CredentialsImpl value, $Res Function(_$CredentialsImpl) then) =
      __$$CredentialsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? userId,
      String? instanceUrl,
      String? accessToken,
      String? orgId,
      String? authorizationToken,
      String? webSocketUrl,
      String? sessionToken,
      String? sessionId,
      String? refreshToken,
      String? devicePushToken,
      int? authorizationTokenExpirationTime,
      int? sessionExpirationTime});
}

/// @nodoc
class __$$CredentialsImplCopyWithImpl<$Res>
    extends _$CredentialsCopyWithImpl<$Res, _$CredentialsImpl>
    implements _$$CredentialsImplCopyWith<$Res> {
  __$$CredentialsImplCopyWithImpl(
      _$CredentialsImpl _value, $Res Function(_$CredentialsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? instanceUrl = freezed,
    Object? accessToken = freezed,
    Object? orgId = freezed,
    Object? authorizationToken = freezed,
    Object? webSocketUrl = freezed,
    Object? sessionToken = freezed,
    Object? sessionId = freezed,
    Object? refreshToken = freezed,
    Object? devicePushToken = freezed,
    Object? authorizationTokenExpirationTime = freezed,
    Object? sessionExpirationTime = freezed,
  }) {
    return _then(_$CredentialsImpl(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _value.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationToken: freezed == authorizationToken
          ? _value.authorizationToken
          : authorizationToken // ignore: cast_nullable_to_non_nullable
              as String?,
      webSocketUrl: freezed == webSocketUrl
          ? _value.webSocketUrl
          : webSocketUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionToken: freezed == sessionToken
          ? _value.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      devicePushToken: freezed == devicePushToken
          ? _value.devicePushToken
          : devicePushToken // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizationTokenExpirationTime: freezed ==
              authorizationTokenExpirationTime
          ? _value.authorizationTokenExpirationTime
          : authorizationTokenExpirationTime // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionExpirationTime: freezed == sessionExpirationTime
          ? _value.sessionExpirationTime
          : sessionExpirationTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CredentialsImpl extends _Credentials {
  const _$CredentialsImpl(
      {this.userId,
      this.instanceUrl,
      this.accessToken,
      this.orgId,
      this.authorizationToken,
      this.webSocketUrl,
      this.sessionToken,
      this.sessionId,
      this.refreshToken,
      this.devicePushToken,
      this.authorizationTokenExpirationTime,
      this.sessionExpirationTime})
      : super._();

  factory _$CredentialsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CredentialsImplFromJson(json);

  @override
  final String? userId;
  @override
  final String? instanceUrl;
  @override
  final String? accessToken;
// This is the SalesForce access token
  @override
  final String? orgId;
  @override
  final String? authorizationToken;
//This is the Shim Service access token
  @override
  final String? webSocketUrl;
  @override
  final String? sessionToken;
  @override
  final String? sessionId;
  @override
  final String? refreshToken;
  @override
  final String? devicePushToken;
  @override
  final int? authorizationTokenExpirationTime;
  @override
  final int? sessionExpirationTime;

  @override
  String toString() {
    return 'Credentials(userId: $userId, instanceUrl: $instanceUrl, accessToken: $accessToken, orgId: $orgId, authorizationToken: $authorizationToken, webSocketUrl: $webSocketUrl, sessionToken: $sessionToken, sessionId: $sessionId, refreshToken: $refreshToken, devicePushToken: $devicePushToken, authorizationTokenExpirationTime: $authorizationTokenExpirationTime, sessionExpirationTime: $sessionExpirationTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CredentialsImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.authorizationToken, authorizationToken) ||
                other.authorizationToken == authorizationToken) &&
            (identical(other.webSocketUrl, webSocketUrl) ||
                other.webSocketUrl == webSocketUrl) &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.devicePushToken, devicePushToken) ||
                other.devicePushToken == devicePushToken) &&
            (identical(other.authorizationTokenExpirationTime,
                    authorizationTokenExpirationTime) ||
                other.authorizationTokenExpirationTime ==
                    authorizationTokenExpirationTime) &&
            (identical(other.sessionExpirationTime, sessionExpirationTime) ||
                other.sessionExpirationTime == sessionExpirationTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      instanceUrl,
      accessToken,
      orgId,
      authorizationToken,
      webSocketUrl,
      sessionToken,
      sessionId,
      refreshToken,
      devicePushToken,
      authorizationTokenExpirationTime,
      sessionExpirationTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CredentialsImplCopyWith<_$CredentialsImpl> get copyWith =>
      __$$CredentialsImplCopyWithImpl<_$CredentialsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CredentialsImplToJson(
      this,
    );
  }
}

abstract class _Credentials extends Credentials {
  const factory _Credentials(
      {final String? userId,
      final String? instanceUrl,
      final String? accessToken,
      final String? orgId,
      final String? authorizationToken,
      final String? webSocketUrl,
      final String? sessionToken,
      final String? sessionId,
      final String? refreshToken,
      final String? devicePushToken,
      final int? authorizationTokenExpirationTime,
      final int? sessionExpirationTime}) = _$CredentialsImpl;
  const _Credentials._() : super._();

  factory _Credentials.fromJson(Map<String, dynamic> json) =
      _$CredentialsImpl.fromJson;

  @override
  String? get userId;
  @override
  String? get instanceUrl;
  @override
  String? get accessToken;
  @override // This is the SalesForce access token
  String? get orgId;
  @override
  String? get authorizationToken;
  @override //This is the Shim Service access token
  String? get webSocketUrl;
  @override
  String? get sessionToken;
  @override
  String? get sessionId;
  @override
  String? get refreshToken;
  @override
  String? get devicePushToken;
  @override
  int? get authorizationTokenExpirationTime;
  @override
  int? get sessionExpirationTime;
  @override
  @JsonKey(ignore: true)
  _$$CredentialsImplCopyWith<_$CredentialsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
