import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'credentials.freezed.dart';
part 'credentials.g.dart';

@freezed
@collectionOnFreezed
class Credentials with _$Credentials {
  /// Isar ID for indexing — Setting this one to '0' for singleton item in collection; otherwise default use here is (can also use custom indices -- see isar docs):
  final Id id = 0;
  const Credentials._();

  String? get fileInstanceUrl =>
      instanceUrl?.replaceFirst(".my.salesforce.", ".file.force.");
  const factory Credentials({
    String? userId,
    String? instanceUrl,
    String? accessToken, // This is the SalesForce access token
    String? orgId,
    String? authorizationToken, //This is the Shim Service access token
    String? webSocketUrl,
    String? sessionToken,
    String? sessionId,
    String? refreshToken,
    String? devicePushToken,
    int? authorizationTokenExpirationTime,
    int? sessionExpirationTime,
  }) = _Credentials;

  factory Credentials.fromJson(Map<String, dynamic> json) =>
      _$CredentialsFromJson(json);
}

extension IsLoggedInExtension on Credentials {
  bool get isLoggedIn =>
      instanceUrl != null &&
      accessToken != null &&
      authorizationToken != null &&
      refreshToken != null
      && sessionToken != null;

  bool get sfLoggedIn =>
      instanceUrl != null &&
      accessToken != null &&
      orgId != null &&
      userId != null &&
      refreshToken != null;
}
