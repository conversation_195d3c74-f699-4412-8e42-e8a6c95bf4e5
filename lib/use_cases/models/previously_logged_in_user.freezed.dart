// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'previously_logged_in_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PreviouslyLoggedInUser _$PreviouslyLoggedInUserFromJson(
    Map<String, dynamic> json) {
  return _PreviouslyLoggedInUser.fromJson(json);
}

/// @nodoc
mixin _$PreviouslyLoggedInUser {
  String? get userId => throw _privateConstructorUsedError;
  String? get orgId => throw _privateConstructorUsedError;
  String? get presenceId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PreviouslyLoggedInUserCopyWith<PreviouslyLoggedInUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PreviouslyLoggedInUserCopyWith<$Res> {
  factory $PreviouslyLoggedInUserCopyWith(PreviouslyLoggedInUser value,
          $Res Function(PreviouslyLoggedInUser) then) =
      _$PreviouslyLoggedInUserCopyWithImpl<$Res, PreviouslyLoggedInUser>;
  @useResult
  $Res call({String? userId, String? orgId, String? presenceId});
}

/// @nodoc
class _$PreviouslyLoggedInUserCopyWithImpl<$Res,
        $Val extends PreviouslyLoggedInUser>
    implements $PreviouslyLoggedInUserCopyWith<$Res> {
  _$PreviouslyLoggedInUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? orgId = freezed,
    Object? presenceId = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      presenceId: freezed == presenceId
          ? _value.presenceId
          : presenceId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PreviouslyLoggedInUserImplCopyWith<$Res>
    implements $PreviouslyLoggedInUserCopyWith<$Res> {
  factory _$$PreviouslyLoggedInUserImplCopyWith(
          _$PreviouslyLoggedInUserImpl value,
          $Res Function(_$PreviouslyLoggedInUserImpl) then) =
      __$$PreviouslyLoggedInUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? userId, String? orgId, String? presenceId});
}

/// @nodoc
class __$$PreviouslyLoggedInUserImplCopyWithImpl<$Res>
    extends _$PreviouslyLoggedInUserCopyWithImpl<$Res,
        _$PreviouslyLoggedInUserImpl>
    implements _$$PreviouslyLoggedInUserImplCopyWith<$Res> {
  __$$PreviouslyLoggedInUserImplCopyWithImpl(
      _$PreviouslyLoggedInUserImpl _value,
      $Res Function(_$PreviouslyLoggedInUserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? orgId = freezed,
    Object? presenceId = freezed,
  }) {
    return _then(_$PreviouslyLoggedInUserImpl(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      presenceId: freezed == presenceId
          ? _value.presenceId
          : presenceId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PreviouslyLoggedInUserImpl extends _PreviouslyLoggedInUser {
  const _$PreviouslyLoggedInUserImpl({this.userId, this.orgId, this.presenceId})
      : super._();

  factory _$PreviouslyLoggedInUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$PreviouslyLoggedInUserImplFromJson(json);

  @override
  final String? userId;
  @override
  final String? orgId;
  @override
  final String? presenceId;

  @override
  String toString() {
    return 'PreviouslyLoggedInUser(userId: $userId, orgId: $orgId, presenceId: $presenceId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PreviouslyLoggedInUserImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.presenceId, presenceId) ||
                other.presenceId == presenceId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userId, orgId, presenceId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PreviouslyLoggedInUserImplCopyWith<_$PreviouslyLoggedInUserImpl>
      get copyWith => __$$PreviouslyLoggedInUserImplCopyWithImpl<
          _$PreviouslyLoggedInUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PreviouslyLoggedInUserImplToJson(
      this,
    );
  }
}

abstract class _PreviouslyLoggedInUser extends PreviouslyLoggedInUser {
  const factory _PreviouslyLoggedInUser(
      {final String? userId,
      final String? orgId,
      final String? presenceId}) = _$PreviouslyLoggedInUserImpl;
  const _PreviouslyLoggedInUser._() : super._();

  factory _PreviouslyLoggedInUser.fromJson(Map<String, dynamic> json) =
      _$PreviouslyLoggedInUserImpl.fromJson;

  @override
  String? get userId;
  @override
  String? get orgId;
  @override
  String? get presenceId;
  @override
  @JsonKey(ignore: true)
  _$$PreviouslyLoggedInUserImplCopyWith<_$PreviouslyLoggedInUserImpl>
      get copyWith => throw _privateConstructorUsedError;
}
