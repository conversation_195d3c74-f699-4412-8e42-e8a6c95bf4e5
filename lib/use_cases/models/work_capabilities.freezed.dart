// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_capabilities.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkCapabilities _$WorkCapabilitiesFromJson(Map<String, dynamic> json) {
  return _WorkCapabilities.fromJson(json);
}

/// @nodoc
mixin _$WorkCapabilities {
  /// NOTE: removing this as it had issues*; shim service was just checking whether the conversation was enhanced (whether it had a 'ConversationIdentifier') [CRM, 10/3/2024]
  /// * issues if the work was available b/c of WorksOpened (upon login) or hard close & re-open
// @Default(false) bool canTransfer,
  dynamic get canRaiseFlag => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WorkCapabilitiesCopyWith<WorkCapabilities> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkCapabilitiesCopyWith<$Res> {
  factory $WorkCapabilitiesCopyWith(
          WorkCapabilities value, $Res Function(WorkCapabilities) then) =
      _$WorkCapabilitiesCopyWithImpl<$Res, WorkCapabilities>;
  @useResult
  $Res call({dynamic canRaiseFlag});
}

/// @nodoc
class _$WorkCapabilitiesCopyWithImpl<$Res, $Val extends WorkCapabilities>
    implements $WorkCapabilitiesCopyWith<$Res> {
  _$WorkCapabilitiesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? canRaiseFlag = freezed,
  }) {
    return _then(_value.copyWith(
      canRaiseFlag: freezed == canRaiseFlag
          ? _value.canRaiseFlag
          : canRaiseFlag // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkCapabilitiesImplCopyWith<$Res>
    implements $WorkCapabilitiesCopyWith<$Res> {
  factory _$$WorkCapabilitiesImplCopyWith(_$WorkCapabilitiesImpl value,
          $Res Function(_$WorkCapabilitiesImpl) then) =
      __$$WorkCapabilitiesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({dynamic canRaiseFlag});
}

/// @nodoc
class __$$WorkCapabilitiesImplCopyWithImpl<$Res>
    extends _$WorkCapabilitiesCopyWithImpl<$Res, _$WorkCapabilitiesImpl>
    implements _$$WorkCapabilitiesImplCopyWith<$Res> {
  __$$WorkCapabilitiesImplCopyWithImpl(_$WorkCapabilitiesImpl _value,
      $Res Function(_$WorkCapabilitiesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? canRaiseFlag = freezed,
  }) {
    return _then(_$WorkCapabilitiesImpl(
      canRaiseFlag:
          freezed == canRaiseFlag ? _value.canRaiseFlag! : canRaiseFlag,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkCapabilitiesImpl extends _WorkCapabilities {
  const _$WorkCapabilitiesImpl({this.canRaiseFlag = false}) : super._();

  factory _$WorkCapabilitiesImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkCapabilitiesImplFromJson(json);

  /// NOTE: removing this as it had issues*; shim service was just checking whether the conversation was enhanced (whether it had a 'ConversationIdentifier') [CRM, 10/3/2024]
  /// * issues if the work was available b/c of WorksOpened (upon login) or hard close & re-open
// @Default(false) bool canTransfer,
  @override
  @JsonKey()
  final dynamic canRaiseFlag;

  @override
  String toString() {
    return 'WorkCapabilities(canRaiseFlag: $canRaiseFlag)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkCapabilitiesImpl &&
            const DeepCollectionEquality()
                .equals(other.canRaiseFlag, canRaiseFlag));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(canRaiseFlag));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkCapabilitiesImplCopyWith<_$WorkCapabilitiesImpl> get copyWith =>
      __$$WorkCapabilitiesImplCopyWithImpl<_$WorkCapabilitiesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkCapabilitiesImplToJson(
      this,
    );
  }
}

abstract class _WorkCapabilities extends WorkCapabilities {
  const factory _WorkCapabilities({final dynamic canRaiseFlag}) =
      _$WorkCapabilitiesImpl;
  const _WorkCapabilities._() : super._();

  factory _WorkCapabilities.fromJson(Map<String, dynamic> json) =
      _$WorkCapabilitiesImpl.fromJson;

  @override

  /// NOTE: removing this as it had issues*; shim service was just checking whether the conversation was enhanced (whether it had a 'ConversationIdentifier') [CRM, 10/3/2024]
  /// * issues if the work was available b/c of WorksOpened (upon login) or hard close & re-open
// @Default(false) bool canTransfer,
  dynamic get canRaiseFlag;
  @override
  @JsonKey(ignore: true)
  _$$WorkCapabilitiesImplCopyWith<_$WorkCapabilitiesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
