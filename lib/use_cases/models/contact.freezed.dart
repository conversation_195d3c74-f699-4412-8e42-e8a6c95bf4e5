// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Contact _$ContactFromJson(Map<String, dynamic> json) {
  return _Contact.fromJson(json);
}

/// @nodoc
mixin _$Contact {
  @JsonKey(name: 'Id')
  SfId? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'FirstName')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'LastName')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'Title')
  String? get title => throw _privateConstructorUsedError;
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone => throw _privateConstructorUsedError;
  @JsonKey(name: 'Email')
  String? get email => throw _privateConstructorUsedError;

  /// fields to support Lak Participant model
  @JsonKey(name: 'ConsumerId')
  SfId? get consumerId => throw _privateConstructorUsedError;
  @JsonKey(name: 'UserDetails')
  Map<String, dynamic>? get userDetails => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContactCopyWith<Contact> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactCopyWith<$Res> {
  factory $ContactCopyWith(Contact value, $Res Function(Contact) then) =
      _$ContactCopyWithImpl<$Res, Contact>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') SfId? id,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'ConsumerId') SfId? consumerId,
      @JsonKey(name: 'UserDetails') Map<String, dynamic>? userDetails});

  $SfIdCopyWith<$Res>? get id;
  $SfIdCopyWith<$Res>? get consumerId;
}

/// @nodoc
class _$ContactCopyWithImpl<$Res, $Val extends Contact>
    implements $ContactCopyWith<$Res> {
  _$ContactCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? title = freezed,
    Object? photoUrl = freezed,
    Object? mobilePhone = freezed,
    Object? email = freezed,
    Object? consumerId = freezed,
    Object? userDetails = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      consumerId: freezed == consumerId
          ? _value.consumerId
          : consumerId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      userDetails: freezed == userDetails
          ? _value.userDetails
          : userDetails // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get id {
    if (_value.id == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.id!, (value) {
      return _then(_value.copyWith(id: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get consumerId {
    if (_value.consumerId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.consumerId!, (value) {
      return _then(_value.copyWith(consumerId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ContactImplCopyWith<$Res> implements $ContactCopyWith<$Res> {
  factory _$$ContactImplCopyWith(
          _$ContactImpl value, $Res Function(_$ContactImpl) then) =
      __$$ContactImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') SfId? id,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'ConsumerId') SfId? consumerId,
      @JsonKey(name: 'UserDetails') Map<String, dynamic>? userDetails});

  @override
  $SfIdCopyWith<$Res>? get id;
  @override
  $SfIdCopyWith<$Res>? get consumerId;
}

/// @nodoc
class __$$ContactImplCopyWithImpl<$Res>
    extends _$ContactCopyWithImpl<$Res, _$ContactImpl>
    implements _$$ContactImplCopyWith<$Res> {
  __$$ContactImplCopyWithImpl(
      _$ContactImpl _value, $Res Function(_$ContactImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? title = freezed,
    Object? photoUrl = freezed,
    Object? mobilePhone = freezed,
    Object? email = freezed,
    Object? consumerId = freezed,
    Object? userDetails = freezed,
  }) {
    return _then(_$ContactImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      consumerId: freezed == consumerId
          ? _value.consumerId
          : consumerId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      userDetails: freezed == userDetails
          ? _value._userDetails
          : userDetails // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContactImpl extends _Contact {
  const _$ContactImpl(
      {@JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'FirstName') this.firstName,
      @JsonKey(name: 'LastName') this.lastName,
      @JsonKey(name: 'Title') this.title,
      @JsonKey(name: 'PhotoUrl') this.photoUrl,
      @JsonKey(name: 'MobilePhone') this.mobilePhone,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'ConsumerId') this.consumerId,
      @JsonKey(name: 'UserDetails') final Map<String, dynamic>? userDetails})
      : _userDetails = userDetails,
        super._();

  factory _$ContactImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContactImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final SfId? id;
  @override
  @JsonKey(name: 'FirstName')
  final String? firstName;
  @override
  @JsonKey(name: 'LastName')
  final String? lastName;
  @override
  @JsonKey(name: 'Title')
  final String? title;
  @override
  @JsonKey(name: 'PhotoUrl')
  final String? photoUrl;
  @override
  @JsonKey(name: 'MobilePhone')
  final String? mobilePhone;
  @override
  @JsonKey(name: 'Email')
  final String? email;

  /// fields to support Lak Participant model
  @override
  @JsonKey(name: 'ConsumerId')
  final SfId? consumerId;
  final Map<String, dynamic>? _userDetails;
  @override
  @JsonKey(name: 'UserDetails')
  Map<String, dynamic>? get userDetails {
    final value = _userDetails;
    if (value == null) return null;
    if (_userDetails is EqualUnmodifiableMapView) return _userDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'Contact(id: $id, firstName: $firstName, lastName: $lastName, title: $title, photoUrl: $photoUrl, mobilePhone: $mobilePhone, email: $email, consumerId: $consumerId, userDetails: $userDetails)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.consumerId, consumerId) ||
                other.consumerId == consumerId) &&
            const DeepCollectionEquality()
                .equals(other._userDetails, _userDetails));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      firstName,
      lastName,
      title,
      photoUrl,
      mobilePhone,
      email,
      consumerId,
      const DeepCollectionEquality().hash(_userDetails));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactImplCopyWith<_$ContactImpl> get copyWith =>
      __$$ContactImplCopyWithImpl<_$ContactImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContactImplToJson(
      this,
    );
  }
}

abstract class _Contact extends Contact {
  const factory _Contact(
      {@JsonKey(name: 'Id') final SfId? id,
      @JsonKey(name: 'FirstName') final String? firstName,
      @JsonKey(name: 'LastName') final String? lastName,
      @JsonKey(name: 'Title') final String? title,
      @JsonKey(name: 'PhotoUrl') final String? photoUrl,
      @JsonKey(name: 'MobilePhone') final String? mobilePhone,
      @JsonKey(name: 'Email') final String? email,
      @JsonKey(name: 'ConsumerId') final SfId? consumerId,
      @JsonKey(name: 'UserDetails')
      final Map<String, dynamic>? userDetails}) = _$ContactImpl;
  const _Contact._() : super._();

  factory _Contact.fromJson(Map<String, dynamic> json) = _$ContactImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  SfId? get id;
  @override
  @JsonKey(name: 'FirstName')
  String? get firstName;
  @override
  @JsonKey(name: 'LastName')
  String? get lastName;
  @override
  @JsonKey(name: 'Title')
  String? get title;
  @override
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl;
  @override
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone;
  @override
  @JsonKey(name: 'Email')
  String? get email;
  @override

  /// fields to support Lak Participant model
  @JsonKey(name: 'ConsumerId')
  SfId? get consumerId;
  @override
  @JsonKey(name: 'UserDetails')
  Map<String, dynamic>? get userDetails;
  @override
  @JsonKey(ignore: true)
  _$$ContactImplCopyWith<_$ContactImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
