// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_channel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingChannelImpl _$$MessagingChannelImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingChannelImpl(
      id: const ParseSfIdConverter().fromJson(json['Id']),
      messageType: json['MessageType'] as String?,
      developerName: json['DeveloperName'] as String?,
      masterLabel: json['MasterLabel'] as String?,
      messagingPlatformKey: json['MessagingPlatformKey'] as String?,
      platformType: json['PlatformType'] as String?,
      language: json['Language'] as String?,
      isoCountryCode: json['IsoCountryCode'] as String?,
      isActive: json['IsActive'] as bool?,
      isDeleted: json['isDeleted'] as bool?,
    );

Map<String, dynamic> _$$MessagingChannelImplToJson(
        _$MessagingChannelImpl instance) =>
    <String, dynamic>{
      'Id': const ParseSfIdConverter().toJson(instance.id),
      'MessageType': instance.messageType,
      'DeveloperName': instance.developerName,
      'MasterLabel': instance.masterLabel,
      'MessagingPlatformKey': instance.messagingPlatformKey,
      'PlatformType': instance.platformType,
      'Language': instance.language,
      'IsoCountryCode': instance.isoCountryCode,
      'IsActive': instance.isActive,
      'isDeleted': instance.isDeleted,
    };
