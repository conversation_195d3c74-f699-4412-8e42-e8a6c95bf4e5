// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingEndUser _$MessagingEndUserFromJson(Map<String, dynamic> json) {
  return _MessagingEndUser.fromJson(json);
}

/// @nodoc
mixin _$MessagingEndUser {
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  SfId get id =>
      throw _privateConstructorUsedError; // @JsonKey(name: 'AccountId') String?  accountId,
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId? get contactId =>
      throw _privateConstructorUsedError; // @JsonKey(name: 'HasInitialResponseSent') bool?  hasInitialResponseSent,
// @JsonKey(name: 'IsFullyOptedIn') bool?  isFullyOptedIn,
// @JsonKey(name: 'IsOptedOut') bool?  isOptedOut,
// @JsonKey(name: 'IsoCountryCode') String?  isoCountryCode,
// @JsonKey(name: 'MessageType') String?  messageType,
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  SfId? get messagingChannelId =>
      throw _privateConstructorUsedError; // @JsonKey(name: 'MessagingConsentStatus') String?  messagingConsentStatus,
  @JsonKey(name: 'MessagingPlatformKey')
  String? get messagingPlatformKey => throw _privateConstructorUsedError;
  @JsonKey(name: 'Name')
  String? get name =>
      throw _privateConstructorUsedError; // @JsonKey(name: 'OwnerId') String?  ownerId,
  @JsonKey(name: 'ProfilePictureUrl')
  String? get profilePictureUrl => throw _privateConstructorUsedError;

  /// items below are from the 'contact' object but were previously combined with this as such, these are added to support legacy code
  @Deprecated('legacy')
  String? get contactPictureUrl => throw _privateConstructorUsedError;
  @Deprecated('legacy')
  String? get contactEmail => throw _privateConstructorUsedError;
  @Deprecated('legacy')
  String? get contactName => throw _privateConstructorUsedError;
  @Deprecated('legacy')
  String? get contactMobilePhone => throw _privateConstructorUsedError;
  @Deprecated('legacy')
  String? get userFullPhotoUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingEndUserCopyWith<MessagingEndUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingEndUserCopyWith<$Res> {
  factory $MessagingEndUserCopyWith(
          MessagingEndUser value, $Res Function(MessagingEndUser) then) =
      _$MessagingEndUserCopyWithImpl<$Res, MessagingEndUser>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
      @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId? contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      SfId? messagingChannelId,
      @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'ProfilePictureUrl') String? profilePictureUrl,
      @Deprecated('legacy') String? contactPictureUrl,
      @Deprecated('legacy') String? contactEmail,
      @Deprecated('legacy') String? contactName,
      @Deprecated('legacy') String? contactMobilePhone,
      @Deprecated('legacy') String? userFullPhotoUrl});

  $SfIdCopyWith<$Res> get id;
  $SfIdCopyWith<$Res>? get contactId;
  $SfIdCopyWith<$Res>? get messagingChannelId;
}

/// @nodoc
class _$MessagingEndUserCopyWithImpl<$Res, $Val extends MessagingEndUser>
    implements $MessagingEndUserCopyWith<$Res> {
  _$MessagingEndUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? contactId = freezed,
    Object? messagingChannelId = freezed,
    Object? messagingPlatformKey = freezed,
    Object? name = freezed,
    Object? profilePictureUrl = freezed,
    Object? contactPictureUrl = freezed,
    Object? contactEmail = freezed,
    Object? contactName = freezed,
    Object? contactMobilePhone = freezed,
    Object? userFullPhotoUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      contactId: freezed == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingChannelId: freezed == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingPlatformKey: freezed == messagingPlatformKey
          ? _value.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactPictureUrl: freezed == contactPictureUrl
          ? _value.contactPictureUrl
          : contactPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactEmail: freezed == contactEmail
          ? _value.contactEmail
          : contactEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      contactName: freezed == contactName
          ? _value.contactName
          : contactName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactMobilePhone: freezed == contactMobilePhone
          ? _value.contactMobilePhone
          : contactMobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      userFullPhotoUrl: freezed == userFullPhotoUrl
          ? _value.userFullPhotoUrl
          : userFullPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_value.id, (value) {
      return _then(_value.copyWith(id: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get contactId {
    if (_value.contactId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.contactId!, (value) {
      return _then(_value.copyWith(contactId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingChannelId {
    if (_value.messagingChannelId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.messagingChannelId!, (value) {
      return _then(_value.copyWith(messagingChannelId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingEndUserImplCopyWith<$Res>
    implements $MessagingEndUserCopyWith<$Res> {
  factory _$$MessagingEndUserImplCopyWith(_$MessagingEndUserImpl value,
          $Res Function(_$MessagingEndUserImpl) then) =
      __$$MessagingEndUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() SfId id,
      @JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId? contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      SfId? messagingChannelId,
      @JsonKey(name: 'MessagingPlatformKey') String? messagingPlatformKey,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'ProfilePictureUrl') String? profilePictureUrl,
      @Deprecated('legacy') String? contactPictureUrl,
      @Deprecated('legacy') String? contactEmail,
      @Deprecated('legacy') String? contactName,
      @Deprecated('legacy') String? contactMobilePhone,
      @Deprecated('legacy') String? userFullPhotoUrl});

  @override
  $SfIdCopyWith<$Res> get id;
  @override
  $SfIdCopyWith<$Res>? get contactId;
  @override
  $SfIdCopyWith<$Res>? get messagingChannelId;
}

/// @nodoc
class __$$MessagingEndUserImplCopyWithImpl<$Res>
    extends _$MessagingEndUserCopyWithImpl<$Res, _$MessagingEndUserImpl>
    implements _$$MessagingEndUserImplCopyWith<$Res> {
  __$$MessagingEndUserImplCopyWithImpl(_$MessagingEndUserImpl _value,
      $Res Function(_$MessagingEndUserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? contactId = freezed,
    Object? messagingChannelId = freezed,
    Object? messagingPlatformKey = freezed,
    Object? name = freezed,
    Object? profilePictureUrl = freezed,
    Object? contactPictureUrl = freezed,
    Object? contactEmail = freezed,
    Object? contactName = freezed,
    Object? contactMobilePhone = freezed,
    Object? userFullPhotoUrl = freezed,
  }) {
    return _then(_$MessagingEndUserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      contactId: freezed == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingChannelId: freezed == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingPlatformKey: freezed == messagingPlatformKey
          ? _value.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactPictureUrl: freezed == contactPictureUrl
          ? _value.contactPictureUrl
          : contactPictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contactEmail: freezed == contactEmail
          ? _value.contactEmail
          : contactEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      contactName: freezed == contactName
          ? _value.contactName
          : contactName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactMobilePhone: freezed == contactMobilePhone
          ? _value.contactMobilePhone
          : contactMobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      userFullPhotoUrl: freezed == userFullPhotoUrl
          ? _value.userFullPhotoUrl
          : userFullPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingEndUserImpl extends _MessagingEndUser {
  const _$MessagingEndUserImpl(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() required this.id,
      @JsonKey(name: 'ContactId') @ParseSfIdConverter() this.contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      this.messagingChannelId,
      @JsonKey(name: 'MessagingPlatformKey') this.messagingPlatformKey,
      @JsonKey(name: 'Name') this.name,
      @JsonKey(name: 'ProfilePictureUrl') this.profilePictureUrl,
      @Deprecated('legacy') this.contactPictureUrl,
      @Deprecated('legacy') this.contactEmail,
      @Deprecated('legacy') this.contactName,
      @Deprecated('legacy') this.contactMobilePhone,
      @Deprecated('legacy') this.userFullPhotoUrl})
      : super._();

  factory _$MessagingEndUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingEndUserImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  final SfId id;
// @JsonKey(name: 'AccountId') String?  accountId,
  @override
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  final SfId? contactId;
// @JsonKey(name: 'HasInitialResponseSent') bool?  hasInitialResponseSent,
// @JsonKey(name: 'IsFullyOptedIn') bool?  isFullyOptedIn,
// @JsonKey(name: 'IsOptedOut') bool?  isOptedOut,
// @JsonKey(name: 'IsoCountryCode') String?  isoCountryCode,
// @JsonKey(name: 'MessageType') String?  messageType,
  @override
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  final SfId? messagingChannelId;
// @JsonKey(name: 'MessagingConsentStatus') String?  messagingConsentStatus,
  @override
  @JsonKey(name: 'MessagingPlatformKey')
  final String? messagingPlatformKey;
  @override
  @JsonKey(name: 'Name')
  final String? name;
// @JsonKey(name: 'OwnerId') String?  ownerId,
  @override
  @JsonKey(name: 'ProfilePictureUrl')
  final String? profilePictureUrl;

  /// items below are from the 'contact' object but were previously combined with this as such, these are added to support legacy code
  @override
  @Deprecated('legacy')
  final String? contactPictureUrl;
  @override
  @Deprecated('legacy')
  final String? contactEmail;
  @override
  @Deprecated('legacy')
  final String? contactName;
  @override
  @Deprecated('legacy')
  final String? contactMobilePhone;
  @override
  @Deprecated('legacy')
  final String? userFullPhotoUrl;

  @override
  String toString() {
    return 'MessagingEndUser(id: $id, contactId: $contactId, messagingChannelId: $messagingChannelId, messagingPlatformKey: $messagingPlatformKey, name: $name, profilePictureUrl: $profilePictureUrl, contactPictureUrl: $contactPictureUrl, contactEmail: $contactEmail, contactName: $contactName, contactMobilePhone: $contactMobilePhone, userFullPhotoUrl: $userFullPhotoUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingEndUserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.contactPictureUrl, contactPictureUrl) ||
                other.contactPictureUrl == contactPictureUrl) &&
            (identical(other.contactEmail, contactEmail) ||
                other.contactEmail == contactEmail) &&
            (identical(other.contactName, contactName) ||
                other.contactName == contactName) &&
            (identical(other.contactMobilePhone, contactMobilePhone) ||
                other.contactMobilePhone == contactMobilePhone) &&
            (identical(other.userFullPhotoUrl, userFullPhotoUrl) ||
                other.userFullPhotoUrl == userFullPhotoUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      contactId,
      messagingChannelId,
      messagingPlatformKey,
      name,
      profilePictureUrl,
      contactPictureUrl,
      contactEmail,
      contactName,
      contactMobilePhone,
      userFullPhotoUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingEndUserImplCopyWith<_$MessagingEndUserImpl> get copyWith =>
      __$$MessagingEndUserImplCopyWithImpl<_$MessagingEndUserImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingEndUserImplToJson(
      this,
    );
  }
}

abstract class _MessagingEndUser extends MessagingEndUser {
  const factory _MessagingEndUser(
      {@JsonKey(name: 'Id') @ParseSfIdConverter() required final SfId id,
      @JsonKey(name: 'ContactId') @ParseSfIdConverter() final SfId? contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      final SfId? messagingChannelId,
      @JsonKey(name: 'MessagingPlatformKey') final String? messagingPlatformKey,
      @JsonKey(name: 'Name') final String? name,
      @JsonKey(name: 'ProfilePictureUrl') final String? profilePictureUrl,
      @Deprecated('legacy') final String? contactPictureUrl,
      @Deprecated('legacy') final String? contactEmail,
      @Deprecated('legacy') final String? contactName,
      @Deprecated('legacy') final String? contactMobilePhone,
      @Deprecated('legacy')
      final String? userFullPhotoUrl}) = _$MessagingEndUserImpl;
  const _MessagingEndUser._() : super._();

  factory _MessagingEndUser.fromJson(Map<String, dynamic> json) =
      _$MessagingEndUserImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  @ParseSfIdConverter()
  SfId get id;
  @override // @JsonKey(name: 'AccountId') String?  accountId,
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId? get contactId;
  @override // @JsonKey(name: 'HasInitialResponseSent') bool?  hasInitialResponseSent,
// @JsonKey(name: 'IsFullyOptedIn') bool?  isFullyOptedIn,
// @JsonKey(name: 'IsOptedOut') bool?  isOptedOut,
// @JsonKey(name: 'IsoCountryCode') String?  isoCountryCode,
// @JsonKey(name: 'MessageType') String?  messageType,
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  SfId? get messagingChannelId;
  @override // @JsonKey(name: 'MessagingConsentStatus') String?  messagingConsentStatus,
  @JsonKey(name: 'MessagingPlatformKey')
  String? get messagingPlatformKey;
  @override
  @JsonKey(name: 'Name')
  String? get name;
  @override // @JsonKey(name: 'OwnerId') String?  ownerId,
  @JsonKey(name: 'ProfilePictureUrl')
  String? get profilePictureUrl;
  @override

  /// items below are from the 'contact' object but were previously combined with this as such, these are added to support legacy code
  @Deprecated('legacy')
  String? get contactPictureUrl;
  @override
  @Deprecated('legacy')
  String? get contactEmail;
  @override
  @Deprecated('legacy')
  String? get contactName;
  @override
  @Deprecated('legacy')
  String? get contactMobilePhone;
  @override
  @Deprecated('legacy')
  String? get userFullPhotoUrl;
  @override
  @JsonKey(ignore: true)
  _$$MessagingEndUserImplCopyWith<_$MessagingEndUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
