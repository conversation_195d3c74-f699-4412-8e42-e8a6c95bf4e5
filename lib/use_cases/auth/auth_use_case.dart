import 'dart:async';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/api/dtos/salesforce_token_body.dart';
import 'package:x1440/api/dtos/salesforce_token_response.dart';
import 'package:x1440/api/salesforce/dtos/oauth_response.dart';
import 'package:x1440/api/salesforce/dtos/revoke_token_body.dart';
import 'package:x1440/frameworks/notifications/notifications_event.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';
import 'package:x1440/services/interfaces/contact_viewmodel_interface.dart';
import 'package:x1440/ui/blocs/org/org_bloc.dart';
import 'package:x1440/ui/blocs/org/org_event.dart';
import 'package:x1440/use_cases/contacts/contacts_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/settings/settings_use_case.dart';
import 'package:x1440/repositories/auth/auth_repository.dart';
import 'package:x1440/repositories/models/active_presence_status.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/services/de_conversations_service.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/models/previously_logged_in_user.dart';
import 'package:x1440/use_cases/models/scrt_credentials.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/viewmodels/conversations_viewmodel.dart';

class AuthUseCase {
  final AuthRepository _authRepository;
  final LocalStorageRepository _localStorage;
  final AppConfig _appConfig;
  final NotificationManager _notificationManager;
  final RemoteLogger _remoteLogger;

  AuthUseCase(this._authRepository, this._localStorage, this._appConfig,
      this._notificationManager, this._remoteLogger);

  Future<void> setCredentials(Credentials credentials) async {
    // Inspect the orgId and truncate to 15 because of SalesForce
    if (credentials.orgId != null && credentials.orgId!.length > 15) {
      credentials =
          credentials.copyWith(orgId: credentials.orgId!.substring(0, 15));
    }
    await _localStorage.setCredentials(credentials);
  }

  Stream<Credentials> get credentialsStream => _localStorage.credentialsStream;

  Future<Credentials> getCredentials() async {
    return await _localStorage.getCredentials();
  }

  Future<bool> get isLoggedIn =>
      _localStorage.getCredentials().then((value) => value.isLoggedIn);

  Future<bool> get sfLoggedIn =>
      _localStorage.getCredentials().then((value) => value.sfLoggedIn);

  Completer<bool>? _loginShimCompleter;

  Future<bool> loginShimWithSalesForce() async {
    _remoteLogger.info(
        'loginShimWithSalesForce called with _loginShimCompleter: $_loginShimCompleter');
    if (_loginShimCompleter != null) {
      return _loginShimCompleter!.future;
    }

    _loginShimCompleter = Completer<bool>();

    try {
      final credentials = await _localStorage.getCredentials();
      if (credentials.accessToken == null ||
          credentials.orgId == null ||
          _isLoggingOut) {
        _loginShimCompleter!.complete(false);
        return false;
      }

      final response = await _authRepository.authShimServiceFromSalesforce(
        SalesforceTokenBody(
          orgId: credentials.orgId!,
          accessToken: credentials.accessToken!,
          expirationSeconds:
              _appConfig.globalConfig?.shimServiceExpirationInSeconds,
        ),
      );

      if (response is Success) {
        final result = response as Success;
        final payload = result.data as SalesforceTokenResponse;

        await _localStorage.setCredentials(credentials.copyWith(
          orgId: credentials.orgId!,
          accessToken: credentials.accessToken!,
          authorizationToken: payload.accessToken,
        ));

        _loginShimCompleter!.complete(true);

        return true;
      } else {
        _remoteLogger
            .warn('Failed to login shim: ${(response as Error).error}');
        _loginShimCompleter!.complete(false);
        return false;
      }
    } catch (e) {
      _remoteLogger.error('Error during login shim: $e');
      _loginShimCompleter!.completeError(e);
      return false;
    } finally {
      _loginShimCompleter = null;
    }
  }

  Future<void> shimLoginAndStartSession() async {
    if (_isLoggingOut) {
      _remoteLogger
          .info("shimLoginAndStartSession called while logging out; returning");
      return;
    }
    _remoteLogger.info("shimLoginAndStartSession called");
    await loginShimWithSalesForce();
    _remoteLogger.info("shimLoginAndStartSession loginShimWithSalesForce done");
    final credentials = await _localStorage.getCredentials();

    // Save auth token in secure storage as well as instanceUrl
    await Utils.saveDataToSecureStorage(
        'access_token', credentials.accessToken);
    await Utils.saveDataToSecureStorage(
        'shim_service_url', _appConfig.shimServiceUrl);
    await Utils.saveDataToSecureStorage(
        'instance_url',
        credentials
            .instanceUrl); // e.g. https://re1693247397909.my.salesforce.com

    _remoteLogger.info(
        "shimLoginAndStartSession credentials isLoggedIn: ${credentials.isLoggedIn}; sfLoggedIn: ${credentials.sfLoggedIn}");
    await GetIt.I<SessionUseCase>().startSession();
    _remoteLogger.info("shimLoginAndStartSession startSession done");
    _isLoggingOut = false;
  }

  Future<bool> attemptAutoLogin() async {
    if (await sfLoggedIn) {
      try {
        if (await refreshSalesforceToken()) {
          await shimLoginAndStartSession();
          final loggedIn = await isLoggedIn;
          if (!loggedIn) {
            _remoteLogger.warn("attemptAutoLogin loggedIn is false");
          }
          return loggedIn;
        }
        _remoteLogger.warn("attemptAutoLogin failed to refreshSalesforceToken");
      } catch (e) {
        _remoteLogger.warn("attemptAutoLogin failed with error: $e");
        return false;
      }
    }
    return false;
  }

  Completer<bool>? _refreshTokenCompleter;

  Future<bool> refreshSalesforceToken() async {
    if (_refreshTokenCompleter != null) {
      return _refreshTokenCompleter!.future;
    }
    _refreshTokenCompleter = Completer<bool>();

    try {
      final credentials = await _localStorage.getCredentials();
      if (credentials.refreshToken == null) {
        _refreshTokenCompleter!.complete(false);
        GetIt.I<AuthBloc>().add(LogoutEvent());
        return false;
      }

      final response = await _authRepository
          .refreshSalesforceToken(credentials.refreshToken!);

      if (response is Success) {
        final result = response as Success;
        await _localStorage.setCredentials(credentials.copyWith(
            webSocketUrl: null, accessToken: result.data.accessToken));

        await Utils.saveDataToSecureStorage(
            'access_token', result.data.accessToken);
        // Handle backward compatibility
        _refreshTokenCompleter!.complete(true);
        return true;
      } else {
        _remoteLogger
            .warn('Failed to refresh token: ${(response as Error).error}');
        _refreshTokenCompleter!.complete(false);
        GetIt.I<AuthBloc>().add(LogoutEvent());
        return false;
      }
    } catch (e) {
      _remoteLogger.error('Error refreshing token: $e');
      if (!_refreshTokenCompleter!.isCompleted) {
        _refreshTokenCompleter!.completeError(e);
      }
      GetIt.I<AuthBloc>().add(LogoutEvent());
      return false;
    } finally {
      _refreshTokenCompleter = null;
    }
  }

  Future<SalesforceConfig> _getSfConfigFromSelectedEnvironment() async {
    final appLocalSettings =
        await GetIt.instance<SettingsUseCase>().appLocalSettings;
    final env = appLocalSettings.selectedEnvironment;

    switch (env.type) {
      case SalesforceEnvironmentType.production:
        return _appConfig.productionSalesforceConfig!;
      case SalesforceEnvironmentType.sandbox:
        return _appConfig.sandboxSalesforceConfig!;
      case SalesforceEnvironmentType.custom:
        return _appConfig.productionSalesforceConfig!.copyWith(
            endPointBase: appLocalSettings.selectedEnvironment.domain);
      default:
        return _appConfig.sandboxSalesforceConfig!;
    }
  }

  Future<Result<OAuthResponse, ApiError>> loginToSalesforce() async {
    final sfConfig = await getSalesforceConfig();
    return await _authRepository.loginToSalesforce(
        sfConfig, Utils.getLanguage());
  }

  Future<void> handleOAuthResponse(OAuthResponse payload) async {
    await _localStorage.setScrtCredentials(const ScrtCredentials());
    final creds = await getCredentials();
    await setCredentials(creds.copyWith(
        userId: payload.userId,
        instanceUrl: payload.instanceUrl,
        accessToken: payload.accessToken,
        orgId: payload.orgId,
        refreshToken: payload.refreshToken,
        webSocketUrl: null));

    PreviouslyLoggedInUser? previouslyLoggedInUser =
        await _localStorage.getPreviouslyLoggedInUser();
    if (previouslyLoggedInUser == null) {
      await _localStorage.setPreviouslyLoggedInUser(
          PreviouslyLoggedInUser(userId: payload.userId, orgId: payload.orgId));
      await _localStorage.clearActivePresenceStatus();
    } else {
      if (previouslyLoggedInUser.userId != payload.userId ||
          previouslyLoggedInUser.orgId != payload.orgId) {
        await _localStorage.clearPreviouslyLoggedInUser();
        await _localStorage.setPreviouslyLoggedInUser(PreviouslyLoggedInUser(
            userId: payload.userId,
            orgId: payload.orgId,
            presenceId: ActivePresenceStatus.offline().id));
        await _localStorage.clearActivePresenceStatus();
      }
    }
  }

  bool get isLoggingOut => _isLoggingOut;
  bool _isLoggingOut = false;
  void setIsLoggingOut([bool? value]) {
    _isLoggingOut = value ?? true;
  }

  Future<bool> logout() async {
    _remoteLogger.info("logout called");
    _isLoggingOut = true;
    _notificationManager.add(ClearNotificationsEvent());

    await _localStorage.clearActivePresenceStatus();
    Credentials credentials = await _localStorage.getCredentials();
    SalesforceConfig sfConfig = await _getSfConfigFromSelectedEnvironment();

    Result<bool, ApiError>? response;
    // TODO: handle failure
    if (credentials.refreshToken != null) {
      response = await _authRepository.logoutFromSalesForce(
          RevokeTokenBody(token: credentials.refreshToken!), sfConfig);
    }

    // Keep in sync the sharedPrefs
    await _localStorage.clearCredentials();
    await Utils.clearDataFromSecureStorage();
    await _localStorage.clearScrtCredentials();

    _isLoggingOut = false;

    // Delete all cookies on inappwebview
    await CookieManager().deleteAllCookies();

    // TODO: improve this clearing -- GetIt Scopes; AuthService?
    GetIt.I.get<DeConversationsService>().resetScrtCredentials();
    GetIt.I.get<ConversationsViewmodel>().clear();
    GetIt.I.get<ConversationsUseCase>().clear();
    GetIt.I.get<ContactsUseCase>().clear();
    GetIt.I.get<ContactsViewmodelInterface>().clear();
    GetIt.I.get<ConversationsBloc>().add(ClearConversationsEvent());
    GetIt.I.get<OrgBloc>().add(ClearOrgEvent());

    return response is Success;
  }

  Future<SalesforceConfig> getSalesforceConfig() async {
    return await _getSfConfigFromSelectedEnvironment();
  }
}
