import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/di/di.config.dart';
import 'package:x1440/frameworks/conversations_service.dart';
import 'package:x1440/frameworks/settings/models/app_local_settings.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_event.dart';
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_manager.dart';
import 'package:x1440/use_cases/models/salesforce_environment.dart';

class SettingsUseCase {
  final LocalStorageRepository _localStorage;

  SettingsUseCase(this._localStorage);

  Stream<AppLocalSettings> get appLocalSettingsStream =>
      _localStorage.appLocalSettingsStream;

  AppLocalSettings _appLocalSettings = AppLocalSettings();
  // AppLocalSettings get appLocalSettings => _appLocalSettings;
  Future<AppLocalSettings> get appLocalSettings =>
      _localStorage.getAppLocalSettings();

  Future<void> init() async {
    _appLocalSettings = await _localStorage.getAppLocalSettings();
    _localStorage.appLocalSettingsStream.listen((event) {
      _appLocalSettings = event;
    });

    if (kDebugMode) {
      setShowDevOptions(true);
    }

    /// make sure any scopes are correctly set
    return setSelectedEnvironment(_appLocalSettings.selectedEnvironment);
  }

  Future<void> setSelectedEnvironment(SalesforceEnvironment environment) async {
    _appLocalSettings = await _localStorage.getAppLocalSettings();
    await _localStorage.setAppLocalSettings(
        _appLocalSettings.copyWith(selectedEnvironment: environment));

    bool shouldUpdateProviders = environment.type == SalesforceEnvironmentType.demo;
    if (environment.type == SalesforceEnvironmentType.demo) {
      await GetIt.I.initDemoScope();
      final convoService = GetIt.I<ConversationsService>();
      await convoService.init();
    } else {
      if (GetIt.I.currentScopeName == 'demo') {
        GetIt.I.popScope();
        shouldUpdateProviders = true;
      }
    }
    if (shouldUpdateProviders) {
      GetIt.I<DemoModeManager>().add(UpdateProvidersScopeEvent());
    }
  }

  Future<void> setAiSuggestionsEnabled(bool enabled) => _localStorage.setAppLocalSettings(
        _appLocalSettings.copyWith(aiSuggestionsEnabled: enabled));

  Future<void> setUserRequestsAiSuggestions(bool enabled) => _localStorage.setAppLocalSettings(
        _appLocalSettings.copyWith(userRequestsAiSuggestions: enabled));

  setShowDevOptions(bool showDevOptions) => _localStorage.setAppLocalSettings(
        _appLocalSettings.copyWith(showDevOptions: showDevOptions));
}
