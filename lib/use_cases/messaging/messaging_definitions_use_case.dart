import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/channel_messaging_definitions_response.dart';
import 'package:x1440/api/dtos/messaging_definition.dart';
import 'package:x1440/api/dtos/messaging_end_user_filters_response.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/websocket/messaging_definitions_repository.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_exceptions.dart';
import 'package:x1440/use_cases/messaging/models/messaging_definition_status.dart';

class MessagingDefinitionsUseCase {
  final RemoteLogger _logger;
  final MessagingDefinitionsRepository _messagingRepository;

  MessagingDefinitionsUseCase(this._logger, this._messagingRepository);

  Future<MessagingDefinitionStatus> getMessagingDefinitionStatus(
      SfId messagingEndUserId, MessagingChannelType messageType) async {
    final filtersResponse = await _messagingRepository
        .getMessagingEndUserFilters(messagingEndUserId);

    MessagingDefinitionStatus definitionStatus =
        const MessagingDefinitionStatus(
            mustUseDefinition: false, definitions: null);

    if (filtersResponse is Success) {
      MessagingEndUserFiltersResponse responseFilters =
          (filtersResponse as Success).data;

      if (responseFilters.filters.isEmpty) {
        throw NoFiltersDefinedException(messagingEndUserId);
      }

      final messageTypeFilter = responseFilters.filters
          .where((filter) => filter.messageType == messageType.value)
          .toList();

      if (messageTypeFilter.isEmpty) {
        throw UndefinedMessagingTypeException(messageType.value);
        // definitionStatus = MessagingDefinitionStatus(
        //     mustUseDefinition: true,
        //     definitions: ChannelMessagingDefinitionsResponse.fromJson(
        //         {"definitions": []}).definitions);
      } else {
        if (messageTypeFilter.first.enforceMessagingComponent) {
          final definitionsResponse =
              await _messagingRepository.getChannelMessagingDefinitions(
                  messageTypeFilter.first.channelId);
          if (definitionsResponse is Success) {
            ChannelMessagingDefinitionsResponse responseDefinitions =
                (definitionsResponse as Success).data;
            definitionStatus = MessagingDefinitionStatus(
                mustUseDefinition: true,
                definitions: responseDefinitions.definitions);
          }

          if (definitionsResponse is Error ||
              definitionStatus.definitions == null ||
              definitionStatus.definitions!.isEmpty) {
            throw NoDefinitionssDefinedException(messagingEndUserId);
          }
        }
      }

      return definitionStatus;
    } else {
      _logger.error(
          'Failed to get channel messaging definitions for $messagingEndUserId.');
      Error error = filtersResponse as Error;
      throw MessagingApiException(error.error);
    }
  }

  List<MessagingDefinition> searchMessagingDefinitions(
    List<MessagingDefinition> definitions,
    String searchTerm,
  ) {
    searchTerm = searchTerm.toLowerCase();

    final list = definitions.where((definition) {
      final nameMatch =
          definition.name?.toLowerCase().contains(searchTerm) ?? false;
      final descriptionMatch =
          definition.description?.toLowerCase().contains(searchTerm) ?? false;

      return nameMatch || descriptionMatch;
    }).toList();

    return list;
  }
}
