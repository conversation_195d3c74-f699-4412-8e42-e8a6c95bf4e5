// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_definition_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingDefinitionStatusImpl _$$MessagingDefinitionStatusImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingDefinitionStatusImpl(
      mustUseDefinition: json['mustUseDefinition'] as bool,
      definitions: (json['definitions'] as List<dynamic>?)
          ?.map((e) => MessagingDefinition.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$MessagingDefinitionStatusImplToJson(
        _$MessagingDefinitionStatusImpl instance) =>
    <String, dynamic>{
      'mustUseDefinition': instance.mustUseDefinition,
      'definitions': instance.definitions?.map((e) => e.toJson()).toList(),
    };
