// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_definition_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingDefinitionStatus _$MessagingDefinitionStatusFromJson(
    Map<String, dynamic> json) {
  return _MessagingDefinitionStatus.fromJson(json);
}

/// @nodoc
mixin _$MessagingDefinitionStatus {
  bool get mustUseDefinition => throw _privateConstructorUsedError;
  List<MessagingDefinition>? get definitions =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingDefinitionStatusCopyWith<MessagingDefinitionStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingDefinitionStatusCopyWith<$Res> {
  factory $MessagingDefinitionStatusCopyWith(MessagingDefinitionStatus value,
          $Res Function(MessagingDefinitionStatus) then) =
      _$MessagingDefinitionStatusCopyWithImpl<$Res, MessagingDefinitionStatus>;
  @useResult
  $Res call({bool mustUseDefinition, List<MessagingDefinition>? definitions});
}

/// @nodoc
class _$MessagingDefinitionStatusCopyWithImpl<$Res,
        $Val extends MessagingDefinitionStatus>
    implements $MessagingDefinitionStatusCopyWith<$Res> {
  _$MessagingDefinitionStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mustUseDefinition = null,
    Object? definitions = freezed,
  }) {
    return _then(_value.copyWith(
      mustUseDefinition: null == mustUseDefinition
          ? _value.mustUseDefinition
          : mustUseDefinition // ignore: cast_nullable_to_non_nullable
              as bool,
      definitions: freezed == definitions
          ? _value.definitions
          : definitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessagingDefinitionStatusImplCopyWith<$Res>
    implements $MessagingDefinitionStatusCopyWith<$Res> {
  factory _$$MessagingDefinitionStatusImplCopyWith(
          _$MessagingDefinitionStatusImpl value,
          $Res Function(_$MessagingDefinitionStatusImpl) then) =
      __$$MessagingDefinitionStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool mustUseDefinition, List<MessagingDefinition>? definitions});
}

/// @nodoc
class __$$MessagingDefinitionStatusImplCopyWithImpl<$Res>
    extends _$MessagingDefinitionStatusCopyWithImpl<$Res,
        _$MessagingDefinitionStatusImpl>
    implements _$$MessagingDefinitionStatusImplCopyWith<$Res> {
  __$$MessagingDefinitionStatusImplCopyWithImpl(
      _$MessagingDefinitionStatusImpl _value,
      $Res Function(_$MessagingDefinitionStatusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mustUseDefinition = null,
    Object? definitions = freezed,
  }) {
    return _then(_$MessagingDefinitionStatusImpl(
      mustUseDefinition: null == mustUseDefinition
          ? _value.mustUseDefinition
          : mustUseDefinition // ignore: cast_nullable_to_non_nullable
              as bool,
      definitions: freezed == definitions
          ? _value._definitions
          : definitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingDefinitionStatusImpl implements _MessagingDefinitionStatus {
  const _$MessagingDefinitionStatusImpl(
      {required this.mustUseDefinition,
      final List<MessagingDefinition>? definitions})
      : _definitions = definitions;

  factory _$MessagingDefinitionStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingDefinitionStatusImplFromJson(json);

  @override
  final bool mustUseDefinition;
  final List<MessagingDefinition>? _definitions;
  @override
  List<MessagingDefinition>? get definitions {
    final value = _definitions;
    if (value == null) return null;
    if (_definitions is EqualUnmodifiableListView) return _definitions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MessagingDefinitionStatus(mustUseDefinition: $mustUseDefinition, definitions: $definitions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingDefinitionStatusImpl &&
            (identical(other.mustUseDefinition, mustUseDefinition) ||
                other.mustUseDefinition == mustUseDefinition) &&
            const DeepCollectionEquality()
                .equals(other._definitions, _definitions));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, mustUseDefinition,
      const DeepCollectionEquality().hash(_definitions));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingDefinitionStatusImplCopyWith<_$MessagingDefinitionStatusImpl>
      get copyWith => __$$MessagingDefinitionStatusImplCopyWithImpl<
          _$MessagingDefinitionStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingDefinitionStatusImplToJson(
      this,
    );
  }
}

abstract class _MessagingDefinitionStatus implements MessagingDefinitionStatus {
  const factory _MessagingDefinitionStatus(
          {required final bool mustUseDefinition,
          final List<MessagingDefinition>? definitions}) =
      _$MessagingDefinitionStatusImpl;

  factory _MessagingDefinitionStatus.fromJson(Map<String, dynamic> json) =
      _$MessagingDefinitionStatusImpl.fromJson;

  @override
  bool get mustUseDefinition;
  @override
  List<MessagingDefinition>? get definitions;
  @override
  @JsonKey(ignore: true)
  _$$MessagingDefinitionStatusImplCopyWith<_$MessagingDefinitionStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}
