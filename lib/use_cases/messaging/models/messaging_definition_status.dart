import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/messaging_definition.dart';

part 'messaging_definition_status.freezed.dart';
part 'messaging_definition_status.g.dart';

@freezed
class MessagingDefinitionStatus with _$MessagingDefinitionStatus {
  const factory MessagingDefinitionStatus({
    required bool mustUseDefinition,
    List<MessagingDefinition>? definitions,
  }) = _MessagingDefinitionStatus;

  factory MessagingDefinitionStatus.fromJson(Map<String, dynamic> json) =>
      _$MessagingDefinitionStatusFromJson(json);
}
