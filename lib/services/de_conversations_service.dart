import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/salesforce/dtos/conversation_entries.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/frameworks/conversations_service.dart';
import 'package:x1440/frameworks/notifications/notifications_event.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/conversation_entry_model.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/salesforce_data_repository/salesforce_data_repository.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/services/shim_service/models/shim_service_payload.dart';
import 'package:x1440/services/shim_service/shim_service.dart';
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_bloc.dart';
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_event.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_event.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/models/messaging_end_user.dart';
import 'package:x1440/use_cases/models/scrt_credentials.dart';
import 'package:x1440/use_cases/notifications/notifications_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/utils/api_status.dart';
import 'package:x1440/utils/extensions/iterable_extension.dart';
import 'package:x1440/utils/extensions/string_extension.dart';
import 'package:x1440/utils/graphql_utils.dart';
import 'package:x1440/utils/json_utils.dart';
import 'package:x1440/utils/loggy_utils.dart';
import 'package:x1440/viewmodels/conversations_viewmodel.dart';

import '../models/app_error_model.dart';
import '../models/content_document_for_enhanced.dart';
import '../models/messaging_session_model.dart';
import '../models/polling_response.dart';
import '../utils/constants.dart';
import '../utils/graphql_queries.dart';
import '../utils/refresh_token_utils.dart';
import '../repositories/graph_ql/graphql_repository.dart';
import 'salesforce_api_service.dart';

class DeConversationsService with ServiceLoggy {
  final LocalStorageRepository _localStorageRepository;
  final ConversationsUseCase _conversationsUseCase;

  void _reportError(AppError newError) =>
      GetIt.I<AppErrorBloc>().add(ReportAppErrorEvent(newError));

  final GraphQlRepository _graphQlRepository;
  final ShimService _shimService;
  final RemoteLogger _logger;

  Completer<ScrtCredentials>? _scrtCredentialsCompleter;
  Future<ScrtCredentials> get _scrtCredentials async {
    _scrtCredentialsCompleter ??= Completer();
    final creds = await _localStorageRepository.getScrtCredentials();
    if (creds.scrtAccessToken == null || creds.scrtHost == null) {
      /// wait for the credentials to be set
      return _scrtCredentialsCompleter!.future;
    }
    if (!_scrtCredentialsCompleter!.isCompleted) {
      _scrtCredentialsCompleter!.complete(creds);
    }
    return _scrtCredentialsCompleter!.future;
  }

  void resetScrtCredentials() {
    _scrtCredentialsCompleter = null;
  }

  Future<String?> get _scrtAccessToken async =>
      (await _scrtCredentials).scrtAccessToken;

  Future<String?> get _scrtHost async => (await _scrtCredentials).scrtHost;

  HttpClient httpClient = HttpClient();

  DeConversationsService(this._shimService, this._localStorageRepository,
      this._logger, this._conversationsUseCase, this._graphQlRepository);

  Future<void> init() async {
    _conversationsUseCase.messageReceiveQueueLegacyStream
        .listen(onMessageReceivedFromQueue);
  }

  void _pollingCallback(PollingResponse pollingResponse) {
    try {
      GetIt.I<ConversationsViewmodel>().handlePollingCallback(pollingResponse);
    } catch (e) {
      _logger.error('DeConvoSvc - _pollingCallback - error: $e');
    }
  }

  Future<void> mappedPollingCallback(
      String type, Map<String, dynamic> json) async {
    if ((json['conversationId'] ?? '').length > 18 &&
        (json['id'] ?? '').length > 18) {
      // TODO: handle read acknowledgement on read
      sendDeliveryAcknowledgement(json['conversationId'], json['id']).then(
          (value) =>
              sendReadAcknowledgement(json['conversationId'], json['id']));
    }

    PollingResponseType parsedType = PollingResponseType.values
            .firstWhereOrNull(
                (e) => describeEnum(e).toLowerCase() == type.toLowerCase()) ??
        PollingResponseType.unknown;

    if (parsedType == PollingResponseType.message) {
      final convoService = GetIt.I<ConversationsService>();
      final message = LakMessage.fromJson(json);
      final messagingSessionId = json['workTargetId'];
      final conversationIdentifier = json['conversationIdentifier'];
      if (messagingSessionId != null) {
        _logger.info(
            'svc-DeCS - mappedPollingCallback - adding message to messagingSessionId: $messagingSessionId; messageJson: $json');

        convoService.addReceivedMessageToMessagingSession(
            message, SfId(messagingSessionId));
      } else if (conversationIdentifier != null) {
        convoService.addReceivedMessageToConversationIdentifier(
            message, conversationIdentifier);
      } else {
        _logger.error(
            'svc-DeCS - mappedPollingCallback - no messagingSessionId or conversationIdentifier');
      }
    } else {
      _pollingCallback(RawPollingResponse(json: json, type: parsedType));
    }
  }

  int id = 0;

  // Common handler for both background and foreground FCM messages
  Future<void> onMessageReceivedFromQueue(
      QueueReceiveMessage queueReceivedMessage) async {
    _logger.info(
        'svc-DeCS - onMessageReceivedFrom Legacy Queue: $queueReceivedMessage');
    String? action = queueReceivedMessage.notificationAction;
    Map<String, dynamic> payload = queueReceivedMessage.payload;

    // Convert the Remote Message data to a Map<String, dynamic>?
    if (payload.isEmpty) {
      _logger.info(
          'failed to parse message payload: ${queueReceivedMessage.notificationId}');
      _reportError(AppError(
        message: S.current.app_error_received_empty_null_message_label,
      ));
      return;
    }

    final notificationsMessageResponse = payload["x1440Token"] == null
        ? null
        : await GetIt.I<NotificationsUseCase>()
            .getNotificationMessage(payload["x1440Token"]);

    ShimServicePayload? shimServicePayload = ShimServicePayload.fromJson(
        notificationsMessageResponse?.message != null
            ? safeJsonDecode(notificationsMessageResponse?.message)
            : payload);

    if (shimServicePayload?.messageType == "ExpiredSession") {
      _logger
          .info('session is expired: ${queueReceivedMessage.notificationId}');

      GetIt.I<AppErrorBloc>().add(ReportAppErrorEvent(AppError(
        message: S.current.app_error_shim_session_expired_label,
        shouldShowSystemNotification: true,
        shouldForceLogout: true,
      )));

      GetIt.I<AuthBloc>().add(SessionExpiredEvent());
      return;
    }
    if (shimServicePayload == null) {
      _logger.info(
          'failed to parse payload: ${queueReceivedMessage.notificationId}');
      return;
    }

    shimServicePayload.message['notificationId'] =
        queueReceivedMessage.notificationId;

    /// workaround for notifications manager to have the message notification details needed for WorkAssigned // TODO: remove on refactor
    GetIt.I.get<NotificationManager>().add(ReportMessageReceivedEvent(
        queueReceivedMessage: queueReceivedMessage,
        shimServicePayload: shimServicePayload));
    try {
      _shimService.messageHandler(
          mappedPollingCallback,
          shimServicePayload.platformType,
          shimServicePayload.messageType,
          shimServicePayload.message,
          action: action);
    } catch (e) {
      _logger.error('svc-DeCS Failed to handle message: $e');
    }
  }

  Future<String?> getChannelAddressIdentifierByConversationChannelId(
      String conversationChannelId) async {
    final credentials = await _localStorageRepository.getCredentials();
    if (!credentials.isLoggedIn) {
      return null;
    }

    final ApiResponse messagingChannelFetchResponse = await SalesforceAPIService
        .fetchMessagingChannelFromConversationChannelId(
            credentials.accessToken!,
            credentials.instanceUrl!,
            conversationChannelId);

    if (messagingChannelFetchResponse is Success) {
      Map<String, dynamic> messagingChannelDecodedJson =
          safeJsonDecode(messagingChannelFetchResponse.response);

      return messagingChannelDecodedJson['ChannelAddressIdentifier'];
    }
    return null;
  }

  Future<List<LakConversation>> parseConversationsFromGraphQl(
      final List<MessagingSession> messagingSessions,
      final List<ConversationEntry> conversationEntries,
      {final Map<String, List<ContentDocumentLinkForEnhanced>>
          contentDocLinksByMessagingEndUserId = const {},
      Map<String, String?> channelAddressIdentifierByScrtUUID =
          const {}}) async {
    // 1. Group ConversationEntries by conversationId (messagingSessionId)
    ///// TODO: CLEAN THIS UP; using diff keys for Enhanced & Regular sessions — can we have the entry declare what its key should be? Some better way to handle it?
    Map<String, List<ConversationEntry>> entriesByConversationId =
        conversationEntries.fold<Map<String, List<ConversationEntry>>>({},
            (map, entry) {
      // TODO: refactor this; using entry.scrtUUID != null to mean this is enhanced (/whatsapp)
      if (entry.scrtUUID != null && entry.messagingEndUserId != null) {
        map.putIfAbsent(entry.messagingEndUserId!, () => []).add(entry);
      } else if (entry.conversationId != null) {
        map.putIfAbsent(entry.conversationId!, () => []).add(entry);
      }
      return map;
    });

    // 2. Group MessagingSessions by MessagingEndUser.Id
    Map<String, List<MessagingSession>> sessionsByEndUserId = [
      ...messagingSessions,
    ].fold<Map<String, List<MessagingSession>>>({}, (map, session) {
      if (session.messagingEndUser?.id != null) {
        map
            .putIfAbsent(session.messagingEndUser!.id.toString(), () => [])
            .add(session);
      }
      return map;
    });

    // 3. Create Conversations
    List<LakConversation> conversations = [];
    Map<String, String> latestMessagingSessionIds = {}; // for agent work status
    for (String messagingEndUserId in sessionsByEndUserId.keys) {
      List<MessagingSession> sortedSessions =
          sessionsByEndUserId[messagingEndUserId]!
            ..sort((a, b) => a.createdDate!.compareTo(b.createdDate!));
      MessagingSession latestSession = sortedSessions.last;

      Set<LakMessage> allMessages = {};

      for (MessagingSession session in sortedSessions) {
        // TODO: can this enhanced/regular distinction be made in the ConversationEntry model?
        /// SMS enhanced: channelName: sfdc_livemessage; ChannelType.sfdcLiveagent

        // TODO: refactor this mess -- as it is, we're getting the same messages many times (but the de-dupe logic is working on adding them to the convo)
        List<ConversationEntry> sessionEntriesBySessionId =
            (entriesByConversationId[session.id.toString()] ?? []);

        List<ConversationEntry> sessionEntriesByConversationId =
            (entriesByConversationId[session.conversationId] ?? []);

        List<ConversationEntry> sessionEntriesByMeuId =
            (entriesByConversationId[session.messagingEndUser?.id.toString()] ??
                []);

        List<ConversationEntry> sessionEntries = [
          ...sessionEntriesBySessionId,
          ...sessionEntriesByConversationId,
          ...sessionEntriesByMeuId
        ];

        Set<String> sessionEntryIds = sessionEntries.map((e) => e.id).toSet();

        sessionEntries = sessionEntryIds.map((id) {
          return sessionEntries.firstWhereOrNull((e) => e.id == id) ??
              sessionEntries.firstWhere((e) => e.id == id);
        }).toList();

        // First, check if 'sessionEntries' is not empty to avoid 'No element' errors.
        if (sessionEntries.isNotEmpty) {
          // Iterate through sessionEntries in reverse to find the latest session that meets the criteria
          for (var sessionEntry in sessionEntries.reversed) {
            if (sessionEntry.relatedRecords != null &&
                sessionEntry.relatedRecords!.isNotEmpty) {
              var firstRelatedRecord = sessionEntry.relatedRecords![0];

              // Check if the session has not been processed yet and meets your specific conditions
              if (latestMessagingSessionIds[sessionEntry.messagingSessionId] ==
                      null &&
                  sessionEntry.messagingSessionId?.startsWith("0dw") == true &&
                  Utils.checkIfMessagingSessionId(firstRelatedRecord)) {
                latestMessagingSessionIds[sessionEntry.messagingSessionId!] =
                    firstRelatedRecord;

                // exit loop after finding the session
                break;
              }
            }
          }
        }

        List<LakMessage> sessionMessages = sessionEntries.map((entry) {
          // TODO: confirm we don't need and remove
          // Convert attachments inside the arrow function
          List<Map<String, String>> attachmentList =
              entry.attachments?.map((conversationEntryAttachment) {
                    return {
                      "contentVersionId":
                          conversationEntryAttachment.versionId ?? ''
                    };
                  }).toList() ??
                  [];

          return LakMessage(
              isTemplate: entry.isTemplate ?? false,
              scrtUUID: entry.scrtUUID,
              id: entry.id,
              messageBody: entry.message ?? '',
              timeStamp: entry.entryTime?.millisecondsSinceEpoch ??
                  entry.createdDate?.millisecondsSinceEpoch ??
                  0,
              actorName: entry.actorName ?? '',
              actorType: () {
                return MessageActorType.values.firstWhereOrNull((element) {
                      if (entry.actorType?.toLowerCase() == 'end_user') {
                        return element == MessageActorType.EndUser;
                      }
                      return element.name.toLowerCase() ==
                          entry.actorType?.toLowerCase();
                    }) ??
                    MessageActorType.Agent;
              }(),
              entryType: MessageEntryType.values.firstWhereOrNull(
                      (element) => describeEnum(element) == entry.entryType) ??
                  MessageEntryType.text,
              attachments: attachmentList,
              conversationEntryAttachments: entry.attachments ?? [],
              readStatus: true);
        }).toList();
        allMessages.addAll(sessionMessages);
      }

      String? meuPhotoUrl = latestSession.messagingEndUser?.profilePictureUrl;
      String? idInUrl = latestSession.endUserContact?.photoUrl?.split("/").last;
      String? meuContactPhotoUrl = idInUrl != null &&
              idInUrl != latestSession.messagingEndUser!.contactId
          ? latestSession.endUserContact?.photoUrl
          : null;

      String? finalPhotoUrl = meuPhotoUrl ?? meuContactPhotoUrl;

      LakMessage conversationLastMessage = allMessages.isNotEmpty
          ? allMessages.first
          : LakMessage(
              id: '',
              actorType: MessageActorType.EndUser,
              entryType: MessageEntryType.text,
              messageBody: '',
              readStatus: true,
            );

      // sortedSessions.reversed
      MessagingSession? findFirstSessionBy(
          bool Function(MessagingSession session) predicate) {
        for (MessagingSession session in sortedSessions.reversed) {
          if (predicate(session)) return session;
        }
        return null;
      }

      MessagingEndUser? latestMessagingEndUser =
          findFirstSessionBy((session) => session.messagingEndUser != null)
              ?.messagingEndUser;

      ////// get correct SCRT uuid
      Set<String> availableSCRTuuids =
          allMessages.map((msg) => msg.scrtUUID).whereType<String>().toSet();

      String? scrtUUID = allMessages
          .toList()
          .firstWhereOrNull((message) => message.scrtUUID != null)
          ?.scrtUUID;

      bool isEnhanced = scrtUUID != null;

      if (!isEnhanced) {
        latestMessagingSessionIds[latestMessagingEndUser!.id.toString()] =
            latestSession.id.toString();
      }

      LakConversation conversation = LakConversation(
        scrtUUID: scrtUUID,
        channelAddressIdentifier: channelAddressIdentifierByScrtUUID[scrtUUID],
        id: latestMessagingEndUser!.id.toString(),
        username:
            latestMessagingEndUser.name ?? latestMessagingEndUser.name ?? '',
        lastMessageBody: conversationLastMessage.messageBody ?? '',
        lastMessageTime: conversationLastMessage.timeStamp.toString(),
        channel: findFirstSessionBy((session) => session.channelType != null)
            ?.channelType,
        pictureUrl: Constants.getLinkUrl(
            findFirstSessionBy((session) => session.channelType != null)
                    ?.channelType ??
                ''),
        ownerId:
            findFirstSessionBy((session) => session.ownerId != null)?.ownerId,
        messagingChannelId: latestSession.messagingChannelId,
        messagingSessionStatus:
            findFirstSessionBy((session) => session.status != null)
                ?.status
                ?.toString(),
        channelType: Constants.getChannelType(
            ChannelProvider.salesforce,
            findFirstSessionBy((session) => session.channelType != null)
                    ?.channelType ??
                ''),
      );

      List<LakMessage> sortedMessages = allMessages.toList()
        ..sort((a, b) => a.timeStamp.compareTo(b.timeStamp));

      for (LakMessage msg in sortedMessages) {
        conversation.addMessage(msg);
      }

      conversations.add(conversation);
    }
    return conversations;
  }

  List<MessagingSession> _parseGraphQlToMessagingSessions(
      List conversationsGraphQl) {
    return conversationsGraphQl.map((graphQlData) {
      return MessagingSession.fromGraphQlNode(graphQlData['node']);
    }).toList();
  }

  Future<void> _fetchConversationEntriesByEnhancedConversationId(
      Map<String, List<ConversationEntry>> enhancedConversationEntries,
      ConversationEntries entries,
      List<MessagingSession> messagingSessions) async {
    final String? conversationUUID = entries.conversationIdentifier;

    final String? conversationChannelId = entries.conversationChannelId;

    if (conversationUUID == null) {
      loggy.warning(
          'attempting to fetch conversation entries from UUID failed: $conversationUUID');
      return;
    }

    // TODO: type these?
    var conversationHistoryResult =
        await _getConversationHistory(conversationUUID);

    var decodedConversationHistoryResult =
        safeJsonDecode(conversationHistoryResult);

    var startProcessing = DateTime.now().millisecondsSinceEpoch;

    List<ConversationEntry> conversationEntries =
        (decodedConversationHistoryResult['conversationEntries'] is List
                ? decodedConversationHistoryResult['conversationEntries']
                : [])
            .map((record) {
              ConversationEntry? entry = ConversationEntry.fromScrtEnhancedJson(
                  record,
                  conversationId: entries.id!,
                  scrtUUID: conversationUUID,
                  conversationChannelId: conversationChannelId);
              return entry;
            })
            .whereType<ConversationEntry>()
            .toList();

    var endProcessing = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        'DE CONVERSATION ENTRIES: PROCESSING TIME - ${endProcessing - startProcessing} ms');

    _patchEnhancedConversationWithMetadata(
        conversationEntries, messagingSessions);

    enhancedConversationEntries[entries.id!] = conversationEntries;
  }

  Future<List<LakConversation>> fetchConversationsAndMessages() async {
    final credentials = await _localStorageRepository.getCredentials();

    if (!credentials.isLoggedIn) {
      return [];
    }

    Map<String, dynamic> variables = {
      'userId': credentials.userId?.substringOrMax(0, 15),
    };

    final startQuery = DateTime.now().millisecondsSinceEpoch;
    loggy.info('GRAPHQL - fetchDEConversationsAndMessages: START');
    QueryOptions queryOptions = QueryOptions(
        query: GraphQLQueries.queryDEConversationsByUserId,
        variables: variables);

    final endQuery = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        'GRAPHQL - _getConversationFromMessagingSessionQuery: END - ${endQuery - startQuery} ms');

    final startProcessing = DateTime.now().millisecondsSinceEpoch;

    List<LakConversation>? typedConversations =
        await _getConversationsFromMessagingSessionQuery(queryOptions);

    final endProcessing = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        'GRAPHQL - _getConversationFromMessagingSessionQuery: END - ${endProcessing - startProcessing} ms');

    typedConversations?.forEach((conversation) {
      PollingResponse newConversationPollingResponse = NewConversationResponse(
        conversation: conversation,
        isFetchedFromLogin: true,
        isRefetch: true,
        type: PollingResponseType.newConversation,
      );
      _pollingCallback(newConversationPollingResponse);
    });
    return typedConversations ?? [];
  }

  Future<LakConversation?> createConversation(String messagingEndUserId) async {
    loggy.info('de: create convo: messagingEndUserId: $messagingEndUserId');

    final credentials = await _localStorageRepository.getCredentials();

    if (!credentials.isLoggedIn) {
      return null;
    }

    Map<String, dynamic> variables = {
      'meu': messagingEndUserId.substringOrMax(0, 15),
    };

    final startQuery = DateTime.now().millisecondsSinceEpoch;

    loggy.info('GRAPHQL - queryMessagingSessionsByMessagingEndUser:START');

    QueryOptions queryOptions = QueryOptions(
        query: GraphQLQueries.queryMessagingSessionsByMessagingEndUser,
        variables: variables);

    // This is FAR from being optimized! We are re-fetching ALL conversations just to get the new one!!
    // This call may create a lag of 5-10 seconds at best
    // CRM NOTE FOLLOWUP (2/18/25): this should not be fetching all conversations but only this MEU? from the queryOptions set above?
    List<LakConversation>? typedConversations =
        await _getConversationsFromMessagingSessionQuery(queryOptions);

    final endQuery = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        'GRAPHQL - queryMessagingSessionsByMessagingEndUser:END - ${endQuery - startQuery} ms');

    return typedConversations?.firstOrNull;
  }

  Future<List<LakConversation>?> _getConversationsFromMessagingSessionQuery(
      QueryOptions queryOptions) async {
    var startFetch = DateTime.now().millisecondsSinceEpoch;

    QueryResult? result = await _queryDEConversationsByUserId(queryOptions);

    var endFetch = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        '_queryDEConversationsByUserId: FETCH TIME - ${endFetch - startFetch} ms');

    if (result == null || result.data == null) {
      return null;
    }

    var data = result.data!;
    Map<String, dynamic> queryPayload = data['uiapi']['query'];

    List conversationsGraphQl = queryPayload['MessagingSession']['edges'];

    List<ConversationEntry> conversationEntries = [];
    Set<String> enhancedConversationIdsToFetch = {};
    Map<String, String> enhancedConversationIdsToFetchByMessagingSessionId = {};
    Map<String, MessagingEndUser> enhancedConversationMessagingEndUsers = {};

    // Parse the GraphQL data to create MessagingSession objects
    List<MessagingSession> messagingSessions =
        _parseGraphQlToMessagingSessions(conversationsGraphQl).toList();

    ///////////////// for DE channels, get the MessagingSessionIds & query by those
    ///////////////// for WhatsApp (& other enhanced?), get the conversationId, use it to fetch the conversation UUID, & then query by that

    List<String> messagingSessionIds = _extractMessagingSessionIds(
        messagingSessions,
        enhancedConversationIdsToFetch,
        enhancedConversationIdsToFetchByMessagingSessionId,
        enhancedConversationMessagingEndUsers);

    startFetch = DateTime.now().millisecondsSinceEpoch;

    // Get all the entries for the enhanced conversations
    Map<String, List<ConversationEntry>> enhancedConversationEntries =
        await _fetchAllConversations(
            enhancedConversationIdsToFetch, messagingSessions);

    endFetch = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        '_fetchAllConversations: FETCH TIME - ${endFetch - startFetch} ms');

    startFetch = DateTime.now().millisecondsSinceEpoch;

    messagingSessions = _createMessagingSessionsFromEnhancedConversationEntries(
        enhancedConversationEntries,
        enhancedConversationMessagingEndUsers,
        messagingSessions).toList();

    endFetch = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        '_createMessagingSessionsFromEnhancedConversationEntries: PROCESSING TIME - ${endFetch - startFetch} ms');

    // This is hacky here and may have to be refactored
    _updateBlocsWithMessagingSessions(messagingSessions.toSet());

    startFetch = DateTime.now().millisecondsSinceEpoch;

    List<ConversationEntry> flattenedEnhancedConversationEntries =
        _combineEnhancedConversationEntries(
            enhancedConversationEntries, messagingSessions);

    endFetch = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        '_combineEnhancedConversationEntries: PROCESSING TIME - ${endFetch - startFetch} ms');

    startFetch = DateTime.now().millisecondsSinceEpoch;

    conversationEntries = await _fetchConversationEntries(messagingSessionIds,
        conversationEntries, flattenedEnhancedConversationEntries, result);

    endFetch = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        '_fetchConversationEntries: PROCESSING TIME - ${endFetch - startFetch} ms');

    Map<String, String?> channelAddressIdentifierByScrtUUID =
        await _getChannelAddressIdentifiers(Map.fromEntries(conversationEntries
            .where((entry) =>
                entry.scrtUUID != null && entry.conversationChannelId != null)
            .map((entry) =>
                MapEntry(entry.scrtUUID!, entry.conversationChannelId!))));

    ///////////// END FETCH CONVERSATION CHANNEL IDS

    startFetch = DateTime.now().millisecondsSinceEpoch;

    conversationEntries =
        await fetchNeededContentDocsForConversationEntries(conversationEntries);

    endFetch = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        'fetchNeededContentDocsForConversationEntries: PROCESSING TIME - ${endFetch - startFetch} ms');

    startFetch = DateTime.now().millisecondsSinceEpoch;

    List<LakConversation> typedConversations =
        await parseConversationsFromGraphQl(
            messagingSessions, conversationEntries,
            contentDocLinksByMessagingEndUserId: {},
            channelAddressIdentifierByScrtUUID:
                channelAddressIdentifierByScrtUUID);

    endFetch = DateTime.now().millisecondsSinceEpoch;

    loggy.info(
        'parseConversationsFromGraphQl: PROCESSING TIME - ${endFetch - startFetch} ms');

    return typedConversations;
  }

  Future<Map<String, String?>> _getChannelAddressIdentifiers(
      Map<String, String> scrtUuidToChannelId) async {
    if (scrtUuidToChannelId.isEmpty) return {};

    // Deduplicate channel IDs to minimize API calls
    final uniqueChannelIds = scrtUuidToChannelId.values.toSet();

    // Fetch all channel address identifiers in parallel
    final channelAddressMap = Map.fromEntries(
        await Future.wait(uniqueChannelIds.map((channelId) async {
      final identifier =
          await getChannelAddressIdentifierByConversationChannelId(channelId);
      return MapEntry(channelId, identifier);
    })));

    // Map back to original SCRT UUIDs
    return scrtUuidToChannelId.map((scrtUuid, channelId) =>
        MapEntry(scrtUuid, channelAddressMap[channelId]));
  }

  Future<QueryResult?> _queryDEConversationsByUserId(
      QueryOptions queryOptions) async {
    final startQuery = DateTime.now().millisecondsSinceEpoch;
    final graphQlClient = await _graphQlRepository.getClient();

    if (graphQlClient == null) {
      throw Exception(
          '_getConversationFromMessagingSessionQuery failed: Failed to get graphQlClient');
    }

    loggy.info('GRAPHQL - fetchDEConversationsAndMessages - START');

    QueryResult? result = await graphQlClient.query(queryOptions);
    final endQuery = DateTime.now().millisecondsSinceEpoch;

    print('Conversation GraphQL result: $result');

    loggy.info(
        'GRAPHQL - fetchDEConversationsAndMessages - END: ${endQuery - startQuery} ms');

    if (result.hasException) {
      if (!result.hasAuthException) {
        // TODO: handle this error case
        _reportError(AppError(
            message: S.current.app_error_graphql_unknown_label,
            isReplaceable: true));
      } else {
        bool refreshSucceeded = await RefreshTokenUtils.refreshGraphQlSession();
        if (refreshSucceeded) {
          result =
              await (await _graphQlRepository.getClient())?.query(queryOptions);
        } else {
          _reportError(AppError(
              message:
                  S.current.app_error_salesforce_lost_connection_gglt_label,
              shouldShowSystemNotification: true,
              shouldForceLogout: true));
        }
      }
    }
    // Check for errors
    if (result?.hasException == true) {
      _reportError(AppError(
          message: S.current.app_error_graphql_exception_label(
              (result?.exception ?? '').toString())));
      // return [];
    }
    return result;
  }

  Future<List<ConversationEntry>> _fetchConversationEntries(
      List<String> messagingSessionIds,
      List<ConversationEntry> conversationEntries,
      List<ConversationEntry> flattenedEnhancedConversationEntries,
      QueryResult? result) async {
    final credentials = await _localStorageRepository.getCredentials();

    if (!credentials.isLoggedIn) {
      return [];
    }

    ApiResponse entriesResponse =
        await SalesforceAPIService.fetchConversationEntries(
      credentials.accessToken!,
      credentials.instanceUrl!,
      messagingSessionIds,
    );

    if (entriesResponse is Success) {
      Map<String, dynamic> entriesDecodedJson =
          safeJsonDecode(entriesResponse.response);

      // Convert JSON records into Conversation Entries objects
      List<Map<String, dynamic>> conversationEntryRecords =
          List<Map<String, dynamic>>.from(entriesDecodedJson['records']);
      conversationEntries = conversationEntryRecords
          .map((record) => ConversationEntry.fromJson(record))
          .toList();

      Map<String, String> conversationChannelIdsByScrtUUID = {};
      for (ConversationEntry entry in flattenedEnhancedConversationEntries) {
        if (entry.scrtUUID != null && entry.conversationChannelId != null) {
          if (conversationChannelIdsByScrtUUID[entry.scrtUUID] != null &&
              conversationChannelIdsByScrtUUID[entry.scrtUUID] !=
                  entry.conversationChannelId) {
            loggy.warning(
                '🌊🌊🌊 conversationChannelIdsByScrtUUID already contains key: ${entry.scrtUUID} with value: ${conversationChannelIdsByScrtUUID[entry.scrtUUID]} and we are trying to add value: ${entry.conversationChannelId} for entry: $entry');
          }
          conversationChannelIdsByScrtUUID[entry.scrtUUID!] =
              entry.conversationChannelId!;
        }
      }

      conversationEntries = [
        ...flattenedEnhancedConversationEntries,
        ...conversationEntries
      ];
    } else {
      _reportError(AppError(
          message: S.current.app_error_fetch_conversation_entries_label(
              (result?.exception ?? '').toString())));
    }
    return conversationEntries;
  }

  List<ConversationEntry> _combineEnhancedConversationEntries(
      Map<String, List<ConversationEntry>> enhancedConversationEntries,
      List<MessagingSession> messagingSessions) {
    List<ConversationEntry> flattenedEnhancedConversationEntries =
        enhancedConversationEntries.values
            .expand((element) => element)
            .toList();

    for (ConversationEntry entry in flattenedEnhancedConversationEntries) {
      entry.actorId ??= messagingSessions
          .firstWhereOrNull(
              (session) => session.conversationId == entry.conversationId)
          ?.messagingEndUser
          ?.id
          .toString();
    }
    return flattenedEnhancedConversationEntries;
  }

  Set<MessagingSession>
      _createMessagingSessionsFromEnhancedConversationEntries(
          Map<String, List<ConversationEntry>> enhancedConversationEntries,
          Map<String, MessagingEndUser> enhancedConversationMessagingEndUsers,
          List<MessagingSession> messagingSessions) {
    Iterable<MessagingSession> messagingSessionFromEnhancedConversationEntries =
        enhancedConversationEntries.keys.map((String key) {
      if (enhancedConversationEntries[key] == null) return null;
      List<ConversationEntry>? entries = enhancedConversationEntries[key]!;
      Set<String> enhancedConversationIds = entries
          .map((entry) => entry.conversationId)
          .whereType<String>()
          .toSet();

      for (String enhancedConversationId in enhancedConversationIds) {
        MessagingEndUser? messagingEndUser =
            enhancedConversationMessagingEndUsers[key];

        if (messagingEndUser != null) {
          final messagingSession = messagingSessions.firstWhereOrNull(
              (session) => session.conversationId == enhancedConversationId);

          if (messagingSession == null) {
            _logger.warn(
                'failed to find messagingSession for enhancedConversationId: $enhancedConversationId');
          }

          return messagingSession ?? MessagingSession(
              id: (entries.last.messagingSessionId ??
                      S.current
                          .app_error_messaging_session_by_conversation_label(
                              enhancedConversationId))
                  .toSfId(),
              conversationId: enhancedConversationId,
              createdDate: entries.last.createdDate, // TODO: this was 'last'
              messagingEndUser: messagingEndUser);
        }
      }
    }).whereType<MessagingSession>();

    messagingSessions = [
      ...messagingSessions.toSet(),
      ...messagingSessionFromEnhancedConversationEntries.toSet()
    ];
    return messagingSessions.toSet();
  }

  List<String> _extractMessagingSessionIds(
      List<MessagingSession> messagingSessions,
      Set<String> enhancedConversationIdsToFetch,
      Map<String, String> enhancedConversationIdsToFetchByMessagingSessionId,
      Map<String, MessagingEndUser> enhancedConversationMessagingEndUsers) {
    List<String> messagingSessionIds = messagingSessions
        .map((session) {
          // TODO: support other enhanced channels
          if (session.isEnhanced) {
            if (session.conversationId == null) {
              loggy.warning(
                  'enhanced session found with no conversationId; ignoring');
              return null;
            }

            enhancedConversationIdsToFetch.add(session.conversationId!);
            enhancedConversationIdsToFetchByMessagingSessionId[
                session.id.toString()] = session.conversationId!;

            if (session.messagingEndUser != null) {
              enhancedConversationMessagingEndUsers[session.conversationId!] =
                  session.messagingEndUser!;
            }
            return null;
          }
          return session.id;
        })
        .whereType<String>()
        .toList();
    return messagingSessionIds;
  }

  Future<Map<String, List<ConversationEntry>>> _fetchAllConversations(
      Set<String> enhancedConversationIdsToFetch,
      List<MessagingSession> messagingSessions) async {
    List<ConversationEntries> conversationRecords = await _conversationsUseCase
        .getConversationEntries(enhancedConversationIdsToFetch.toList());

    // Get all the messaging sessions. Note the use of map here which execute the
    // _fetchConversationEntriesByEnhancedConversationId in parallel on the event loop
    Map<String, List<ConversationEntry>> enhancedConversationEntries = {};
    await Future.wait(conversationRecords.map((entry) =>
        _fetchConversationEntriesByEnhancedConversationId(
            enhancedConversationEntries, entry, messagingSessions)));
    return enhancedConversationEntries;
  }

  void _patchEnhancedConversationWithMetadata(
      List<ConversationEntry>? conversationEntries,
      List<MessagingSession> messagingSessions) {
    // "key" is an enhanced conversation id that doesn't map to anything in the app directly
    // conversationEntry.conversationId is messagingSessionId
    conversationEntries?.forEach((entry) {
      MessagingSession? matchedMessagingSession =
          messagingSessions.firstWhereOrNull(
              (session) => session.conversationId == entry.conversationId);

      if (matchedMessagingSession == null) {
        loggy.warning(
            '🚀failed to find matchedMessagingSession for entry: $entry');
        if (entry.scrtUUID != null) {
          loggy.warning('🚀🚀FAILURE entry has scrtUUID: ${entry.scrtUUID}');
        }
      }

      entry.messagingEndUserId =
          matchedMessagingSession?.messagingEndUser?.id.toString();
      entry.messagingSessionId = entry.relatedRecords?.firstOrNull ??
          matchedMessagingSession?.id.toString();

      entry.entryTime = entry.createdDate ??
          matchedMessagingSession
              ?.createdDate ?? // TODO: this was only the second two options
          entry.entryTime;
    });
  }

  // void _patchEnhancedConversationsWithMetadata(
  //     Map<String, List<ConversationEntry>> enhancedConversationEntries,
  //     List<MessagingSession> messagingSessions) {
  //   for (String key in enhancedConversationEntries.keys) {
  //     // "key" is an enhanced conversation id that doesn't map to anything in the app directly
  //     // conversationEntry.conversationId is messagingSessionId
  //     enhancedConversationEntries[key]?.forEach((entry) {
  //       MessagingSession? matchedMessagingSession =
  //           messagingSessions.firstWhereOrNull(
  //               (session) => session.conversationId == entry.conversationId);

  //       if (matchedMessagingSession == null) {
  //         loggy.warning(
  //             '🚀failed to find matchedMessagingSession for entry: $entry');
  //         if (entry.scrtUUID != null) {
  //           loggy.warning('🚀🚀FAILURE entry has scrtUUID: ${entry.scrtUUID}');
  //         }
  //       }

  //       entry.messagingEndUserId =
  //           matchedMessagingSession?.messagingEndUser?.id.toString();
  //       entry.messagingSessionId = entry.relatedRecords?.firstOrNull ??
  //           matchedMessagingSession?.id.toString();

  //       entry.entryTime = entry.createdDate ??
  //           matchedMessagingSession
  //               ?.createdDate ?? // TODO: this was only the second two options
  //           entry.entryTime;
  //     });
  //   }
  // }

  /// Supporting legacy spaghetti code where:
  ///  - ConversationsBloc needs to refresh after this update has been made (but the function that calls this needs to continue so we get through the "initial fetch" that is awaited by methods that need it
  ///  - AiSuggestionsBloc needs to fetch suggestions for all conversations that are open
  Future<void> _updateBlocsWithMessagingSessions(
      Set<MessagingSession> messagingSessions) async {
    await _conversationsUseCase.updateMessagingSessions(messagingSessions);
    GetIt.I<ConversationsBloc>().add(RefreshConversationsEvent());

    for (final conversation in _conversationsUseCase.conversations.values
        .where((convo) => convo.isOpened)) {
      if (conversation.id != null) {
        GetIt.I<AiSuggestionsBloc>()
            .add(GetAiSuggestionEvent(conversation.id!));
      }
    }
  }

  Future<List<ConversationEntry>> fetchNeededContentDocsForConversationEntries(
      final List<ConversationEntry> conversationEntries) async {
    List<String> conversationEntryIdsWithAttachments = conversationEntries
        .where((entry) =>
            entry.hasAttachments == true ||
            entry.attachments?.isNotEmpty == true && entry.id.length < 19)
        .map((entry) => entry.id)
        .toList();

    List<ConversationEntry> updatedConversationEntries = [];

    final SalesforceDataRepository sfDataRepo =
        GetIt.instance.get<SalesforceDataRepository>();
    Map<String, List<ConversationEntryAttachment>> attachmentsByEntryId =
        await sfDataRepo.fetchConversationEntryAttachmentsFromLinkedEntities(
            conversationEntryIdsWithAttachments);

    // fetch conversation entries's documents if there are any
    for (var entry in conversationEntries) {
      if (attachmentsByEntryId.containsKey(entry.id)) {
        entry.attachments = attachmentsByEntryId[entry.id] ?? [];
      }
      updatedConversationEntries.add(entry);
    }

    return updatedConversationEntries;
  }

  Future<LakConversation?> fetchConversationForMessagingSession(
      String messagingSessionId) async {
    String? messagingEndUserId = (await _conversationsUseCase
            .getMEUIdForMessagingSession(messagingSessionId.toSfId()))
        .toString();

    return await createConversation(messagingEndUserId);
    }

  Future<bool> startTypingIndicatorShim(
      String conversationIdentifierUUID) async {
    if ((await _scrtHost)?.isNotEmpty != true ||
        (await _scrtAccessToken)?.isNotEmpty != true) {
      loggy.error('startTypingIndicator failure: scrtHost: ${await _scrtHost}; '
          'scrtAccessToken: ${await _scrtAccessToken}');
      return false;
    }

    try {
      await () async {
        Uri endpoint =
            Uri.parse('${(await _scrtHost)!}/agent/v1/entry/startIndicator');

        String body =
            jsonEncode({"conversationId": conversationIdentifierUUID});

        final request = await httpClient.postUrl(endpoint);

        request.headers.add('accept', '*/*');
        request.headers.contentType =
            ContentType('application', 'json', charset: 'utf-8');
        request.headers
            .add('Authorization', 'Bearer ${await _scrtAccessToken}');
        request.write(body);

        HttpClientResponse response = await request.close();

        try {
          String resStr =
              (await response.transform(utf8.decoder).toList()).join();
        } catch (err) {
          loggy.error('Error Handling startTypingIndicator Response: $err');
        }
      }();
      return true;
    } catch (e) {
      loggy.error('Error starting typing indicator: $e');
    }
    return false;
  }

  Future<bool> endTypingIndicatorShim(String conversationIdentifier) async {
    if ((await _scrtHost)?.isNotEmpty != true ||
        (await _scrtAccessToken)?.isNotEmpty != true) {
      loggy.error(
          'endTypingIndicatorShim failure: scrtHost: ${await _scrtHost}; '
          'scrtAccessToken: ${await _scrtAccessToken}');
      return false;
    }

    try {
      await () async {
        Uri endpoint =
            Uri.parse('${await _scrtHost}/agent/v1/entry/endIndicator');

        String body = jsonEncode({"conversationId": conversationIdentifier});

        final request = await httpClient.postUrl(endpoint);

        request.headers.add('accept', '*/*');
        request.headers.contentType =
            ContentType('application', 'json', charset: 'utf-8');
        request.headers
            .add('Authorization', 'Bearer ${await _scrtAccessToken}');
        request.write(body);

        HttpClientResponse response = await request.close();

        try {
          String resStr =
              (await response.transform(utf8.decoder).toList()).join();
        } catch (err) {
          loggy.error('Error Handling endTypingIndicator Response: $err');
        }
      }();
      return true;
    } catch (e) {
      loggy.error('Error starting typing indicator: $e');
    }
    return false;
  }

  Future<bool> sendDeliveryAcknowledgement(
      String conversationIdentifier, String entryIdentifier) async {
    if ((await _scrtHost)?.isNotEmpty != true ||
        (await _scrtAccessToken)?.isNotEmpty != true) {
      loggy.error(
          'sendDeliveryAcknowledgement failure: scrtHost: ${await _scrtHost}; '
          'scrtAccessToken: ${await _scrtAccessToken}');
      return false;
    }

    try {
      await () async {
        Uri endpoint = Uri.parse(
            '${(await _scrtHost)!}/agent/v1/entry/delivery/acknowledgement');

        String body = jsonEncode({
          "conversationId": conversationIdentifier,
          "entryId": entryIdentifier
        });

        final request = await httpClient.postUrl(endpoint);

        request.headers.add('accept', '*/*');
        request.headers.contentType =
            ContentType('application', 'json', charset: 'utf-8');
        request.headers
            .add('Authorization', 'Bearer ${await _scrtAccessToken}');
        request.write(body);

        HttpClientResponse response = await request.close();

        try {
          String resStr =
              (await response.transform(utf8.decoder).toList()).join();
        } catch (err) {
          loggy.error(
              'Error Handling sendDeliveryAcknowledgement Response: $err');
        }
      }();
      return true;
    } catch (e) {
      loggy.error('Error sendDeliveryAcknowledgement: $e');
    }
    return false;
  }

  Future<bool> sendReadAcknowledgement(
      String conversationIdentifier, String entryIdentifier) async {
    if ((await _scrtHost)?.isNotEmpty != true ||
        (await _scrtAccessToken)?.isNotEmpty != true) {
      loggy.error(
          'sendReadAcknowledgement failure: scrtHost: ${await _scrtHost}; '
          'scrtAccessToken: ${await _scrtAccessToken}');
      return false;
    }

    try {
      await () async {
        Uri endpoint =
            Uri.parse('${await _scrtHost}/agent/v1/entry/read/acknowledgement');

        String body = jsonEncode({
          "conversationId": conversationIdentifier,
          "entryId": entryIdentifier
        });

        final request = await httpClient.postUrl(endpoint);

        request.headers.add('accept', '*/*');
        request.headers.contentType =
            ContentType('application', 'json', charset: 'utf-8');
        request.headers
            .add('Authorization', 'Bearer ${await _scrtAccessToken}');
        request.write(body);

        HttpClientResponse response = await request.close();

        try {
          String resStr =
              (await response.transform(utf8.decoder).toList()).join();
        } catch (err) {
          loggy.error('Error Handling sendReadAcknowledgement Response: $err');
        }
      }();
      return true;
    } catch (e) {
      loggy.error('Error sendReadAcknowledgement: $e');
    }
    return false;
  }

  Future<String> _getConversationHistory(
      String conversationIdentifierUuid) async {
    final scrtHost = await _scrtHost;
    final scrtAccessToken = await _scrtAccessToken;
    if (scrtHost?.isNotEmpty != true || scrtAccessToken?.isNotEmpty != true) {
      _logger.error('getConversationHistory failure: scrtHost: $scrtHost; '
          'scrtAccessToken: $scrtAccessToken');
      return '';
    }
    try {
      Uri endpoint = Uri.parse(
          '${scrtHost!}/agent/v1/conversation/$conversationIdentifierUuid/entries?role=AGENT&limit=200&entryTypes=MESSAGE');

      var startQuery = DateTime.now().millisecondsSinceEpoch;
      loggy.info('GRAPHQL - SCRT CALL HISTORY: START');

      HttpClientRequest request = await httpClient.getUrl(endpoint);

      var endQuery = DateTime.now().millisecondsSinceEpoch;

      loggy.info(
          'GRAPHQL - SCRT CALL HISTORY: END - ${endQuery - startQuery} ms');

      request.headers.add('Authorization', 'Bearer $scrtAccessToken');
      request.headers.add('accept', '*/*');
      request.headers.add('content-type', 'application/json');

      startQuery = DateTime.now().millisecondsSinceEpoch;

      HttpClientResponse response = await request.close();
      if (response.statusCode != 200) {
        loggy.error(
            'getConversationHistory failed for $conversationIdentifierUuid; statusCode: ${response.statusCode}');
        return '';
      }
      String resStr = (await response.transform(utf8.decoder).toList()).join();

      endQuery = DateTime.now().millisecondsSinceEpoch;

      loggy.info(
          'getConversationHistory: ${endQuery - startQuery} ms for $conversationIdentifierUuid');

      return resStr;
    } catch (e) {
      loggy.error('Error getConversationHistory: $e');
      rethrow;
    }
  }
}
