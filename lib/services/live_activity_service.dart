// import 'package:live_activities/live_activities.dart';
// import 'package:live_activities/models/activity_update.dart';
// import 'package:live_activities/models/alert_config.dart';
// import 'package:live_activities/models/live_activity_state.dart';
//
// import 'package:x1440/utils/loggy_utils.dart';
//
// import 'interfaces/custom_notification_service_interface.dart';
//
// /// for implementing iOS Live Activitity; to be used in the LiveActivityViewModel (other uses should reference that VM)
// class LiveActivityService
//     with ServiceLoggy
//     implements LiveActivitiesServiceInterface {
//   final _liveActivitiesPlugin = LiveActivities();
//   final String _deepLinkBasePath;
//   final void Function() _onLiveActivityEnded;
//
//   final List<String> _alertConfigsSent = [];
//
//   LiveActivityService({
//     required String appGroupId,
//     required String urlScheme,
//     required void Function() onLiveActivityEnded,
//     required String deepLinkBasePath,
//   })  : _deepLinkBasePath = deepLinkBasePath,
//         _onLiveActivityEnded = onLiveActivityEnded {
//     init(appGroupId: appGroupId);
//   }
//
//   @override
//   Future<void> init({
//     required String appGroupId,
//     // required String urlScheme,
//   }) async {
//     await _liveActivitiesPlugin.init(
//       appGroupId: appGroupId,
//       // urlScheme: urlScheme,
//     );
//
//     _liveActivitiesPlugin.activityUpdateStream.listen(
//         (ActivityUpdate activityUpdate) {
//       loggy.info('activityUpdateStream received data: $activityUpdate');
//       // activityUpdate
//       activityUpdate.map(
//         active: (activity) {
//           loggy.info('activity update token: $activity');
//         },
//         ended: (activity) {
//           loggy.info('activity ended: $activity');
//           _onLiveActivityEnded();
//         },
//         unknown: (activity) {
//           loggy.info('unknown response: $activity');
//         },
//         stale: (StaleActivityUpdate value) {
//           loggy.info('stale update response: $value');
//         },
//       );
//     }, onError: (error) {
//       loggy.error('activityUpdateStream error: $error');
//     }, onDone: () {
//       loggy.info('activityUpdateStream done');
//     });
//     // _liveActivitiesPlugin.urlSchemeStream().listen((data) {
//     //   // TODO: do we need this for our buttons to work? (I think taking it out may have broken them, but we're using the deep links to handle the connection)
//     //   loggy.info('urlSchemeStream received data: $data');
//     // });
//   }
//
//   bool _isForegrounded = true;
//
//   String?
//       _liveActivityId; // TODO: handle these outside this VM; (how) do we want to support multiple Live Activities? from the VM, should definitely support multiple
//
//   String get deepLinkBasePath => _deepLinkBasePath;
//
//   Map<String, dynamic> getLiveActivityPayload(
//           [Map<String, dynamic> data = const {}]) =>
//       {'deepLinkBasePath': _deepLinkBasePath, ...(data)};
//
//   Future<Map<String, LiveActivityState?>> getAllActivityStates() =>
//       _liveActivitiesPlugin.getAllActivitiesIds().then(
//           (List<String> liveActivityIds) async => Map.fromEntries(
//               await Future.wait(liveActivityIds.map((String activityId) async =>
//                   MapEntry(activityId, await getActivityState(activityId))))));
//
//   Future<int> getNumberOfActiveActivities() =>
//       getAllActivityStates().then((map) => map.values
//           .where(
//               (LiveActivityState? state) => state == LiveActivityState.active)
//           .length);
//
//   Future<LiveActivityState?> getActivityState(activityId) async {
//     return await _liveActivitiesPlugin.getActivityState(activityId);
//   }
//
//   /// NOTE: when using SetActivity, any existing activity will be overwritten; this service does not manage any live activity state
//   @override
//   Future<String?> setActivity(
//       {Map<String, dynamic> data = const {},
//       AlertConfig? alertConfig,
//       bool? isForegrounded}) async {
//     loggy.info(
//         'setting activity with alertConfig: ${alertConfig != null}; isForegrounded: $isForegrounded; _isForegrounded: $_isForegrounded to: $data');
//     _isForegrounded = isForegrounded ?? _isForegrounded;
//     String? id = await _createActivity(data, alertConfig);
//     loggy.info('setActivity returned id: $id');
//     return id;
//   }
//
//   Future<String?> _createActivity(
//       [Map<String, dynamic> data = const {}, AlertConfig? alertConfig]) async {
//     loggy.info('creating activity with newActivity: $data');
//
//     loggy.info(_liveActivityId == null
//         ? '_liveActivityId is null'
//         : 'creating activity with current liveActivityState ${await _liveActivitiesPlugin.getActivityState(_liveActivityId!)}');
//
//     /// if we've lost our live activity reference, end any active activities
//     if (_liveActivityId == null ||
//         (await _liveActivitiesPlugin.getActivityState(_liveActivityId!) !=
//             LiveActivityState.active)) {
//       loggy.info('ending all activities before creating new one');
//       await _liveActivitiesPlugin.endAllActivities();
//
//       _liveActivityId = await _liveActivitiesPlugin.createActivity(
//           getLiveActivityPayload(data),
//           removeWhenAppIsKilled: true);
//
//       loggy.info(
//           'created live activity with id: $_liveActivityId; alertConfig: $alertConfig');
//     }
//
//     await _updateActivity(
//         _liveActivityId!, getLiveActivityPayload(data), alertConfig);
//     return _liveActivityId;
//   }
//
//   Future<dynamic> _updateActivity(String id, Map<String, dynamic> data,
//       [AlertConfig? alertConfig]) async {
//     loggy.info(
//         'updating activity with id: $id; _isForegrounded: $_isForegrounded; activity: $data; alertConfig: $alertConfig');
//
//     Map<String, dynamic>? latestMessage = data['messages']?.first;
//
//     loggy.info(
//         'update activity with latest message: $latestMessage; ${latestMessage?['id']}');
//
//     String? idForAlertConfig = latestMessage?['id'];
//
//     if (alertConfig != null && idForAlertConfig != null) {
//       loggy.info(
//           '🛸🛸🛸🛸🛸🛸🛸🛸🛸🛸🛸🛸🛸🛸\n should send ALERT CONFIG?\n👽👽👽👽👽👽👽👽👽👽');
//       if (_alertConfigsSent.remove(idForAlertConfig)) {
//         alertConfig = null;
//       } else {
//         _alertConfigsSent.add(idForAlertConfig!);
//       }
//
//       if (alertConfig == null) {
//         loggy.info('NOT sending alertConfig: $alertConfig');
//       }
//     }
//
//     return _liveActivitiesPlugin.updateActivity(
//         id, getLiveActivityPayload(data), alertConfig);
//   }
//
//   @override
//   Future<dynamic> endAllActivities() {
//     loggy.info('endAllActivities');
//     return _liveActivitiesPlugin.endAllActivities();
//   }
//
//   @override
//   Future<void> dispose() async {
//     loggy.info('dispose');
//     await endAllActivities();
//     _liveActivitiesPlugin.dispose();
//   }
// }
