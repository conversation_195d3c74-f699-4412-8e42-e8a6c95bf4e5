import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' show post, Response;
import 'package:loggy/loggy.dart';
import 'package:x1440/utils/api_status.dart';
import 'package:x1440/utils/extensions/response_extension.dart';
import 'package:x1440/utils/json_utils.dart';
import 'package:x1440/utils/mocks/demo_payloads.dart';

import '../models/airesponse_message_model.dart';
import '../utils/constants.dart';

enum GPTPersona { Agent, Customer }

/// Custom API Service
/// This class contains all the methods to interact with the Demo APIs
/// provided by <PERSON> (12-21-23) plus any other needed APIs to store/manage data
/// in any of the 1440 Salesforce orgs
@Deprecated('maintained from legacy code')
class Fourteen40PublicSalesforceApiService {
  static Loggy loggy = Loggy('CustomSalesforceApiService');

  static Future<ApiResponse> submitFormData({
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? company,
    String? userJSON,
    String? deviceJSON,
  }) async {
    var url = Uri.parse("${Constants.DEV_ENV_URL}/${Constants.LEAD_ENDPOINT}");

    String requestBody = jsonEncode(
      {
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "company": company,
        "userJSON": userJSON,
        "deviceJSON": deviceJSON,
      },
    );

    Response response = await post(url, body: requestBody);

    if (response.isSuccessful) {
      loggy.info('submitFormData successful');
      return Success(response: response.body);
    } else {
      loggy.error(
          'submitFormData failed with status code: ${response.statusCode}');
      return Failure(code: response.statusCode, response: response.body);
    }
  }

  static Future<ApiResponse> getDemoAiSuggestions(
      String? engine,
      List<AiApiMessage> messages,
      GPTPersona? persona,
      bool isBotConversation) async {
    var url = Uri.parse(Constants.GPT_ENDPOINT);

    if (isBotConversation && persona == GPTPersona.Agent) {
      // persona = GPTPersona.DemoUser;
      messages.add(AiApiMessage.fromJson(
          safeJsonDecode(DemoPayloads.demoUserSuggestionSystemMessagePayload)));
    } else if (isBotConversation && persona == GPTPersona.Customer) {
      // persona = GPTPersona.x1440;
      messages.add(AiApiMessage.fromJson(
          safeJsonDecode(DemoPayloads.x1440SuggestionSystemMessagePayload1)));
      messages.add(AiApiMessage.fromJson(
          safeJsonDecode(DemoPayloads.x1440SuggestionSystemMessagePayload2)));
    }

    List<Map<String, String>> jsonMessages = messages
        .map((message) => {
              ...message.toJson(),
              // if the role is System, leave as is, otherwise:
              // If persona is Customer, then we need to flip the role
              'role': message.role == "system"
                  ? message.role
                  : persona == GPTPersona.Customer
                      ? message.role == "user"
                          ? "agent"
                          : "user"
                      : message.role
            })
        .toList();

    var headers = {'Content-Type': 'application/json'};

    var requestBody = jsonEncode({
      "engine": engine,
      "persona": describeEnum(persona ?? GPTPersona.Agent),
      "messages": jsonMessages,
    });

    print('demodemo-- $requestBody');

    var response = await post(url, headers: headers, body: requestBody);

    if (response.statusCode == 200) {
      return Success(response: response.body);
    } else {
      loggy.info(
          "getDemoAiSuggestions failure with status code: ${response.body}");
      return Failure(code: response.statusCode, response: response.body);
    }
  }
}
