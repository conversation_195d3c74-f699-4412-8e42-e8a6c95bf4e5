import 'package:freezed_annotation/freezed_annotation.dart';

part 'end_user_contact.freezed.dart';
part 'end_user_contact.g.dart';

@freezed
class EndUserContact with _$EndUserContact {
  const factory EndUserContact({
    required String id,
    String? name,
    String? photoUrl,
  }) = _EndUserContact;

  factory EndUserContact.fromJson(Map<String, dynamic> json) =>
      _$EndUserContactFromJson(json);

  factory EndUserContact.fromGraphQlNode(Map<String, dynamic> json) {
    return EndUserContact(
      id: json['Id'],
      name: json['Name']?['value'],
      photoUrl: json['PhotoUrl']?['value'],
    );
  }
}