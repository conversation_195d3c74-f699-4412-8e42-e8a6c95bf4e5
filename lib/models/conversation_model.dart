import 'legacy/legacy_liveagentkit_types.dart';
import 'sf_id.dart';

enum ConversationStatus {
  opened,
  closed,
  //sf:"new"
  outbound,
}

class Conversation {
  @Deprecated('legacy support')
  final LakConversation? lakConversation;

  final ConversationStatus status;

  SfId? get id => lakConversation?.sfId;
  String? get scrtUUID => lakConversation?.scrtUUID;

  bool get showChatField => !isOutbound;

  bool get isOpened => status == ConversationStatus.opened;

  bool get isOutbound => status == ConversationStatus.outbound;

  bool get newMessageIsNewOutbound => status == ConversationStatus.closed;

  bool get canEndConversation => isOpened && !isOutbound;

  bool get isWhatsApp => lakConversation?.isWhatsApp == true;

  Conversation({
    this.status = ConversationStatus.closed,
    this.lakConversation,
  });

  bool get isEnhanced => scrtUUID?.isNotEmpty == true; // TODO: figure out a better way to do this, maybe with enums?

  Conversation copyWith({
    ConversationStatus? status,
    LakConversation? lakConversation,
  }) {
    return Conversation(
      status: status ?? this.status,
      lakConversation: (lakConversation ?? this.lakConversation)?.copyWith(),
    );
  }

  void addMessage(LakMessage msg) => lakConversation?.addMessage(msg);
}