// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'end_user_contact.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EndUserContact _$EndUserContactFromJson(Map<String, dynamic> json) {
  return _EndUserContact.fromJson(json);
}

/// @nodoc
mixin _$EndUserContact {
  String get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get photoUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EndUserContactCopyWith<EndUserContact> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EndUserContactCopyWith<$Res> {
  factory $EndUserContactCopyWith(
          EndUserContact value, $Res Function(EndUserContact) then) =
      _$EndUserContactCopyWithImpl<$Res, EndUserContact>;
  @useResult
  $Res call({String id, String? name, String? photoUrl});
}

/// @nodoc
class _$EndUserContactCopyWithImpl<$Res, $Val extends EndUserContact>
    implements $EndUserContactCopyWith<$Res> {
  _$EndUserContactCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? photoUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EndUserContactImplCopyWith<$Res>
    implements $EndUserContactCopyWith<$Res> {
  factory _$$EndUserContactImplCopyWith(_$EndUserContactImpl value,
          $Res Function(_$EndUserContactImpl) then) =
      __$$EndUserContactImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String? name, String? photoUrl});
}

/// @nodoc
class __$$EndUserContactImplCopyWithImpl<$Res>
    extends _$EndUserContactCopyWithImpl<$Res, _$EndUserContactImpl>
    implements _$$EndUserContactImplCopyWith<$Res> {
  __$$EndUserContactImplCopyWithImpl(
      _$EndUserContactImpl _value, $Res Function(_$EndUserContactImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? photoUrl = freezed,
  }) {
    return _then(_$EndUserContactImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EndUserContactImpl implements _EndUserContact {
  const _$EndUserContactImpl({required this.id, this.name, this.photoUrl});

  factory _$EndUserContactImpl.fromJson(Map<String, dynamic> json) =>
      _$$EndUserContactImplFromJson(json);

  @override
  final String id;
  @override
  final String? name;
  @override
  final String? photoUrl;

  @override
  String toString() {
    return 'EndUserContact(id: $id, name: $name, photoUrl: $photoUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EndUserContactImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, photoUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EndUserContactImplCopyWith<_$EndUserContactImpl> get copyWith =>
      __$$EndUserContactImplCopyWithImpl<_$EndUserContactImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EndUserContactImplToJson(
      this,
    );
  }
}

abstract class _EndUserContact implements EndUserContact {
  const factory _EndUserContact(
      {required final String id,
      final String? name,
      final String? photoUrl}) = _$EndUserContactImpl;

  factory _EndUserContact.fromJson(Map<String, dynamic> json) =
      _$EndUserContactImpl.fromJson;

  @override
  String get id;
  @override
  String? get name;
  @override
  String? get photoUrl;
  @override
  @JsonKey(ignore: true)
  _$$EndUserContactImplCopyWith<_$EndUserContactImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
