// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_session_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingSession _$MessagingSessionFromJson(Map<String, dynamic> json) {
  return _MessagingSession.fromJson(json);
}

/// @nodoc
mixin _$MessagingSession {
  @ParseSfIdConverter()
  SfId get id => throw _privateConstructorUsedError;
  String? get channelName => throw _privateConstructorUsedError;
  String? get channelType => throw _privateConstructorUsedError;
  String? get caseId => throw _privateConstructorUsedError;
  String? get leadId => throw _privateConstructorUsedError;
  String? get opportunityId => throw _privateConstructorUsedError;

  /// NOTE: important! these "conversationId"'s are not the same as the ones in the Conversation model. Conversations use the MessagingEndUserId as the ConversationId. These ConversationIds are from SF -- they only exist for enhanced conversations, have a prefix of "0dw" and are called "conversationIdentifiers" in many places in Salesforce
  String? get conversationId => throw _privateConstructorUsedError;
  DateTime? get createdDate => throw _privateConstructorUsedError;
  String? get ownerId => throw _privateConstructorUsedError;
  MessagingSessionStatus? get status => throw _privateConstructorUsedError;
  EndUserContact? get endUserContact => throw _privateConstructorUsedError;
  MessagingEndUser? get messagingEndUser => throw _privateConstructorUsedError;
  String? get messagingChannelId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingSessionCopyWith<MessagingSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingSessionCopyWith<$Res> {
  factory $MessagingSessionCopyWith(
          MessagingSession value, $Res Function(MessagingSession) then) =
      _$MessagingSessionCopyWithImpl<$Res, MessagingSession>;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId id,
      String? channelName,
      String? channelType,
      String? caseId,
      String? leadId,
      String? opportunityId,
      String? conversationId,
      DateTime? createdDate,
      String? ownerId,
      MessagingSessionStatus? status,
      EndUserContact? endUserContact,
      MessagingEndUser? messagingEndUser,
      String? messagingChannelId});

  $SfIdCopyWith<$Res> get id;
  $EndUserContactCopyWith<$Res>? get endUserContact;
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser;
}

/// @nodoc
class _$MessagingSessionCopyWithImpl<$Res, $Val extends MessagingSession>
    implements $MessagingSessionCopyWith<$Res> {
  _$MessagingSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? channelName = freezed,
    Object? channelType = freezed,
    Object? caseId = freezed,
    Object? leadId = freezed,
    Object? opportunityId = freezed,
    Object? conversationId = freezed,
    Object? createdDate = freezed,
    Object? ownerId = freezed,
    Object? status = freezed,
    Object? endUserContact = freezed,
    Object? messagingEndUser = freezed,
    Object? messagingChannelId = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      channelType: freezed == channelType
          ? _value.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String?,
      caseId: freezed == caseId
          ? _value.caseId
          : caseId // ignore: cast_nullable_to_non_nullable
              as String?,
      leadId: freezed == leadId
          ? _value.leadId
          : leadId // ignore: cast_nullable_to_non_nullable
              as String?,
      opportunityId: freezed == opportunityId
          ? _value.opportunityId
          : opportunityId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessagingSessionStatus?,
      endUserContact: freezed == endUserContact
          ? _value.endUserContact
          : endUserContact // ignore: cast_nullable_to_non_nullable
              as EndUserContact?,
      messagingEndUser: freezed == messagingEndUser
          ? _value.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as MessagingEndUser?,
      messagingChannelId: freezed == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_value.id, (value) {
      return _then(_value.copyWith(id: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $EndUserContactCopyWith<$Res>? get endUserContact {
    if (_value.endUserContact == null) {
      return null;
    }

    return $EndUserContactCopyWith<$Res>(_value.endUserContact!, (value) {
      return _then(_value.copyWith(endUserContact: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser {
    if (_value.messagingEndUser == null) {
      return null;
    }

    return $MessagingEndUserCopyWith<$Res>(_value.messagingEndUser!, (value) {
      return _then(_value.copyWith(messagingEndUser: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingSessionImplCopyWith<$Res>
    implements $MessagingSessionCopyWith<$Res> {
  factory _$$MessagingSessionImplCopyWith(_$MessagingSessionImpl value,
          $Res Function(_$MessagingSessionImpl) then) =
      __$$MessagingSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId id,
      String? channelName,
      String? channelType,
      String? caseId,
      String? leadId,
      String? opportunityId,
      String? conversationId,
      DateTime? createdDate,
      String? ownerId,
      MessagingSessionStatus? status,
      EndUserContact? endUserContact,
      MessagingEndUser? messagingEndUser,
      String? messagingChannelId});

  @override
  $SfIdCopyWith<$Res> get id;
  @override
  $EndUserContactCopyWith<$Res>? get endUserContact;
  @override
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser;
}

/// @nodoc
class __$$MessagingSessionImplCopyWithImpl<$Res>
    extends _$MessagingSessionCopyWithImpl<$Res, _$MessagingSessionImpl>
    implements _$$MessagingSessionImplCopyWith<$Res> {
  __$$MessagingSessionImplCopyWithImpl(_$MessagingSessionImpl _value,
      $Res Function(_$MessagingSessionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? channelName = freezed,
    Object? channelType = freezed,
    Object? caseId = freezed,
    Object? leadId = freezed,
    Object? opportunityId = freezed,
    Object? conversationId = freezed,
    Object? createdDate = freezed,
    Object? ownerId = freezed,
    Object? status = freezed,
    Object? endUserContact = freezed,
    Object? messagingEndUser = freezed,
    Object? messagingChannelId = freezed,
  }) {
    return _then(_$MessagingSessionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      channelType: freezed == channelType
          ? _value.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String?,
      caseId: freezed == caseId
          ? _value.caseId
          : caseId // ignore: cast_nullable_to_non_nullable
              as String?,
      leadId: freezed == leadId
          ? _value.leadId
          : leadId // ignore: cast_nullable_to_non_nullable
              as String?,
      opportunityId: freezed == opportunityId
          ? _value.opportunityId
          : opportunityId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessagingSessionStatus?,
      endUserContact: freezed == endUserContact
          ? _value.endUserContact
          : endUserContact // ignore: cast_nullable_to_non_nullable
              as EndUserContact?,
      messagingEndUser: freezed == messagingEndUser
          ? _value.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as MessagingEndUser?,
      messagingChannelId: freezed == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingSessionImpl extends _MessagingSession {
  const _$MessagingSessionImpl(
      {@ParseSfIdConverter() required this.id,
      this.channelName,
      this.channelType,
      this.caseId,
      this.leadId,
      this.opportunityId,
      this.conversationId,
      this.createdDate,
      this.ownerId,
      this.status,
      this.endUserContact,
      this.messagingEndUser,
      this.messagingChannelId})
      : super._();

  factory _$MessagingSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingSessionImplFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId id;
  @override
  final String? channelName;
  @override
  final String? channelType;
  @override
  final String? caseId;
  @override
  final String? leadId;
  @override
  final String? opportunityId;

  /// NOTE: important! these "conversationId"'s are not the same as the ones in the Conversation model. Conversations use the MessagingEndUserId as the ConversationId. These ConversationIds are from SF -- they only exist for enhanced conversations, have a prefix of "0dw" and are called "conversationIdentifiers" in many places in Salesforce
  @override
  final String? conversationId;
  @override
  final DateTime? createdDate;
  @override
  final String? ownerId;
  @override
  final MessagingSessionStatus? status;
  @override
  final EndUserContact? endUserContact;
  @override
  final MessagingEndUser? messagingEndUser;
  @override
  final String? messagingChannelId;

  @override
  String toString() {
    return 'MessagingSession(id: $id, channelName: $channelName, channelType: $channelType, caseId: $caseId, leadId: $leadId, opportunityId: $opportunityId, conversationId: $conversationId, createdDate: $createdDate, ownerId: $ownerId, status: $status, endUserContact: $endUserContact, messagingEndUser: $messagingEndUser, messagingChannelId: $messagingChannelId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingSessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.caseId, caseId) || other.caseId == caseId) &&
            (identical(other.leadId, leadId) || other.leadId == leadId) &&
            (identical(other.opportunityId, opportunityId) ||
                other.opportunityId == opportunityId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.endUserContact, endUserContact) ||
                other.endUserContact == endUserContact) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      channelName,
      channelType,
      caseId,
      leadId,
      opportunityId,
      conversationId,
      createdDate,
      ownerId,
      status,
      endUserContact,
      messagingEndUser,
      messagingChannelId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingSessionImplCopyWith<_$MessagingSessionImpl> get copyWith =>
      __$$MessagingSessionImplCopyWithImpl<_$MessagingSessionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingSessionImplToJson(
      this,
    );
  }
}

abstract class _MessagingSession extends MessagingSession {
  const factory _MessagingSession(
      {@ParseSfIdConverter() required final SfId id,
      final String? channelName,
      final String? channelType,
      final String? caseId,
      final String? leadId,
      final String? opportunityId,
      final String? conversationId,
      final DateTime? createdDate,
      final String? ownerId,
      final MessagingSessionStatus? status,
      final EndUserContact? endUserContact,
      final MessagingEndUser? messagingEndUser,
      final String? messagingChannelId}) = _$MessagingSessionImpl;
  const _MessagingSession._() : super._();

  factory _MessagingSession.fromJson(Map<String, dynamic> json) =
      _$MessagingSessionImpl.fromJson;

  @override
  @ParseSfIdConverter()
  SfId get id;
  @override
  String? get channelName;
  @override
  String? get channelType;
  @override
  String? get caseId;
  @override
  String? get leadId;
  @override
  String? get opportunityId;
  @override

  /// NOTE: important! these "conversationId"'s are not the same as the ones in the Conversation model. Conversations use the MessagingEndUserId as the ConversationId. These ConversationIds are from SF -- they only exist for enhanced conversations, have a prefix of "0dw" and are called "conversationIdentifiers" in many places in Salesforce
  String? get conversationId;
  @override
  DateTime? get createdDate;
  @override
  String? get ownerId;
  @override
  MessagingSessionStatus? get status;
  @override
  EndUserContact? get endUserContact;
  @override
  MessagingEndUser? get messagingEndUser;
  @override
  String? get messagingChannelId;
  @override
  @JsonKey(ignore: true)
  _$$MessagingSessionImplCopyWith<_$MessagingSessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
