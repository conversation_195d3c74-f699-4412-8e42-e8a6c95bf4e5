// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tapped_notification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TappedNotification _$TappedNotificationFromJson(Map<String, dynamic> json) {
  return _TappedNotification.fromJson(json);
}

/// @nodoc
mixin _$TappedNotification {
  String? get notificationId => throw _privateConstructorUsedError;
  String? get conversationId => throw _privateConstructorUsedError;
  String? get categoryId => throw _privateConstructorUsedError;
  String? get action => throw _privateConstructorUsedError;
  Map<String, dynamic>? get payload => throw _privateConstructorUsedError;
  QueueReceiveMessage? get queueReceiveMessage =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TappedNotificationCopyWith<TappedNotification> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TappedNotificationCopyWith<$Res> {
  factory $TappedNotificationCopyWith(
          TappedNotification value, $Res Function(TappedNotification) then) =
      _$TappedNotificationCopyWithImpl<$Res, TappedNotification>;
  @useResult
  $Res call(
      {String? notificationId,
      String? conversationId,
      String? categoryId,
      String? action,
      Map<String, dynamic>? payload,
      QueueReceiveMessage? queueReceiveMessage});

  $QueueReceiveMessageCopyWith<$Res>? get queueReceiveMessage;
}

/// @nodoc
class _$TappedNotificationCopyWithImpl<$Res, $Val extends TappedNotification>
    implements $TappedNotificationCopyWith<$Res> {
  _$TappedNotificationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationId = freezed,
    Object? conversationId = freezed,
    Object? categoryId = freezed,
    Object? action = freezed,
    Object? payload = freezed,
    Object? queueReceiveMessage = freezed,
  }) {
    return _then(_value.copyWith(
      notificationId: freezed == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      queueReceiveMessage: freezed == queueReceiveMessage
          ? _value.queueReceiveMessage
          : queueReceiveMessage // ignore: cast_nullable_to_non_nullable
              as QueueReceiveMessage?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $QueueReceiveMessageCopyWith<$Res>? get queueReceiveMessage {
    if (_value.queueReceiveMessage == null) {
      return null;
    }

    return $QueueReceiveMessageCopyWith<$Res>(_value.queueReceiveMessage!,
        (value) {
      return _then(_value.copyWith(queueReceiveMessage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TappedNotificationImplCopyWith<$Res>
    implements $TappedNotificationCopyWith<$Res> {
  factory _$$TappedNotificationImplCopyWith(_$TappedNotificationImpl value,
          $Res Function(_$TappedNotificationImpl) then) =
      __$$TappedNotificationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? notificationId,
      String? conversationId,
      String? categoryId,
      String? action,
      Map<String, dynamic>? payload,
      QueueReceiveMessage? queueReceiveMessage});

  @override
  $QueueReceiveMessageCopyWith<$Res>? get queueReceiveMessage;
}

/// @nodoc
class __$$TappedNotificationImplCopyWithImpl<$Res>
    extends _$TappedNotificationCopyWithImpl<$Res, _$TappedNotificationImpl>
    implements _$$TappedNotificationImplCopyWith<$Res> {
  __$$TappedNotificationImplCopyWithImpl(_$TappedNotificationImpl _value,
      $Res Function(_$TappedNotificationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationId = freezed,
    Object? conversationId = freezed,
    Object? categoryId = freezed,
    Object? action = freezed,
    Object? payload = freezed,
    Object? queueReceiveMessage = freezed,
  }) {
    return _then(_$TappedNotificationImpl(
      notificationId: freezed == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
      action: freezed == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _value._payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      queueReceiveMessage: freezed == queueReceiveMessage
          ? _value.queueReceiveMessage
          : queueReceiveMessage // ignore: cast_nullable_to_non_nullable
              as QueueReceiveMessage?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TappedNotificationImpl extends _TappedNotification {
  const _$TappedNotificationImpl(
      {this.notificationId,
      this.conversationId,
      this.categoryId,
      this.action,
      final Map<String, dynamic>? payload,
      this.queueReceiveMessage})
      : _payload = payload,
        super._();

  factory _$TappedNotificationImpl.fromJson(Map<String, dynamic> json) =>
      _$$TappedNotificationImplFromJson(json);

  @override
  final String? notificationId;
  @override
  final String? conversationId;
  @override
  final String? categoryId;
  @override
  final String? action;
  final Map<String, dynamic>? _payload;
  @override
  Map<String, dynamic>? get payload {
    final value = _payload;
    if (value == null) return null;
    if (_payload is EqualUnmodifiableMapView) return _payload;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final QueueReceiveMessage? queueReceiveMessage;

  @override
  String toString() {
    return 'TappedNotification(notificationId: $notificationId, conversationId: $conversationId, categoryId: $categoryId, action: $action, payload: $payload, queueReceiveMessage: $queueReceiveMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TappedNotificationImpl &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.action, action) || other.action == action) &&
            const DeepCollectionEquality().equals(other._payload, _payload) &&
            (identical(other.queueReceiveMessage, queueReceiveMessage) ||
                other.queueReceiveMessage == queueReceiveMessage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      notificationId,
      conversationId,
      categoryId,
      action,
      const DeepCollectionEquality().hash(_payload),
      queueReceiveMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TappedNotificationImplCopyWith<_$TappedNotificationImpl> get copyWith =>
      __$$TappedNotificationImplCopyWithImpl<_$TappedNotificationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TappedNotificationImplToJson(
      this,
    );
  }
}

abstract class _TappedNotification extends TappedNotification {
  const factory _TappedNotification(
          {final String? notificationId,
          final String? conversationId,
          final String? categoryId,
          final String? action,
          final Map<String, dynamic>? payload,
          final QueueReceiveMessage? queueReceiveMessage}) =
      _$TappedNotificationImpl;
  const _TappedNotification._() : super._();

  factory _TappedNotification.fromJson(Map<String, dynamic> json) =
      _$TappedNotificationImpl.fromJson;

  @override
  String? get notificationId;
  @override
  String? get conversationId;
  @override
  String? get categoryId;
  @override
  String? get action;
  @override
  Map<String, dynamic>? get payload;
  @override
  QueueReceiveMessage? get queueReceiveMessage;
  @override
  @JsonKey(ignore: true)
  _$$TappedNotificationImplCopyWith<_$TappedNotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
