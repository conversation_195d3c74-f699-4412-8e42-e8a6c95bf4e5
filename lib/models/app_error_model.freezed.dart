// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppError _$AppErrorFromJson(Map<String, dynamic> json) {
  return _AppError.fromJson(json);
}

/// @nodoc
mixin _$AppError {
  String? get message => throw _privateConstructorUsedError;
  String? get details => throw _privateConstructorUsedError;
  String? get iconPath => throw _privateConstructorUsedError;
  int? get statusCode => throw _privateConstructorUsedError;
  bool get isAppException => throw _privateConstructorUsedError;
  bool get showErrorOverlay => throw _privateConstructorUsedError;
  bool get shouldForceLogout => throw _privateConstructorUsedError;
  bool get shouldShowSystemNotification => throw _privateConstructorUsedError;

  /// if another error should take precedence and ignore this one
  bool get isReplaceable => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AppErrorCopyWith<AppError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppErrorCopyWith<$Res> {
  factory $AppErrorCopyWith(AppError value, $Res Function(AppError) then) =
      _$AppErrorCopyWithImpl<$Res, AppError>;
  @useResult
  $Res call(
      {String? message,
      String? details,
      String? iconPath,
      int? statusCode,
      bool isAppException,
      bool showErrorOverlay,
      bool shouldForceLogout,
      bool shouldShowSystemNotification,
      bool isReplaceable});
}

/// @nodoc
class _$AppErrorCopyWithImpl<$Res, $Val extends AppError>
    implements $AppErrorCopyWith<$Res> {
  _$AppErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
    Object? details = freezed,
    Object? iconPath = freezed,
    Object? statusCode = freezed,
    Object? isAppException = null,
    Object? showErrorOverlay = null,
    Object? shouldForceLogout = null,
    Object? shouldShowSystemNotification = null,
    Object? isReplaceable = null,
  }) {
    return _then(_value.copyWith(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      iconPath: freezed == iconPath
          ? _value.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      isAppException: null == isAppException
          ? _value.isAppException
          : isAppException // ignore: cast_nullable_to_non_nullable
              as bool,
      showErrorOverlay: null == showErrorOverlay
          ? _value.showErrorOverlay
          : showErrorOverlay // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldForceLogout: null == shouldForceLogout
          ? _value.shouldForceLogout
          : shouldForceLogout // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldShowSystemNotification: null == shouldShowSystemNotification
          ? _value.shouldShowSystemNotification
          : shouldShowSystemNotification // ignore: cast_nullable_to_non_nullable
              as bool,
      isReplaceable: null == isReplaceable
          ? _value.isReplaceable
          : isReplaceable // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$AppErrorImplCopyWith(
          _$AppErrorImpl value, $Res Function(_$AppErrorImpl) then) =
      __$$AppErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? message,
      String? details,
      String? iconPath,
      int? statusCode,
      bool isAppException,
      bool showErrorOverlay,
      bool shouldForceLogout,
      bool shouldShowSystemNotification,
      bool isReplaceable});
}

/// @nodoc
class __$$AppErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$AppErrorImpl>
    implements _$$AppErrorImplCopyWith<$Res> {
  __$$AppErrorImplCopyWithImpl(
      _$AppErrorImpl _value, $Res Function(_$AppErrorImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
    Object? details = freezed,
    Object? iconPath = freezed,
    Object? statusCode = freezed,
    Object? isAppException = null,
    Object? showErrorOverlay = null,
    Object? shouldForceLogout = null,
    Object? shouldShowSystemNotification = null,
    Object? isReplaceable = null,
  }) {
    return _then(_$AppErrorImpl(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      iconPath: freezed == iconPath
          ? _value.iconPath
          : iconPath // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      isAppException: null == isAppException
          ? _value.isAppException
          : isAppException // ignore: cast_nullable_to_non_nullable
              as bool,
      showErrorOverlay: null == showErrorOverlay
          ? _value.showErrorOverlay
          : showErrorOverlay // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldForceLogout: null == shouldForceLogout
          ? _value.shouldForceLogout
          : shouldForceLogout // ignore: cast_nullable_to_non_nullable
              as bool,
      shouldShowSystemNotification: null == shouldShowSystemNotification
          ? _value.shouldShowSystemNotification
          : shouldShowSystemNotification // ignore: cast_nullable_to_non_nullable
              as bool,
      isReplaceable: null == isReplaceable
          ? _value.isReplaceable
          : isReplaceable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppErrorImpl extends _AppError {
  const _$AppErrorImpl(
      {this.message,
      this.details,
      this.iconPath,
      this.statusCode,
      this.isAppException = false,
      this.showErrorOverlay = false,
      this.shouldForceLogout = false,
      this.shouldShowSystemNotification = false,
      this.isReplaceable = false})
      : super._();

  factory _$AppErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppErrorImplFromJson(json);

  @override
  final String? message;
  @override
  final String? details;
  @override
  final String? iconPath;
  @override
  final int? statusCode;
  @override
  @JsonKey()
  final bool isAppException;
  @override
  @JsonKey()
  final bool showErrorOverlay;
  @override
  @JsonKey()
  final bool shouldForceLogout;
  @override
  @JsonKey()
  final bool shouldShowSystemNotification;

  /// if another error should take precedence and ignore this one
  @override
  @JsonKey()
  final bool isReplaceable;

  @override
  String toString() {
    return 'AppError(message: $message, details: $details, iconPath: $iconPath, statusCode: $statusCode, isAppException: $isAppException, showErrorOverlay: $showErrorOverlay, shouldForceLogout: $shouldForceLogout, shouldShowSystemNotification: $shouldShowSystemNotification, isReplaceable: $isReplaceable)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppErrorImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.details, details) || other.details == details) &&
            (identical(other.iconPath, iconPath) ||
                other.iconPath == iconPath) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.isAppException, isAppException) ||
                other.isAppException == isAppException) &&
            (identical(other.showErrorOverlay, showErrorOverlay) ||
                other.showErrorOverlay == showErrorOverlay) &&
            (identical(other.shouldForceLogout, shouldForceLogout) ||
                other.shouldForceLogout == shouldForceLogout) &&
            (identical(other.shouldShowSystemNotification,
                    shouldShowSystemNotification) ||
                other.shouldShowSystemNotification ==
                    shouldShowSystemNotification) &&
            (identical(other.isReplaceable, isReplaceable) ||
                other.isReplaceable == isReplaceable));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      details,
      iconPath,
      statusCode,
      isAppException,
      showErrorOverlay,
      shouldForceLogout,
      shouldShowSystemNotification,
      isReplaceable);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppErrorImplCopyWith<_$AppErrorImpl> get copyWith =>
      __$$AppErrorImplCopyWithImpl<_$AppErrorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppErrorImplToJson(
      this,
    );
  }
}

abstract class _AppError extends AppError {
  const factory _AppError(
      {final String? message,
      final String? details,
      final String? iconPath,
      final int? statusCode,
      final bool isAppException,
      final bool showErrorOverlay,
      final bool shouldForceLogout,
      final bool shouldShowSystemNotification,
      final bool isReplaceable}) = _$AppErrorImpl;
  const _AppError._() : super._();

  factory _AppError.fromJson(Map<String, dynamic> json) =
      _$AppErrorImpl.fromJson;

  @override
  String? get message;
  @override
  String? get details;
  @override
  String? get iconPath;
  @override
  int? get statusCode;
  @override
  bool get isAppException;
  @override
  bool get showErrorOverlay;
  @override
  bool get shouldForceLogout;
  @override
  bool get shouldShowSystemNotification;
  @override

  /// if another error should take precedence and ignore this one
  bool get isReplaceable;
  @override
  @JsonKey(ignore: true)
  _$$AppErrorImplCopyWith<_$AppErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
