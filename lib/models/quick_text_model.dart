class QuickText {
  final String id;
  String? category;
  String? channel;
  bool? isInsertable;
  String? lastReferencedDate;
  String? lastViewedDate;
  String? message;
  String? name;
  String? ownerId;
  String? sourceType;

  QuickText({
    required this.id,
    this.category,
    this.channel,
    this.isInsertable,
    this.lastReferencedDate,
    this.lastViewedDate,
    this.message,
    this.name,
    this.ownerId,
    this.sourceType,
  });

  @override
  String toString() {
    return 'QuickText{id: $id, category: $category, channel: $channel,  isInsertable: $isInsertable, lastReferencedDate: $lastReferencedDate, lastViewedDate: $lastViewedDate, message: $message, name: $name, ownerId: $ownerId, sourceType: $sourceType}';
  }
}
