/// Model for user service presence
/// Represents a user's presence status in a Salesforce Org. This model is used
/// when querying for Active Agents in Salesforce Omnichannel. It contains the
/// ServicePresenceStatusId which helps determine which channel they are
/// available on to see if they can accept a transferred conversation in the same
/// channel. IsCurrentState field is used to determine if the user is currently available.
class UserServicePresence {
  String? id;
  String? servicePresenceStatusId;
  String? userId;
  String? userFullPhotoUrl;
  String? userName;
  bool? isCurrentState;

  UserServicePresence({
    required this.id,
    required this.servicePresenceStatusId,
    required this.userId,
    required this.userFullPhotoUrl,
    required this.userName,
    required this.isCurrentState,
  });

  factory UserServicePresence.fromJson(Map<String, dynamic> json) {
    return UserServicePresence(
      id: json['Id'],
      servicePresenceStatusId: json['ServicePresenceStatusId'],
      userId: json['User']['Id'],
      userFullPhotoUrl: json['User']['FullPhotoUrl'],
      userName: json['User']['Name'],
      isCurrentState: json['IsCurrentState'],
    );
  }

  @override
  String toString() {
    return 'UserServicePresence{id: $id, servicePresenceStatusId: $servicePresenceStatusId, userId: $userId, userFullPhotoUrl: $userFullPhotoUrl, userName: $userName, isCurrentState: $isCurrentState}';
  }
}
