import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';
import 'package:x1440/utils/extensions/string_extension.dart';

part 'sf_id.freezed.dart';

part 'sf_id.g.dart';

/// Handling Standard 15/18 character Salesforce ID's (where Salesforce tends to return 18 character ones, but we only want/need the 15 and in some cases must use the 15)
/// IMPORTANT NOTE: Salesforce also has UUID's that don't match this structure and this class is not intended to handle those. They usually have "Identifier" instead of "Id" in the name (but the ConversationIdentifier is sometimes called ConversationId, so be wary!)
class ParseSfIdConverter implements JsonConverter<SfId, Object?> {
  const ParseSfIdConverter();

  @override
  SfId fromJson(Object? json) {
    if (json is String) {
      return SfId(json);
    }
    return SfId();
  }

  @override
  Object toJson(SfId object) => object.value;
}

@freezed
@embeddedOnFreezed
class SfId with _$SfId {
  SfId._();

  factory SfId([@ParseSfIdConverter() @Default('') String value]) = _SfId;

  factory SfId.fromJson(Map<String, dynamic> json) => _$SfIdFromJson(json);

  @override
  String toString() => value.substringOrMax(0, 15);

  String get normalizedValue =>
      value.substring(0, value.length > 15 ? 15 : value.length);

  bool startsWith(String s) => value.startsWith(s);

  @override
  bool operator ==(Object other) =>
      other is SfId && normalizedValue == other.normalizedValue;

  @override
  int get hashCode => normalizedValue.hashCode;

  bool get isEmpty => value.isEmpty;

  bool get isNotEmpty => value.isNotEmpty;
}

extension ToSfId on String {
  SfId toSfId() => SfId(this);
}

extension ToSfIdLakConvo on LakConversation {
  SfId get sfId => SfId(id);
}
