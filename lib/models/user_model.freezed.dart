// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  @JsonKey(name: 'Id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'Username')
  String? get username => throw _privateConstructorUsedError;
  @JsonKey(name: 'LastName')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'FirstName')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'Name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'Street')
  String? get street => throw _privateConstructorUsedError;
  @JsonKey(name: 'City')
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: 'State')
  String? get state => throw _privateConstructorUsedError;
  @JsonKey(name: 'PostalCode')
  String? get postalCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'Country')
  String? get country => throw _privateConstructorUsedError;
  @JsonKey(name: 'Email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'Phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone => throw _privateConstructorUsedError;
  @JsonKey(name: 'Alias')
  String? get alias => throw _privateConstructorUsedError;
  @JsonKey(name: 'Local_SMS_Channel_Id__c')
  String? get localSMSChannelId => throw _privateConstructorUsedError;
  @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
  String? get localWhatsAppChannelId => throw _privateConstructorUsedError;
  @JsonKey(name: 'ContactId')
  String? get contactId => throw _privateConstructorUsedError;
  @JsonKey(name: 'AccountId')
  String? get accountId => throw _privateConstructorUsedError;
  @JsonKey(name: 'FullPhotoUrl')
  String? get fullPhotoUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Username') String? username,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'Street') String? street,
      @JsonKey(name: 'City') String? city,
      @JsonKey(name: 'State') String? state,
      @JsonKey(name: 'PostalCode') String? postalCode,
      @JsonKey(name: 'Country') String? country,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Alias') String? alias,
      @JsonKey(name: 'Local_SMS_Channel_Id__c') String? localSMSChannelId,
      @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
      String? localWhatsAppChannelId,
      @JsonKey(name: 'ContactId') String? contactId,
      @JsonKey(name: 'AccountId') String? accountId,
      @JsonKey(name: 'FullPhotoUrl') String? fullPhotoUrl});
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? username = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? name = freezed,
    Object? street = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? postalCode = freezed,
    Object? country = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? mobilePhone = freezed,
    Object? alias = freezed,
    Object? localSMSChannelId = freezed,
    Object? localWhatsAppChannelId = freezed,
    Object? contactId = freezed,
    Object? accountId = freezed,
    Object? fullPhotoUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      alias: freezed == alias
          ? _value.alias
          : alias // ignore: cast_nullable_to_non_nullable
              as String?,
      localSMSChannelId: freezed == localSMSChannelId
          ? _value.localSMSChannelId
          : localSMSChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      localWhatsAppChannelId: freezed == localWhatsAppChannelId
          ? _value.localWhatsAppChannelId
          : localWhatsAppChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      contactId: freezed == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      fullPhotoUrl: freezed == fullPhotoUrl
          ? _value.fullPhotoUrl
          : fullPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Username') String? username,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'Street') String? street,
      @JsonKey(name: 'City') String? city,
      @JsonKey(name: 'State') String? state,
      @JsonKey(name: 'PostalCode') String? postalCode,
      @JsonKey(name: 'Country') String? country,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Alias') String? alias,
      @JsonKey(name: 'Local_SMS_Channel_Id__c') String? localSMSChannelId,
      @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
      String? localWhatsAppChannelId,
      @JsonKey(name: 'ContactId') String? contactId,
      @JsonKey(name: 'AccountId') String? accountId,
      @JsonKey(name: 'FullPhotoUrl') String? fullPhotoUrl});
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? username = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? name = freezed,
    Object? street = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? postalCode = freezed,
    Object? country = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? mobilePhone = freezed,
    Object? alias = freezed,
    Object? localSMSChannelId = freezed,
    Object? localWhatsAppChannelId = freezed,
    Object? contactId = freezed,
    Object? accountId = freezed,
    Object? fullPhotoUrl = freezed,
  }) {
    return _then(_$UserImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      postalCode: freezed == postalCode
          ? _value.postalCode
          : postalCode // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      alias: freezed == alias
          ? _value.alias
          : alias // ignore: cast_nullable_to_non_nullable
              as String?,
      localSMSChannelId: freezed == localSMSChannelId
          ? _value.localSMSChannelId
          : localSMSChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      localWhatsAppChannelId: freezed == localWhatsAppChannelId
          ? _value.localWhatsAppChannelId
          : localWhatsAppChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      contactId: freezed == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      fullPhotoUrl: freezed == fullPhotoUrl
          ? _value.fullPhotoUrl
          : fullPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl(
      {@JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'Username') this.username,
      @JsonKey(name: 'LastName') this.lastName,
      @JsonKey(name: 'FirstName') this.firstName,
      @JsonKey(name: 'Name') this.name,
      @JsonKey(name: 'Street') this.street,
      @JsonKey(name: 'City') this.city,
      @JsonKey(name: 'State') this.state,
      @JsonKey(name: 'PostalCode') this.postalCode,
      @JsonKey(name: 'Country') this.country,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'Phone') this.phone,
      @JsonKey(name: 'MobilePhone') this.mobilePhone,
      @JsonKey(name: 'Alias') this.alias,
      @JsonKey(name: 'Local_SMS_Channel_Id__c') this.localSMSChannelId,
      @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
      this.localWhatsAppChannelId,
      @JsonKey(name: 'ContactId') this.contactId,
      @JsonKey(name: 'AccountId') this.accountId,
      @JsonKey(name: 'FullPhotoUrl') this.fullPhotoUrl});

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'Username')
  final String? username;
  @override
  @JsonKey(name: 'LastName')
  final String? lastName;
  @override
  @JsonKey(name: 'FirstName')
  final String? firstName;
  @override
  @JsonKey(name: 'Name')
  final String? name;
  @override
  @JsonKey(name: 'Street')
  final String? street;
  @override
  @JsonKey(name: 'City')
  final String? city;
  @override
  @JsonKey(name: 'State')
  final String? state;
  @override
  @JsonKey(name: 'PostalCode')
  final String? postalCode;
  @override
  @JsonKey(name: 'Country')
  final String? country;
  @override
  @JsonKey(name: 'Email')
  final String? email;
  @override
  @JsonKey(name: 'Phone')
  final String? phone;
  @override
  @JsonKey(name: 'MobilePhone')
  final String? mobilePhone;
  @override
  @JsonKey(name: 'Alias')
  final String? alias;
  @override
  @JsonKey(name: 'Local_SMS_Channel_Id__c')
  final String? localSMSChannelId;
  @override
  @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
  final String? localWhatsAppChannelId;
  @override
  @JsonKey(name: 'ContactId')
  final String? contactId;
  @override
  @JsonKey(name: 'AccountId')
  final String? accountId;
  @override
  @JsonKey(name: 'FullPhotoUrl')
  final String? fullPhotoUrl;

  @override
  String toString() {
    return 'User(id: $id, username: $username, lastName: $lastName, firstName: $firstName, name: $name, street: $street, city: $city, state: $state, postalCode: $postalCode, country: $country, email: $email, phone: $phone, mobilePhone: $mobilePhone, alias: $alias, localSMSChannelId: $localSMSChannelId, localWhatsAppChannelId: $localWhatsAppChannelId, contactId: $contactId, accountId: $accountId, fullPhotoUrl: $fullPhotoUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.alias, alias) || other.alias == alias) &&
            (identical(other.localSMSChannelId, localSMSChannelId) ||
                other.localSMSChannelId == localSMSChannelId) &&
            (identical(other.localWhatsAppChannelId, localWhatsAppChannelId) ||
                other.localWhatsAppChannelId == localWhatsAppChannelId) &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.fullPhotoUrl, fullPhotoUrl) ||
                other.fullPhotoUrl == fullPhotoUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        username,
        lastName,
        firstName,
        name,
        street,
        city,
        state,
        postalCode,
        country,
        email,
        phone,
        mobilePhone,
        alias,
        localSMSChannelId,
        localWhatsAppChannelId,
        contactId,
        accountId,
        fullPhotoUrl
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User implements User {
  const factory _User(
      {@JsonKey(name: 'Id') final String? id,
      @JsonKey(name: 'Username') final String? username,
      @JsonKey(name: 'LastName') final String? lastName,
      @JsonKey(name: 'FirstName') final String? firstName,
      @JsonKey(name: 'Name') final String? name,
      @JsonKey(name: 'Street') final String? street,
      @JsonKey(name: 'City') final String? city,
      @JsonKey(name: 'State') final String? state,
      @JsonKey(name: 'PostalCode') final String? postalCode,
      @JsonKey(name: 'Country') final String? country,
      @JsonKey(name: 'Email') final String? email,
      @JsonKey(name: 'Phone') final String? phone,
      @JsonKey(name: 'MobilePhone') final String? mobilePhone,
      @JsonKey(name: 'Alias') final String? alias,
      @JsonKey(name: 'Local_SMS_Channel_Id__c') final String? localSMSChannelId,
      @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
      final String? localWhatsAppChannelId,
      @JsonKey(name: 'ContactId') final String? contactId,
      @JsonKey(name: 'AccountId') final String? accountId,
      @JsonKey(name: 'FullPhotoUrl') final String? fullPhotoUrl}) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  String? get id;
  @override
  @JsonKey(name: 'Username')
  String? get username;
  @override
  @JsonKey(name: 'LastName')
  String? get lastName;
  @override
  @JsonKey(name: 'FirstName')
  String? get firstName;
  @override
  @JsonKey(name: 'Name')
  String? get name;
  @override
  @JsonKey(name: 'Street')
  String? get street;
  @override
  @JsonKey(name: 'City')
  String? get city;
  @override
  @JsonKey(name: 'State')
  String? get state;
  @override
  @JsonKey(name: 'PostalCode')
  String? get postalCode;
  @override
  @JsonKey(name: 'Country')
  String? get country;
  @override
  @JsonKey(name: 'Email')
  String? get email;
  @override
  @JsonKey(name: 'Phone')
  String? get phone;
  @override
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone;
  @override
  @JsonKey(name: 'Alias')
  String? get alias;
  @override
  @JsonKey(name: 'Local_SMS_Channel_Id__c')
  String? get localSMSChannelId;
  @override
  @JsonKey(name: 'Local_WhatsApp_Channel_Id__c')
  String? get localWhatsAppChannelId;
  @override
  @JsonKey(name: 'ContactId')
  String? get contactId;
  @override
  @JsonKey(name: 'AccountId')
  String? get accountId;
  @override
  @JsonKey(name: 'FullPhotoUrl')
  String? get fullPhotoUrl;
  @override
  @JsonKey(ignore: true)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
