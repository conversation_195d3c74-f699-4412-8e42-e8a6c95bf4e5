// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: json['Id'] as String?,
      username: json['Username'] as String?,
      lastName: json['LastName'] as String?,
      firstName: json['FirstName'] as String?,
      name: json['Name'] as String?,
      street: json['Street'] as String?,
      city: json['City'] as String?,
      state: json['State'] as String?,
      postalCode: json['PostalCode'] as String?,
      country: json['Country'] as String?,
      email: json['Email'] as String?,
      phone: json['Phone'] as String?,
      mobilePhone: json['MobilePhone'] as String?,
      alias: json['Alias'] as String?,
      localSMSChannelId: json['Local_SMS_Channel_Id__c'] as String?,
      localWhatsAppChannelId: json['Local_WhatsApp_Channel_Id__c'] as String?,
      contactId: json['ContactId'] as String?,
      accountId: json['AccountId'] as String?,
      fullPhotoUrl: json['FullPhotoUrl'] as String?,
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'Username': instance.username,
      'LastName': instance.lastName,
      'FirstName': instance.firstName,
      'Name': instance.name,
      'Street': instance.street,
      'City': instance.city,
      'State': instance.state,
      'PostalCode': instance.postalCode,
      'Country': instance.country,
      'Email': instance.email,
      'Phone': instance.phone,
      'MobilePhone': instance.mobilePhone,
      'Alias': instance.alias,
      'Local_SMS_Channel_Id__c': instance.localSMSChannelId,
      'Local_WhatsApp_Channel_Id__c': instance.localWhatsAppChannelId,
      'ContactId': instance.contactId,
      'AccountId': instance.accountId,
      'FullPhotoUrl': instance.fullPhotoUrl,
    };
