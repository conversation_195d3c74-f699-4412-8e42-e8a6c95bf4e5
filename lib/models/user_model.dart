import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class User with _$User {
  const factory User({
    @J<PERSON><PERSON><PERSON>(name: 'Id') String? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: '<PERSON>rna<PERSON>') String? username,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'LastName') String? lastName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'FirstName') String? firstName,
    @<PERSON>son<PERSON>ey(name: 'Name') String? name,
    @<PERSON>son<PERSON><PERSON>(name: 'Street') String? street,
    @JsonKey(name: 'City') String? city,
    @<PERSON>sonKey(name: 'State') String? state,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'PostalCode') String? postalCode,
    @J<PERSON><PERSON><PERSON>(name: 'Country') String? country,
    @<PERSON>son<PERSON><PERSON>(name: 'Email') String? email,
    @<PERSON>son<PERSON><PERSON>(name: 'Phone') String? phone,
    @Json<PERSON>ey(name: 'MobilePhone') String? mobilePhone,
    @<PERSON>sonKey(name: '<PERSON><PERSON>') String? alias,
    @<PERSON>son<PERSON>ey(name: 'Local_SMS_Channel_Id__c') String? localSMSChannelId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Local_WhatsApp_Channel_Id__c')
    String? localWhatsAppChannelId,
    @JsonKey(name: 'ContactId') String? contactId,
    @JsonKey(name: 'AccountId') String? accountId,
    @JsonKey(name: 'FullPhotoUrl') String? fullPhotoUrl,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
