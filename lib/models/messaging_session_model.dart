import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/salesforce/dtos/messaging_session_response.dart';
import 'package:x1440/models/end_user_contact.dart';
import 'package:x1440/use_cases/models/messaging_end_user.dart';
import 'package:x1440/utils/json_utils.dart';

import 'sf_id.dart';

part 'messaging_session_model.freezed.dart';
part 'messaging_session_model.g.dart';

enum MessagingSessionStatus {
  created('new'),
  active('active'),
  consent('consent'),
  waiting('waiting'),
  paused('paused'),
  inactive('inactive'),
  ended('ended'),
  error('error');

  final String value;

  const MessagingSessionStatus(this.value);

  static MessagingSessionStatus? fromString(String status) {
    final lowerCaseStatus = status.toLowerCase();
    for (var element in MessagingSessionStatus.values) {
      if (element.value == lowerCaseStatus) {
        return element;
      }
    }
    return null;
  }
}

@freezed
class MessagingSession with _$MessagingSession {
  bool get isEnhanced =>
      // channelType == 'WhatsApp' ||
      // channelType == 'sfdcLiveagent' ||
      conversationId != null;
  // channelType == 'Text'; // TODO: distinguish between enhanced & regular sms

  const MessagingSession._();

  const factory MessagingSession({
    @ParseSfIdConverter() required SfId id,
    String? channelName,
    String? channelType,
    String? caseId,
    String? leadId,
    String? opportunityId,

    /// NOTE: important! these "conversationId"'s are not the same as the ones in the Conversation model. Conversations use the MessagingEndUserId as the ConversationId. These ConversationIds are from SF -- they only exist for enhanced conversations, have a prefix of "0dw" and are called "conversationIdentifiers" in many places in Salesforce
    String? conversationId,
    DateTime? createdDate,
    String? ownerId,
    MessagingSessionStatus? status,
    EndUserContact? endUserContact,
    MessagingEndUser? messagingEndUser,
    String? messagingChannelId,
  }) = _MessagingSession;

  factory MessagingSession.fromJson(Map<String, dynamic> json) =>
      _$MessagingSessionFromJson(json);

  factory MessagingSession.fromGraphQlNode(Map<String, dynamic> json) {
    return MessagingSession(
      id: SfId(json['Id']),
      caseId: json['CaseId']?['value'],
      leadId: json['LeadId']?['value'],
      opportunityId: json['OpportunityId']?['value'],
      channelName: json['ChannelName']?['value'],
      channelType: json['ChannelType']?['value'],
      conversationId: json['ConversationId']?['value'],
      createdDate: json['CreatedDate']?['value'] != null
          ? DateTime.parse(json['CreatedDate']['value'])
          : null,
      ownerId: json['OwnerId']?['value'],
      status: MessagingSessionStatus.fromString(json['Status']?['value']),
      // endUserContact: endUserContact,
      endUserContact: json['EndUserContact'] != null
          ? EndUserContact.fromGraphQlNode(json['EndUserContact'])
          : null,
      messagingEndUser: json['MessagingEndUser'] != null
          ? MessagingEndUser.fromGraphQlNode(
              safeJsonDecode(json['MessagingEndUser']))
          : null,
      messagingChannelId: json['MessagingChannel']?['Id'],
    );
  }

  factory MessagingSession.fromMessagingSessionResponse(
      MessagingSessionResponse response) {
    return MessagingSession(
      id: response.id.toSfId(),
      caseId: response.caseId,
      leadId: response.leadId,
      opportunityId: response.opportunityId,
      channelName: response.channelName,
      channelType: response.channelType,
      conversationId: response.conversationId,
      createdDate: response.createdDate,
      ownerId: response.ownerId,
      status: MessagingSessionStatus.fromString(
          response.status.toString().split('.').last),
      endUserContact: null,
      messagingEndUser: null,
      messagingChannelId: response.messagingChannelId,
    );
  }
}
