// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_error_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppErrorImpl _$$AppErrorImplFromJson(Map<String, dynamic> json) =>
    _$AppErrorImpl(
      message: json['message'] as String?,
      details: json['details'] as String?,
      iconPath: json['iconPath'] as String?,
      statusCode: (json['statusCode'] as num?)?.toInt(),
      isAppException: json['isAppException'] as bool? ?? false,
      showErrorOverlay: json['showErrorOverlay'] as bool? ?? false,
      shouldForceLogout: json['shouldForceLogout'] as bool? ?? false,
      shouldShowSystemNotification:
          json['shouldShowSystemNotification'] as bool? ?? false,
      isReplaceable: json['isReplaceable'] as bool? ?? false,
    );

Map<String, dynamic> _$$AppErrorImplToJson(_$AppErrorImpl instance) =>
    <String, dynamic>{
      'message': instance.message,
      'details': instance.details,
      'iconPath': instance.iconPath,
      'statusCode': instance.statusCode,
      'isAppException': instance.isAppException,
      'showErrorOverlay': instance.showErrorOverlay,
      'shouldForceLogout': instance.shouldForceLogout,
      'shouldShowSystemNotification': instance.shouldShowSystemNotification,
      'isReplaceable': instance.isReplaceable,
    };
