// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_session_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingSessionImpl _$$MessagingSessionImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingSessionImpl(
      id: const ParseSfIdConverter().fromJson(json['id']),
      channelName: json['channelName'] as String?,
      channelType: json['channelType'] as String?,
      caseId: json['caseId'] as String?,
      leadId: json['leadId'] as String?,
      opportunityId: json['opportunityId'] as String?,
      conversationId: json['conversationId'] as String?,
      createdDate: json['createdDate'] == null
          ? null
          : DateTime.parse(json['createdDate'] as String),
      ownerId: json['ownerId'] as String?,
      status:
          $enumDecodeNullable(_$MessagingSessionStatusEnumMap, json['status']),
      endUserContact: json['endUserContact'] == null
          ? null
          : EndUserContact.fromJson(
              json['endUserContact'] as Map<String, dynamic>),
      messagingEndUser: json['messagingEndUser'] == null
          ? null
          : MessagingEndUser.fromJson(
              json['messagingEndUser'] as Map<String, dynamic>),
      messagingChannelId: json['messagingChannelId'] as String?,
    );

Map<String, dynamic> _$$MessagingSessionImplToJson(
        _$MessagingSessionImpl instance) =>
    <String, dynamic>{
      'id': const ParseSfIdConverter().toJson(instance.id),
      'channelName': instance.channelName,
      'channelType': instance.channelType,
      'caseId': instance.caseId,
      'leadId': instance.leadId,
      'opportunityId': instance.opportunityId,
      'conversationId': instance.conversationId,
      'createdDate': instance.createdDate?.toIso8601String(),
      'ownerId': instance.ownerId,
      'status': _$MessagingSessionStatusEnumMap[instance.status],
      'endUserContact': instance.endUserContact?.toJson(),
      'messagingEndUser': instance.messagingEndUser?.toJson(),
      'messagingChannelId': instance.messagingChannelId,
    };

const _$MessagingSessionStatusEnumMap = {
  MessagingSessionStatus.created: 'created',
  MessagingSessionStatus.active: 'active',
  MessagingSessionStatus.consent: 'consent',
  MessagingSessionStatus.waiting: 'waiting',
  MessagingSessionStatus.paused: 'paused',
  MessagingSessionStatus.inactive: 'inactive',
  MessagingSessionStatus.ended: 'ended',
  MessagingSessionStatus.error: 'error',
};
