// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tapped_notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TappedNotificationImpl _$$TappedNotificationImplFromJson(
        Map<String, dynamic> json) =>
    _$TappedNotificationImpl(
      notificationId: json['notificationId'] as String?,
      conversationId: json['conversationId'] as String?,
      categoryId: json['categoryId'] as String?,
      action: json['action'] as String?,
      payload: json['payload'] as Map<String, dynamic>?,
      queueReceiveMessage: json['queueReceiveMessage'] == null
          ? null
          : QueueReceiveMessage.fromJson(
              json['queueReceiveMessage'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$TappedNotificationImplToJson(
        _$TappedNotificationImpl instance) =>
    <String, dynamic>{
      'notificationId': instance.notificationId,
      'conversationId': instance.conversationId,
      'categoryId': instance.categoryId,
      'action': instance.action,
      'payload': instance.payload,
      'queueReceiveMessage': instance.queueReceiveMessage?.toJson(),
    };
