// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sf_id.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SfId _$SfIdFromJson(Map<String, dynamic> json) {
  return _SfId.fromJson(json);
}

/// @nodoc
mixin _$SfId {
  @ParseSfIdConverter()
  String get value => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SfIdCopyWith<SfId> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SfIdCopyWith<$Res> {
  factory $SfIdCopyWith(SfId value, $Res Function(SfId) then) =
      _$SfIdCopyWithImpl<$Res, SfId>;
  @useResult
  $Res call({@ParseSfIdConverter() String value});
}

/// @nodoc
class _$SfIdCopyWithImpl<$Res, $Val extends SfId>
    implements $SfIdCopyWith<$Res> {
  _$SfIdCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_value.copyWith(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SfIdImplCopyWith<$Res> implements $SfIdCopyWith<$Res> {
  factory _$$SfIdImplCopyWith(
          _$SfIdImpl value, $Res Function(_$SfIdImpl) then) =
      __$$SfIdImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@ParseSfIdConverter() String value});
}

/// @nodoc
class __$$SfIdImplCopyWithImpl<$Res>
    extends _$SfIdCopyWithImpl<$Res, _$SfIdImpl>
    implements _$$SfIdImplCopyWith<$Res> {
  __$$SfIdImplCopyWithImpl(_$SfIdImpl _value, $Res Function(_$SfIdImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SfIdImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SfIdImpl extends _SfId {
  _$SfIdImpl([@ParseSfIdConverter() this.value = '']) : super._();

  factory _$SfIdImpl.fromJson(Map<String, dynamic> json) =>
      _$$SfIdImplFromJson(json);

  @override
  @JsonKey()
  @ParseSfIdConverter()
  final String value;

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SfIdImplCopyWith<_$SfIdImpl> get copyWith =>
      __$$SfIdImplCopyWithImpl<_$SfIdImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SfIdImplToJson(
      this,
    );
  }
}

abstract class _SfId extends SfId {
  factory _SfId([@ParseSfIdConverter() final String value]) = _$SfIdImpl;
  _SfId._() : super._();

  factory _SfId.fromJson(Map<String, dynamic> json) = _$SfIdImpl.fromJson;

  @override
  @ParseSfIdConverter()
  String get value;
  @override
  @JsonKey(ignore: true)
  _$$SfIdImplCopyWith<_$SfIdImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
