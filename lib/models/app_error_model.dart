import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'app_error_model.freezed.dart';

part 'app_error_model.g.dart';

class NotificationPermissionsAppBlockingError extends AppBlockingError {
  // @override
  // Future<bool> requestPermissionsCheck() =>
  //     AwesomeNotifications().isNotificationAllowed();

  // @override
  // Future<void> openSystemSettingsPage() =>
  //     AwesomeNotifications().requestPermissionToSendNotifications();

  @override
  String get name => S.current.permissions_notification_label;

  @override
  String get message => S.current.permissions_notification_message_label;

  @override
  String get detailedGrantPermissionInstructions =>
      S.current.permissions_notification_detailed_grant_label;
}

class AppBlockingError {
  //}extends AppError {
  String? get message => null; // in AppError
  Future<void> openSystemSettingsPage() async {}

  Future<bool> requestPermissionsCheck() async => false;

  String get name => S.current.app_permissions_notification_label;

  String get detailedGrantPermissionInstructions =>
      S.current.app_permissions_notification_detailed_grant_label;
}

// class AppLifecycleStateDetachedError extends AppError {
//   @override
//   bool get shouldForceLogout => true;
//
//   @override
//   bool get isAppException => true;
//
//   @override
//   String get message => 'app has been detached; probably a hard close';
// }

AppError orgNotProvisionedAppError = AppError(
    message: S.current.org_not_provisioned_title,
    details: S.current.org_not_provisioned_subtitle,
    // iconPath: 'assets/icons/organization_not_provisioned.svg',
    // statusCode: 404,
    showErrorOverlay: true,
    shouldForceLogout: true,
    isReplaceable: true);

@freezed
@collectionOnFreezed
class AppError with _$AppError {
  Id get localDbId => Isar.autoIncrement;

  const AppError._();

  const factory AppError({
    String? message,
    String? details,
    String? iconPath,
    int? statusCode,
    @Default(false) bool isAppException,
    @Default(false) bool showErrorOverlay,
    @Default(false) bool shouldForceLogout,
    @Default(false) bool shouldShowSystemNotification,

    /// if another error should take precedence and ignore this one
    @Default(false) bool isReplaceable,
  }) = _AppError;

  factory AppError.fromJson(Map<String, dynamic> json) =>
      _$AppErrorFromJson(json);
}
