import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/services/shim_service/models/shim_service_payload.dart';

part 'tapped_notification.freezed.dart';
part 'tapped_notification.g.dart';
@freezed
class TappedNotification with _$TappedNotification {
  const TappedNotification._();

  ShimServicePayload? get shimServicePayload => queueReceiveMessage == null ? null : ShimServicePayload.fromJson(queueReceiveMessage!.payload);

  const factory TappedNotification({
    String? notificationId,
    String? conversationId,
    String? categoryId,
    String? action,
    Map<String, dynamic>? payload,
    QueueReceiveMessage? queueReceiveMessage,
  }) = _TappedNotification;

  factory TappedNotification.fromJson(Map<String, dynamic> json) =>
      _$TappedNotificationFromJson(json);
}