// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversation_entries.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ConversationEntries _$ConversationEntriesFromJson(Map<String, dynamic> json) {
  return _ConversationEntries.fromJson(json);
}

/// @nodoc
mixin _$ConversationEntries {
  @JsonKey(name: 'Id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'ConversationIdentifier')
  String? get conversationIdentifier => throw _privateConstructorUsedError;
  @JsonKey(name: 'ConversationChannelId')
  String? get conversationChannelId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ConversationEntriesCopyWith<ConversationEntries> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversationEntriesCopyWith<$Res> {
  factory $ConversationEntriesCopyWith(
          ConversationEntries value, $Res Function(ConversationEntries) then) =
      _$ConversationEntriesCopyWithImpl<$Res, ConversationEntries>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'ConversationIdentifier') String? conversationIdentifier,
      @JsonKey(name: 'ConversationChannelId') String? conversationChannelId});
}

/// @nodoc
class _$ConversationEntriesCopyWithImpl<$Res, $Val extends ConversationEntries>
    implements $ConversationEntriesCopyWith<$Res> {
  _$ConversationEntriesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? conversationIdentifier = freezed,
    Object? conversationChannelId = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationIdentifier: freezed == conversationIdentifier
          ? _value.conversationIdentifier
          : conversationIdentifier // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationChannelId: freezed == conversationChannelId
          ? _value.conversationChannelId
          : conversationChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConversationEntriesImplCopyWith<$Res>
    implements $ConversationEntriesCopyWith<$Res> {
  factory _$$ConversationEntriesImplCopyWith(_$ConversationEntriesImpl value,
          $Res Function(_$ConversationEntriesImpl) then) =
      __$$ConversationEntriesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'ConversationIdentifier') String? conversationIdentifier,
      @JsonKey(name: 'ConversationChannelId') String? conversationChannelId});
}

/// @nodoc
class __$$ConversationEntriesImplCopyWithImpl<$Res>
    extends _$ConversationEntriesCopyWithImpl<$Res, _$ConversationEntriesImpl>
    implements _$$ConversationEntriesImplCopyWith<$Res> {
  __$$ConversationEntriesImplCopyWithImpl(_$ConversationEntriesImpl _value,
      $Res Function(_$ConversationEntriesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? conversationIdentifier = freezed,
    Object? conversationChannelId = freezed,
  }) {
    return _then(_$ConversationEntriesImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationIdentifier: freezed == conversationIdentifier
          ? _value.conversationIdentifier
          : conversationIdentifier // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationChannelId: freezed == conversationChannelId
          ? _value.conversationChannelId
          : conversationChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ConversationEntriesImpl extends _ConversationEntries {
  const _$ConversationEntriesImpl(
      {@JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'ConversationIdentifier') this.conversationIdentifier,
      @JsonKey(name: 'ConversationChannelId') this.conversationChannelId})
      : super._();

  factory _$ConversationEntriesImpl.fromJson(Map<String, dynamic> json) =>
      _$$ConversationEntriesImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'ConversationIdentifier')
  final String? conversationIdentifier;
  @override
  @JsonKey(name: 'ConversationChannelId')
  final String? conversationChannelId;

  @override
  String toString() {
    return 'ConversationEntries(id: $id, conversationIdentifier: $conversationIdentifier, conversationChannelId: $conversationChannelId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversationEntriesImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.conversationIdentifier, conversationIdentifier) ||
                other.conversationIdentifier == conversationIdentifier) &&
            (identical(other.conversationChannelId, conversationChannelId) ||
                other.conversationChannelId == conversationChannelId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, conversationIdentifier, conversationChannelId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversationEntriesImplCopyWith<_$ConversationEntriesImpl> get copyWith =>
      __$$ConversationEntriesImplCopyWithImpl<_$ConversationEntriesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ConversationEntriesImplToJson(
      this,
    );
  }
}

abstract class _ConversationEntries extends ConversationEntries {
  const factory _ConversationEntries(
      {@JsonKey(name: 'Id') final String? id,
      @JsonKey(name: 'ConversationIdentifier')
      final String? conversationIdentifier,
      @JsonKey(name: 'ConversationChannelId')
      final String? conversationChannelId}) = _$ConversationEntriesImpl;
  const _ConversationEntries._() : super._();

  factory _ConversationEntries.fromJson(Map<String, dynamic> json) =
      _$ConversationEntriesImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  String? get id;
  @override
  @JsonKey(name: 'ConversationIdentifier')
  String? get conversationIdentifier;
  @override
  @JsonKey(name: 'ConversationChannelId')
  String? get conversationChannelId;
  @override
  @JsonKey(ignore: true)
  _$$ConversationEntriesImplCopyWith<_$ConversationEntriesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
