import 'package:freezed_annotation/freezed_annotation.dart';

part 'salesforce_soql_query_response.freezed.dart';
part 'salesforce_soql_query_response.g.dart';

@freezed
class SalesforceSoqlQueryResponse with _$SalesforceSoqlQueryResponse {
  const factory SalesforceSoqlQueryResponse({
    required bool done,
    required int totalSize,
    String? nextRecordsUrl,
    required List<Map<String, dynamic>> records,
  }) = _SalesforceSoqlQueryResponse;

  factory SalesforceSoqlQueryResponse.fromJson(Map<String, dynamic> json) =>
      _$SalesforceSoqlQueryResponseFromJson(json);
}