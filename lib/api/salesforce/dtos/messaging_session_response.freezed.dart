// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_session_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingSessionResponse _$MessagingSessionResponseFromJson(
    Map<String, dynamic> json) {
  return _MessagingSessionResponse.fromJson(json);
}

/// @nodoc
mixin _$MessagingSessionResponse {
  @JsonKey(name: "Id")
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: "Name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "ChannelName")
  String? get channelName => throw _privateConstructorUsedError;
  @JsonKey(name: "SessionKey")
  String? get sessionKey => throw _privateConstructorUsedError;
  @JsonKey(name: "Origin")
  @MessagingSessionOriginConverter()
  MessagingSessionOrigin? get origin => throw _privateConstructorUsedError;
  @JsonKey(name: "ChannelType")
  String? get channelType => throw _privateConstructorUsedError;
  @JsonKey(name: "ConversationId")
  String? get conversationId => throw _privateConstructorUsedError;
  @JsonKey(name: "MessagingChannelId")
  String? get messagingChannelId => throw _privateConstructorUsedError;
  @JsonKey(name: "MessagingEndUserId")
  String? get messagingEndUserId => throw _privateConstructorUsedError;
  @JsonKey(name: "CreatedDate")
  DateTime? get createdDate => throw _privateConstructorUsedError;
  @JsonKey(name: "OwnerId")
  String? get ownerId => throw _privateConstructorUsedError;
  @JsonKey(name: "Status")
  @MessagingSessionStatusConverter()
  MessagingSessionStatus? get status => throw _privateConstructorUsedError;
  @JsonKey(name: "EndUserAccountId")
  String? get endUserAccountId => throw _privateConstructorUsedError;
  @JsonKey(name: "EndUserContactId")
  String? get endUserContactId => throw _privateConstructorUsedError;
  @JsonKey(name: "EndUserLanguage")
  String? get endUserLanguage => throw _privateConstructorUsedError;
  @JsonKey(name: "CaseId")
  String? get caseId => throw _privateConstructorUsedError;
  @JsonKey(name: "LeadId")
  String? get leadId => throw _privateConstructorUsedError;
  @JsonKey(name: "OpportunityId")
  String? get opportunityId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingSessionResponseCopyWith<MessagingSessionResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingSessionResponseCopyWith<$Res> {
  factory $MessagingSessionResponseCopyWith(MessagingSessionResponse value,
          $Res Function(MessagingSessionResponse) then) =
      _$MessagingSessionResponseCopyWithImpl<$Res, MessagingSessionResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: "Id") String id,
      @JsonKey(name: "Name") String? name,
      @JsonKey(name: "ChannelName") String? channelName,
      @JsonKey(name: "SessionKey") String? sessionKey,
      @JsonKey(name: "Origin")
      @MessagingSessionOriginConverter()
      MessagingSessionOrigin? origin,
      @JsonKey(name: "ChannelType") String? channelType,
      @JsonKey(name: "ConversationId") String? conversationId,
      @JsonKey(name: "MessagingChannelId") String? messagingChannelId,
      @JsonKey(name: "MessagingEndUserId") String? messagingEndUserId,
      @JsonKey(name: "CreatedDate") DateTime? createdDate,
      @JsonKey(name: "OwnerId") String? ownerId,
      @JsonKey(name: "Status")
      @MessagingSessionStatusConverter()
      MessagingSessionStatus? status,
      @JsonKey(name: "EndUserAccountId") String? endUserAccountId,
      @JsonKey(name: "EndUserContactId") String? endUserContactId,
      @JsonKey(name: "EndUserLanguage") String? endUserLanguage,
      @JsonKey(name: "CaseId") String? caseId,
      @JsonKey(name: "LeadId") String? leadId,
      @JsonKey(name: "OpportunityId") String? opportunityId});
}

/// @nodoc
class _$MessagingSessionResponseCopyWithImpl<$Res,
        $Val extends MessagingSessionResponse>
    implements $MessagingSessionResponseCopyWith<$Res> {
  _$MessagingSessionResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? channelName = freezed,
    Object? sessionKey = freezed,
    Object? origin = freezed,
    Object? channelType = freezed,
    Object? conversationId = freezed,
    Object? messagingChannelId = freezed,
    Object? messagingEndUserId = freezed,
    Object? createdDate = freezed,
    Object? ownerId = freezed,
    Object? status = freezed,
    Object? endUserAccountId = freezed,
    Object? endUserContactId = freezed,
    Object? endUserLanguage = freezed,
    Object? caseId = freezed,
    Object? leadId = freezed,
    Object? opportunityId = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionKey: freezed == sessionKey
          ? _value.sessionKey
          : sessionKey // ignore: cast_nullable_to_non_nullable
              as String?,
      origin: freezed == origin
          ? _value.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as MessagingSessionOrigin?,
      channelType: freezed == channelType
          ? _value.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingChannelId: freezed == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingEndUserId: freezed == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessagingSessionStatus?,
      endUserAccountId: freezed == endUserAccountId
          ? _value.endUserAccountId
          : endUserAccountId // ignore: cast_nullable_to_non_nullable
              as String?,
      endUserContactId: freezed == endUserContactId
          ? _value.endUserContactId
          : endUserContactId // ignore: cast_nullable_to_non_nullable
              as String?,
      endUserLanguage: freezed == endUserLanguage
          ? _value.endUserLanguage
          : endUserLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      caseId: freezed == caseId
          ? _value.caseId
          : caseId // ignore: cast_nullable_to_non_nullable
              as String?,
      leadId: freezed == leadId
          ? _value.leadId
          : leadId // ignore: cast_nullable_to_non_nullable
              as String?,
      opportunityId: freezed == opportunityId
          ? _value.opportunityId
          : opportunityId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessagingSessionResponseImplCopyWith<$Res>
    implements $MessagingSessionResponseCopyWith<$Res> {
  factory _$$MessagingSessionResponseImplCopyWith(
          _$MessagingSessionResponseImpl value,
          $Res Function(_$MessagingSessionResponseImpl) then) =
      __$$MessagingSessionResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "Id") String id,
      @JsonKey(name: "Name") String? name,
      @JsonKey(name: "ChannelName") String? channelName,
      @JsonKey(name: "SessionKey") String? sessionKey,
      @JsonKey(name: "Origin")
      @MessagingSessionOriginConverter()
      MessagingSessionOrigin? origin,
      @JsonKey(name: "ChannelType") String? channelType,
      @JsonKey(name: "ConversationId") String? conversationId,
      @JsonKey(name: "MessagingChannelId") String? messagingChannelId,
      @JsonKey(name: "MessagingEndUserId") String? messagingEndUserId,
      @JsonKey(name: "CreatedDate") DateTime? createdDate,
      @JsonKey(name: "OwnerId") String? ownerId,
      @JsonKey(name: "Status")
      @MessagingSessionStatusConverter()
      MessagingSessionStatus? status,
      @JsonKey(name: "EndUserAccountId") String? endUserAccountId,
      @JsonKey(name: "EndUserContactId") String? endUserContactId,
      @JsonKey(name: "EndUserLanguage") String? endUserLanguage,
      @JsonKey(name: "CaseId") String? caseId,
      @JsonKey(name: "LeadId") String? leadId,
      @JsonKey(name: "OpportunityId") String? opportunityId});
}

/// @nodoc
class __$$MessagingSessionResponseImplCopyWithImpl<$Res>
    extends _$MessagingSessionResponseCopyWithImpl<$Res,
        _$MessagingSessionResponseImpl>
    implements _$$MessagingSessionResponseImplCopyWith<$Res> {
  __$$MessagingSessionResponseImplCopyWithImpl(
      _$MessagingSessionResponseImpl _value,
      $Res Function(_$MessagingSessionResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = freezed,
    Object? channelName = freezed,
    Object? sessionKey = freezed,
    Object? origin = freezed,
    Object? channelType = freezed,
    Object? conversationId = freezed,
    Object? messagingChannelId = freezed,
    Object? messagingEndUserId = freezed,
    Object? createdDate = freezed,
    Object? ownerId = freezed,
    Object? status = freezed,
    Object? endUserAccountId = freezed,
    Object? endUserContactId = freezed,
    Object? endUserLanguage = freezed,
    Object? caseId = freezed,
    Object? leadId = freezed,
    Object? opportunityId = freezed,
  }) {
    return _then(_$MessagingSessionResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      channelName: freezed == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionKey: freezed == sessionKey
          ? _value.sessionKey
          : sessionKey // ignore: cast_nullable_to_non_nullable
              as String?,
      origin: freezed == origin
          ? _value.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as MessagingSessionOrigin?,
      channelType: freezed == channelType
          ? _value.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingChannelId: freezed == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingEndUserId: freezed == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessagingSessionStatus?,
      endUserAccountId: freezed == endUserAccountId
          ? _value.endUserAccountId
          : endUserAccountId // ignore: cast_nullable_to_non_nullable
              as String?,
      endUserContactId: freezed == endUserContactId
          ? _value.endUserContactId
          : endUserContactId // ignore: cast_nullable_to_non_nullable
              as String?,
      endUserLanguage: freezed == endUserLanguage
          ? _value.endUserLanguage
          : endUserLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      caseId: freezed == caseId
          ? _value.caseId
          : caseId // ignore: cast_nullable_to_non_nullable
              as String?,
      leadId: freezed == leadId
          ? _value.leadId
          : leadId // ignore: cast_nullable_to_non_nullable
              as String?,
      opportunityId: freezed == opportunityId
          ? _value.opportunityId
          : opportunityId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingSessionResponseImpl implements _MessagingSessionResponse {
  const _$MessagingSessionResponseImpl(
      {@JsonKey(name: "Id") required this.id,
      @JsonKey(name: "Name") this.name,
      @JsonKey(name: "ChannelName") this.channelName,
      @JsonKey(name: "SessionKey") this.sessionKey,
      @JsonKey(name: "Origin") @MessagingSessionOriginConverter() this.origin,
      @JsonKey(name: "ChannelType") this.channelType,
      @JsonKey(name: "ConversationId") this.conversationId,
      @JsonKey(name: "MessagingChannelId") this.messagingChannelId,
      @JsonKey(name: "MessagingEndUserId") this.messagingEndUserId,
      @JsonKey(name: "CreatedDate") this.createdDate,
      @JsonKey(name: "OwnerId") this.ownerId,
      @JsonKey(name: "Status") @MessagingSessionStatusConverter() this.status,
      @JsonKey(name: "EndUserAccountId") this.endUserAccountId,
      @JsonKey(name: "EndUserContactId") this.endUserContactId,
      @JsonKey(name: "EndUserLanguage") this.endUserLanguage,
      @JsonKey(name: "CaseId") this.caseId,
      @JsonKey(name: "LeadId") this.leadId,
      @JsonKey(name: "OpportunityId") this.opportunityId});

  factory _$MessagingSessionResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingSessionResponseImplFromJson(json);

  @override
  @JsonKey(name: "Id")
  final String id;
  @override
  @JsonKey(name: "Name")
  final String? name;
  @override
  @JsonKey(name: "ChannelName")
  final String? channelName;
  @override
  @JsonKey(name: "SessionKey")
  final String? sessionKey;
  @override
  @JsonKey(name: "Origin")
  @MessagingSessionOriginConverter()
  final MessagingSessionOrigin? origin;
  @override
  @JsonKey(name: "ChannelType")
  final String? channelType;
  @override
  @JsonKey(name: "ConversationId")
  final String? conversationId;
  @override
  @JsonKey(name: "MessagingChannelId")
  final String? messagingChannelId;
  @override
  @JsonKey(name: "MessagingEndUserId")
  final String? messagingEndUserId;
  @override
  @JsonKey(name: "CreatedDate")
  final DateTime? createdDate;
  @override
  @JsonKey(name: "OwnerId")
  final String? ownerId;
  @override
  @JsonKey(name: "Status")
  @MessagingSessionStatusConverter()
  final MessagingSessionStatus? status;
  @override
  @JsonKey(name: "EndUserAccountId")
  final String? endUserAccountId;
  @override
  @JsonKey(name: "EndUserContactId")
  final String? endUserContactId;
  @override
  @JsonKey(name: "EndUserLanguage")
  final String? endUserLanguage;
  @override
  @JsonKey(name: "CaseId")
  final String? caseId;
  @override
  @JsonKey(name: "LeadId")
  final String? leadId;
  @override
  @JsonKey(name: "OpportunityId")
  final String? opportunityId;

  @override
  String toString() {
    return 'MessagingSessionResponse(id: $id, name: $name, channelName: $channelName, sessionKey: $sessionKey, origin: $origin, channelType: $channelType, conversationId: $conversationId, messagingChannelId: $messagingChannelId, messagingEndUserId: $messagingEndUserId, createdDate: $createdDate, ownerId: $ownerId, status: $status, endUserAccountId: $endUserAccountId, endUserContactId: $endUserContactId, endUserLanguage: $endUserLanguage, caseId: $caseId, leadId: $leadId, opportunityId: $opportunityId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingSessionResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.sessionKey, sessionKey) ||
                other.sessionKey == sessionKey) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.endUserAccountId, endUserAccountId) ||
                other.endUserAccountId == endUserAccountId) &&
            (identical(other.endUserContactId, endUserContactId) ||
                other.endUserContactId == endUserContactId) &&
            (identical(other.endUserLanguage, endUserLanguage) ||
                other.endUserLanguage == endUserLanguage) &&
            (identical(other.caseId, caseId) || other.caseId == caseId) &&
            (identical(other.leadId, leadId) || other.leadId == leadId) &&
            (identical(other.opportunityId, opportunityId) ||
                other.opportunityId == opportunityId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      channelName,
      sessionKey,
      origin,
      channelType,
      conversationId,
      messagingChannelId,
      messagingEndUserId,
      createdDate,
      ownerId,
      status,
      endUserAccountId,
      endUserContactId,
      endUserLanguage,
      caseId,
      leadId,
      opportunityId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingSessionResponseImplCopyWith<_$MessagingSessionResponseImpl>
      get copyWith => __$$MessagingSessionResponseImplCopyWithImpl<
          _$MessagingSessionResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingSessionResponseImplToJson(
      this,
    );
  }
}

abstract class _MessagingSessionResponse implements MessagingSessionResponse {
  const factory _MessagingSessionResponse(
          {@JsonKey(name: "Id") required final String id,
          @JsonKey(name: "Name") final String? name,
          @JsonKey(name: "ChannelName") final String? channelName,
          @JsonKey(name: "SessionKey") final String? sessionKey,
          @JsonKey(name: "Origin")
          @MessagingSessionOriginConverter()
          final MessagingSessionOrigin? origin,
          @JsonKey(name: "ChannelType") final String? channelType,
          @JsonKey(name: "ConversationId") final String? conversationId,
          @JsonKey(name: "MessagingChannelId") final String? messagingChannelId,
          @JsonKey(name: "MessagingEndUserId") final String? messagingEndUserId,
          @JsonKey(name: "CreatedDate") final DateTime? createdDate,
          @JsonKey(name: "OwnerId") final String? ownerId,
          @JsonKey(name: "Status")
          @MessagingSessionStatusConverter()
          final MessagingSessionStatus? status,
          @JsonKey(name: "EndUserAccountId") final String? endUserAccountId,
          @JsonKey(name: "EndUserContactId") final String? endUserContactId,
          @JsonKey(name: "EndUserLanguage") final String? endUserLanguage,
          @JsonKey(name: "CaseId") final String? caseId,
          @JsonKey(name: "LeadId") final String? leadId,
          @JsonKey(name: "OpportunityId") final String? opportunityId}) =
      _$MessagingSessionResponseImpl;

  factory _MessagingSessionResponse.fromJson(Map<String, dynamic> json) =
      _$MessagingSessionResponseImpl.fromJson;

  @override
  @JsonKey(name: "Id")
  String get id;
  @override
  @JsonKey(name: "Name")
  String? get name;
  @override
  @JsonKey(name: "ChannelName")
  String? get channelName;
  @override
  @JsonKey(name: "SessionKey")
  String? get sessionKey;
  @override
  @JsonKey(name: "Origin")
  @MessagingSessionOriginConverter()
  MessagingSessionOrigin? get origin;
  @override
  @JsonKey(name: "ChannelType")
  String? get channelType;
  @override
  @JsonKey(name: "ConversationId")
  String? get conversationId;
  @override
  @JsonKey(name: "MessagingChannelId")
  String? get messagingChannelId;
  @override
  @JsonKey(name: "MessagingEndUserId")
  String? get messagingEndUserId;
  @override
  @JsonKey(name: "CreatedDate")
  DateTime? get createdDate;
  @override
  @JsonKey(name: "OwnerId")
  String? get ownerId;
  @override
  @JsonKey(name: "Status")
  @MessagingSessionStatusConverter()
  MessagingSessionStatus? get status;
  @override
  @JsonKey(name: "EndUserAccountId")
  String? get endUserAccountId;
  @override
  @JsonKey(name: "EndUserContactId")
  String? get endUserContactId;
  @override
  @JsonKey(name: "EndUserLanguage")
  String? get endUserLanguage;
  @override
  @JsonKey(name: "CaseId")
  String? get caseId;
  @override
  @JsonKey(name: "LeadId")
  String? get leadId;
  @override
  @JsonKey(name: "OpportunityId")
  String? get opportunityId;
  @override
  @JsonKey(ignore: true)
  _$$MessagingSessionResponseImplCopyWith<_$MessagingSessionResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
