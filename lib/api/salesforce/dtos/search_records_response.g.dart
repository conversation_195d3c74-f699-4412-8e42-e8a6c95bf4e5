// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_records_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SearchRecordsResponseImpl _$$SearchRecordsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SearchRecordsResponseImpl(
      searchRecords: (json['searchRecords'] as List<dynamic>)
          .map((e) => SearchRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$SearchRecordsResponseImplToJson(
        _$SearchRecordsResponseImpl instance) =>
    <String, dynamic>{
      'searchRecords': instance.searchRecords.map((e) => e.toJson()).toList(),
    };

_$SearchRecordImpl _$$SearchRecordImplFromJson(Map<String, dynamic> json) =>
    _$SearchRecordImpl(
      attributes:
          Attributes.fromJson(json['attributes'] as Map<String, dynamic>),
      name: json['Name'] as String?,
      firstName: json['FirstName'] as String?,
      lastName: json['LastName'] as String?,
      photoUrl: json['PhotoUrl'] as String?,
      id: json['Id'] as String?,
      email: json['Email'] as String?,
      mobilePhone: json['MobilePhone'] as String?,
      title: json['Title'] as String?,
    );

Map<String, dynamic> _$$SearchRecordImplToJson(_$SearchRecordImpl instance) =>
    <String, dynamic>{
      'attributes': instance.attributes.toJson(),
      'Name': instance.name,
      'FirstName': instance.firstName,
      'LastName': instance.lastName,
      'PhotoUrl': instance.photoUrl,
      'Id': instance.id,
      'Email': instance.email,
      'MobilePhone': instance.mobilePhone,
      'Title': instance.title,
    };

_$AttributesImpl _$$AttributesImplFromJson(Map<String, dynamic> json) =>
    _$AttributesImpl(
      type: json['type'] as String,
      url: json['url'] as String,
    );

Map<String, dynamic> _$$AttributesImplToJson(_$AttributesImpl instance) =>
    <String, dynamic>{
      'type': instance.type,
      'url': instance.url,
    };
