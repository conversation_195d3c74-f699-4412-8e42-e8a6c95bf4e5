import 'package:freezed_annotation/freezed_annotation.dart';

part 'oauth_response.freezed.dart';
part 'oauth_response.g.dart';

@freezed
class OAuthResponse with _$OAuthResponse {
  @JsonSerializable(explicitToJson: true)
  const factory OAuthResponse({
    @Json<PERSON>ey(name: 'access_token') String? accessToken,
    @Json<PERSON>ey(name: 'instance_url') String? instanceUrl,
    @JsonKey(name: 'refresh_token') String? refreshToken,
    String? id,
    @Json<PERSON>ey(name: 'issued_at') String? issuedAt,
    String? signature,
    String? scope,
    @J<PERSON><PERSON>ey(name: 'token_type') String? tokenType,
    String? orgId,
    String? userId,
  }) = _OAuthResponse;

  factory OAuthResponse.fromJson(Map<String, dynamic> json) =>
      _$OAuthResponseFromJson(json);
}
