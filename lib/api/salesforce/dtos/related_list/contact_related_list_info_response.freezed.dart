// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_related_list_info_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ContactRelatedListInfoResponse _$ContactRelatedListInfoResponseFromJson(
    Map<String, dynamic> json) {
  return _ContactRelatedListInfoResponse.fromJson(json);
}

/// @nodoc
mixin _$ContactRelatedListInfoResponse {
  Map<String, dynamic> get body => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContactRelatedListInfoResponseCopyWith<ContactRelatedListInfoResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactRelatedListInfoResponseCopyWith<$Res> {
  factory $ContactRelatedListInfoResponseCopyWith(
          ContactRelatedListInfoResponse value,
          $Res Function(ContactRelatedListInfoResponse) then) =
      _$ContactRelatedListInfoResponseCopyWithImpl<$Res,
          ContactRelatedListInfoResponse>;
  @useResult
  $Res call({Map<String, dynamic> body});
}

/// @nodoc
class _$ContactRelatedListInfoResponseCopyWithImpl<$Res,
        $Val extends ContactRelatedListInfoResponse>
    implements $ContactRelatedListInfoResponseCopyWith<$Res> {
  _$ContactRelatedListInfoResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
  }) {
    return _then(_value.copyWith(
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContactRelatedListInfoResponseImplCopyWith<$Res>
    implements $ContactRelatedListInfoResponseCopyWith<$Res> {
  factory _$$ContactRelatedListInfoResponseImplCopyWith(
          _$ContactRelatedListInfoResponseImpl value,
          $Res Function(_$ContactRelatedListInfoResponseImpl) then) =
      __$$ContactRelatedListInfoResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<String, dynamic> body});
}

/// @nodoc
class __$$ContactRelatedListInfoResponseImplCopyWithImpl<$Res>
    extends _$ContactRelatedListInfoResponseCopyWithImpl<$Res,
        _$ContactRelatedListInfoResponseImpl>
    implements _$$ContactRelatedListInfoResponseImplCopyWith<$Res> {
  __$$ContactRelatedListInfoResponseImplCopyWithImpl(
      _$ContactRelatedListInfoResponseImpl _value,
      $Res Function(_$ContactRelatedListInfoResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
  }) {
    return _then(_$ContactRelatedListInfoResponseImpl(
      body: null == body
          ? _value._body
          : body // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContactRelatedListInfoResponseImpl
    implements _ContactRelatedListInfoResponse {
  const _$ContactRelatedListInfoResponseImpl(
      {final Map<String, dynamic> body = const {}})
      : _body = body;

  factory _$ContactRelatedListInfoResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ContactRelatedListInfoResponseImplFromJson(json);

  final Map<String, dynamic> _body;
  @override
  @JsonKey()
  Map<String, dynamic> get body {
    if (_body is EqualUnmodifiableMapView) return _body;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_body);
  }

  @override
  String toString() {
    return 'ContactRelatedListInfoResponse(body: $body)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactRelatedListInfoResponseImpl &&
            const DeepCollectionEquality().equals(other._body, _body));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_body));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactRelatedListInfoResponseImplCopyWith<
          _$ContactRelatedListInfoResponseImpl>
      get copyWith => __$$ContactRelatedListInfoResponseImplCopyWithImpl<
          _$ContactRelatedListInfoResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContactRelatedListInfoResponseImplToJson(
      this,
    );
  }
}

abstract class _ContactRelatedListInfoResponse
    implements ContactRelatedListInfoResponse {
  const factory _ContactRelatedListInfoResponse(
      {final Map<String, dynamic> body}) = _$ContactRelatedListInfoResponseImpl;

  factory _ContactRelatedListInfoResponse.fromJson(Map<String, dynamic> json) =
      _$ContactRelatedListInfoResponseImpl.fromJson;

  @override
  Map<String, dynamic> get body;
  @override
  @JsonKey(ignore: true)
  _$$ContactRelatedListInfoResponseImplCopyWith<
          _$ContactRelatedListInfoResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
