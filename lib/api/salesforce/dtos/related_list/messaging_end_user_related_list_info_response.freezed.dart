// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user_related_list_info_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingEndUserRelatedListInfoResponse
    _$MessagingEndUserRelatedListInfoResponseFromJson(
        Map<String, dynamic> json) {
  return _MessagingEndUserRelatedListInfoResponse.fromJson(json);
}

/// @nodoc
mixin _$MessagingEndUserRelatedListInfoResponse {
  Map<String, dynamic> get body => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingEndUserRelatedListInfoResponseCopyWith<
          MessagingEndUserRelatedListInfoResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingEndUserRelatedListInfoResponseCopyWith<$Res> {
  factory $MessagingEndUserRelatedListInfoResponseCopyWith(
          MessagingEndUserRelatedListInfoResponse value,
          $Res Function(MessagingEndUserRelatedListInfoResponse) then) =
      _$MessagingEndUserRelatedListInfoResponseCopyWithImpl<$Res,
          MessagingEndUserRelatedListInfoResponse>;
  @useResult
  $Res call({Map<String, dynamic> body});
}

/// @nodoc
class _$MessagingEndUserRelatedListInfoResponseCopyWithImpl<$Res,
        $Val extends MessagingEndUserRelatedListInfoResponse>
    implements $MessagingEndUserRelatedListInfoResponseCopyWith<$Res> {
  _$MessagingEndUserRelatedListInfoResponseCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
  }) {
    return _then(_value.copyWith(
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessagingEndUserRelatedListInfoResponseImplCopyWith<$Res>
    implements $MessagingEndUserRelatedListInfoResponseCopyWith<$Res> {
  factory _$$MessagingEndUserRelatedListInfoResponseImplCopyWith(
          _$MessagingEndUserRelatedListInfoResponseImpl value,
          $Res Function(_$MessagingEndUserRelatedListInfoResponseImpl) then) =
      __$$MessagingEndUserRelatedListInfoResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<String, dynamic> body});
}

/// @nodoc
class __$$MessagingEndUserRelatedListInfoResponseImplCopyWithImpl<$Res>
    extends _$MessagingEndUserRelatedListInfoResponseCopyWithImpl<$Res,
        _$MessagingEndUserRelatedListInfoResponseImpl>
    implements _$$MessagingEndUserRelatedListInfoResponseImplCopyWith<$Res> {
  __$$MessagingEndUserRelatedListInfoResponseImplCopyWithImpl(
      _$MessagingEndUserRelatedListInfoResponseImpl _value,
      $Res Function(_$MessagingEndUserRelatedListInfoResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
  }) {
    return _then(_$MessagingEndUserRelatedListInfoResponseImpl(
      body: null == body
          ? _value._body
          : body // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingEndUserRelatedListInfoResponseImpl
    implements _MessagingEndUserRelatedListInfoResponse {
  const _$MessagingEndUserRelatedListInfoResponseImpl(
      {final Map<String, dynamic> body = const {}})
      : _body = body;

  factory _$MessagingEndUserRelatedListInfoResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MessagingEndUserRelatedListInfoResponseImplFromJson(json);

  final Map<String, dynamic> _body;
  @override
  @JsonKey()
  Map<String, dynamic> get body {
    if (_body is EqualUnmodifiableMapView) return _body;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_body);
  }

  @override
  String toString() {
    return 'MessagingEndUserRelatedListInfoResponse(body: $body)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingEndUserRelatedListInfoResponseImpl &&
            const DeepCollectionEquality().equals(other._body, _body));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_body));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingEndUserRelatedListInfoResponseImplCopyWith<
          _$MessagingEndUserRelatedListInfoResponseImpl>
      get copyWith =>
          __$$MessagingEndUserRelatedListInfoResponseImplCopyWithImpl<
              _$MessagingEndUserRelatedListInfoResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingEndUserRelatedListInfoResponseImplToJson(
      this,
    );
  }
}

abstract class _MessagingEndUserRelatedListInfoResponse
    implements MessagingEndUserRelatedListInfoResponse {
  const factory _MessagingEndUserRelatedListInfoResponse(
          {final Map<String, dynamic> body}) =
      _$MessagingEndUserRelatedListInfoResponseImpl;

  factory _MessagingEndUserRelatedListInfoResponse.fromJson(
          Map<String, dynamic> json) =
      _$MessagingEndUserRelatedListInfoResponseImpl.fromJson;

  @override
  Map<String, dynamic> get body;
  @override
  @JsonKey(ignore: true)
  _$$MessagingEndUserRelatedListInfoResponseImplCopyWith<
          _$MessagingEndUserRelatedListInfoResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
