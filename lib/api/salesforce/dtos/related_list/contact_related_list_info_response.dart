import 'package:freezed_annotation/freezed_annotation.dart';

part 'contact_related_list_info_response.freezed.dart';
part 'contact_related_list_info_response.g.dart';

@freezed
class ContactRelatedListInfoResponse with _$ContactRelatedListInfoResponse {
  const factory ContactRelatedListInfoResponse({
    @Default({}) Map<String, dynamic> body
  }) = _ContactRelatedListInfoResponse;

  factory ContactRelatedListInfoResponse.fromJson(Map<String, dynamic> json) =>
      _$ContactRelatedListInfoResponseFromJson(json);
}