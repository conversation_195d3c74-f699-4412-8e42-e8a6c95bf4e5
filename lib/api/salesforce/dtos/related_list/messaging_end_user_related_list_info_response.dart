import 'package:freezed_annotation/freezed_annotation.dart';

part 'messaging_end_user_related_list_info_response.freezed.dart';
part 'messaging_end_user_related_list_info_response.g.dart';

@freezed
class MessagingEndUserRelatedListInfoResponse with _$MessagingEndUserRelatedListInfoResponse {
  const factory MessagingEndUserRelatedListInfoResponse({
    @Default({}) Map<String, dynamic> body
  }) = _MessagingEndUserRelatedListInfoResponse;

  factory MessagingEndUserRelatedListInfoResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserRelatedListInfoResponseFromJson(json);
}