// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'revoke_token_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RevokeTokenBody _$RevokeTokenBodyFromJson(Map<String, dynamic> json) {
  return _RevokeTokenBody.fromJson(json);
}

/// @nodoc
mixin _$RevokeTokenBody {
  String get token => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RevokeTokenBodyCopyWith<RevokeTokenBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RevokeTokenBodyCopyWith<$Res> {
  factory $RevokeTokenBodyCopyWith(
          RevokeTokenBody value, $Res Function(RevokeTokenBody) then) =
      _$RevokeTokenBodyCopyWithImpl<$Res, RevokeTokenBody>;
  @useResult
  $Res call({String token});
}

/// @nodoc
class _$RevokeTokenBodyCopyWithImpl<$Res, $Val extends RevokeTokenBody>
    implements $RevokeTokenBodyCopyWith<$Res> {
  _$RevokeTokenBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
  }) {
    return _then(_value.copyWith(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RevokeTokenBodyImplCopyWith<$Res>
    implements $RevokeTokenBodyCopyWith<$Res> {
  factory _$$RevokeTokenBodyImplCopyWith(_$RevokeTokenBodyImpl value,
          $Res Function(_$RevokeTokenBodyImpl) then) =
      __$$RevokeTokenBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String token});
}

/// @nodoc
class __$$RevokeTokenBodyImplCopyWithImpl<$Res>
    extends _$RevokeTokenBodyCopyWithImpl<$Res, _$RevokeTokenBodyImpl>
    implements _$$RevokeTokenBodyImplCopyWith<$Res> {
  __$$RevokeTokenBodyImplCopyWithImpl(
      _$RevokeTokenBodyImpl _value, $Res Function(_$RevokeTokenBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
  }) {
    return _then(_$RevokeTokenBodyImpl(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RevokeTokenBodyImpl implements _RevokeTokenBody {
  const _$RevokeTokenBodyImpl({required this.token});

  factory _$RevokeTokenBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$RevokeTokenBodyImplFromJson(json);

  @override
  final String token;

  @override
  String toString() {
    return 'RevokeTokenBody(token: $token)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RevokeTokenBodyImpl &&
            (identical(other.token, token) || other.token == token));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, token);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RevokeTokenBodyImplCopyWith<_$RevokeTokenBodyImpl> get copyWith =>
      __$$RevokeTokenBodyImplCopyWithImpl<_$RevokeTokenBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RevokeTokenBodyImplToJson(
      this,
    );
  }
}

abstract class _RevokeTokenBody implements RevokeTokenBody {
  const factory _RevokeTokenBody({required final String token}) =
      _$RevokeTokenBodyImpl;

  factory _RevokeTokenBody.fromJson(Map<String, dynamic> json) =
      _$RevokeTokenBodyImpl.fromJson;

  @override
  String get token;
  @override
  @JsonKey(ignore: true)
  _$$RevokeTokenBodyImplCopyWith<_$RevokeTokenBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
