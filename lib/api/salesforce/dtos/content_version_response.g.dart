// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'content_version_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContentVersionResponseImpl _$$ContentVersionResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ContentVersionResponseImpl(
      id: json['Id'] as String?,
      contentDocumentId: json['ContentDocumentId'] as String?,
      isLatest: json['IsLatest'] as bool?,
      contentBodyId: json['ContentBodyId'] as String?,
      versionNumber: json['VersionNumber'] as String?,
      title: json['Title'] as String?,
      sharingOption: json['SharingOption'] as String?,
      sharingPrivacy: json['SharingPrivacy'] as String?,
      pathOnClient: json['PathOnClient'] as String?,
      ratingCount: (json['RatingCount'] as num?)?.toInt(),
      isDeleted: json['IsDeleted'] as bool?,
      contentModifiedDate: json['ContentModifiedDate'] as String?,
      contentModifiedById: json['ContentModifiedById'] as String?,
      language: json['Language'] as String?,
      positiveRatingCount: (json['PositiveRatingCount'] as num?)?.toInt(),
      negativeRatingCount: (json['NegativeRatingCount'] as num?)?.toInt(),
      ownerId: json['OwnerId'] as String?,
      createdById: json['CreatedById'] as String?,
      createdDate: json['CreatedDate'] as String?,
      lastModifiedById: json['LastModifiedById'] as String?,
      lastModifiedDate: json['LastModifiedDate'] as String?,
      systemModstamp: json['SystemModstamp'] as String?,
      fileType: json['FileType'] as String?,
      publishStatus: json['PublishStatus'] as String?,
      versionData: json['VersionData'] as String?,
      contentSize: (json['ContentSize'] as num?)?.toInt(),
      fileExtension: json['FileExtension'] as String?,
      firstPublishLocationId: json['FirstPublishLocationId'] as String?,
      origin: json['Origin'] as String?,
      contentLocation: json['ContentLocation'] as String?,
      versionDataUrl: json['VersionDataUrl'] as String?,
    );

Map<String, dynamic> _$$ContentVersionResponseImplToJson(
        _$ContentVersionResponseImpl instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'ContentDocumentId': instance.contentDocumentId,
      'IsLatest': instance.isLatest,
      'ContentBodyId': instance.contentBodyId,
      'VersionNumber': instance.versionNumber,
      'Title': instance.title,
      'SharingOption': instance.sharingOption,
      'SharingPrivacy': instance.sharingPrivacy,
      'PathOnClient': instance.pathOnClient,
      'RatingCount': instance.ratingCount,
      'IsDeleted': instance.isDeleted,
      'ContentModifiedDate': instance.contentModifiedDate,
      'ContentModifiedById': instance.contentModifiedById,
      'Language': instance.language,
      'PositiveRatingCount': instance.positiveRatingCount,
      'NegativeRatingCount': instance.negativeRatingCount,
      'OwnerId': instance.ownerId,
      'CreatedById': instance.createdById,
      'CreatedDate': instance.createdDate,
      'LastModifiedById': instance.lastModifiedById,
      'LastModifiedDate': instance.lastModifiedDate,
      'SystemModstamp': instance.systemModstamp,
      'FileType': instance.fileType,
      'PublishStatus': instance.publishStatus,
      'VersionData': instance.versionData,
      'ContentSize': instance.contentSize,
      'FileExtension': instance.fileExtension,
      'FirstPublishLocationId': instance.firstPublishLocationId,
      'Origin': instance.origin,
      'ContentLocation': instance.contentLocation,
      'VersionDataUrl': instance.versionDataUrl,
    };
