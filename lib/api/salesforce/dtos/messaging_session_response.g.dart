// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_session_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingSessionResponseImpl _$$MessagingSessionResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingSessionResponseImpl(
      id: json['Id'] as String,
      name: json['Name'] as String?,
      channelName: json['ChannelName'] as String?,
      sessionKey: json['SessionKey'] as String?,
      origin: _$JsonConverterFromJson<String, MessagingSessionOrigin>(
          json['Origin'], const MessagingSessionOriginConverter().fromJson),
      channelType: json['ChannelType'] as String?,
      conversationId: json['ConversationId'] as String?,
      messagingChannelId: json['MessagingChannelId'] as String?,
      messagingEndUserId: json['MessagingEndUserId'] as String?,
      createdDate: json['CreatedDate'] == null
          ? null
          : DateTime.parse(json['CreatedDate'] as String),
      ownerId: json['OwnerId'] as String?,
      status: _$JsonConverterFromJson<String, MessagingSessionStatus>(
          json['Status'], const MessagingSessionStatusConverter().fromJson),
      endUserAccountId: json['EndUserAccountId'] as String?,
      endUserContactId: json['EndUserContactId'] as String?,
      endUserLanguage: json['EndUserLanguage'] as String?,
      caseId: json['CaseId'] as String?,
      leadId: json['LeadId'] as String?,
      opportunityId: json['OpportunityId'] as String?,
    );

Map<String, dynamic> _$$MessagingSessionResponseImplToJson(
        _$MessagingSessionResponseImpl instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'Name': instance.name,
      'ChannelName': instance.channelName,
      'SessionKey': instance.sessionKey,
      'Origin': _$JsonConverterToJson<String, MessagingSessionOrigin>(
          instance.origin, const MessagingSessionOriginConverter().toJson),
      'ChannelType': instance.channelType,
      'ConversationId': instance.conversationId,
      'MessagingChannelId': instance.messagingChannelId,
      'MessagingEndUserId': instance.messagingEndUserId,
      'CreatedDate': instance.createdDate?.toIso8601String(),
      'OwnerId': instance.ownerId,
      'Status': _$JsonConverterToJson<String, MessagingSessionStatus>(
          instance.status, const MessagingSessionStatusConverter().toJson),
      'EndUserAccountId': instance.endUserAccountId,
      'EndUserContactId': instance.endUserContactId,
      'EndUserLanguage': instance.endUserLanguage,
      'CaseId': instance.caseId,
      'LeadId': instance.leadId,
      'OpportunityId': instance.opportunityId,
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
