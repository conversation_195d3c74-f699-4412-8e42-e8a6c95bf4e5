import 'package:freezed_annotation/freezed_annotation.dart';

part 'record_layout.freezed.dart';
part 'record_layout.g.dart';

@freezed
class RecordLayout with _$RecordLayout {
  const factory RecordLayout({
    String? id,
    String? name,
    @Default([]) List<Map<String, dynamic>> sections,
    // required List<RecordLayoutSection> sections,
  }) = _RecordLayout;

  factory RecordLayout.fromJson(Map<String, dynamic> json) =>
      _$RecordLayoutFromJson(json);
}