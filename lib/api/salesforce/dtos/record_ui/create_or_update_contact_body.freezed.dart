// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_or_update_contact_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateOrUpdateContactBody _$CreateOrUpdateContactBodyFromJson(
    Map<String, dynamic> json) {
  return _CreateOrUpdateContactBody.fromJson(json);
}

/// @nodoc
mixin _$CreateOrUpdateContactBody {
  String get lastName => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  @ParsePhoneNumberConverter()
  String get mobilePhone => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateOrUpdateContactBodyCopyWith<CreateOrUpdateContactBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateOrUpdateContactBodyCopyWith<$Res> {
  factory $CreateOrUpdateContactBodyCopyWith(CreateOrUpdateContactBody value,
          $Res Function(CreateOrUpdateContactBody) then) =
      _$CreateOrUpdateContactBodyCopyWithImpl<$Res, CreateOrUpdateContactBody>;
  @useResult
  $Res call(
      {String lastName,
      String firstName,
      String? email,
      @ParsePhoneNumberConverter() String mobilePhone});
}

/// @nodoc
class _$CreateOrUpdateContactBodyCopyWithImpl<$Res,
        $Val extends CreateOrUpdateContactBody>
    implements $CreateOrUpdateContactBodyCopyWith<$Res> {
  _$CreateOrUpdateContactBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastName = null,
    Object? firstName = null,
    Object? email = freezed,
    Object? mobilePhone = null,
  }) {
    return _then(_value.copyWith(
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: null == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateOrUpdateContactBodyImplCopyWith<$Res>
    implements $CreateOrUpdateContactBodyCopyWith<$Res> {
  factory _$$CreateOrUpdateContactBodyImplCopyWith(
          _$CreateOrUpdateContactBodyImpl value,
          $Res Function(_$CreateOrUpdateContactBodyImpl) then) =
      __$$CreateOrUpdateContactBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String lastName,
      String firstName,
      String? email,
      @ParsePhoneNumberConverter() String mobilePhone});
}

/// @nodoc
class __$$CreateOrUpdateContactBodyImplCopyWithImpl<$Res>
    extends _$CreateOrUpdateContactBodyCopyWithImpl<$Res,
        _$CreateOrUpdateContactBodyImpl>
    implements _$$CreateOrUpdateContactBodyImplCopyWith<$Res> {
  __$$CreateOrUpdateContactBodyImplCopyWithImpl(
      _$CreateOrUpdateContactBodyImpl _value,
      $Res Function(_$CreateOrUpdateContactBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastName = null,
    Object? firstName = null,
    Object? email = freezed,
    Object? mobilePhone = null,
  }) {
    return _then(_$CreateOrUpdateContactBodyImpl(
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: null == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateOrUpdateContactBodyImpl implements _CreateOrUpdateContactBody {
  const _$CreateOrUpdateContactBodyImpl(
      {required this.lastName,
      required this.firstName,
      this.email,
      @ParsePhoneNumberConverter() required this.mobilePhone});

  factory _$CreateOrUpdateContactBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateOrUpdateContactBodyImplFromJson(json);

  @override
  final String lastName;
  @override
  final String firstName;
  @override
  final String? email;
  @override
  @ParsePhoneNumberConverter()
  final String mobilePhone;

  @override
  String toString() {
    return 'CreateOrUpdateContactBody(lastName: $lastName, firstName: $firstName, email: $email, mobilePhone: $mobilePhone)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateOrUpdateContactBodyImpl &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, lastName, firstName, email, mobilePhone);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateOrUpdateContactBodyImplCopyWith<_$CreateOrUpdateContactBodyImpl>
      get copyWith => __$$CreateOrUpdateContactBodyImplCopyWithImpl<
          _$CreateOrUpdateContactBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateOrUpdateContactBodyImplToJson(
      this,
    );
  }
}

abstract class _CreateOrUpdateContactBody implements CreateOrUpdateContactBody {
  const factory _CreateOrUpdateContactBody(
          {required final String lastName,
          required final String firstName,
          final String? email,
          @ParsePhoneNumberConverter() required final String mobilePhone}) =
      _$CreateOrUpdateContactBodyImpl;

  factory _CreateOrUpdateContactBody.fromJson(Map<String, dynamic> json) =
      _$CreateOrUpdateContactBodyImpl.fromJson;

  @override
  String get lastName;
  @override
  String get firstName;
  @override
  String? get email;
  @override
  @ParsePhoneNumberConverter()
  String get mobilePhone;
  @override
  @JsonKey(ignore: true)
  _$$CreateOrUpdateContactBodyImplCopyWith<_$CreateOrUpdateContactBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
