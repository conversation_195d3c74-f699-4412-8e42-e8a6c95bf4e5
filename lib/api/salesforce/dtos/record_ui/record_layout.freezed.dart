// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'record_layout.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RecordLayout _$RecordLayoutFromJson(Map<String, dynamic> json) {
  return _RecordLayout.fromJson(json);
}

/// @nodoc
mixin _$RecordLayout {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get sections => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RecordLayoutCopyWith<RecordLayout> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecordLayoutCopyWith<$Res> {
  factory $RecordLayoutCopyWith(
          RecordLayout value, $Res Function(RecordLayout) then) =
      _$RecordLayoutCopyWithImpl<$Res, RecordLayout>;
  @useResult
  $Res call({String? id, String? name, List<Map<String, dynamic>> sections});
}

/// @nodoc
class _$RecordLayoutCopyWithImpl<$Res, $Val extends RecordLayout>
    implements $RecordLayoutCopyWith<$Res> {
  _$RecordLayoutCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? sections = null,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      sections: null == sections
          ? _value.sections
          : sections // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecordLayoutImplCopyWith<$Res>
    implements $RecordLayoutCopyWith<$Res> {
  factory _$$RecordLayoutImplCopyWith(
          _$RecordLayoutImpl value, $Res Function(_$RecordLayoutImpl) then) =
      __$$RecordLayoutImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? id, String? name, List<Map<String, dynamic>> sections});
}

/// @nodoc
class __$$RecordLayoutImplCopyWithImpl<$Res>
    extends _$RecordLayoutCopyWithImpl<$Res, _$RecordLayoutImpl>
    implements _$$RecordLayoutImplCopyWith<$Res> {
  __$$RecordLayoutImplCopyWithImpl(
      _$RecordLayoutImpl _value, $Res Function(_$RecordLayoutImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? sections = null,
  }) {
    return _then(_$RecordLayoutImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      sections: null == sections
          ? _value._sections
          : sections // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecordLayoutImpl implements _RecordLayout {
  const _$RecordLayoutImpl(
      {this.id,
      this.name,
      final List<Map<String, dynamic>> sections = const []})
      : _sections = sections;

  factory _$RecordLayoutImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecordLayoutImplFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  final List<Map<String, dynamic>> _sections;
  @override
  @JsonKey()
  List<Map<String, dynamic>> get sections {
    if (_sections is EqualUnmodifiableListView) return _sections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sections);
  }

  @override
  String toString() {
    return 'RecordLayout(id: $id, name: $name, sections: $sections)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecordLayoutImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._sections, _sections));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, const DeepCollectionEquality().hash(_sections));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RecordLayoutImplCopyWith<_$RecordLayoutImpl> get copyWith =>
      __$$RecordLayoutImplCopyWithImpl<_$RecordLayoutImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecordLayoutImplToJson(
      this,
    );
  }
}

abstract class _RecordLayout implements RecordLayout {
  const factory _RecordLayout(
      {final String? id,
      final String? name,
      final List<Map<String, dynamic>> sections}) = _$RecordLayoutImpl;

  factory _RecordLayout.fromJson(Map<String, dynamic> json) =
      _$RecordLayoutImpl.fromJson;

  @override
  String? get id;
  @override
  String? get name;
  @override
  List<Map<String, dynamic>> get sections;
  @override
  @JsonKey(ignore: true)
  _$$RecordLayoutImplCopyWith<_$RecordLayoutImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
