import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'update_messaging_end_user_body.freezed.dart';
part 'update_messaging_end_user_body.g.dart';
@freezed
class UpdateMessagingEndUserBody with _$UpdateMessagingEndUserBody {
  const factory UpdateMessagingEndUserBody({
    // @J<PERSON><PERSON><PERSON>(name: 'ContactId')
    @ParseSfIdConverter() required SfId contactId,
    // @<PERSON><PERSON><PERSON><PERSON>(name: 'MessagingPlatformKey')
    // String? messagingPlatformKey,
    // @<PERSON><PERSON><PERSON><PERSON>(name: 'Name')
    // String? name,
  }) = _UpdateMessagingEndUserBody;

  factory UpdateMessagingEndUserBody.fromJson(Map<String, dynamic> json) =>
      _$UpdateMessagingEndUserBodyFromJson(json);
}