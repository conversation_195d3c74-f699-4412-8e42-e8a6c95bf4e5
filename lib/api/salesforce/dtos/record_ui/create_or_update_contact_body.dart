import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/converters/parse_phone_number_converter.dart';

part 'create_or_update_contact_body.freezed.dart';
part 'create_or_update_contact_body.g.dart';
@freezed
class CreateOrUpdateContactBody with _$CreateOrUpdateContactBody {

  const factory CreateOrUpdateContactBody({
    required String lastName,
    required String firstName,
    String? email,
    @ParsePhoneNumberConverter() required String mobilePhone,
  }) = _CreateOrUpdateContactBody;

  factory CreateOrUpdateContactBody.fromJson(Map<String, dynamic> json) =>
      _$CreateOrUpdateContactBodyFromJson(json);
}