// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'record_layout_section_user_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RecordLayoutSectionUserState _$RecordLayoutSectionUserStateFromJson(
    Map<String, dynamic> json) {
  return _RecordLayoutSectionUserState.fromJson(json);
}

/// @nodoc
mixin _$RecordLayoutSectionUserState {
  String? get id => throw _privateConstructorUsedError;
  bool get collapsed => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RecordLayoutSectionUserStateCopyWith<RecordLayoutSectionUserState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecordLayoutSectionUserStateCopyWith<$Res> {
  factory $RecordLayoutSectionUserStateCopyWith(
          RecordLayoutSectionUserState value,
          $Res Function(RecordLayoutSectionUserState) then) =
      _$RecordLayoutSectionUserStateCopyWithImpl<$Res,
          RecordLayoutSectionUserState>;
  @useResult
  $Res call({String? id, bool collapsed});
}

/// @nodoc
class _$RecordLayoutSectionUserStateCopyWithImpl<$Res,
        $Val extends RecordLayoutSectionUserState>
    implements $RecordLayoutSectionUserStateCopyWith<$Res> {
  _$RecordLayoutSectionUserStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? collapsed = null,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      collapsed: null == collapsed
          ? _value.collapsed
          : collapsed // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecordLayoutSectionUserStateImplCopyWith<$Res>
    implements $RecordLayoutSectionUserStateCopyWith<$Res> {
  factory _$$RecordLayoutSectionUserStateImplCopyWith(
          _$RecordLayoutSectionUserStateImpl value,
          $Res Function(_$RecordLayoutSectionUserStateImpl) then) =
      __$$RecordLayoutSectionUserStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? id, bool collapsed});
}

/// @nodoc
class __$$RecordLayoutSectionUserStateImplCopyWithImpl<$Res>
    extends _$RecordLayoutSectionUserStateCopyWithImpl<$Res,
        _$RecordLayoutSectionUserStateImpl>
    implements _$$RecordLayoutSectionUserStateImplCopyWith<$Res> {
  __$$RecordLayoutSectionUserStateImplCopyWithImpl(
      _$RecordLayoutSectionUserStateImpl _value,
      $Res Function(_$RecordLayoutSectionUserStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? collapsed = null,
  }) {
    return _then(_$RecordLayoutSectionUserStateImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      collapsed: null == collapsed
          ? _value.collapsed
          : collapsed // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecordLayoutSectionUserStateImpl
    implements _RecordLayoutSectionUserState {
  const _$RecordLayoutSectionUserStateImpl({this.id, this.collapsed = true});

  factory _$RecordLayoutSectionUserStateImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$RecordLayoutSectionUserStateImplFromJson(json);

  @override
  final String? id;
  @override
  @JsonKey()
  final bool collapsed;

  @override
  String toString() {
    return 'RecordLayoutSectionUserState(id: $id, collapsed: $collapsed)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecordLayoutSectionUserStateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.collapsed, collapsed) ||
                other.collapsed == collapsed));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, collapsed);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RecordLayoutSectionUserStateImplCopyWith<
          _$RecordLayoutSectionUserStateImpl>
      get copyWith => __$$RecordLayoutSectionUserStateImplCopyWithImpl<
          _$RecordLayoutSectionUserStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecordLayoutSectionUserStateImplToJson(
      this,
    );
  }
}

abstract class _RecordLayoutSectionUserState
    implements RecordLayoutSectionUserState {
  const factory _RecordLayoutSectionUserState(
      {final String? id,
      final bool collapsed}) = _$RecordLayoutSectionUserStateImpl;

  factory _RecordLayoutSectionUserState.fromJson(Map<String, dynamic> json) =
      _$RecordLayoutSectionUserStateImpl.fromJson;

  @override
  String? get id;
  @override
  bool get collapsed;
  @override
  @JsonKey(ignore: true)
  _$$RecordLayoutSectionUserStateImplCopyWith<
          _$RecordLayoutSectionUserStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
