import 'package:freezed_annotation/freezed_annotation.dart';

part 'record_layout_section_user_state.freezed.dart';
part 'record_layout_section_user_state.g.dart';
@freezed
class RecordLayoutSectionUserState with _$RecordLayoutSectionUserState {
  const factory RecordLayoutSectionUserState({
    String? id,
    @Default(true) bool collapsed,
  }) = _RecordLayoutSectionUserState;

  factory RecordLayoutSectionUserState.fromJson(Map<String, dynamic> json) =>
      _$RecordLayoutSectionUserStateFromJson(json);
}