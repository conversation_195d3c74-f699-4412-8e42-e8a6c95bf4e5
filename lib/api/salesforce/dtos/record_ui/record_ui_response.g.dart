// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'record_ui_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecordUiResponseImpl _$$RecordUiResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$RecordUiResponseImpl(
      layoutUserStates:
          (json['layoutUserStates'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    k,
                    RecordLayoutSectionUserState.fromJson(
                        e as Map<String, dynamic>)),
              ) ??
              const {},
      layouts: (json['layouts'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                k,
                (e as Map<String, dynamic>).map(
                  (k, e) => MapEntry(
                      k,
                      (e as Map<String, dynamic>).map(
                        (k, e) => MapEntry(
                            k,
                            (e as Map<String, dynamic>).map(
                              (k, e) => MapEntry(
                                  k,
                                  RecordLayout.fromJson(
                                      e as Map<String, dynamic>)),
                            )),
                      )),
                )),
          ) ??
          const {},
      objectInfos: (json['objectInfos'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry(k, ObjectInfo.fromJson(e as Map<String, dynamic>)),
          ) ??
          const {},
      records: json['records'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$RecordUiResponseImplToJson(
        _$RecordUiResponseImpl instance) =>
    <String, dynamic>{
      'layoutUserStates':
          instance.layoutUserStates.map((k, e) => MapEntry(k, e.toJson())),
      'layouts': instance.layouts.map((k, e) => MapEntry(
          k,
          e.map((k, e) => MapEntry(
              k,
              e.map((k, e) =>
                  MapEntry(k, e.map((k, e) => MapEntry(k, e.toJson())))))))),
      'objectInfos':
          instance.objectInfos.map((k, e) => MapEntry(k, e.toJson())),
      'records': instance.records,
    };
