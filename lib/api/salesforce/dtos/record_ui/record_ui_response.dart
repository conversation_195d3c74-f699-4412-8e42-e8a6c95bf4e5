import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/object_info/object_info.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/record_layout.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/record_layout_section_user_state.dart';

part 'record_ui_response.freezed.dart';
part 'record_ui_response.g.dart';

@freezed
class RecordUiResponse with _$RecordUiResponse {
  /*
  Property Name	Type	Description	Filter Group and Version	Available Version
layoutUserStates	Map<String, Record Layout Section User State>	A map of layout IDs to user state information.	Medium, 41.0	41.0
layouts	Map<String, Map<String, Map<String, Map<String, Record Layout>>>>	A map of object API names to layout information for each object.	Medium, 41.0	41.0
objectInfos	Map<String, Object Info>	A map of object API names to each object’s metadata.	Big, 41.0	41.0
records	Map<String, Record>	A map of record IDs to each record’s data.	Small, 41.0	41.0
   */
  const factory RecordUiResponse({
    @Default({}) Map<String, RecordLayoutSectionUserState> layoutUserStates,
    @Default({}) Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>	layouts,
    @Default({}) Map<String, ObjectInfo> objectInfos,
    @Default({}) Map<String, dynamic> records,
  }) = _RecordUiResponse;

  factory RecordUiResponse.fromJson(Map<String, dynamic> json) =>
      _$RecordUiResponseFromJson(json);
}