import 'package:freezed_annotation/freezed_annotation.dart';

part 'field.freezed.dart';
part 'field.g.dart';
@freezed
class Field with _$Field {
  const factory Field({
    String? apiName,
    @Default(false) bool calculated,
    @Default(false) bool compound,
    String? compoundComponentName,
    String? compoundFieldName,
    String? controllerName,
    @Default([]) List<String> controllingFields,
    @Default(false) bool createable,
    @Default(false) bool custom,
    String? dataType,
    /// this can come as bool:false
    // String? externalId,
    String? extraTypeInfo,
    @Default(false) bool filterable,
    // filteredLookupInfo; SF Type: Filtered Lookup Info: https://developer.salesforce.com/docs/atlas.en-us.uiapi.meta/uiapi/ui_api_responses_filtered_lookup_info.htm#ui_api_responses_filtered_lookup_info
    @Default(false) bool highScaleNumber,
    @Default(false) bool htmlFormatted,
    String? inlineHelpText,
    String? label,
    int? length,
    String? maskType,
    @Default(false) bool nameField,
    @Default(false) bool polymorphicForeignKey,
    int? precision,
    @Default(false) bool reference,
    String? referenceTargetField,
    @Default({}) Map<String, dynamic> referenceToInfos,
    String? relationshipName,
    @Default(false) bool required,
    int? scale,
    @Default(false) bool searchPrefilterable,
    @Default(false) bool sortable,
    @Default(false) bool unique,
    @Default(false) bool updateable,
  }) = _Field;

  factory Field.fromJson(Map<String, dynamic> json) =>
      _$FieldFromJson(json);
}