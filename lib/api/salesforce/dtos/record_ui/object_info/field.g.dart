// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'field.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FieldImpl _$$FieldImplFromJson(Map<String, dynamic> json) => _$FieldImpl(
      apiName: json['apiName'] as String?,
      calculated: json['calculated'] as bool? ?? false,
      compound: json['compound'] as bool? ?? false,
      compoundComponentName: json['compoundComponentName'] as String?,
      compoundFieldName: json['compoundFieldName'] as String?,
      controllerName: json['controllerName'] as String?,
      controllingFields: (json['controllingFields'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      createable: json['createable'] as bool? ?? false,
      custom: json['custom'] as bool? ?? false,
      dataType: json['dataType'] as String?,
      extraTypeInfo: json['extraTypeInfo'] as String?,
      filterable: json['filterable'] as bool? ?? false,
      highScaleNumber: json['highScaleNumber'] as bool? ?? false,
      htmlFormatted: json['htmlFormatted'] as bool? ?? false,
      inlineHelpText: json['inlineHelpText'] as String?,
      label: json['label'] as String?,
      length: (json['length'] as num?)?.toInt(),
      maskType: json['maskType'] as String?,
      nameField: json['nameField'] as bool? ?? false,
      polymorphicForeignKey: json['polymorphicForeignKey'] as bool? ?? false,
      precision: (json['precision'] as num?)?.toInt(),
      reference: json['reference'] as bool? ?? false,
      referenceTargetField: json['referenceTargetField'] as String?,
      referenceToInfos:
          json['referenceToInfos'] as Map<String, dynamic>? ?? const {},
      relationshipName: json['relationshipName'] as String?,
      required: json['required'] as bool? ?? false,
      scale: (json['scale'] as num?)?.toInt(),
      searchPrefilterable: json['searchPrefilterable'] as bool? ?? false,
      sortable: json['sortable'] as bool? ?? false,
      unique: json['unique'] as bool? ?? false,
      updateable: json['updateable'] as bool? ?? false,
    );

Map<String, dynamic> _$$FieldImplToJson(_$FieldImpl instance) =>
    <String, dynamic>{
      'apiName': instance.apiName,
      'calculated': instance.calculated,
      'compound': instance.compound,
      'compoundComponentName': instance.compoundComponentName,
      'compoundFieldName': instance.compoundFieldName,
      'controllerName': instance.controllerName,
      'controllingFields': instance.controllingFields,
      'createable': instance.createable,
      'custom': instance.custom,
      'dataType': instance.dataType,
      'extraTypeInfo': instance.extraTypeInfo,
      'filterable': instance.filterable,
      'highScaleNumber': instance.highScaleNumber,
      'htmlFormatted': instance.htmlFormatted,
      'inlineHelpText': instance.inlineHelpText,
      'label': instance.label,
      'length': instance.length,
      'maskType': instance.maskType,
      'nameField': instance.nameField,
      'polymorphicForeignKey': instance.polymorphicForeignKey,
      'precision': instance.precision,
      'reference': instance.reference,
      'referenceTargetField': instance.referenceTargetField,
      'referenceToInfos': instance.referenceToInfos,
      'relationshipName': instance.relationshipName,
      'required': instance.required,
      'scale': instance.scale,
      'searchPrefilterable': instance.searchPrefilterable,
      'sortable': instance.sortable,
      'unique': instance.unique,
      'updateable': instance.updateable,
    };
