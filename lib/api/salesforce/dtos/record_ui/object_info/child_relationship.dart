import 'package:freezed_annotation/freezed_annotation.dart';

part 'child_relationship.freezed.dart';
part 'child_relationship.g.dart';
@freezed
class ChildRelationship with _$ChildRelationship {
  const factory ChildRelationship({
    String? childObjectApiName,
    String? fieldName,
    @Default([]) List<String> junctionIdListNames,
    @Default([]) List<String> junctionReferenceTo,
    String? relationshipName,
  }) = _ChildRelationship;

  factory ChildRelationship.fromJson(Map<String, dynamic> json) =>
      _$ChildRelationshipFromJson(json);
}