// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'object_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ObjectInfoImpl _$$ObjectInfoImplFromJson(Map<String, dynamic> json) =>
    _$ObjectInfoImpl(
      apiName: json['apiName'] as String?,
      associateEntityType: json['associateEntityType'] as String?,
      associateParentEntity: json['associateParentEntity'] as String?,
      childRelationships: (json['childRelationships'] as List<dynamic>?)
              ?.map(
                  (e) => ChildRelationship.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      compactLayoutable: json['compactLayoutable'] as bool? ?? false,
      createable: json['createable'] as bool? ?? false,
      custom: json['custom'] as bool? ?? false,
      defaultRecordTypeId: json['defaultRecordTypeId'] as String?,
      deleteable: json['deleteable'] as bool? ?? false,
      dependentFields: (json['dependentFields'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as Object),
          ) ??
          const {},
      feedEnabled: json['feedEnabled'] as bool? ?? false,
      fields: json['fields'] as Map<String, dynamic>? ?? const {},
      keyPrefix: json['keyPrefix'] as String?,
      label: json['label'] as String?,
      labelPlural: json['labelPlural'] as String?,
      layoutable: json['layoutable'] as bool? ?? false,
      mruEnabled: json['mruEnabled'] as bool? ?? false,
      nameFields: (json['nameFields'] as List<dynamic>?)
              ?.map((e) => e as String?)
              .toList() ??
          const [],
      queryable: json['queryable'] as bool? ?? false,
      searchable: json['searchable'] as bool? ?? false,
      searchLayoutable: json['searchLayoutable'] as bool? ?? false,
      updateable: json['updateable'] as bool? ?? false,
    );

Map<String, dynamic> _$$ObjectInfoImplToJson(_$ObjectInfoImpl instance) =>
    <String, dynamic>{
      'apiName': instance.apiName,
      'associateEntityType': instance.associateEntityType,
      'associateParentEntity': instance.associateParentEntity,
      'childRelationships':
          instance.childRelationships.map((e) => e.toJson()).toList(),
      'compactLayoutable': instance.compactLayoutable,
      'createable': instance.createable,
      'custom': instance.custom,
      'defaultRecordTypeId': instance.defaultRecordTypeId,
      'deleteable': instance.deleteable,
      'dependentFields': instance.dependentFields,
      'feedEnabled': instance.feedEnabled,
      'fields': instance.fields,
      'keyPrefix': instance.keyPrefix,
      'label': instance.label,
      'labelPlural': instance.labelPlural,
      'layoutable': instance.layoutable,
      'mruEnabled': instance.mruEnabled,
      'nameFields': instance.nameFields,
      'queryable': instance.queryable,
      'searchable': instance.searchable,
      'searchLayoutable': instance.searchLayoutable,
      'updateable': instance.updateable,
    };
