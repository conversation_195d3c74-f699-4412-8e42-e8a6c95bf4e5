// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'object_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ObjectInfo _$ObjectInfoFromJson(Map<String, dynamic> json) {
  return _ObjectInfo.fromJson(json);
}

/// @nodoc
mixin _$ObjectInfo {
  String? get apiName => throw _privateConstructorUsedError;
  String? get associateEntityType => throw _privateConstructorUsedError;
  String? get associateParentEntity => throw _privateConstructorUsedError;
  List<ChildRelationship> get childRelationships =>
      throw _privateConstructorUsedError;
  bool get compactLayoutable => throw _privateConstructorUsedError;
  bool get createable => throw _privateConstructorUsedError;
  bool get custom => throw _privateConstructorUsedError;
  String? get defaultRecordTypeId => throw _privateConstructorUsedError;
  bool get deleteable => throw _privateConstructorUsedError;
  Map<String, Object> get dependentFields => throw _privateConstructorUsedError;
  bool get feedEnabled =>
      throw _privateConstructorUsedError; // TODO: get Field working ... had issues parsing
// @Default({}) Map<String, Field> fields,
  Map<String, dynamic> get fields => throw _privateConstructorUsedError;
  String? get keyPrefix => throw _privateConstructorUsedError;
  String? get label => throw _privateConstructorUsedError;
  String? get labelPlural => throw _privateConstructorUsedError;
  bool get layoutable => throw _privateConstructorUsedError;
  bool get mruEnabled => throw _privateConstructorUsedError;
  List<String?> get nameFields => throw _privateConstructorUsedError;
  bool get queryable =>
      throw _privateConstructorUsedError; // Map<String, RecordTypeInfo> recordTypeInfos,
  bool get searchable => throw _privateConstructorUsedError;
  bool get searchLayoutable =>
      throw _privateConstructorUsedError; // ThemeInfo themeInfo,
  bool get updateable => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ObjectInfoCopyWith<ObjectInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ObjectInfoCopyWith<$Res> {
  factory $ObjectInfoCopyWith(
          ObjectInfo value, $Res Function(ObjectInfo) then) =
      _$ObjectInfoCopyWithImpl<$Res, ObjectInfo>;
  @useResult
  $Res call(
      {String? apiName,
      String? associateEntityType,
      String? associateParentEntity,
      List<ChildRelationship> childRelationships,
      bool compactLayoutable,
      bool createable,
      bool custom,
      String? defaultRecordTypeId,
      bool deleteable,
      Map<String, Object> dependentFields,
      bool feedEnabled,
      Map<String, dynamic> fields,
      String? keyPrefix,
      String? label,
      String? labelPlural,
      bool layoutable,
      bool mruEnabled,
      List<String?> nameFields,
      bool queryable,
      bool searchable,
      bool searchLayoutable,
      bool updateable});
}

/// @nodoc
class _$ObjectInfoCopyWithImpl<$Res, $Val extends ObjectInfo>
    implements $ObjectInfoCopyWith<$Res> {
  _$ObjectInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiName = freezed,
    Object? associateEntityType = freezed,
    Object? associateParentEntity = freezed,
    Object? childRelationships = null,
    Object? compactLayoutable = null,
    Object? createable = null,
    Object? custom = null,
    Object? defaultRecordTypeId = freezed,
    Object? deleteable = null,
    Object? dependentFields = null,
    Object? feedEnabled = null,
    Object? fields = null,
    Object? keyPrefix = freezed,
    Object? label = freezed,
    Object? labelPlural = freezed,
    Object? layoutable = null,
    Object? mruEnabled = null,
    Object? nameFields = null,
    Object? queryable = null,
    Object? searchable = null,
    Object? searchLayoutable = null,
    Object? updateable = null,
  }) {
    return _then(_value.copyWith(
      apiName: freezed == apiName
          ? _value.apiName
          : apiName // ignore: cast_nullable_to_non_nullable
              as String?,
      associateEntityType: freezed == associateEntityType
          ? _value.associateEntityType
          : associateEntityType // ignore: cast_nullable_to_non_nullable
              as String?,
      associateParentEntity: freezed == associateParentEntity
          ? _value.associateParentEntity
          : associateParentEntity // ignore: cast_nullable_to_non_nullable
              as String?,
      childRelationships: null == childRelationships
          ? _value.childRelationships
          : childRelationships // ignore: cast_nullable_to_non_nullable
              as List<ChildRelationship>,
      compactLayoutable: null == compactLayoutable
          ? _value.compactLayoutable
          : compactLayoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      createable: null == createable
          ? _value.createable
          : createable // ignore: cast_nullable_to_non_nullable
              as bool,
      custom: null == custom
          ? _value.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultRecordTypeId: freezed == defaultRecordTypeId
          ? _value.defaultRecordTypeId
          : defaultRecordTypeId // ignore: cast_nullable_to_non_nullable
              as String?,
      deleteable: null == deleteable
          ? _value.deleteable
          : deleteable // ignore: cast_nullable_to_non_nullable
              as bool,
      dependentFields: null == dependentFields
          ? _value.dependentFields
          : dependentFields // ignore: cast_nullable_to_non_nullable
              as Map<String, Object>,
      feedEnabled: null == feedEnabled
          ? _value.feedEnabled
          : feedEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      fields: null == fields
          ? _value.fields
          : fields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      keyPrefix: freezed == keyPrefix
          ? _value.keyPrefix
          : keyPrefix // ignore: cast_nullable_to_non_nullable
              as String?,
      label: freezed == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
      labelPlural: freezed == labelPlural
          ? _value.labelPlural
          : labelPlural // ignore: cast_nullable_to_non_nullable
              as String?,
      layoutable: null == layoutable
          ? _value.layoutable
          : layoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      mruEnabled: null == mruEnabled
          ? _value.mruEnabled
          : mruEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      nameFields: null == nameFields
          ? _value.nameFields
          : nameFields // ignore: cast_nullable_to_non_nullable
              as List<String?>,
      queryable: null == queryable
          ? _value.queryable
          : queryable // ignore: cast_nullable_to_non_nullable
              as bool,
      searchable: null == searchable
          ? _value.searchable
          : searchable // ignore: cast_nullable_to_non_nullable
              as bool,
      searchLayoutable: null == searchLayoutable
          ? _value.searchLayoutable
          : searchLayoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      updateable: null == updateable
          ? _value.updateable
          : updateable // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ObjectInfoImplCopyWith<$Res>
    implements $ObjectInfoCopyWith<$Res> {
  factory _$$ObjectInfoImplCopyWith(
          _$ObjectInfoImpl value, $Res Function(_$ObjectInfoImpl) then) =
      __$$ObjectInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? apiName,
      String? associateEntityType,
      String? associateParentEntity,
      List<ChildRelationship> childRelationships,
      bool compactLayoutable,
      bool createable,
      bool custom,
      String? defaultRecordTypeId,
      bool deleteable,
      Map<String, Object> dependentFields,
      bool feedEnabled,
      Map<String, dynamic> fields,
      String? keyPrefix,
      String? label,
      String? labelPlural,
      bool layoutable,
      bool mruEnabled,
      List<String?> nameFields,
      bool queryable,
      bool searchable,
      bool searchLayoutable,
      bool updateable});
}

/// @nodoc
class __$$ObjectInfoImplCopyWithImpl<$Res>
    extends _$ObjectInfoCopyWithImpl<$Res, _$ObjectInfoImpl>
    implements _$$ObjectInfoImplCopyWith<$Res> {
  __$$ObjectInfoImplCopyWithImpl(
      _$ObjectInfoImpl _value, $Res Function(_$ObjectInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiName = freezed,
    Object? associateEntityType = freezed,
    Object? associateParentEntity = freezed,
    Object? childRelationships = null,
    Object? compactLayoutable = null,
    Object? createable = null,
    Object? custom = null,
    Object? defaultRecordTypeId = freezed,
    Object? deleteable = null,
    Object? dependentFields = null,
    Object? feedEnabled = null,
    Object? fields = null,
    Object? keyPrefix = freezed,
    Object? label = freezed,
    Object? labelPlural = freezed,
    Object? layoutable = null,
    Object? mruEnabled = null,
    Object? nameFields = null,
    Object? queryable = null,
    Object? searchable = null,
    Object? searchLayoutable = null,
    Object? updateable = null,
  }) {
    return _then(_$ObjectInfoImpl(
      apiName: freezed == apiName
          ? _value.apiName
          : apiName // ignore: cast_nullable_to_non_nullable
              as String?,
      associateEntityType: freezed == associateEntityType
          ? _value.associateEntityType
          : associateEntityType // ignore: cast_nullable_to_non_nullable
              as String?,
      associateParentEntity: freezed == associateParentEntity
          ? _value.associateParentEntity
          : associateParentEntity // ignore: cast_nullable_to_non_nullable
              as String?,
      childRelationships: null == childRelationships
          ? _value._childRelationships
          : childRelationships // ignore: cast_nullable_to_non_nullable
              as List<ChildRelationship>,
      compactLayoutable: null == compactLayoutable
          ? _value.compactLayoutable
          : compactLayoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      createable: null == createable
          ? _value.createable
          : createable // ignore: cast_nullable_to_non_nullable
              as bool,
      custom: null == custom
          ? _value.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultRecordTypeId: freezed == defaultRecordTypeId
          ? _value.defaultRecordTypeId
          : defaultRecordTypeId // ignore: cast_nullable_to_non_nullable
              as String?,
      deleteable: null == deleteable
          ? _value.deleteable
          : deleteable // ignore: cast_nullable_to_non_nullable
              as bool,
      dependentFields: null == dependentFields
          ? _value._dependentFields
          : dependentFields // ignore: cast_nullable_to_non_nullable
              as Map<String, Object>,
      feedEnabled: null == feedEnabled
          ? _value.feedEnabled
          : feedEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      fields: null == fields
          ? _value._fields
          : fields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      keyPrefix: freezed == keyPrefix
          ? _value.keyPrefix
          : keyPrefix // ignore: cast_nullable_to_non_nullable
              as String?,
      label: freezed == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
      labelPlural: freezed == labelPlural
          ? _value.labelPlural
          : labelPlural // ignore: cast_nullable_to_non_nullable
              as String?,
      layoutable: null == layoutable
          ? _value.layoutable
          : layoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      mruEnabled: null == mruEnabled
          ? _value.mruEnabled
          : mruEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      nameFields: null == nameFields
          ? _value._nameFields
          : nameFields // ignore: cast_nullable_to_non_nullable
              as List<String?>,
      queryable: null == queryable
          ? _value.queryable
          : queryable // ignore: cast_nullable_to_non_nullable
              as bool,
      searchable: null == searchable
          ? _value.searchable
          : searchable // ignore: cast_nullable_to_non_nullable
              as bool,
      searchLayoutable: null == searchLayoutable
          ? _value.searchLayoutable
          : searchLayoutable // ignore: cast_nullable_to_non_nullable
              as bool,
      updateable: null == updateable
          ? _value.updateable
          : updateable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ObjectInfoImpl implements _ObjectInfo {
  const _$ObjectInfoImpl(
      {this.apiName,
      this.associateEntityType,
      this.associateParentEntity,
      final List<ChildRelationship> childRelationships = const [],
      this.compactLayoutable = false,
      this.createable = false,
      this.custom = false,
      this.defaultRecordTypeId,
      this.deleteable = false,
      final Map<String, Object> dependentFields = const {},
      this.feedEnabled = false,
      final Map<String, dynamic> fields = const {},
      this.keyPrefix,
      this.label,
      this.labelPlural,
      this.layoutable = false,
      this.mruEnabled = false,
      final List<String?> nameFields = const [],
      this.queryable = false,
      this.searchable = false,
      this.searchLayoutable = false,
      this.updateable = false})
      : _childRelationships = childRelationships,
        _dependentFields = dependentFields,
        _fields = fields,
        _nameFields = nameFields;

  factory _$ObjectInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ObjectInfoImplFromJson(json);

  @override
  final String? apiName;
  @override
  final String? associateEntityType;
  @override
  final String? associateParentEntity;
  final List<ChildRelationship> _childRelationships;
  @override
  @JsonKey()
  List<ChildRelationship> get childRelationships {
    if (_childRelationships is EqualUnmodifiableListView)
      return _childRelationships;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_childRelationships);
  }

  @override
  @JsonKey()
  final bool compactLayoutable;
  @override
  @JsonKey()
  final bool createable;
  @override
  @JsonKey()
  final bool custom;
  @override
  final String? defaultRecordTypeId;
  @override
  @JsonKey()
  final bool deleteable;
  final Map<String, Object> _dependentFields;
  @override
  @JsonKey()
  Map<String, Object> get dependentFields {
    if (_dependentFields is EqualUnmodifiableMapView) return _dependentFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_dependentFields);
  }

  @override
  @JsonKey()
  final bool feedEnabled;
// TODO: get Field working ... had issues parsing
// @Default({}) Map<String, Field> fields,
  final Map<String, dynamic> _fields;
// TODO: get Field working ... had issues parsing
// @Default({}) Map<String, Field> fields,
  @override
  @JsonKey()
  Map<String, dynamic> get fields {
    if (_fields is EqualUnmodifiableMapView) return _fields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fields);
  }

  @override
  final String? keyPrefix;
  @override
  final String? label;
  @override
  final String? labelPlural;
  @override
  @JsonKey()
  final bool layoutable;
  @override
  @JsonKey()
  final bool mruEnabled;
  final List<String?> _nameFields;
  @override
  @JsonKey()
  List<String?> get nameFields {
    if (_nameFields is EqualUnmodifiableListView) return _nameFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_nameFields);
  }

  @override
  @JsonKey()
  final bool queryable;
// Map<String, RecordTypeInfo> recordTypeInfos,
  @override
  @JsonKey()
  final bool searchable;
  @override
  @JsonKey()
  final bool searchLayoutable;
// ThemeInfo themeInfo,
  @override
  @JsonKey()
  final bool updateable;

  @override
  String toString() {
    return 'ObjectInfo(apiName: $apiName, associateEntityType: $associateEntityType, associateParentEntity: $associateParentEntity, childRelationships: $childRelationships, compactLayoutable: $compactLayoutable, createable: $createable, custom: $custom, defaultRecordTypeId: $defaultRecordTypeId, deleteable: $deleteable, dependentFields: $dependentFields, feedEnabled: $feedEnabled, fields: $fields, keyPrefix: $keyPrefix, label: $label, labelPlural: $labelPlural, layoutable: $layoutable, mruEnabled: $mruEnabled, nameFields: $nameFields, queryable: $queryable, searchable: $searchable, searchLayoutable: $searchLayoutable, updateable: $updateable)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ObjectInfoImpl &&
            (identical(other.apiName, apiName) || other.apiName == apiName) &&
            (identical(other.associateEntityType, associateEntityType) ||
                other.associateEntityType == associateEntityType) &&
            (identical(other.associateParentEntity, associateParentEntity) ||
                other.associateParentEntity == associateParentEntity) &&
            const DeepCollectionEquality()
                .equals(other._childRelationships, _childRelationships) &&
            (identical(other.compactLayoutable, compactLayoutable) ||
                other.compactLayoutable == compactLayoutable) &&
            (identical(other.createable, createable) ||
                other.createable == createable) &&
            (identical(other.custom, custom) || other.custom == custom) &&
            (identical(other.defaultRecordTypeId, defaultRecordTypeId) ||
                other.defaultRecordTypeId == defaultRecordTypeId) &&
            (identical(other.deleteable, deleteable) ||
                other.deleteable == deleteable) &&
            const DeepCollectionEquality()
                .equals(other._dependentFields, _dependentFields) &&
            (identical(other.feedEnabled, feedEnabled) ||
                other.feedEnabled == feedEnabled) &&
            const DeepCollectionEquality().equals(other._fields, _fields) &&
            (identical(other.keyPrefix, keyPrefix) ||
                other.keyPrefix == keyPrefix) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.labelPlural, labelPlural) ||
                other.labelPlural == labelPlural) &&
            (identical(other.layoutable, layoutable) ||
                other.layoutable == layoutable) &&
            (identical(other.mruEnabled, mruEnabled) ||
                other.mruEnabled == mruEnabled) &&
            const DeepCollectionEquality()
                .equals(other._nameFields, _nameFields) &&
            (identical(other.queryable, queryable) ||
                other.queryable == queryable) &&
            (identical(other.searchable, searchable) ||
                other.searchable == searchable) &&
            (identical(other.searchLayoutable, searchLayoutable) ||
                other.searchLayoutable == searchLayoutable) &&
            (identical(other.updateable, updateable) ||
                other.updateable == updateable));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        apiName,
        associateEntityType,
        associateParentEntity,
        const DeepCollectionEquality().hash(_childRelationships),
        compactLayoutable,
        createable,
        custom,
        defaultRecordTypeId,
        deleteable,
        const DeepCollectionEquality().hash(_dependentFields),
        feedEnabled,
        const DeepCollectionEquality().hash(_fields),
        keyPrefix,
        label,
        labelPlural,
        layoutable,
        mruEnabled,
        const DeepCollectionEquality().hash(_nameFields),
        queryable,
        searchable,
        searchLayoutable,
        updateable
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ObjectInfoImplCopyWith<_$ObjectInfoImpl> get copyWith =>
      __$$ObjectInfoImplCopyWithImpl<_$ObjectInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ObjectInfoImplToJson(
      this,
    );
  }
}

abstract class _ObjectInfo implements ObjectInfo {
  const factory _ObjectInfo(
      {final String? apiName,
      final String? associateEntityType,
      final String? associateParentEntity,
      final List<ChildRelationship> childRelationships,
      final bool compactLayoutable,
      final bool createable,
      final bool custom,
      final String? defaultRecordTypeId,
      final bool deleteable,
      final Map<String, Object> dependentFields,
      final bool feedEnabled,
      final Map<String, dynamic> fields,
      final String? keyPrefix,
      final String? label,
      final String? labelPlural,
      final bool layoutable,
      final bool mruEnabled,
      final List<String?> nameFields,
      final bool queryable,
      final bool searchable,
      final bool searchLayoutable,
      final bool updateable}) = _$ObjectInfoImpl;

  factory _ObjectInfo.fromJson(Map<String, dynamic> json) =
      _$ObjectInfoImpl.fromJson;

  @override
  String? get apiName;
  @override
  String? get associateEntityType;
  @override
  String? get associateParentEntity;
  @override
  List<ChildRelationship> get childRelationships;
  @override
  bool get compactLayoutable;
  @override
  bool get createable;
  @override
  bool get custom;
  @override
  String? get defaultRecordTypeId;
  @override
  bool get deleteable;
  @override
  Map<String, Object> get dependentFields;
  @override
  bool get feedEnabled;
  @override // TODO: get Field working ... had issues parsing
// @Default({}) Map<String, Field> fields,
  Map<String, dynamic> get fields;
  @override
  String? get keyPrefix;
  @override
  String? get label;
  @override
  String? get labelPlural;
  @override
  bool get layoutable;
  @override
  bool get mruEnabled;
  @override
  List<String?> get nameFields;
  @override
  bool get queryable;
  @override // Map<String, RecordTypeInfo> recordTypeInfos,
  bool get searchable;
  @override
  bool get searchLayoutable;
  @override // ThemeInfo themeInfo,
  bool get updateable;
  @override
  @JsonKey(ignore: true)
  _$$ObjectInfoImplCopyWith<_$ObjectInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
