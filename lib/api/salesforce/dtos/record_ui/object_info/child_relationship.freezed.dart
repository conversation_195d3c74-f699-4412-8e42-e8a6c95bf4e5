// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'child_relationship.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ChildRelationship _$ChildRelationshipFromJson(Map<String, dynamic> json) {
  return _ChildRelationship.fromJson(json);
}

/// @nodoc
mixin _$ChildRelationship {
  String? get childObjectApiName => throw _privateConstructorUsedError;
  String? get fieldName => throw _privateConstructorUsedError;
  List<String> get junctionIdListNames => throw _privateConstructorUsedError;
  List<String> get junctionReferenceTo => throw _privateConstructorUsedError;
  String? get relationshipName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChildRelationshipCopyWith<ChildRelationship> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChildRelationshipCopyWith<$Res> {
  factory $ChildRelationshipCopyWith(
          ChildRelationship value, $Res Function(ChildRelationship) then) =
      _$ChildRelationshipCopyWithImpl<$Res, ChildRelationship>;
  @useResult
  $Res call(
      {String? childObjectApiName,
      String? fieldName,
      List<String> junctionIdListNames,
      List<String> junctionReferenceTo,
      String? relationshipName});
}

/// @nodoc
class _$ChildRelationshipCopyWithImpl<$Res, $Val extends ChildRelationship>
    implements $ChildRelationshipCopyWith<$Res> {
  _$ChildRelationshipCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? childObjectApiName = freezed,
    Object? fieldName = freezed,
    Object? junctionIdListNames = null,
    Object? junctionReferenceTo = null,
    Object? relationshipName = freezed,
  }) {
    return _then(_value.copyWith(
      childObjectApiName: freezed == childObjectApiName
          ? _value.childObjectApiName
          : childObjectApiName // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldName: freezed == fieldName
          ? _value.fieldName
          : fieldName // ignore: cast_nullable_to_non_nullable
              as String?,
      junctionIdListNames: null == junctionIdListNames
          ? _value.junctionIdListNames
          : junctionIdListNames // ignore: cast_nullable_to_non_nullable
              as List<String>,
      junctionReferenceTo: null == junctionReferenceTo
          ? _value.junctionReferenceTo
          : junctionReferenceTo // ignore: cast_nullable_to_non_nullable
              as List<String>,
      relationshipName: freezed == relationshipName
          ? _value.relationshipName
          : relationshipName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChildRelationshipImplCopyWith<$Res>
    implements $ChildRelationshipCopyWith<$Res> {
  factory _$$ChildRelationshipImplCopyWith(_$ChildRelationshipImpl value,
          $Res Function(_$ChildRelationshipImpl) then) =
      __$$ChildRelationshipImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? childObjectApiName,
      String? fieldName,
      List<String> junctionIdListNames,
      List<String> junctionReferenceTo,
      String? relationshipName});
}

/// @nodoc
class __$$ChildRelationshipImplCopyWithImpl<$Res>
    extends _$ChildRelationshipCopyWithImpl<$Res, _$ChildRelationshipImpl>
    implements _$$ChildRelationshipImplCopyWith<$Res> {
  __$$ChildRelationshipImplCopyWithImpl(_$ChildRelationshipImpl _value,
      $Res Function(_$ChildRelationshipImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? childObjectApiName = freezed,
    Object? fieldName = freezed,
    Object? junctionIdListNames = null,
    Object? junctionReferenceTo = null,
    Object? relationshipName = freezed,
  }) {
    return _then(_$ChildRelationshipImpl(
      childObjectApiName: freezed == childObjectApiName
          ? _value.childObjectApiName
          : childObjectApiName // ignore: cast_nullable_to_non_nullable
              as String?,
      fieldName: freezed == fieldName
          ? _value.fieldName
          : fieldName // ignore: cast_nullable_to_non_nullable
              as String?,
      junctionIdListNames: null == junctionIdListNames
          ? _value._junctionIdListNames
          : junctionIdListNames // ignore: cast_nullable_to_non_nullable
              as List<String>,
      junctionReferenceTo: null == junctionReferenceTo
          ? _value._junctionReferenceTo
          : junctionReferenceTo // ignore: cast_nullable_to_non_nullable
              as List<String>,
      relationshipName: freezed == relationshipName
          ? _value.relationshipName
          : relationshipName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChildRelationshipImpl implements _ChildRelationship {
  const _$ChildRelationshipImpl(
      {this.childObjectApiName,
      this.fieldName,
      final List<String> junctionIdListNames = const [],
      final List<String> junctionReferenceTo = const [],
      this.relationshipName})
      : _junctionIdListNames = junctionIdListNames,
        _junctionReferenceTo = junctionReferenceTo;

  factory _$ChildRelationshipImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChildRelationshipImplFromJson(json);

  @override
  final String? childObjectApiName;
  @override
  final String? fieldName;
  final List<String> _junctionIdListNames;
  @override
  @JsonKey()
  List<String> get junctionIdListNames {
    if (_junctionIdListNames is EqualUnmodifiableListView)
      return _junctionIdListNames;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_junctionIdListNames);
  }

  final List<String> _junctionReferenceTo;
  @override
  @JsonKey()
  List<String> get junctionReferenceTo {
    if (_junctionReferenceTo is EqualUnmodifiableListView)
      return _junctionReferenceTo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_junctionReferenceTo);
  }

  @override
  final String? relationshipName;

  @override
  String toString() {
    return 'ChildRelationship(childObjectApiName: $childObjectApiName, fieldName: $fieldName, junctionIdListNames: $junctionIdListNames, junctionReferenceTo: $junctionReferenceTo, relationshipName: $relationshipName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChildRelationshipImpl &&
            (identical(other.childObjectApiName, childObjectApiName) ||
                other.childObjectApiName == childObjectApiName) &&
            (identical(other.fieldName, fieldName) ||
                other.fieldName == fieldName) &&
            const DeepCollectionEquality()
                .equals(other._junctionIdListNames, _junctionIdListNames) &&
            const DeepCollectionEquality()
                .equals(other._junctionReferenceTo, _junctionReferenceTo) &&
            (identical(other.relationshipName, relationshipName) ||
                other.relationshipName == relationshipName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      childObjectApiName,
      fieldName,
      const DeepCollectionEquality().hash(_junctionIdListNames),
      const DeepCollectionEquality().hash(_junctionReferenceTo),
      relationshipName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChildRelationshipImplCopyWith<_$ChildRelationshipImpl> get copyWith =>
      __$$ChildRelationshipImplCopyWithImpl<_$ChildRelationshipImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChildRelationshipImplToJson(
      this,
    );
  }
}

abstract class _ChildRelationship implements ChildRelationship {
  const factory _ChildRelationship(
      {final String? childObjectApiName,
      final String? fieldName,
      final List<String> junctionIdListNames,
      final List<String> junctionReferenceTo,
      final String? relationshipName}) = _$ChildRelationshipImpl;

  factory _ChildRelationship.fromJson(Map<String, dynamic> json) =
      _$ChildRelationshipImpl.fromJson;

  @override
  String? get childObjectApiName;
  @override
  String? get fieldName;
  @override
  List<String> get junctionIdListNames;
  @override
  List<String> get junctionReferenceTo;
  @override
  String? get relationshipName;
  @override
  @JsonKey(ignore: true)
  _$$ChildRelationshipImplCopyWith<_$ChildRelationshipImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
