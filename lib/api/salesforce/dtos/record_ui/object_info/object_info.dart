import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/object_info/child_relationship.dart';

part 'object_info.freezed.dart';
part 'object_info.g.dart';

@freezed
class ObjectInfo with _$ObjectInfo {
  const factory ObjectInfo({
    String? apiName,
    String? associateEntityType,
    String? associateParentEntity,
    @Default([]) List<ChildRelationship> childRelationships,
    @Default(false) bool compactLayoutable,
    @Default(false) bool createable,
    @Default(false) bool custom,
    String? defaultRecordTypeId,
    @Default(false) bool deleteable,
    @Default({}) Map<String, Object> dependentFields,
    @Default(false) bool feedEnabled,
    // TODO: get Field working ... had issues parsing
    // @Default({}) Map<String, Field> fields,
    @Default({}) Map<String, dynamic> fields,
    String? keyPrefix,
    String? label,
    String? labelPlural,
    @Default(false) bool layoutable,
    @Default(false) bool mruEnabled,
    @Default([]) List<String?> nameFields,
    @Default(false) bool queryable,
    // Map<String, RecordTypeInfo> recordTypeInfos,
    @Default(false) bool searchable,
    @Default(false) bool searchLayoutable,
    // ThemeInfo themeInfo,
    @Default(false) bool updateable,
  }) = _ObjectInfo;

  factory ObjectInfo.fromJson(Map<String, dynamic> json) =>
      _$ObjectInfoFromJson(json);
}