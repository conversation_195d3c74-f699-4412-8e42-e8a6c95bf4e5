// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'field.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Field _$FieldFromJson(Map<String, dynamic> json) {
  return _Field.fromJson(json);
}

/// @nodoc
mixin _$Field {
  String? get apiName => throw _privateConstructorUsedError;
  bool get calculated => throw _privateConstructorUsedError;
  bool get compound => throw _privateConstructorUsedError;
  String? get compoundComponentName => throw _privateConstructorUsedError;
  String? get compoundFieldName => throw _privateConstructorUsedError;
  String? get controllerName => throw _privateConstructorUsedError;
  List<String> get controllingFields => throw _privateConstructorUsedError;
  bool get createable => throw _privateConstructorUsedError;
  bool get custom => throw _privateConstructorUsedError;
  String? get dataType => throw _privateConstructorUsedError;

  /// this can come as bool:false
// String? externalId,
  String? get extraTypeInfo => throw _privateConstructorUsedError;
  bool get filterable =>
      throw _privateConstructorUsedError; // filteredLookupInfo; SF Type: Filtered Lookup Info: https://developer.salesforce.com/docs/atlas.en-us.uiapi.meta/uiapi/ui_api_responses_filtered_lookup_info.htm#ui_api_responses_filtered_lookup_info
  bool get highScaleNumber => throw _privateConstructorUsedError;
  bool get htmlFormatted => throw _privateConstructorUsedError;
  String? get inlineHelpText => throw _privateConstructorUsedError;
  String? get label => throw _privateConstructorUsedError;
  int? get length => throw _privateConstructorUsedError;
  String? get maskType => throw _privateConstructorUsedError;
  bool get nameField => throw _privateConstructorUsedError;
  bool get polymorphicForeignKey => throw _privateConstructorUsedError;
  int? get precision => throw _privateConstructorUsedError;
  bool get reference => throw _privateConstructorUsedError;
  String? get referenceTargetField => throw _privateConstructorUsedError;
  Map<String, dynamic> get referenceToInfos =>
      throw _privateConstructorUsedError;
  String? get relationshipName => throw _privateConstructorUsedError;
  bool get required => throw _privateConstructorUsedError;
  int? get scale => throw _privateConstructorUsedError;
  bool get searchPrefilterable => throw _privateConstructorUsedError;
  bool get sortable => throw _privateConstructorUsedError;
  bool get unique => throw _privateConstructorUsedError;
  bool get updateable => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FieldCopyWith<Field> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FieldCopyWith<$Res> {
  factory $FieldCopyWith(Field value, $Res Function(Field) then) =
      _$FieldCopyWithImpl<$Res, Field>;
  @useResult
  $Res call(
      {String? apiName,
      bool calculated,
      bool compound,
      String? compoundComponentName,
      String? compoundFieldName,
      String? controllerName,
      List<String> controllingFields,
      bool createable,
      bool custom,
      String? dataType,
      String? extraTypeInfo,
      bool filterable,
      bool highScaleNumber,
      bool htmlFormatted,
      String? inlineHelpText,
      String? label,
      int? length,
      String? maskType,
      bool nameField,
      bool polymorphicForeignKey,
      int? precision,
      bool reference,
      String? referenceTargetField,
      Map<String, dynamic> referenceToInfos,
      String? relationshipName,
      bool required,
      int? scale,
      bool searchPrefilterable,
      bool sortable,
      bool unique,
      bool updateable});
}

/// @nodoc
class _$FieldCopyWithImpl<$Res, $Val extends Field>
    implements $FieldCopyWith<$Res> {
  _$FieldCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiName = freezed,
    Object? calculated = null,
    Object? compound = null,
    Object? compoundComponentName = freezed,
    Object? compoundFieldName = freezed,
    Object? controllerName = freezed,
    Object? controllingFields = null,
    Object? createable = null,
    Object? custom = null,
    Object? dataType = freezed,
    Object? extraTypeInfo = freezed,
    Object? filterable = null,
    Object? highScaleNumber = null,
    Object? htmlFormatted = null,
    Object? inlineHelpText = freezed,
    Object? label = freezed,
    Object? length = freezed,
    Object? maskType = freezed,
    Object? nameField = null,
    Object? polymorphicForeignKey = null,
    Object? precision = freezed,
    Object? reference = null,
    Object? referenceTargetField = freezed,
    Object? referenceToInfos = null,
    Object? relationshipName = freezed,
    Object? required = null,
    Object? scale = freezed,
    Object? searchPrefilterable = null,
    Object? sortable = null,
    Object? unique = null,
    Object? updateable = null,
  }) {
    return _then(_value.copyWith(
      apiName: freezed == apiName
          ? _value.apiName
          : apiName // ignore: cast_nullable_to_non_nullable
              as String?,
      calculated: null == calculated
          ? _value.calculated
          : calculated // ignore: cast_nullable_to_non_nullable
              as bool,
      compound: null == compound
          ? _value.compound
          : compound // ignore: cast_nullable_to_non_nullable
              as bool,
      compoundComponentName: freezed == compoundComponentName
          ? _value.compoundComponentName
          : compoundComponentName // ignore: cast_nullable_to_non_nullable
              as String?,
      compoundFieldName: freezed == compoundFieldName
          ? _value.compoundFieldName
          : compoundFieldName // ignore: cast_nullable_to_non_nullable
              as String?,
      controllerName: freezed == controllerName
          ? _value.controllerName
          : controllerName // ignore: cast_nullable_to_non_nullable
              as String?,
      controllingFields: null == controllingFields
          ? _value.controllingFields
          : controllingFields // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createable: null == createable
          ? _value.createable
          : createable // ignore: cast_nullable_to_non_nullable
              as bool,
      custom: null == custom
          ? _value.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as bool,
      dataType: freezed == dataType
          ? _value.dataType
          : dataType // ignore: cast_nullable_to_non_nullable
              as String?,
      extraTypeInfo: freezed == extraTypeInfo
          ? _value.extraTypeInfo
          : extraTypeInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      filterable: null == filterable
          ? _value.filterable
          : filterable // ignore: cast_nullable_to_non_nullable
              as bool,
      highScaleNumber: null == highScaleNumber
          ? _value.highScaleNumber
          : highScaleNumber // ignore: cast_nullable_to_non_nullable
              as bool,
      htmlFormatted: null == htmlFormatted
          ? _value.htmlFormatted
          : htmlFormatted // ignore: cast_nullable_to_non_nullable
              as bool,
      inlineHelpText: freezed == inlineHelpText
          ? _value.inlineHelpText
          : inlineHelpText // ignore: cast_nullable_to_non_nullable
              as String?,
      label: freezed == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
      length: freezed == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as int?,
      maskType: freezed == maskType
          ? _value.maskType
          : maskType // ignore: cast_nullable_to_non_nullable
              as String?,
      nameField: null == nameField
          ? _value.nameField
          : nameField // ignore: cast_nullable_to_non_nullable
              as bool,
      polymorphicForeignKey: null == polymorphicForeignKey
          ? _value.polymorphicForeignKey
          : polymorphicForeignKey // ignore: cast_nullable_to_non_nullable
              as bool,
      precision: freezed == precision
          ? _value.precision
          : precision // ignore: cast_nullable_to_non_nullable
              as int?,
      reference: null == reference
          ? _value.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as bool,
      referenceTargetField: freezed == referenceTargetField
          ? _value.referenceTargetField
          : referenceTargetField // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceToInfos: null == referenceToInfos
          ? _value.referenceToInfos
          : referenceToInfos // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      relationshipName: freezed == relationshipName
          ? _value.relationshipName
          : relationshipName // ignore: cast_nullable_to_non_nullable
              as String?,
      required: null == required
          ? _value.required
          : required // ignore: cast_nullable_to_non_nullable
              as bool,
      scale: freezed == scale
          ? _value.scale
          : scale // ignore: cast_nullable_to_non_nullable
              as int?,
      searchPrefilterable: null == searchPrefilterable
          ? _value.searchPrefilterable
          : searchPrefilterable // ignore: cast_nullable_to_non_nullable
              as bool,
      sortable: null == sortable
          ? _value.sortable
          : sortable // ignore: cast_nullable_to_non_nullable
              as bool,
      unique: null == unique
          ? _value.unique
          : unique // ignore: cast_nullable_to_non_nullable
              as bool,
      updateable: null == updateable
          ? _value.updateable
          : updateable // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FieldImplCopyWith<$Res> implements $FieldCopyWith<$Res> {
  factory _$$FieldImplCopyWith(
          _$FieldImpl value, $Res Function(_$FieldImpl) then) =
      __$$FieldImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? apiName,
      bool calculated,
      bool compound,
      String? compoundComponentName,
      String? compoundFieldName,
      String? controllerName,
      List<String> controllingFields,
      bool createable,
      bool custom,
      String? dataType,
      String? extraTypeInfo,
      bool filterable,
      bool highScaleNumber,
      bool htmlFormatted,
      String? inlineHelpText,
      String? label,
      int? length,
      String? maskType,
      bool nameField,
      bool polymorphicForeignKey,
      int? precision,
      bool reference,
      String? referenceTargetField,
      Map<String, dynamic> referenceToInfos,
      String? relationshipName,
      bool required,
      int? scale,
      bool searchPrefilterable,
      bool sortable,
      bool unique,
      bool updateable});
}

/// @nodoc
class __$$FieldImplCopyWithImpl<$Res>
    extends _$FieldCopyWithImpl<$Res, _$FieldImpl>
    implements _$$FieldImplCopyWith<$Res> {
  __$$FieldImplCopyWithImpl(
      _$FieldImpl _value, $Res Function(_$FieldImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiName = freezed,
    Object? calculated = null,
    Object? compound = null,
    Object? compoundComponentName = freezed,
    Object? compoundFieldName = freezed,
    Object? controllerName = freezed,
    Object? controllingFields = null,
    Object? createable = null,
    Object? custom = null,
    Object? dataType = freezed,
    Object? extraTypeInfo = freezed,
    Object? filterable = null,
    Object? highScaleNumber = null,
    Object? htmlFormatted = null,
    Object? inlineHelpText = freezed,
    Object? label = freezed,
    Object? length = freezed,
    Object? maskType = freezed,
    Object? nameField = null,
    Object? polymorphicForeignKey = null,
    Object? precision = freezed,
    Object? reference = null,
    Object? referenceTargetField = freezed,
    Object? referenceToInfos = null,
    Object? relationshipName = freezed,
    Object? required = null,
    Object? scale = freezed,
    Object? searchPrefilterable = null,
    Object? sortable = null,
    Object? unique = null,
    Object? updateable = null,
  }) {
    return _then(_$FieldImpl(
      apiName: freezed == apiName
          ? _value.apiName
          : apiName // ignore: cast_nullable_to_non_nullable
              as String?,
      calculated: null == calculated
          ? _value.calculated
          : calculated // ignore: cast_nullable_to_non_nullable
              as bool,
      compound: null == compound
          ? _value.compound
          : compound // ignore: cast_nullable_to_non_nullable
              as bool,
      compoundComponentName: freezed == compoundComponentName
          ? _value.compoundComponentName
          : compoundComponentName // ignore: cast_nullable_to_non_nullable
              as String?,
      compoundFieldName: freezed == compoundFieldName
          ? _value.compoundFieldName
          : compoundFieldName // ignore: cast_nullable_to_non_nullable
              as String?,
      controllerName: freezed == controllerName
          ? _value.controllerName
          : controllerName // ignore: cast_nullable_to_non_nullable
              as String?,
      controllingFields: null == controllingFields
          ? _value._controllingFields
          : controllingFields // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createable: null == createable
          ? _value.createable
          : createable // ignore: cast_nullable_to_non_nullable
              as bool,
      custom: null == custom
          ? _value.custom
          : custom // ignore: cast_nullable_to_non_nullable
              as bool,
      dataType: freezed == dataType
          ? _value.dataType
          : dataType // ignore: cast_nullable_to_non_nullable
              as String?,
      extraTypeInfo: freezed == extraTypeInfo
          ? _value.extraTypeInfo
          : extraTypeInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      filterable: null == filterable
          ? _value.filterable
          : filterable // ignore: cast_nullable_to_non_nullable
              as bool,
      highScaleNumber: null == highScaleNumber
          ? _value.highScaleNumber
          : highScaleNumber // ignore: cast_nullable_to_non_nullable
              as bool,
      htmlFormatted: null == htmlFormatted
          ? _value.htmlFormatted
          : htmlFormatted // ignore: cast_nullable_to_non_nullable
              as bool,
      inlineHelpText: freezed == inlineHelpText
          ? _value.inlineHelpText
          : inlineHelpText // ignore: cast_nullable_to_non_nullable
              as String?,
      label: freezed == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String?,
      length: freezed == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as int?,
      maskType: freezed == maskType
          ? _value.maskType
          : maskType // ignore: cast_nullable_to_non_nullable
              as String?,
      nameField: null == nameField
          ? _value.nameField
          : nameField // ignore: cast_nullable_to_non_nullable
              as bool,
      polymorphicForeignKey: null == polymorphicForeignKey
          ? _value.polymorphicForeignKey
          : polymorphicForeignKey // ignore: cast_nullable_to_non_nullable
              as bool,
      precision: freezed == precision
          ? _value.precision
          : precision // ignore: cast_nullable_to_non_nullable
              as int?,
      reference: null == reference
          ? _value.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as bool,
      referenceTargetField: freezed == referenceTargetField
          ? _value.referenceTargetField
          : referenceTargetField // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceToInfos: null == referenceToInfos
          ? _value._referenceToInfos
          : referenceToInfos // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      relationshipName: freezed == relationshipName
          ? _value.relationshipName
          : relationshipName // ignore: cast_nullable_to_non_nullable
              as String?,
      required: null == required
          ? _value.required
          : required // ignore: cast_nullable_to_non_nullable
              as bool,
      scale: freezed == scale
          ? _value.scale
          : scale // ignore: cast_nullable_to_non_nullable
              as int?,
      searchPrefilterable: null == searchPrefilterable
          ? _value.searchPrefilterable
          : searchPrefilterable // ignore: cast_nullable_to_non_nullable
              as bool,
      sortable: null == sortable
          ? _value.sortable
          : sortable // ignore: cast_nullable_to_non_nullable
              as bool,
      unique: null == unique
          ? _value.unique
          : unique // ignore: cast_nullable_to_non_nullable
              as bool,
      updateable: null == updateable
          ? _value.updateable
          : updateable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FieldImpl implements _Field {
  const _$FieldImpl(
      {this.apiName,
      this.calculated = false,
      this.compound = false,
      this.compoundComponentName,
      this.compoundFieldName,
      this.controllerName,
      final List<String> controllingFields = const [],
      this.createable = false,
      this.custom = false,
      this.dataType,
      this.extraTypeInfo,
      this.filterable = false,
      this.highScaleNumber = false,
      this.htmlFormatted = false,
      this.inlineHelpText,
      this.label,
      this.length,
      this.maskType,
      this.nameField = false,
      this.polymorphicForeignKey = false,
      this.precision,
      this.reference = false,
      this.referenceTargetField,
      final Map<String, dynamic> referenceToInfos = const {},
      this.relationshipName,
      this.required = false,
      this.scale,
      this.searchPrefilterable = false,
      this.sortable = false,
      this.unique = false,
      this.updateable = false})
      : _controllingFields = controllingFields,
        _referenceToInfos = referenceToInfos;

  factory _$FieldImpl.fromJson(Map<String, dynamic> json) =>
      _$$FieldImplFromJson(json);

  @override
  final String? apiName;
  @override
  @JsonKey()
  final bool calculated;
  @override
  @JsonKey()
  final bool compound;
  @override
  final String? compoundComponentName;
  @override
  final String? compoundFieldName;
  @override
  final String? controllerName;
  final List<String> _controllingFields;
  @override
  @JsonKey()
  List<String> get controllingFields {
    if (_controllingFields is EqualUnmodifiableListView)
      return _controllingFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_controllingFields);
  }

  @override
  @JsonKey()
  final bool createable;
  @override
  @JsonKey()
  final bool custom;
  @override
  final String? dataType;

  /// this can come as bool:false
// String? externalId,
  @override
  final String? extraTypeInfo;
  @override
  @JsonKey()
  final bool filterable;
// filteredLookupInfo; SF Type: Filtered Lookup Info: https://developer.salesforce.com/docs/atlas.en-us.uiapi.meta/uiapi/ui_api_responses_filtered_lookup_info.htm#ui_api_responses_filtered_lookup_info
  @override
  @JsonKey()
  final bool highScaleNumber;
  @override
  @JsonKey()
  final bool htmlFormatted;
  @override
  final String? inlineHelpText;
  @override
  final String? label;
  @override
  final int? length;
  @override
  final String? maskType;
  @override
  @JsonKey()
  final bool nameField;
  @override
  @JsonKey()
  final bool polymorphicForeignKey;
  @override
  final int? precision;
  @override
  @JsonKey()
  final bool reference;
  @override
  final String? referenceTargetField;
  final Map<String, dynamic> _referenceToInfos;
  @override
  @JsonKey()
  Map<String, dynamic> get referenceToInfos {
    if (_referenceToInfos is EqualUnmodifiableMapView) return _referenceToInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_referenceToInfos);
  }

  @override
  final String? relationshipName;
  @override
  @JsonKey()
  final bool required;
  @override
  final int? scale;
  @override
  @JsonKey()
  final bool searchPrefilterable;
  @override
  @JsonKey()
  final bool sortable;
  @override
  @JsonKey()
  final bool unique;
  @override
  @JsonKey()
  final bool updateable;

  @override
  String toString() {
    return 'Field(apiName: $apiName, calculated: $calculated, compound: $compound, compoundComponentName: $compoundComponentName, compoundFieldName: $compoundFieldName, controllerName: $controllerName, controllingFields: $controllingFields, createable: $createable, custom: $custom, dataType: $dataType, extraTypeInfo: $extraTypeInfo, filterable: $filterable, highScaleNumber: $highScaleNumber, htmlFormatted: $htmlFormatted, inlineHelpText: $inlineHelpText, label: $label, length: $length, maskType: $maskType, nameField: $nameField, polymorphicForeignKey: $polymorphicForeignKey, precision: $precision, reference: $reference, referenceTargetField: $referenceTargetField, referenceToInfos: $referenceToInfos, relationshipName: $relationshipName, required: $required, scale: $scale, searchPrefilterable: $searchPrefilterable, sortable: $sortable, unique: $unique, updateable: $updateable)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FieldImpl &&
            (identical(other.apiName, apiName) || other.apiName == apiName) &&
            (identical(other.calculated, calculated) ||
                other.calculated == calculated) &&
            (identical(other.compound, compound) ||
                other.compound == compound) &&
            (identical(other.compoundComponentName, compoundComponentName) ||
                other.compoundComponentName == compoundComponentName) &&
            (identical(other.compoundFieldName, compoundFieldName) ||
                other.compoundFieldName == compoundFieldName) &&
            (identical(other.controllerName, controllerName) ||
                other.controllerName == controllerName) &&
            const DeepCollectionEquality()
                .equals(other._controllingFields, _controllingFields) &&
            (identical(other.createable, createable) ||
                other.createable == createable) &&
            (identical(other.custom, custom) || other.custom == custom) &&
            (identical(other.dataType, dataType) ||
                other.dataType == dataType) &&
            (identical(other.extraTypeInfo, extraTypeInfo) ||
                other.extraTypeInfo == extraTypeInfo) &&
            (identical(other.filterable, filterable) ||
                other.filterable == filterable) &&
            (identical(other.highScaleNumber, highScaleNumber) ||
                other.highScaleNumber == highScaleNumber) &&
            (identical(other.htmlFormatted, htmlFormatted) ||
                other.htmlFormatted == htmlFormatted) &&
            (identical(other.inlineHelpText, inlineHelpText) ||
                other.inlineHelpText == inlineHelpText) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.maskType, maskType) ||
                other.maskType == maskType) &&
            (identical(other.nameField, nameField) ||
                other.nameField == nameField) &&
            (identical(other.polymorphicForeignKey, polymorphicForeignKey) ||
                other.polymorphicForeignKey == polymorphicForeignKey) &&
            (identical(other.precision, precision) ||
                other.precision == precision) &&
            (identical(other.reference, reference) ||
                other.reference == reference) &&
            (identical(other.referenceTargetField, referenceTargetField) ||
                other.referenceTargetField == referenceTargetField) &&
            const DeepCollectionEquality()
                .equals(other._referenceToInfos, _referenceToInfos) &&
            (identical(other.relationshipName, relationshipName) ||
                other.relationshipName == relationshipName) &&
            (identical(other.required, required) ||
                other.required == required) &&
            (identical(other.scale, scale) || other.scale == scale) &&
            (identical(other.searchPrefilterable, searchPrefilterable) ||
                other.searchPrefilterable == searchPrefilterable) &&
            (identical(other.sortable, sortable) ||
                other.sortable == sortable) &&
            (identical(other.unique, unique) || other.unique == unique) &&
            (identical(other.updateable, updateable) ||
                other.updateable == updateable));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        apiName,
        calculated,
        compound,
        compoundComponentName,
        compoundFieldName,
        controllerName,
        const DeepCollectionEquality().hash(_controllingFields),
        createable,
        custom,
        dataType,
        extraTypeInfo,
        filterable,
        highScaleNumber,
        htmlFormatted,
        inlineHelpText,
        label,
        length,
        maskType,
        nameField,
        polymorphicForeignKey,
        precision,
        reference,
        referenceTargetField,
        const DeepCollectionEquality().hash(_referenceToInfos),
        relationshipName,
        required,
        scale,
        searchPrefilterable,
        sortable,
        unique,
        updateable
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FieldImplCopyWith<_$FieldImpl> get copyWith =>
      __$$FieldImplCopyWithImpl<_$FieldImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FieldImplToJson(
      this,
    );
  }
}

abstract class _Field implements Field {
  const factory _Field(
      {final String? apiName,
      final bool calculated,
      final bool compound,
      final String? compoundComponentName,
      final String? compoundFieldName,
      final String? controllerName,
      final List<String> controllingFields,
      final bool createable,
      final bool custom,
      final String? dataType,
      final String? extraTypeInfo,
      final bool filterable,
      final bool highScaleNumber,
      final bool htmlFormatted,
      final String? inlineHelpText,
      final String? label,
      final int? length,
      final String? maskType,
      final bool nameField,
      final bool polymorphicForeignKey,
      final int? precision,
      final bool reference,
      final String? referenceTargetField,
      final Map<String, dynamic> referenceToInfos,
      final String? relationshipName,
      final bool required,
      final int? scale,
      final bool searchPrefilterable,
      final bool sortable,
      final bool unique,
      final bool updateable}) = _$FieldImpl;

  factory _Field.fromJson(Map<String, dynamic> json) = _$FieldImpl.fromJson;

  @override
  String? get apiName;
  @override
  bool get calculated;
  @override
  bool get compound;
  @override
  String? get compoundComponentName;
  @override
  String? get compoundFieldName;
  @override
  String? get controllerName;
  @override
  List<String> get controllingFields;
  @override
  bool get createable;
  @override
  bool get custom;
  @override
  String? get dataType;
  @override

  /// this can come as bool:false
// String? externalId,
  String? get extraTypeInfo;
  @override
  bool get filterable;
  @override // filteredLookupInfo; SF Type: Filtered Lookup Info: https://developer.salesforce.com/docs/atlas.en-us.uiapi.meta/uiapi/ui_api_responses_filtered_lookup_info.htm#ui_api_responses_filtered_lookup_info
  bool get highScaleNumber;
  @override
  bool get htmlFormatted;
  @override
  String? get inlineHelpText;
  @override
  String? get label;
  @override
  int? get length;
  @override
  String? get maskType;
  @override
  bool get nameField;
  @override
  bool get polymorphicForeignKey;
  @override
  int? get precision;
  @override
  bool get reference;
  @override
  String? get referenceTargetField;
  @override
  Map<String, dynamic> get referenceToInfos;
  @override
  String? get relationshipName;
  @override
  bool get required;
  @override
  int? get scale;
  @override
  bool get searchPrefilterable;
  @override
  bool get sortable;
  @override
  bool get unique;
  @override
  bool get updateable;
  @override
  @JsonKey(ignore: true)
  _$$FieldImplCopyWith<_$FieldImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
