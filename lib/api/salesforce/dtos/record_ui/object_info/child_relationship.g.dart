// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'child_relationship.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ChildRelationshipImpl _$$ChildRelationshipImplFromJson(
        Map<String, dynamic> json) =>
    _$ChildRelationshipImpl(
      childObjectApiName: json['childObjectApiName'] as String?,
      fieldName: json['fieldName'] as String?,
      junctionIdListNames: (json['junctionIdListNames'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      junctionReferenceTo: (json['junctionReferenceTo'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      relationshipName: json['relationshipName'] as String?,
    );

Map<String, dynamic> _$$ChildRelationshipImplToJson(
        _$ChildRelationshipImpl instance) =>
    <String, dynamic>{
      'childObjectApiName': instance.childObjectApiName,
      'fieldName': instance.fieldName,
      'junctionIdListNames': instance.junctionIdListNames,
      'junctionReferenceTo': instance.junctionReferenceTo,
      'relationshipName': instance.relationshipName,
    };
