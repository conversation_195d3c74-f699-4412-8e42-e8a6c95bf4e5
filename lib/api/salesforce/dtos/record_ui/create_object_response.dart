import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_object_response.freezed.dart';
part 'create_object_response.g.dart';
@freezed
class CreateSfObjectReponse with _$CreateSfObjectReponse {
  const factory CreateSfObjectReponse({
    required String id,
    required bool success,
    required List<String>? errors,
  }) = _CreateSfObjectReponse;

  factory CreateSfObjectReponse.fromJson(Map<String, dynamic> json) =>
      _$CreateSfObjectReponseFromJson(json);
}