import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'create_messaging_end_user_body.freezed.dart';
part 'create_messaging_end_user_body.g.dart';
@freezed
class CreateMessagingEndUserBody with _$CreateMessagingEndUserBody {
  const factory CreateMessagingEndUserBody({
    @J<PERSON><PERSON><PERSON>(name: 'ContactId') @ParseSfIdConverter() required SfId contactId,
    @<PERSON>son<PERSON><PERSON>(name: 'MessagingChannelId') @ParseSfIdConverter() required SfId messagingChannelId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'MessagingConsentStatus') @Default('ExplicitlyOptedIn') String messagingConsentStatus,
    @Json<PERSON>ey(name: 'MessagingPlatformKey') required String messagingPlatformKey,
    @Json<PERSON>ey(name: 'MessageType') @Default('Text') String messageType,
    @Json<PERSON>ey(name: 'Name') @Default('Text') String name,
    }) = _CreateMessagingEndUserBody;

  factory CreateMessagingEndUserBody.fromJson(Map<String, dynamic> json) =>
      _$CreateMessagingEndUserBodyFromJson(json);
  }
