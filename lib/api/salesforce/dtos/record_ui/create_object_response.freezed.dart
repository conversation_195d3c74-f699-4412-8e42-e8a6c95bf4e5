// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_object_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateSfObjectReponse _$CreateSfObjectReponseFromJson(
    Map<String, dynamic> json) {
  return _CreateSfObjectReponse.fromJson(json);
}

/// @nodoc
mixin _$CreateSfObjectReponse {
  String get id => throw _privateConstructorUsedError;
  bool get success => throw _privateConstructorUsedError;
  List<String>? get errors => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateSfObjectReponseCopyWith<CreateSfObjectReponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateSfObjectReponseCopyWith<$Res> {
  factory $CreateSfObjectReponseCopyWith(CreateSfObjectReponse value,
          $Res Function(CreateSfObjectReponse) then) =
      _$CreateSfObjectReponseCopyWithImpl<$Res, CreateSfObjectReponse>;
  @useResult
  $Res call({String id, bool success, List<String>? errors});
}

/// @nodoc
class _$CreateSfObjectReponseCopyWithImpl<$Res,
        $Val extends CreateSfObjectReponse>
    implements $CreateSfObjectReponseCopyWith<$Res> {
  _$CreateSfObjectReponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? success = null,
    Object? errors = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      errors: freezed == errors
          ? _value.errors
          : errors // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateSfObjectReponseImplCopyWith<$Res>
    implements $CreateSfObjectReponseCopyWith<$Res> {
  factory _$$CreateSfObjectReponseImplCopyWith(
          _$CreateSfObjectReponseImpl value,
          $Res Function(_$CreateSfObjectReponseImpl) then) =
      __$$CreateSfObjectReponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, bool success, List<String>? errors});
}

/// @nodoc
class __$$CreateSfObjectReponseImplCopyWithImpl<$Res>
    extends _$CreateSfObjectReponseCopyWithImpl<$Res,
        _$CreateSfObjectReponseImpl>
    implements _$$CreateSfObjectReponseImplCopyWith<$Res> {
  __$$CreateSfObjectReponseImplCopyWithImpl(_$CreateSfObjectReponseImpl _value,
      $Res Function(_$CreateSfObjectReponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? success = null,
    Object? errors = freezed,
  }) {
    return _then(_$CreateSfObjectReponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      errors: freezed == errors
          ? _value._errors
          : errors // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateSfObjectReponseImpl implements _CreateSfObjectReponse {
  const _$CreateSfObjectReponseImpl(
      {required this.id,
      required this.success,
      required final List<String>? errors})
      : _errors = errors;

  factory _$CreateSfObjectReponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateSfObjectReponseImplFromJson(json);

  @override
  final String id;
  @override
  final bool success;
  final List<String>? _errors;
  @override
  List<String>? get errors {
    final value = _errors;
    if (value == null) return null;
    if (_errors is EqualUnmodifiableListView) return _errors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CreateSfObjectReponse(id: $id, success: $success, errors: $errors)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateSfObjectReponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.success, success) || other.success == success) &&
            const DeepCollectionEquality().equals(other._errors, _errors));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, success, const DeepCollectionEquality().hash(_errors));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateSfObjectReponseImplCopyWith<_$CreateSfObjectReponseImpl>
      get copyWith => __$$CreateSfObjectReponseImplCopyWithImpl<
          _$CreateSfObjectReponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateSfObjectReponseImplToJson(
      this,
    );
  }
}

abstract class _CreateSfObjectReponse implements CreateSfObjectReponse {
  const factory _CreateSfObjectReponse(
      {required final String id,
      required final bool success,
      required final List<String>? errors}) = _$CreateSfObjectReponseImpl;

  factory _CreateSfObjectReponse.fromJson(Map<String, dynamic> json) =
      _$CreateSfObjectReponseImpl.fromJson;

  @override
  String get id;
  @override
  bool get success;
  @override
  List<String>? get errors;
  @override
  @JsonKey(ignore: true)
  _$$CreateSfObjectReponseImplCopyWith<_$CreateSfObjectReponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
