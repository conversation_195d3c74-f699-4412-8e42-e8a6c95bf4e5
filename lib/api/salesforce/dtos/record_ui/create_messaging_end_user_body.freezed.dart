// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_messaging_end_user_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateMessagingEndUserBody _$CreateMessagingEndUserBodyFromJson(
    Map<String, dynamic> json) {
  return _CreateMessagingEndUserBody.fromJson(json);
}

/// @nodoc
mixin _$CreateMessagingEndUserBody {
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId get contactId => throw _privateConstructorUsedError;
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  SfId get messagingChannelId => throw _privateConstructorUsedError;
  @JsonKey(name: 'MessagingConsentStatus')
  String get messagingConsentStatus => throw _privateConstructorUsedError;
  @JsonKey(name: 'MessagingPlatformKey')
  String get messagingPlatformKey => throw _privateConstructorUsedError;
  @JsonKey(name: 'MessageType')
  String get messageType => throw _privateConstructorUsedError;
  @JsonKey(name: 'Name')
  String get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateMessagingEndUserBodyCopyWith<CreateMessagingEndUserBody>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateMessagingEndUserBodyCopyWith<$Res> {
  factory $CreateMessagingEndUserBodyCopyWith(CreateMessagingEndUserBody value,
          $Res Function(CreateMessagingEndUserBody) then) =
      _$CreateMessagingEndUserBodyCopyWithImpl<$Res,
          CreateMessagingEndUserBody>;
  @useResult
  $Res call(
      {@JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      SfId messagingChannelId,
      @JsonKey(name: 'MessagingConsentStatus') String messagingConsentStatus,
      @JsonKey(name: 'MessagingPlatformKey') String messagingPlatformKey,
      @JsonKey(name: 'MessageType') String messageType,
      @JsonKey(name: 'Name') String name});

  $SfIdCopyWith<$Res> get contactId;
  $SfIdCopyWith<$Res> get messagingChannelId;
}

/// @nodoc
class _$CreateMessagingEndUserBodyCopyWithImpl<$Res,
        $Val extends CreateMessagingEndUserBody>
    implements $CreateMessagingEndUserBodyCopyWith<$Res> {
  _$CreateMessagingEndUserBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = null,
    Object? messagingChannelId = null,
    Object? messagingConsentStatus = null,
    Object? messagingPlatformKey = null,
    Object? messageType = null,
    Object? name = null,
  }) {
    return _then(_value.copyWith(
      contactId: null == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingChannelId: null == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingConsentStatus: null == messagingConsentStatus
          ? _value.messagingConsentStatus
          : messagingConsentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      messagingPlatformKey: null == messagingPlatformKey
          ? _value.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String,
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get contactId {
    return $SfIdCopyWith<$Res>(_value.contactId, (value) {
      return _then(_value.copyWith(contactId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingChannelId {
    return $SfIdCopyWith<$Res>(_value.messagingChannelId, (value) {
      return _then(_value.copyWith(messagingChannelId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreateMessagingEndUserBodyImplCopyWith<$Res>
    implements $CreateMessagingEndUserBodyCopyWith<$Res> {
  factory _$$CreateMessagingEndUserBodyImplCopyWith(
          _$CreateMessagingEndUserBodyImpl value,
          $Res Function(_$CreateMessagingEndUserBodyImpl) then) =
      __$$CreateMessagingEndUserBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'ContactId') @ParseSfIdConverter() SfId contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      SfId messagingChannelId,
      @JsonKey(name: 'MessagingConsentStatus') String messagingConsentStatus,
      @JsonKey(name: 'MessagingPlatformKey') String messagingPlatformKey,
      @JsonKey(name: 'MessageType') String messageType,
      @JsonKey(name: 'Name') String name});

  @override
  $SfIdCopyWith<$Res> get contactId;
  @override
  $SfIdCopyWith<$Res> get messagingChannelId;
}

/// @nodoc
class __$$CreateMessagingEndUserBodyImplCopyWithImpl<$Res>
    extends _$CreateMessagingEndUserBodyCopyWithImpl<$Res,
        _$CreateMessagingEndUserBodyImpl>
    implements _$$CreateMessagingEndUserBodyImplCopyWith<$Res> {
  __$$CreateMessagingEndUserBodyImplCopyWithImpl(
      _$CreateMessagingEndUserBodyImpl _value,
      $Res Function(_$CreateMessagingEndUserBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = null,
    Object? messagingChannelId = null,
    Object? messagingConsentStatus = null,
    Object? messagingPlatformKey = null,
    Object? messageType = null,
    Object? name = null,
  }) {
    return _then(_$CreateMessagingEndUserBodyImpl(
      contactId: null == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingChannelId: null == messagingChannelId
          ? _value.messagingChannelId
          : messagingChannelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingConsentStatus: null == messagingConsentStatus
          ? _value.messagingConsentStatus
          : messagingConsentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      messagingPlatformKey: null == messagingPlatformKey
          ? _value.messagingPlatformKey
          : messagingPlatformKey // ignore: cast_nullable_to_non_nullable
              as String,
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateMessagingEndUserBodyImpl implements _CreateMessagingEndUserBody {
  const _$CreateMessagingEndUserBodyImpl(
      {@JsonKey(name: 'ContactId')
      @ParseSfIdConverter()
      required this.contactId,
      @JsonKey(name: 'MessagingChannelId')
      @ParseSfIdConverter()
      required this.messagingChannelId,
      @JsonKey(name: 'MessagingConsentStatus')
      this.messagingConsentStatus = 'ExplicitlyOptedIn',
      @JsonKey(name: 'MessagingPlatformKey') required this.messagingPlatformKey,
      @JsonKey(name: 'MessageType') this.messageType = 'Text',
      @JsonKey(name: 'Name') this.name = 'Text'});

  factory _$CreateMessagingEndUserBodyImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CreateMessagingEndUserBodyImplFromJson(json);

  @override
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  final SfId contactId;
  @override
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  final SfId messagingChannelId;
  @override
  @JsonKey(name: 'MessagingConsentStatus')
  final String messagingConsentStatus;
  @override
  @JsonKey(name: 'MessagingPlatformKey')
  final String messagingPlatformKey;
  @override
  @JsonKey(name: 'MessageType')
  final String messageType;
  @override
  @JsonKey(name: 'Name')
  final String name;

  @override
  String toString() {
    return 'CreateMessagingEndUserBody(contactId: $contactId, messagingChannelId: $messagingChannelId, messagingConsentStatus: $messagingConsentStatus, messagingPlatformKey: $messagingPlatformKey, messageType: $messageType, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateMessagingEndUserBodyImpl &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messagingChannelId, messagingChannelId) ||
                other.messagingChannelId == messagingChannelId) &&
            (identical(other.messagingConsentStatus, messagingConsentStatus) ||
                other.messagingConsentStatus == messagingConsentStatus) &&
            (identical(other.messagingPlatformKey, messagingPlatformKey) ||
                other.messagingPlatformKey == messagingPlatformKey) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, contactId, messagingChannelId,
      messagingConsentStatus, messagingPlatformKey, messageType, name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateMessagingEndUserBodyImplCopyWith<_$CreateMessagingEndUserBodyImpl>
      get copyWith => __$$CreateMessagingEndUserBodyImplCopyWithImpl<
          _$CreateMessagingEndUserBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateMessagingEndUserBodyImplToJson(
      this,
    );
  }
}

abstract class _CreateMessagingEndUserBody
    implements CreateMessagingEndUserBody {
  const factory _CreateMessagingEndUserBody(
          {@JsonKey(name: 'ContactId')
          @ParseSfIdConverter()
          required final SfId contactId,
          @JsonKey(name: 'MessagingChannelId')
          @ParseSfIdConverter()
          required final SfId messagingChannelId,
          @JsonKey(name: 'MessagingConsentStatus')
          final String messagingConsentStatus,
          @JsonKey(name: 'MessagingPlatformKey')
          required final String messagingPlatformKey,
          @JsonKey(name: 'MessageType') final String messageType,
          @JsonKey(name: 'Name') final String name}) =
      _$CreateMessagingEndUserBodyImpl;

  factory _CreateMessagingEndUserBody.fromJson(Map<String, dynamic> json) =
      _$CreateMessagingEndUserBodyImpl.fromJson;

  @override
  @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId get contactId;
  @override
  @JsonKey(name: 'MessagingChannelId')
  @ParseSfIdConverter()
  SfId get messagingChannelId;
  @override
  @JsonKey(name: 'MessagingConsentStatus')
  String get messagingConsentStatus;
  @override
  @JsonKey(name: 'MessagingPlatformKey')
  String get messagingPlatformKey;
  @override
  @JsonKey(name: 'MessageType')
  String get messageType;
  @override
  @JsonKey(name: 'Name')
  String get name;
  @override
  @JsonKey(ignore: true)
  _$$CreateMessagingEndUserBodyImplCopyWith<_$CreateMessagingEndUserBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
