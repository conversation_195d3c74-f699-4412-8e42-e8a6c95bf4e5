// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'record_layout.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecordLayoutImpl _$$RecordLayoutImplFromJson(Map<String, dynamic> json) =>
    _$RecordLayoutImpl(
      id: json['id'] as String?,
      name: json['name'] as String?,
      sections: (json['sections'] as List<dynamic>?)
              ?.map((e) => e as Map<String, dynamic>)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$RecordLayoutImplToJson(_$RecordLayoutImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'sections': instance.sections,
    };
