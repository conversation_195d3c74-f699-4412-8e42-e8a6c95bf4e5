// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_messaging_end_user_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreateMessagingEndUserBodyImpl _$$CreateMessagingEndUserBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateMessagingEndUserBodyImpl(
      contactId: const ParseSfIdConverter().from<PERSON>son(json['ContactId']),
      messagingChannelId:
          const ParseSfIdConverter().from<PERSON>son(json['MessagingChannelId']),
      messagingConsentStatus:
          json['MessagingConsentStatus'] as String? ?? 'ExplicitlyOptedIn',
      messagingPlatformKey: json['MessagingPlatformKey'] as String,
      messageType: json['MessageType'] as String? ?? 'Text',
      name: json['Name'] as String? ?? 'Text',
    );

Map<String, dynamic> _$$CreateMessagingEndUserBodyImplToJson(
        _$CreateMessagingEndUserBodyImpl instance) =>
    <String, dynamic>{
      'ContactId': const ParseSfIdConverter().toJson(instance.contactId),
      'MessagingChannelId':
          const ParseSfIdConverter().toJson(instance.messagingChannelId),
      'MessagingConsentStatus': instance.messagingConsentStatus,
      'MessagingPlatformKey': instance.messagingPlatformKey,
      'MessageType': instance.messageType,
      'Name': instance.name,
    };
