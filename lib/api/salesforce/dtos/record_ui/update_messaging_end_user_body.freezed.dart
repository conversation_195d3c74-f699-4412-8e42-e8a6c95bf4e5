// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_messaging_end_user_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UpdateMessagingEndUserBody _$UpdateMessagingEndUserBodyFromJson(
    Map<String, dynamic> json) {
  return _UpdateMessagingEndUserBody.fromJson(json);
}

/// @nodoc
mixin _$UpdateMessagingEndUserBody {
// @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId get contactId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UpdateMessagingEndUserBodyCopyWith<UpdateMessagingEndUserBody>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateMessagingEndUserBodyCopyWith<$Res> {
  factory $UpdateMessagingEndUserBodyCopyWith(UpdateMessagingEndUserBody value,
          $Res Function(UpdateMessagingEndUserBody) then) =
      _$UpdateMessagingEndUserBodyCopyWithImpl<$Res,
          UpdateMessagingEndUserBody>;
  @useResult
  $Res call({@ParseSfIdConverter() SfId contactId});

  $SfIdCopyWith<$Res> get contactId;
}

/// @nodoc
class _$UpdateMessagingEndUserBodyCopyWithImpl<$Res,
        $Val extends UpdateMessagingEndUserBody>
    implements $UpdateMessagingEndUserBodyCopyWith<$Res> {
  _$UpdateMessagingEndUserBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = null,
  }) {
    return _then(_value.copyWith(
      contactId: null == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get contactId {
    return $SfIdCopyWith<$Res>(_value.contactId, (value) {
      return _then(_value.copyWith(contactId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UpdateMessagingEndUserBodyImplCopyWith<$Res>
    implements $UpdateMessagingEndUserBodyCopyWith<$Res> {
  factory _$$UpdateMessagingEndUserBodyImplCopyWith(
          _$UpdateMessagingEndUserBodyImpl value,
          $Res Function(_$UpdateMessagingEndUserBodyImpl) then) =
      __$$UpdateMessagingEndUserBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@ParseSfIdConverter() SfId contactId});

  @override
  $SfIdCopyWith<$Res> get contactId;
}

/// @nodoc
class __$$UpdateMessagingEndUserBodyImplCopyWithImpl<$Res>
    extends _$UpdateMessagingEndUserBodyCopyWithImpl<$Res,
        _$UpdateMessagingEndUserBodyImpl>
    implements _$$UpdateMessagingEndUserBodyImplCopyWith<$Res> {
  __$$UpdateMessagingEndUserBodyImplCopyWithImpl(
      _$UpdateMessagingEndUserBodyImpl _value,
      $Res Function(_$UpdateMessagingEndUserBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = null,
  }) {
    return _then(_$UpdateMessagingEndUserBodyImpl(
      contactId: null == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateMessagingEndUserBodyImpl implements _UpdateMessagingEndUserBody {
  const _$UpdateMessagingEndUserBodyImpl(
      {@ParseSfIdConverter() required this.contactId});

  factory _$UpdateMessagingEndUserBodyImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$UpdateMessagingEndUserBodyImplFromJson(json);

// @JsonKey(name: 'ContactId')
  @override
  @ParseSfIdConverter()
  final SfId contactId;

  @override
  String toString() {
    return 'UpdateMessagingEndUserBody(contactId: $contactId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateMessagingEndUserBodyImpl &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, contactId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateMessagingEndUserBodyImplCopyWith<_$UpdateMessagingEndUserBodyImpl>
      get copyWith => __$$UpdateMessagingEndUserBodyImplCopyWithImpl<
          _$UpdateMessagingEndUserBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateMessagingEndUserBodyImplToJson(
      this,
    );
  }
}

abstract class _UpdateMessagingEndUserBody
    implements UpdateMessagingEndUserBody {
  const factory _UpdateMessagingEndUserBody(
          {@ParseSfIdConverter() required final SfId contactId}) =
      _$UpdateMessagingEndUserBodyImpl;

  factory _UpdateMessagingEndUserBody.fromJson(Map<String, dynamic> json) =
      _$UpdateMessagingEndUserBodyImpl.fromJson;

  @override // @JsonKey(name: 'ContactId')
  @ParseSfIdConverter()
  SfId get contactId;
  @override
  @JsonKey(ignore: true)
  _$$UpdateMessagingEndUserBodyImplCopyWith<_$UpdateMessagingEndUserBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
