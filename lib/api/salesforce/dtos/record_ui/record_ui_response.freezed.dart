// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'record_ui_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RecordUiResponse _$RecordUiResponseFromJson(Map<String, dynamic> json) {
  return _RecordUiResponse.fromJson(json);
}

/// @nodoc
mixin _$RecordUiResponse {
  Map<String, RecordLayoutSectionUserState> get layoutUserStates =>
      throw _privateConstructorUsedError;
  Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
      get layouts => throw _privateConstructorUsedError;
  Map<String, ObjectInfo> get objectInfos => throw _privateConstructorUsedError;
  Map<String, dynamic> get records => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RecordUiResponseCopyWith<RecordUiResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecordUiResponseCopyWith<$Res> {
  factory $RecordUiResponseCopyWith(
          RecordUiResponse value, $Res Function(RecordUiResponse) then) =
      _$RecordUiResponseCopyWithImpl<$Res, RecordUiResponse>;
  @useResult
  $Res call(
      {Map<String, RecordLayoutSectionUserState> layoutUserStates,
      Map<String, Map<String, Map<String, Map<String, RecordLayout>>>> layouts,
      Map<String, ObjectInfo> objectInfos,
      Map<String, dynamic> records});
}

/// @nodoc
class _$RecordUiResponseCopyWithImpl<$Res, $Val extends RecordUiResponse>
    implements $RecordUiResponseCopyWith<$Res> {
  _$RecordUiResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? layoutUserStates = null,
    Object? layouts = null,
    Object? objectInfos = null,
    Object? records = null,
  }) {
    return _then(_value.copyWith(
      layoutUserStates: null == layoutUserStates
          ? _value.layoutUserStates
          : layoutUserStates // ignore: cast_nullable_to_non_nullable
              as Map<String, RecordLayoutSectionUserState>,
      layouts: null == layouts
          ? _value.layouts
          : layouts // ignore: cast_nullable_to_non_nullable
              as Map<String,
                  Map<String, Map<String, Map<String, RecordLayout>>>>,
      objectInfos: null == objectInfos
          ? _value.objectInfos
          : objectInfos // ignore: cast_nullable_to_non_nullable
              as Map<String, ObjectInfo>,
      records: null == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecordUiResponseImplCopyWith<$Res>
    implements $RecordUiResponseCopyWith<$Res> {
  factory _$$RecordUiResponseImplCopyWith(_$RecordUiResponseImpl value,
          $Res Function(_$RecordUiResponseImpl) then) =
      __$$RecordUiResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Map<String, RecordLayoutSectionUserState> layoutUserStates,
      Map<String, Map<String, Map<String, Map<String, RecordLayout>>>> layouts,
      Map<String, ObjectInfo> objectInfos,
      Map<String, dynamic> records});
}

/// @nodoc
class __$$RecordUiResponseImplCopyWithImpl<$Res>
    extends _$RecordUiResponseCopyWithImpl<$Res, _$RecordUiResponseImpl>
    implements _$$RecordUiResponseImplCopyWith<$Res> {
  __$$RecordUiResponseImplCopyWithImpl(_$RecordUiResponseImpl _value,
      $Res Function(_$RecordUiResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? layoutUserStates = null,
    Object? layouts = null,
    Object? objectInfos = null,
    Object? records = null,
  }) {
    return _then(_$RecordUiResponseImpl(
      layoutUserStates: null == layoutUserStates
          ? _value._layoutUserStates
          : layoutUserStates // ignore: cast_nullable_to_non_nullable
              as Map<String, RecordLayoutSectionUserState>,
      layouts: null == layouts
          ? _value._layouts
          : layouts // ignore: cast_nullable_to_non_nullable
              as Map<String,
                  Map<String, Map<String, Map<String, RecordLayout>>>>,
      objectInfos: null == objectInfos
          ? _value._objectInfos
          : objectInfos // ignore: cast_nullable_to_non_nullable
              as Map<String, ObjectInfo>,
      records: null == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecordUiResponseImpl implements _RecordUiResponse {
  const _$RecordUiResponseImpl(
      {final Map<String, RecordLayoutSectionUserState> layoutUserStates =
          const {},
      final Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
          layouts = const {},
      final Map<String, ObjectInfo> objectInfos = const {},
      final Map<String, dynamic> records = const {}})
      : _layoutUserStates = layoutUserStates,
        _layouts = layouts,
        _objectInfos = objectInfos,
        _records = records;

  factory _$RecordUiResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecordUiResponseImplFromJson(json);

  final Map<String, RecordLayoutSectionUserState> _layoutUserStates;
  @override
  @JsonKey()
  Map<String, RecordLayoutSectionUserState> get layoutUserStates {
    if (_layoutUserStates is EqualUnmodifiableMapView) return _layoutUserStates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_layoutUserStates);
  }

  final Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
      _layouts;
  @override
  @JsonKey()
  Map<String, Map<String, Map<String, Map<String, RecordLayout>>>> get layouts {
    if (_layouts is EqualUnmodifiableMapView) return _layouts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_layouts);
  }

  final Map<String, ObjectInfo> _objectInfos;
  @override
  @JsonKey()
  Map<String, ObjectInfo> get objectInfos {
    if (_objectInfos is EqualUnmodifiableMapView) return _objectInfos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_objectInfos);
  }

  final Map<String, dynamic> _records;
  @override
  @JsonKey()
  Map<String, dynamic> get records {
    if (_records is EqualUnmodifiableMapView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_records);
  }

  @override
  String toString() {
    return 'RecordUiResponse(layoutUserStates: $layoutUserStates, layouts: $layouts, objectInfos: $objectInfos, records: $records)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecordUiResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._layoutUserStates, _layoutUserStates) &&
            const DeepCollectionEquality().equals(other._layouts, _layouts) &&
            const DeepCollectionEquality()
                .equals(other._objectInfos, _objectInfos) &&
            const DeepCollectionEquality().equals(other._records, _records));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_layoutUserStates),
      const DeepCollectionEquality().hash(_layouts),
      const DeepCollectionEquality().hash(_objectInfos),
      const DeepCollectionEquality().hash(_records));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RecordUiResponseImplCopyWith<_$RecordUiResponseImpl> get copyWith =>
      __$$RecordUiResponseImplCopyWithImpl<_$RecordUiResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecordUiResponseImplToJson(
      this,
    );
  }
}

abstract class _RecordUiResponse implements RecordUiResponse {
  const factory _RecordUiResponse(
      {final Map<String, RecordLayoutSectionUserState> layoutUserStates,
      final Map<String, Map<String, Map<String, Map<String, RecordLayout>>>>
          layouts,
      final Map<String, ObjectInfo> objectInfos,
      final Map<String, dynamic> records}) = _$RecordUiResponseImpl;

  factory _RecordUiResponse.fromJson(Map<String, dynamic> json) =
      _$RecordUiResponseImpl.fromJson;

  @override
  Map<String, RecordLayoutSectionUserState> get layoutUserStates;
  @override
  Map<String, Map<String, Map<String, Map<String, RecordLayout>>>> get layouts;
  @override
  Map<String, ObjectInfo> get objectInfos;
  @override
  Map<String, dynamic> get records;
  @override
  @JsonKey(ignore: true)
  _$$RecordUiResponseImplCopyWith<_$RecordUiResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
