// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rs_l_consumer_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RSLConsumerResponse _$RSLConsumerResponseFromJson(Map<String, dynamic> json) {
  return _RSLConsumerResponse.fromJson(json);
}

/// @nodoc
mixin _$RSLConsumerResponse {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  String? get state => throw _privateConstructorUsedError;
  String? get zip => throw _privateConstructorUsedError;
  String? get country => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  String? get createdDate => throw _privateConstructorUsedError;
  String? get lastModifiedDate => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RSLConsumerResponseCopyWith<RSLConsumerResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RSLConsumerResponseCopyWith<$Res> {
  factory $RSLConsumerResponseCopyWith(
          RSLConsumerResponse value, $Res Function(RSLConsumerResponse) then) =
      _$RSLConsumerResponseCopyWithImpl<$Res, RSLConsumerResponse>;
  @useResult
  $Res call(
      {String? id,
      String? name,
      String? email,
      String? phone,
      String? address,
      String? city,
      String? state,
      String? zip,
      String? country,
      String? status,
      String? createdDate,
      String? lastModifiedDate});
}

/// @nodoc
class _$RSLConsumerResponseCopyWithImpl<$Res, $Val extends RSLConsumerResponse>
    implements $RSLConsumerResponseCopyWith<$Res> {
  _$RSLConsumerResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? zip = freezed,
    Object? country = freezed,
    Object? status = freezed,
    Object? createdDate = freezed,
    Object? lastModifiedDate = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      zip: freezed == zip
          ? _value.zip
          : zip // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _value.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RSLConsumerResponseImplCopyWith<$Res>
    implements $RSLConsumerResponseCopyWith<$Res> {
  factory _$$RSLConsumerResponseImplCopyWith(_$RSLConsumerResponseImpl value,
          $Res Function(_$RSLConsumerResponseImpl) then) =
      __$$RSLConsumerResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? name,
      String? email,
      String? phone,
      String? address,
      String? city,
      String? state,
      String? zip,
      String? country,
      String? status,
      String? createdDate,
      String? lastModifiedDate});
}

/// @nodoc
class __$$RSLConsumerResponseImplCopyWithImpl<$Res>
    extends _$RSLConsumerResponseCopyWithImpl<$Res, _$RSLConsumerResponseImpl>
    implements _$$RSLConsumerResponseImplCopyWith<$Res> {
  __$$RSLConsumerResponseImplCopyWithImpl(_$RSLConsumerResponseImpl _value,
      $Res Function(_$RSLConsumerResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? zip = freezed,
    Object? country = freezed,
    Object? status = freezed,
    Object? createdDate = freezed,
    Object? lastModifiedDate = freezed,
  }) {
    return _then(_$RSLConsumerResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      zip: freezed == zip
          ? _value.zip
          : zip // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _value.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RSLConsumerResponseImpl implements _RSLConsumerResponse {
  const _$RSLConsumerResponseImpl(
      {this.id,
      this.name,
      this.email,
      this.phone,
      this.address,
      this.city,
      this.state,
      this.zip,
      this.country,
      this.status,
      this.createdDate,
      this.lastModifiedDate});

  factory _$RSLConsumerResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$RSLConsumerResponseImplFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  @override
  final String? email;
  @override
  final String? phone;
  @override
  final String? address;
  @override
  final String? city;
  @override
  final String? state;
  @override
  final String? zip;
  @override
  final String? country;
  @override
  final String? status;
  @override
  final String? createdDate;
  @override
  final String? lastModifiedDate;

  @override
  String toString() {
    return 'RSLConsumerResponse(id: $id, name: $name, email: $email, phone: $phone, address: $address, city: $city, state: $state, zip: $zip, country: $country, status: $status, createdDate: $createdDate, lastModifiedDate: $lastModifiedDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RSLConsumerResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.zip, zip) || other.zip == zip) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, email, phone, address,
      city, state, zip, country, status, createdDate, lastModifiedDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RSLConsumerResponseImplCopyWith<_$RSLConsumerResponseImpl> get copyWith =>
      __$$RSLConsumerResponseImplCopyWithImpl<_$RSLConsumerResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RSLConsumerResponseImplToJson(
      this,
    );
  }
}

abstract class _RSLConsumerResponse implements RSLConsumerResponse {
  const factory _RSLConsumerResponse(
      {final String? id,
      final String? name,
      final String? email,
      final String? phone,
      final String? address,
      final String? city,
      final String? state,
      final String? zip,
      final String? country,
      final String? status,
      final String? createdDate,
      final String? lastModifiedDate}) = _$RSLConsumerResponseImpl;

  factory _RSLConsumerResponse.fromJson(Map<String, dynamic> json) =
      _$RSLConsumerResponseImpl.fromJson;

  @override
  String? get id;
  @override
  String? get name;
  @override
  String? get email;
  @override
  String? get phone;
  @override
  String? get address;
  @override
  String? get city;
  @override
  String? get state;
  @override
  String? get zip;
  @override
  String? get country;
  @override
  String? get status;
  @override
  String? get createdDate;
  @override
  String? get lastModifiedDate;
  @override
  @JsonKey(ignore: true)
  _$$RSLConsumerResponseImplCopyWith<_$RSLConsumerResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
