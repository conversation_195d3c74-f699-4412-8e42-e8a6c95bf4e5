// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContactResponseImpl _$$ContactResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ContactResponseImpl(
      attributes: json['attributes'] == null
          ? null
          : Attributes.fromJson(json['attributes'] as Map<String, dynamic>),
      id: json['Id'] as String?,
      accountId: json['AccountId'] as String?,
      individualId: json['IndividualId'] as String?,
      photoUrl: json['PhotoUrl'] as String?,
      ownerId: json['OwnerId'] as String?,
      department: json['Department'] as String?,
      title: json['Title'] as String?,
      email: json['Email'] as String?,
      phone: json['Phone'] as String?,
      mobilePhone: json['MobilePhone'] as String?,
      lastName: json['LastName'] as String?,
      firstName: json['FirstName'] as String?,
      name: json['Name'] as String?,
    );

Map<String, dynamic> _$$ContactResponseImplToJson(
        _$ContactResponseImpl instance) =>
    <String, dynamic>{
      'attributes': instance.attributes?.toJson(),
      'Id': instance.id,
      'AccountId': instance.accountId,
      'IndividualId': instance.individualId,
      'PhotoUrl': instance.photoUrl,
      'OwnerId': instance.ownerId,
      'Department': instance.department,
      'Title': instance.title,
      'Email': instance.email,
      'Phone': instance.phone,
      'MobilePhone': instance.mobilePhone,
      'LastName': instance.lastName,
      'FirstName': instance.firstName,
      'Name': instance.name,
    };
