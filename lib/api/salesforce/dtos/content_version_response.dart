/*
Defines ContentVersionResponse based on example response body:
{
    "attributes": {
        "type": "ContentVersion",
        "url": "/services/data/v60.0/sobjects/ContentVersion/068Hn00000R9pU7IAJ"
    },
    "Id": "068Hn00000R9pU7IAJ",
    "ContentDocumentId": "069Hn00000QQyVlIAL",
    "IsLatest": true,
    "ContentUrl": null,
    "ContentBodyId": "05THn00001Tlp28MAB",
    "VersionNumber": "1",
    "Title": "Small Text",
    "Description": null,
    "ReasonForChange": null,
    "SharingOption": "A",
    "SharingPrivacy": "N",
    "PathOnClient": "Small Text.txt",
    "RatingCount": 0,
    "IsDeleted": false,
    "ContentModifiedDate": "2024-05-25T16:02:16.000+0000",
    "ContentModifiedById": "005Hn00000I02ebIAB",
    "Language": "en_US",
    "PositiveRatingCount": 0,
    "NegativeRatingCount": 0,
    "FeaturedContentBoost": null,
    "FeaturedContentDate": null,
    "OwnerId": "005Hn00000I02ebIAB",
    "CreatedById": "005Hn00000I02ebIAB",
    "CreatedDate": "2024-05-25T16:02:16.000+0000",
    "LastModifiedById": "005Hn00000I02ebIAB",
    "LastModifiedDate": "2024-05-25T16:02:17.000+0000",
    "SystemModstamp": "2024-05-25T16:02:22.000+0000",
    "TagCsv": null,
    "FileType": "TEXT",
    "PublishStatus": "P",
    "VersionData": "/services/data/v60.0/sobjects/ContentVersion/068Hn00000R9pU7IAJ/VersionData",
    "ContentSize": 13121,
    "FileExtension": "txt",
    "FirstPublishLocationId": "005Hn00000I02ebIAB",
    "Origin": "C",
    "NetworkId": null,
    "ContentLocation": "S",
    "TextPreview": null,
    "ExternalDocumentInfo1": null,
    "ExternalDocumentInfo2": null,
    "ExternalDataSourceId": null,
    "Checksum": "323f1b4f46ebaa1997fb09d72462cfbf",
    "IsMajorVersion": true,
    "IsAssetEnabled": false,
    "VersionDataUrl": "https://re1693247397909.file.force.com/sfc/servlet.shepherd/version/download/068Hn00000R9pU7"
}
 */

import 'package:freezed_annotation/freezed_annotation.dart';

part 'content_version_response.freezed.dart';
part 'content_version_response.g.dart';

@freezed
class ContentVersionResponse with _$ContentVersionResponse {
  const factory ContentVersionResponse({
    @JsonKey(name: 'Id') String? id,
    @JsonKey(name: 'ContentDocumentId') String? contentDocumentId,
    @JsonKey(name: 'IsLatest') bool? isLatest,
    @JsonKey(name: 'ContentBodyId') String? contentBodyId,
    @JsonKey(name: 'VersionNumber') String? versionNumber,
    @JsonKey(name: 'Title') String? title,
    @JsonKey(name: 'SharingOption') String? sharingOption,
    @JsonKey(name: 'SharingPrivacy') String? sharingPrivacy,
    @JsonKey(name: 'PathOnClient') String? pathOnClient,
    @JsonKey(name: 'RatingCount') int? ratingCount,
    @JsonKey(name: 'IsDeleted') bool? isDeleted,
    @JsonKey(name: 'ContentModifiedDate') String? contentModifiedDate,
    @JsonKey(name: 'ContentModifiedById') String? contentModifiedById,
    @JsonKey(name: 'Language') String? language,
    @JsonKey(name: 'PositiveRatingCount') int? positiveRatingCount,
    @JsonKey(name: 'NegativeRatingCount') int? negativeRatingCount,
    @JsonKey(name: 'OwnerId') String? ownerId,
    @JsonKey(name: 'CreatedById') String? createdById,
    @JsonKey(name: 'CreatedDate') String? createdDate,
    @JsonKey(name: 'LastModifiedById') String? lastModifiedById,
    @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
    @JsonKey(name: 'SystemModstamp') String? systemModstamp,
    @JsonKey(name: 'FileType') String? fileType,
    @JsonKey(name: 'PublishStatus') String? publishStatus,
    @JsonKey(name: 'VersionData') String? versionData,
    @JsonKey(name: 'ContentSize') int? contentSize,
    @JsonKey(name: 'FileExtension') String? fileExtension,
    @JsonKey(name: 'FirstPublishLocationId') String? firstPublishLocationId,
    @JsonKey(name: 'Origin') String? origin,
    @JsonKey(name: 'ContentLocation') String? contentLocation,
    @JsonKey(name: 'VersionDataUrl') String? versionDataUrl,
  }) = _ContentVersionResponse;

  factory ContentVersionResponse.fromJson(Map<String, dynamic> json) =>
      _$ContentVersionResponseFromJson(json);
}