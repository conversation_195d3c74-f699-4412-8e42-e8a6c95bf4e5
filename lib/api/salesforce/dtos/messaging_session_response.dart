import 'package:freezed_annotation/freezed_annotation.dart';

part 'messaging_session_response.freezed.dart';
part 'messaging_session_response.g.dart';

enum MessagingSessionStatus {
  newStatus,
  active,
  consent,
  waiting,
  paused,
  inactive,
  ended,
  error
}

enum MessagingSessionOrigin {
  agentInitiated,
  conversationClose,
  conversationControlLost,
  help,
  inboundInitiated,
  optIn,
  optOut,
  triggeredOutbound
}

class MessagingSessionStatusConverter
    implements JsonConverter<MessagingSessionStatus, String> {
  const MessagingSessionStatusConverter();

  @override
  MessagingSessionStatus fromJson(String json) {
    return MessagingSessionStatus.values.firstWhere(
      (e) => e.toString().split('.').last.toLowerCase() == json.toLowerCase(),
      orElse: () =>
          throw ArgumentError('Invalid messagingSessionStatus: $json'),
    );
  }

  @override
  String toJson(MessagingSessionStatus object) =>
      object.toString().split('.').last;
}

class MessagingSessionOriginConverter
    implements JsonConverter<MessagingSessionOrigin, String> {
  const MessagingSessionOriginConverter();

  @override
  MessagingSessionOrigin fromJson(String json) {
    return MessagingSessionOrigin.values.firstWhere(
      (e) => e.toString().split('.').last.toLowerCase() == json.toLowerCase(),
      orElse: () =>
          throw ArgumentError('Invalid messagingSessionOrigin: $json'),
    );
  }

  @override
  String toJson(MessagingSessionOrigin object) =>
      object.toString().split('.').last;
}

@freezed
class MessagingSessionResponse with _$MessagingSessionResponse {
  const factory MessagingSessionResponse({
    @JsonKey(name: "Id") required String id,
    @JsonKey(name: "Name") String? name,
    @JsonKey(name: "ChannelName") String? channelName,
    @JsonKey(name: "SessionKey") String? sessionKey,
    @JsonKey(name: "Origin")
    @MessagingSessionOriginConverter()
    MessagingSessionOrigin? origin,
    @JsonKey(name: "ChannelType") String? channelType,
    @JsonKey(name: "ConversationId") String? conversationId,
    @JsonKey(name: "MessagingChannelId") String? messagingChannelId,
    @JsonKey(name: "MessagingEndUserId") String? messagingEndUserId,
    @JsonKey(name: "CreatedDate") DateTime? createdDate,
    @JsonKey(name: "OwnerId") String? ownerId,
    @JsonKey(name: "Status")
    @MessagingSessionStatusConverter()
    MessagingSessionStatus? status,
    @JsonKey(name: "EndUserAccountId") String? endUserAccountId,
    @JsonKey(name: "EndUserContactId") String? endUserContactId,
    @JsonKey(name: "EndUserLanguage") String? endUserLanguage,
    @JsonKey(name: "CaseId") String? caseId,
    @JsonKey(name: "LeadId") String? leadId,
    @JsonKey(name: "OpportunityId") String? opportunityId,
  }) = _MessagingSessionResponse;

  factory MessagingSessionResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingSessionResponseFromJson(json);
}
