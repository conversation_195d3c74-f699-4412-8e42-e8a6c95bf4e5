// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ContactResponse _$ContactResponseFromJson(Map<String, dynamic> json) {
  return _ContactResponse.fromJson(json);
}

/// @nodoc
mixin _$ContactResponse {
  Attributes? get attributes => throw _privateConstructorUsedError;
  @JsonKey(name: 'Id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'AccountId')
  String? get accountId => throw _privateConstructorUsedError;
  @JsonKey(name: 'IndividualId')
  String? get individualId => throw _privateConstructorUsedError;
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'OwnerId')
  String? get ownerId => throw _privateConstructorUsedError;
  @JsonKey(name: 'Department')
  String? get department => throw _privateConstructorUsedError;
  @JsonKey(name: 'Title')
  String? get title => throw _privateConstructorUsedError;
  @JsonKey(name: 'Email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'Phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone => throw _privateConstructorUsedError;
  @JsonKey(name: 'LastName')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'FirstName')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'Name')
  String? get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContactResponseCopyWith<ContactResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactResponseCopyWith<$Res> {
  factory $ContactResponseCopyWith(
          ContactResponse value, $Res Function(ContactResponse) then) =
      _$ContactResponseCopyWithImpl<$Res, ContactResponse>;
  @useResult
  $Res call(
      {Attributes? attributes,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'AccountId') String? accountId,
      @JsonKey(name: 'IndividualId') String? individualId,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'OwnerId') String? ownerId,
      @JsonKey(name: 'Department') String? department,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'Name') String? name});

  $AttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class _$ContactResponseCopyWithImpl<$Res, $Val extends ContactResponse>
    implements $ContactResponseCopyWith<$Res> {
  _$ContactResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = freezed,
    Object? id = freezed,
    Object? accountId = freezed,
    Object? individualId = freezed,
    Object? photoUrl = freezed,
    Object? ownerId = freezed,
    Object? department = freezed,
    Object? title = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? mobilePhone = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? name = freezed,
  }) {
    return _then(_value.copyWith(
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      individualId: freezed == individualId
          ? _value.individualId
          : individualId // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res>? get attributes {
    if (_value.attributes == null) {
      return null;
    }

    return $AttributesCopyWith<$Res>(_value.attributes!, (value) {
      return _then(_value.copyWith(attributes: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ContactResponseImplCopyWith<$Res>
    implements $ContactResponseCopyWith<$Res> {
  factory _$$ContactResponseImplCopyWith(_$ContactResponseImpl value,
          $Res Function(_$ContactResponseImpl) then) =
      __$$ContactResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Attributes? attributes,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'AccountId') String? accountId,
      @JsonKey(name: 'IndividualId') String? individualId,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'OwnerId') String? ownerId,
      @JsonKey(name: 'Department') String? department,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'Phone') String? phone,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'Name') String? name});

  @override
  $AttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class __$$ContactResponseImplCopyWithImpl<$Res>
    extends _$ContactResponseCopyWithImpl<$Res, _$ContactResponseImpl>
    implements _$$ContactResponseImplCopyWith<$Res> {
  __$$ContactResponseImplCopyWithImpl(
      _$ContactResponseImpl _value, $Res Function(_$ContactResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = freezed,
    Object? id = freezed,
    Object? accountId = freezed,
    Object? individualId = freezed,
    Object? photoUrl = freezed,
    Object? ownerId = freezed,
    Object? department = freezed,
    Object? title = freezed,
    Object? email = freezed,
    Object? phone = freezed,
    Object? mobilePhone = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? name = freezed,
  }) {
    return _then(_$ContactResponseImpl(
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      individualId: freezed == individualId
          ? _value.individualId
          : individualId // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContactResponseImpl implements _ContactResponse {
  const _$ContactResponseImpl(
      {this.attributes,
      @JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'AccountId') this.accountId,
      @JsonKey(name: 'IndividualId') this.individualId,
      @JsonKey(name: 'PhotoUrl') this.photoUrl,
      @JsonKey(name: 'OwnerId') this.ownerId,
      @JsonKey(name: 'Department') this.department,
      @JsonKey(name: 'Title') this.title,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'Phone') this.phone,
      @JsonKey(name: 'MobilePhone') this.mobilePhone,
      @JsonKey(name: 'LastName') this.lastName,
      @JsonKey(name: 'FirstName') this.firstName,
      @JsonKey(name: 'Name') this.name});

  factory _$ContactResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContactResponseImplFromJson(json);

  @override
  final Attributes? attributes;
  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'AccountId')
  final String? accountId;
  @override
  @JsonKey(name: 'IndividualId')
  final String? individualId;
  @override
  @JsonKey(name: 'PhotoUrl')
  final String? photoUrl;
  @override
  @JsonKey(name: 'OwnerId')
  final String? ownerId;
  @override
  @JsonKey(name: 'Department')
  final String? department;
  @override
  @JsonKey(name: 'Title')
  final String? title;
  @override
  @JsonKey(name: 'Email')
  final String? email;
  @override
  @JsonKey(name: 'Phone')
  final String? phone;
  @override
  @JsonKey(name: 'MobilePhone')
  final String? mobilePhone;
  @override
  @JsonKey(name: 'LastName')
  final String? lastName;
  @override
  @JsonKey(name: 'FirstName')
  final String? firstName;
  @override
  @JsonKey(name: 'Name')
  final String? name;

  @override
  String toString() {
    return 'ContactResponse(attributes: $attributes, id: $id, accountId: $accountId, individualId: $individualId, photoUrl: $photoUrl, ownerId: $ownerId, department: $department, title: $title, email: $email, phone: $phone, mobilePhone: $mobilePhone, lastName: $lastName, firstName: $firstName, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactResponseImpl &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.individualId, individualId) ||
                other.individualId == individualId) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      attributes,
      id,
      accountId,
      individualId,
      photoUrl,
      ownerId,
      department,
      title,
      email,
      phone,
      mobilePhone,
      lastName,
      firstName,
      name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactResponseImplCopyWith<_$ContactResponseImpl> get copyWith =>
      __$$ContactResponseImplCopyWithImpl<_$ContactResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContactResponseImplToJson(
      this,
    );
  }
}

abstract class _ContactResponse implements ContactResponse {
  const factory _ContactResponse(
      {final Attributes? attributes,
      @JsonKey(name: 'Id') final String? id,
      @JsonKey(name: 'AccountId') final String? accountId,
      @JsonKey(name: 'IndividualId') final String? individualId,
      @JsonKey(name: 'PhotoUrl') final String? photoUrl,
      @JsonKey(name: 'OwnerId') final String? ownerId,
      @JsonKey(name: 'Department') final String? department,
      @JsonKey(name: 'Title') final String? title,
      @JsonKey(name: 'Email') final String? email,
      @JsonKey(name: 'Phone') final String? phone,
      @JsonKey(name: 'MobilePhone') final String? mobilePhone,
      @JsonKey(name: 'LastName') final String? lastName,
      @JsonKey(name: 'FirstName') final String? firstName,
      @JsonKey(name: 'Name') final String? name}) = _$ContactResponseImpl;

  factory _ContactResponse.fromJson(Map<String, dynamic> json) =
      _$ContactResponseImpl.fromJson;

  @override
  Attributes? get attributes;
  @override
  @JsonKey(name: 'Id')
  String? get id;
  @override
  @JsonKey(name: 'AccountId')
  String? get accountId;
  @override
  @JsonKey(name: 'IndividualId')
  String? get individualId;
  @override
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl;
  @override
  @JsonKey(name: 'OwnerId')
  String? get ownerId;
  @override
  @JsonKey(name: 'Department')
  String? get department;
  @override
  @JsonKey(name: 'Title')
  String? get title;
  @override
  @JsonKey(name: 'Email')
  String? get email;
  @override
  @JsonKey(name: 'Phone')
  String? get phone;
  @override
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone;
  @override
  @JsonKey(name: 'LastName')
  String? get lastName;
  @override
  @JsonKey(name: 'FirstName')
  String? get firstName;
  @override
  @JsonKey(name: 'Name')
  String? get name;
  @override
  @JsonKey(ignore: true)
  _$$ContactResponseImplCopyWith<_$ContactResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
