// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search_records_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SearchRecordsResponse _$SearchRecordsResponseFromJson(
    Map<String, dynamic> json) {
  return _SearchRecordsResponse.fromJson(json);
}

/// @nodoc
mixin _$SearchRecordsResponse {
  List<SearchRecord> get searchRecords => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SearchRecordsResponseCopyWith<SearchRecordsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchRecordsResponseCopyWith<$Res> {
  factory $SearchRecordsResponseCopyWith(SearchRecordsResponse value,
          $Res Function(SearchRecordsResponse) then) =
      _$SearchRecordsResponseCopyWithImpl<$Res, SearchRecordsResponse>;
  @useResult
  $Res call({List<SearchRecord> searchRecords});
}

/// @nodoc
class _$SearchRecordsResponseCopyWithImpl<$Res,
        $Val extends SearchRecordsResponse>
    implements $SearchRecordsResponseCopyWith<$Res> {
  _$SearchRecordsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchRecords = null,
  }) {
    return _then(_value.copyWith(
      searchRecords: null == searchRecords
          ? _value.searchRecords
          : searchRecords // ignore: cast_nullable_to_non_nullable
              as List<SearchRecord>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SearchRecordsResponseImplCopyWith<$Res>
    implements $SearchRecordsResponseCopyWith<$Res> {
  factory _$$SearchRecordsResponseImplCopyWith(
          _$SearchRecordsResponseImpl value,
          $Res Function(_$SearchRecordsResponseImpl) then) =
      __$$SearchRecordsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SearchRecord> searchRecords});
}

/// @nodoc
class __$$SearchRecordsResponseImplCopyWithImpl<$Res>
    extends _$SearchRecordsResponseCopyWithImpl<$Res,
        _$SearchRecordsResponseImpl>
    implements _$$SearchRecordsResponseImplCopyWith<$Res> {
  __$$SearchRecordsResponseImplCopyWithImpl(_$SearchRecordsResponseImpl _value,
      $Res Function(_$SearchRecordsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchRecords = null,
  }) {
    return _then(_$SearchRecordsResponseImpl(
      searchRecords: null == searchRecords
          ? _value._searchRecords
          : searchRecords // ignore: cast_nullable_to_non_nullable
              as List<SearchRecord>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SearchRecordsResponseImpl implements _SearchRecordsResponse {
  const _$SearchRecordsResponseImpl(
      {required final List<SearchRecord> searchRecords})
      : _searchRecords = searchRecords;

  factory _$SearchRecordsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SearchRecordsResponseImplFromJson(json);

  final List<SearchRecord> _searchRecords;
  @override
  List<SearchRecord> get searchRecords {
    if (_searchRecords is EqualUnmodifiableListView) return _searchRecords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_searchRecords);
  }

  @override
  String toString() {
    return 'SearchRecordsResponse(searchRecords: $searchRecords)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchRecordsResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._searchRecords, _searchRecords));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_searchRecords));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchRecordsResponseImplCopyWith<_$SearchRecordsResponseImpl>
      get copyWith => __$$SearchRecordsResponseImplCopyWithImpl<
          _$SearchRecordsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SearchRecordsResponseImplToJson(
      this,
    );
  }
}

abstract class _SearchRecordsResponse implements SearchRecordsResponse {
  const factory _SearchRecordsResponse(
          {required final List<SearchRecord> searchRecords}) =
      _$SearchRecordsResponseImpl;

  factory _SearchRecordsResponse.fromJson(Map<String, dynamic> json) =
      _$SearchRecordsResponseImpl.fromJson;

  @override
  List<SearchRecord> get searchRecords;
  @override
  @JsonKey(ignore: true)
  _$$SearchRecordsResponseImplCopyWith<_$SearchRecordsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SearchRecord _$SearchRecordFromJson(Map<String, dynamic> json) {
  return _SearchRecord.fromJson(json);
}

/// @nodoc
mixin _$SearchRecord {
  Attributes get attributes => throw _privateConstructorUsedError;
  @JsonKey(name: 'Name')
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'FirstName')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'LastName')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'Id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'Email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone => throw _privateConstructorUsedError;
  @JsonKey(name: 'Title')
  String? get title => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SearchRecordCopyWith<SearchRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchRecordCopyWith<$Res> {
  factory $SearchRecordCopyWith(
          SearchRecord value, $Res Function(SearchRecord) then) =
      _$SearchRecordCopyWithImpl<$Res, SearchRecord>;
  @useResult
  $Res call(
      {Attributes attributes,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Title') String? title});

  $AttributesCopyWith<$Res> get attributes;
}

/// @nodoc
class _$SearchRecordCopyWithImpl<$Res, $Val extends SearchRecord>
    implements $SearchRecordCopyWith<$Res> {
  _$SearchRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = null,
    Object? name = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? photoUrl = freezed,
    Object? id = freezed,
    Object? email = freezed,
    Object? mobilePhone = freezed,
    Object? title = freezed,
  }) {
    return _then(_value.copyWith(
      attributes: null == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res> get attributes {
    return $AttributesCopyWith<$Res>(_value.attributes, (value) {
      return _then(_value.copyWith(attributes: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SearchRecordImplCopyWith<$Res>
    implements $SearchRecordCopyWith<$Res> {
  factory _$$SearchRecordImplCopyWith(
          _$SearchRecordImpl value, $Res Function(_$SearchRecordImpl) then) =
      __$$SearchRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Attributes attributes,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Title') String? title});

  @override
  $AttributesCopyWith<$Res> get attributes;
}

/// @nodoc
class __$$SearchRecordImplCopyWithImpl<$Res>
    extends _$SearchRecordCopyWithImpl<$Res, _$SearchRecordImpl>
    implements _$$SearchRecordImplCopyWith<$Res> {
  __$$SearchRecordImplCopyWithImpl(
      _$SearchRecordImpl _value, $Res Function(_$SearchRecordImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = null,
    Object? name = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? photoUrl = freezed,
    Object? id = freezed,
    Object? email = freezed,
    Object? mobilePhone = freezed,
    Object? title = freezed,
  }) {
    return _then(_$SearchRecordImpl(
      attributes: null == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      photoUrl: freezed == photoUrl
          ? _value.photoUrl
          : photoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      mobilePhone: freezed == mobilePhone
          ? _value.mobilePhone
          : mobilePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SearchRecordImpl implements _SearchRecord {
  const _$SearchRecordImpl(
      {required this.attributes,
      @JsonKey(name: 'Name') this.name,
      @JsonKey(name: 'FirstName') this.firstName,
      @JsonKey(name: 'LastName') this.lastName,
      @JsonKey(name: 'PhotoUrl') this.photoUrl,
      @JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'Email') this.email,
      @JsonKey(name: 'MobilePhone') this.mobilePhone,
      @JsonKey(name: 'Title') this.title});

  factory _$SearchRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$SearchRecordImplFromJson(json);

  @override
  final Attributes attributes;
  @override
  @JsonKey(name: 'Name')
  final String? name;
  @override
  @JsonKey(name: 'FirstName')
  final String? firstName;
  @override
  @JsonKey(name: 'LastName')
  final String? lastName;
  @override
  @JsonKey(name: 'PhotoUrl')
  final String? photoUrl;
  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'Email')
  final String? email;
  @override
  @JsonKey(name: 'MobilePhone')
  final String? mobilePhone;
  @override
  @JsonKey(name: 'Title')
  final String? title;

  @override
  String toString() {
    return 'SearchRecord(attributes: $attributes, name: $name, firstName: $firstName, lastName: $lastName, photoUrl: $photoUrl, id: $id, email: $email, mobilePhone: $mobilePhone, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchRecordImpl &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.mobilePhone, mobilePhone) ||
                other.mobilePhone == mobilePhone) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, attributes, name, firstName,
      lastName, photoUrl, id, email, mobilePhone, title);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchRecordImplCopyWith<_$SearchRecordImpl> get copyWith =>
      __$$SearchRecordImplCopyWithImpl<_$SearchRecordImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SearchRecordImplToJson(
      this,
    );
  }
}

abstract class _SearchRecord implements SearchRecord {
  const factory _SearchRecord(
      {required final Attributes attributes,
      @JsonKey(name: 'Name') final String? name,
      @JsonKey(name: 'FirstName') final String? firstName,
      @JsonKey(name: 'LastName') final String? lastName,
      @JsonKey(name: 'PhotoUrl') final String? photoUrl,
      @JsonKey(name: 'Id') final String? id,
      @JsonKey(name: 'Email') final String? email,
      @JsonKey(name: 'MobilePhone') final String? mobilePhone,
      @JsonKey(name: 'Title') final String? title}) = _$SearchRecordImpl;

  factory _SearchRecord.fromJson(Map<String, dynamic> json) =
      _$SearchRecordImpl.fromJson;

  @override
  Attributes get attributes;
  @override
  @JsonKey(name: 'Name')
  String? get name;
  @override
  @JsonKey(name: 'FirstName')
  String? get firstName;
  @override
  @JsonKey(name: 'LastName')
  String? get lastName;
  @override
  @JsonKey(name: 'PhotoUrl')
  String? get photoUrl;
  @override
  @JsonKey(name: 'Id')
  String? get id;
  @override
  @JsonKey(name: 'Email')
  String? get email;
  @override
  @JsonKey(name: 'MobilePhone')
  String? get mobilePhone;
  @override
  @JsonKey(name: 'Title')
  String? get title;
  @override
  @JsonKey(ignore: true)
  _$$SearchRecordImplCopyWith<_$SearchRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Attributes _$AttributesFromJson(Map<String, dynamic> json) {
  return _Attributes.fromJson(json);
}

/// @nodoc
mixin _$Attributes {
  String get type => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AttributesCopyWith<Attributes> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AttributesCopyWith<$Res> {
  factory $AttributesCopyWith(
          Attributes value, $Res Function(Attributes) then) =
      _$AttributesCopyWithImpl<$Res, Attributes>;
  @useResult
  $Res call({String type, String url});
}

/// @nodoc
class _$AttributesCopyWithImpl<$Res, $Val extends Attributes>
    implements $AttributesCopyWith<$Res> {
  _$AttributesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? url = null,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AttributesImplCopyWith<$Res>
    implements $AttributesCopyWith<$Res> {
  factory _$$AttributesImplCopyWith(
          _$AttributesImpl value, $Res Function(_$AttributesImpl) then) =
      __$$AttributesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String type, String url});
}

/// @nodoc
class __$$AttributesImplCopyWithImpl<$Res>
    extends _$AttributesCopyWithImpl<$Res, _$AttributesImpl>
    implements _$$AttributesImplCopyWith<$Res> {
  __$$AttributesImplCopyWithImpl(
      _$AttributesImpl _value, $Res Function(_$AttributesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? url = null,
  }) {
    return _then(_$AttributesImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AttributesImpl implements _Attributes {
  const _$AttributesImpl({required this.type, required this.url});

  factory _$AttributesImpl.fromJson(Map<String, dynamic> json) =>
      _$$AttributesImplFromJson(json);

  @override
  final String type;
  @override
  final String url;

  @override
  String toString() {
    return 'Attributes(type: $type, url: $url)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AttributesImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AttributesImplCopyWith<_$AttributesImpl> get copyWith =>
      __$$AttributesImplCopyWithImpl<_$AttributesImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AttributesImplToJson(
      this,
    );
  }
}

abstract class _Attributes implements Attributes {
  const factory _Attributes(
      {required final String type,
      required final String url}) = _$AttributesImpl;

  factory _Attributes.fromJson(Map<String, dynamic> json) =
      _$AttributesImpl.fromJson;

  @override
  String get type;
  @override
  String get url;
  @override
  @JsonKey(ignore: true)
  _$$AttributesImplCopyWith<_$AttributesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
