import 'package:freezed_annotation/freezed_annotation.dart';

part 'conversation_entries.freezed.dart';
part 'conversation_entries.g.dart';

// TODO: update to match actual response
@freezed
class ConversationEntries with _$ConversationEntries {
  const ConversationEntries._();
  const factory ConversationEntries({
    @JsonKey(name: 'Id') String? id,
    @JsonKey(name: 'ConversationIdentifier') String? conversationIdentifier,
    @Json<PERSON>ey(name: 'ConversationChannelId') String? conversationChannelId,
  }) = _ConversationEntries;

  factory ConversationEntries.fromJson(Map<String, dynamic> json) =>
      _$ConversationEntriesFromJson(json);
}
