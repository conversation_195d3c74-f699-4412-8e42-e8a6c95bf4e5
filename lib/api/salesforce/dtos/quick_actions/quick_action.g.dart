// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quick_action.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QuickActionImpl _$$QuickActionImplFromJson(Map<String, dynamic> json) =>
    _$QuickActionImpl(
      isResolvable: json['isResolvable'] as bool? ?? true,
      actionUrl: json['action_url__c'] as String?,
      actionName: json['action_name__c'] as String?,
      actionIcon: json['action_icon__c'] as String?,
      actionDescription: json['action_description__c'] as String?,
    );

Map<String, dynamic> _$$QuickActionImplToJson(_$QuickActionImpl instance) =>
    <String, dynamic>{
      'isResolvable': instance.isResolvable,
      'action_url__c': instance.actionUrl,
      'action_name__c': instance.actionName,
      'action_icon__c': instance.actionIcon,
      'action_description__c': instance.actionDescription,
    };
