import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
//import 'package:material_symbols_icons/symbols.dart';
import 'package:material_symbols_icons/get.dart';
part 'quick_action.freezed.dart';
part 'quick_action.g.dart';

// TODO: update to match actual response
@freezed
class QuickAction with _$QuickAction {
  const QuickAction._();
  const factory QuickAction(
          {@Default(true) bool isResolvable,
          @Json<PERSON>ey(name: 'action_url__c') String? actionUrl,
          @<PERSON><PERSON><PERSON>ey(name: 'action_name__c') String? actionName,
          @<PERSON><PERSON><PERSON><PERSON>(name: 'action_icon__c') String? actionIcon,
          @J<PERSON><PERSON>ey(name: 'action_description__c') String? actionDescription}) =
      _QuickAction;

  @override
  int get hashCode => actionUrl.hashCode ^ actionName.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuickAction &&
          other.actionUrl == actionUrl &&
          other.actionName == actionName;

  factory QuickAction.fromJson(Map<String, dynamic> json) =>
      _$QuickActionFromJson(json);

  IconData get iconData {
    // int iconCode = 0xf0555; // Default icon code: question mark
    // if (actionIcon != null) {
    //   try {
    //     // Remove '0x' prefix if present, then parse
    //     String hexString = actionIcon!.startsWith('0x')
    //         ? actionIcon!.substring(2)
    //         : actionIcon!;
    //     iconCode = int.parse(hexString, radix: 16);
    //   } catch (e) {
    //     if (kDebugMode) {
    //       print('Error parsing actionIcon: $e');
    //     }
    //     // Use default icon code if parsing fails
    //   }
    // }
    // return IconData(iconCode, fontFamily: 'MaterialIcons');
    if (actionIcon != null) {
      return SymbolsGet.get(
        actionIcon!,
        SymbolStyle.sharp,
      );
    }
    return SymbolsGet.get('question_mark', SymbolStyle.sharp);
  }
}

enum QuickActionsType {
  conversations('conversations'),
  chat('chat');

  final String value;
  const QuickActionsType(this.value);
}
