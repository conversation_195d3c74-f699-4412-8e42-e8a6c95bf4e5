// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quick_action.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

QuickAction _$QuickActionFromJson(Map<String, dynamic> json) {
  return _QuickAction.fromJson(json);
}

/// @nodoc
mixin _$QuickAction {
  bool get isResolvable => throw _privateConstructorUsedError;
  @JsonKey(name: 'action_url__c')
  String? get actionUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'action_name__c')
  String? get actionName => throw _privateConstructorUsedError;
  @JsonKey(name: 'action_icon__c')
  String? get actionIcon => throw _privateConstructorUsedError;
  @JsonKey(name: 'action_description__c')
  String? get actionDescription => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuickActionCopyWith<QuickAction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickActionCopyWith<$Res> {
  factory $QuickActionCopyWith(
          QuickAction value, $Res Function(QuickAction) then) =
      _$QuickActionCopyWithImpl<$Res, QuickAction>;
  @useResult
  $Res call(
      {bool isResolvable,
      @JsonKey(name: 'action_url__c') String? actionUrl,
      @JsonKey(name: 'action_name__c') String? actionName,
      @JsonKey(name: 'action_icon__c') String? actionIcon,
      @JsonKey(name: 'action_description__c') String? actionDescription});
}

/// @nodoc
class _$QuickActionCopyWithImpl<$Res, $Val extends QuickAction>
    implements $QuickActionCopyWith<$Res> {
  _$QuickActionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isResolvable = null,
    Object? actionUrl = freezed,
    Object? actionName = freezed,
    Object? actionIcon = freezed,
    Object? actionDescription = freezed,
  }) {
    return _then(_value.copyWith(
      isResolvable: null == isResolvable
          ? _value.isResolvable
          : isResolvable // ignore: cast_nullable_to_non_nullable
              as bool,
      actionUrl: freezed == actionUrl
          ? _value.actionUrl
          : actionUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      actionName: freezed == actionName
          ? _value.actionName
          : actionName // ignore: cast_nullable_to_non_nullable
              as String?,
      actionIcon: freezed == actionIcon
          ? _value.actionIcon
          : actionIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      actionDescription: freezed == actionDescription
          ? _value.actionDescription
          : actionDescription // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuickActionImplCopyWith<$Res>
    implements $QuickActionCopyWith<$Res> {
  factory _$$QuickActionImplCopyWith(
          _$QuickActionImpl value, $Res Function(_$QuickActionImpl) then) =
      __$$QuickActionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isResolvable,
      @JsonKey(name: 'action_url__c') String? actionUrl,
      @JsonKey(name: 'action_name__c') String? actionName,
      @JsonKey(name: 'action_icon__c') String? actionIcon,
      @JsonKey(name: 'action_description__c') String? actionDescription});
}

/// @nodoc
class __$$QuickActionImplCopyWithImpl<$Res>
    extends _$QuickActionCopyWithImpl<$Res, _$QuickActionImpl>
    implements _$$QuickActionImplCopyWith<$Res> {
  __$$QuickActionImplCopyWithImpl(
      _$QuickActionImpl _value, $Res Function(_$QuickActionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isResolvable = null,
    Object? actionUrl = freezed,
    Object? actionName = freezed,
    Object? actionIcon = freezed,
    Object? actionDescription = freezed,
  }) {
    return _then(_$QuickActionImpl(
      isResolvable: null == isResolvable
          ? _value.isResolvable
          : isResolvable // ignore: cast_nullable_to_non_nullable
              as bool,
      actionUrl: freezed == actionUrl
          ? _value.actionUrl
          : actionUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      actionName: freezed == actionName
          ? _value.actionName
          : actionName // ignore: cast_nullable_to_non_nullable
              as String?,
      actionIcon: freezed == actionIcon
          ? _value.actionIcon
          : actionIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      actionDescription: freezed == actionDescription
          ? _value.actionDescription
          : actionDescription // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuickActionImpl extends _QuickAction with DiagnosticableTreeMixin {
  const _$QuickActionImpl(
      {this.isResolvable = true,
      @JsonKey(name: 'action_url__c') this.actionUrl,
      @JsonKey(name: 'action_name__c') this.actionName,
      @JsonKey(name: 'action_icon__c') this.actionIcon,
      @JsonKey(name: 'action_description__c') this.actionDescription})
      : super._();

  factory _$QuickActionImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuickActionImplFromJson(json);

  @override
  @JsonKey()
  final bool isResolvable;
  @override
  @JsonKey(name: 'action_url__c')
  final String? actionUrl;
  @override
  @JsonKey(name: 'action_name__c')
  final String? actionName;
  @override
  @JsonKey(name: 'action_icon__c')
  final String? actionIcon;
  @override
  @JsonKey(name: 'action_description__c')
  final String? actionDescription;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'QuickAction(isResolvable: $isResolvable, actionUrl: $actionUrl, actionName: $actionName, actionIcon: $actionIcon, actionDescription: $actionDescription)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'QuickAction'))
      ..add(DiagnosticsProperty('isResolvable', isResolvable))
      ..add(DiagnosticsProperty('actionUrl', actionUrl))
      ..add(DiagnosticsProperty('actionName', actionName))
      ..add(DiagnosticsProperty('actionIcon', actionIcon))
      ..add(DiagnosticsProperty('actionDescription', actionDescription));
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuickActionImplCopyWith<_$QuickActionImpl> get copyWith =>
      __$$QuickActionImplCopyWithImpl<_$QuickActionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuickActionImplToJson(
      this,
    );
  }
}

abstract class _QuickAction extends QuickAction {
  const factory _QuickAction(
      {final bool isResolvable,
      @JsonKey(name: 'action_url__c') final String? actionUrl,
      @JsonKey(name: 'action_name__c') final String? actionName,
      @JsonKey(name: 'action_icon__c') final String? actionIcon,
      @JsonKey(name: 'action_description__c')
      final String? actionDescription}) = _$QuickActionImpl;
  const _QuickAction._() : super._();

  factory _QuickAction.fromJson(Map<String, dynamic> json) =
      _$QuickActionImpl.fromJson;

  @override
  bool get isResolvable;
  @override
  @JsonKey(name: 'action_url__c')
  String? get actionUrl;
  @override
  @JsonKey(name: 'action_name__c')
  String? get actionName;
  @override
  @JsonKey(name: 'action_icon__c')
  String? get actionIcon;
  @override
  @JsonKey(name: 'action_description__c')
  String? get actionDescription;
  @override
  @JsonKey(ignore: true)
  _$$QuickActionImplCopyWith<_$QuickActionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
