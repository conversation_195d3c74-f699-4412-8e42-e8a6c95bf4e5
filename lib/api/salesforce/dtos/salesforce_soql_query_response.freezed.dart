// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'salesforce_soql_query_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SalesforceSoqlQueryResponse _$SalesforceSoqlQueryResponseFromJson(
    Map<String, dynamic> json) {
  return _SalesforceSoqlQueryResponse.fromJson(json);
}

/// @nodoc
mixin _$SalesforceSoqlQueryResponse {
  bool get done => throw _privateConstructorUsedError;
  int get totalSize => throw _privateConstructorUsedError;
  String? get nextRecordsUrl => throw _privateConstructorUsedError;
  List<Map<String, dynamic>> get records => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SalesforceSoqlQueryResponseCopyWith<SalesforceSoqlQueryResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SalesforceSoqlQueryResponseCopyWith<$Res> {
  factory $SalesforceSoqlQueryResponseCopyWith(
          SalesforceSoqlQueryResponse value,
          $Res Function(SalesforceSoqlQueryResponse) then) =
      _$SalesforceSoqlQueryResponseCopyWithImpl<$Res,
          SalesforceSoqlQueryResponse>;
  @useResult
  $Res call(
      {bool done,
      int totalSize,
      String? nextRecordsUrl,
      List<Map<String, dynamic>> records});
}

/// @nodoc
class _$SalesforceSoqlQueryResponseCopyWithImpl<$Res,
        $Val extends SalesforceSoqlQueryResponse>
    implements $SalesforceSoqlQueryResponseCopyWith<$Res> {
  _$SalesforceSoqlQueryResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? done = null,
    Object? totalSize = null,
    Object? nextRecordsUrl = freezed,
    Object? records = null,
  }) {
    return _then(_value.copyWith(
      done: null == done
          ? _value.done
          : done // ignore: cast_nullable_to_non_nullable
              as bool,
      totalSize: null == totalSize
          ? _value.totalSize
          : totalSize // ignore: cast_nullable_to_non_nullable
              as int,
      nextRecordsUrl: freezed == nextRecordsUrl
          ? _value.nextRecordsUrl
          : nextRecordsUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      records: null == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SalesforceSoqlQueryResponseImplCopyWith<$Res>
    implements $SalesforceSoqlQueryResponseCopyWith<$Res> {
  factory _$$SalesforceSoqlQueryResponseImplCopyWith(
          _$SalesforceSoqlQueryResponseImpl value,
          $Res Function(_$SalesforceSoqlQueryResponseImpl) then) =
      __$$SalesforceSoqlQueryResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool done,
      int totalSize,
      String? nextRecordsUrl,
      List<Map<String, dynamic>> records});
}

/// @nodoc
class __$$SalesforceSoqlQueryResponseImplCopyWithImpl<$Res>
    extends _$SalesforceSoqlQueryResponseCopyWithImpl<$Res,
        _$SalesforceSoqlQueryResponseImpl>
    implements _$$SalesforceSoqlQueryResponseImplCopyWith<$Res> {
  __$$SalesforceSoqlQueryResponseImplCopyWithImpl(
      _$SalesforceSoqlQueryResponseImpl _value,
      $Res Function(_$SalesforceSoqlQueryResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? done = null,
    Object? totalSize = null,
    Object? nextRecordsUrl = freezed,
    Object? records = null,
  }) {
    return _then(_$SalesforceSoqlQueryResponseImpl(
      done: null == done
          ? _value.done
          : done // ignore: cast_nullable_to_non_nullable
              as bool,
      totalSize: null == totalSize
          ? _value.totalSize
          : totalSize // ignore: cast_nullable_to_non_nullable
              as int,
      nextRecordsUrl: freezed == nextRecordsUrl
          ? _value.nextRecordsUrl
          : nextRecordsUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      records: null == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SalesforceSoqlQueryResponseImpl
    implements _SalesforceSoqlQueryResponse {
  const _$SalesforceSoqlQueryResponseImpl(
      {required this.done,
      required this.totalSize,
      this.nextRecordsUrl,
      required final List<Map<String, dynamic>> records})
      : _records = records;

  factory _$SalesforceSoqlQueryResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$SalesforceSoqlQueryResponseImplFromJson(json);

  @override
  final bool done;
  @override
  final int totalSize;
  @override
  final String? nextRecordsUrl;
  final List<Map<String, dynamic>> _records;
  @override
  List<Map<String, dynamic>> get records {
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_records);
  }

  @override
  String toString() {
    return 'SalesforceSoqlQueryResponse(done: $done, totalSize: $totalSize, nextRecordsUrl: $nextRecordsUrl, records: $records)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SalesforceSoqlQueryResponseImpl &&
            (identical(other.done, done) || other.done == done) &&
            (identical(other.totalSize, totalSize) ||
                other.totalSize == totalSize) &&
            (identical(other.nextRecordsUrl, nextRecordsUrl) ||
                other.nextRecordsUrl == nextRecordsUrl) &&
            const DeepCollectionEquality().equals(other._records, _records));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, done, totalSize, nextRecordsUrl,
      const DeepCollectionEquality().hash(_records));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SalesforceSoqlQueryResponseImplCopyWith<_$SalesforceSoqlQueryResponseImpl>
      get copyWith => __$$SalesforceSoqlQueryResponseImplCopyWithImpl<
          _$SalesforceSoqlQueryResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SalesforceSoqlQueryResponseImplToJson(
      this,
    );
  }
}

abstract class _SalesforceSoqlQueryResponse
    implements SalesforceSoqlQueryResponse {
  const factory _SalesforceSoqlQueryResponse(
          {required final bool done,
          required final int totalSize,
          final String? nextRecordsUrl,
          required final List<Map<String, dynamic>> records}) =
      _$SalesforceSoqlQueryResponseImpl;

  factory _SalesforceSoqlQueryResponse.fromJson(Map<String, dynamic> json) =
      _$SalesforceSoqlQueryResponseImpl.fromJson;

  @override
  bool get done;
  @override
  int get totalSize;
  @override
  String? get nextRecordsUrl;
  @override
  List<Map<String, dynamic>> get records;
  @override
  @JsonKey(ignore: true)
  _$$SalesforceSoqlQueryResponseImplCopyWith<_$SalesforceSoqlQueryResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
