// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'oauth_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OAuthResponseImpl _$$OAuthResponseImplFromJson(Map<String, dynamic> json) =>
    _$OAuthResponseImpl(
      accessToken: json['access_token'] as String?,
      instanceUrl: json['instance_url'] as String?,
      refreshToken: json['refresh_token'] as String?,
      id: json['id'] as String?,
      issuedAt: json['issued_at'] as String?,
      signature: json['signature'] as String?,
      scope: json['scope'] as String?,
      tokenType: json['token_type'] as String?,
      orgId: json['orgId'] as String?,
      userId: json['userId'] as String?,
    );

Map<String, dynamic> _$$OAuthResponseImplToJson(_$OAuthResponseImpl instance) =>
    <String, dynamic>{
      'access_token': instance.accessToken,
      'instance_url': instance.instanceUrl,
      'refresh_token': instance.refreshToken,
      'id': instance.id,
      'issued_at': instance.issuedAt,
      'signature': instance.signature,
      'scope': instance.scope,
      'token_type': instance.tokenType,
      'orgId': instance.orgId,
      'userId': instance.userId,
    };
