import 'package:freezed_annotation/freezed_annotation.dart';

part 'messaging_end_user_response.freezed.dart';
part 'messaging_end_user_response.g.dart';

// TODO: update to match actual response
@freezed
class MessagingEndUserResponse with _$MessagingEndUserResponse {
  const factory MessagingEndUserResponse({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? city,
    String? state,
    String? zip,
    String? country,
    String? status,
    String? createdDate,
    String? lastModifiedDate,
  }) = _MessagingEndUserResponse;

  factory MessagingEndUserResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserResponseFromJson(json);
}