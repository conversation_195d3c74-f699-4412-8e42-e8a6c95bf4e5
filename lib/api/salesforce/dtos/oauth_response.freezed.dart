// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'oauth_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OAuthResponse _$OAuthResponseFromJson(Map<String, dynamic> json) {
  return _OAuthResponse.fromJson(json);
}

/// @nodoc
mixin _$OAuthResponse {
  @JsonKey(name: 'access_token')
  String? get accessToken => throw _privateConstructorUsedError;
  @JsonKey(name: 'instance_url')
  String? get instanceUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'refresh_token')
  String? get refreshToken => throw _privateConstructorUsedError;
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'issued_at')
  String? get issuedAt => throw _privateConstructorUsedError;
  String? get signature => throw _privateConstructorUsedError;
  String? get scope => throw _privateConstructorUsedError;
  @JsonKey(name: 'token_type')
  String? get tokenType => throw _privateConstructorUsedError;
  String? get orgId => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OAuthResponseCopyWith<OAuthResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OAuthResponseCopyWith<$Res> {
  factory $OAuthResponseCopyWith(
          OAuthResponse value, $Res Function(OAuthResponse) then) =
      _$OAuthResponseCopyWithImpl<$Res, OAuthResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'access_token') String? accessToken,
      @JsonKey(name: 'instance_url') String? instanceUrl,
      @JsonKey(name: 'refresh_token') String? refreshToken,
      String? id,
      @JsonKey(name: 'issued_at') String? issuedAt,
      String? signature,
      String? scope,
      @JsonKey(name: 'token_type') String? tokenType,
      String? orgId,
      String? userId});
}

/// @nodoc
class _$OAuthResponseCopyWithImpl<$Res, $Val extends OAuthResponse>
    implements $OAuthResponseCopyWith<$Res> {
  _$OAuthResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? instanceUrl = freezed,
    Object? refreshToken = freezed,
    Object? id = freezed,
    Object? issuedAt = freezed,
    Object? signature = freezed,
    Object? scope = freezed,
    Object? tokenType = freezed,
    Object? orgId = freezed,
    Object? userId = freezed,
  }) {
    return _then(_value.copyWith(
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _value.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      issuedAt: freezed == issuedAt
          ? _value.issuedAt
          : issuedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      signature: freezed == signature
          ? _value.signature
          : signature // ignore: cast_nullable_to_non_nullable
              as String?,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
      tokenType: freezed == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OAuthResponseImplCopyWith<$Res>
    implements $OAuthResponseCopyWith<$Res> {
  factory _$$OAuthResponseImplCopyWith(
          _$OAuthResponseImpl value, $Res Function(_$OAuthResponseImpl) then) =
      __$$OAuthResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'access_token') String? accessToken,
      @JsonKey(name: 'instance_url') String? instanceUrl,
      @JsonKey(name: 'refresh_token') String? refreshToken,
      String? id,
      @JsonKey(name: 'issued_at') String? issuedAt,
      String? signature,
      String? scope,
      @JsonKey(name: 'token_type') String? tokenType,
      String? orgId,
      String? userId});
}

/// @nodoc
class __$$OAuthResponseImplCopyWithImpl<$Res>
    extends _$OAuthResponseCopyWithImpl<$Res, _$OAuthResponseImpl>
    implements _$$OAuthResponseImplCopyWith<$Res> {
  __$$OAuthResponseImplCopyWithImpl(
      _$OAuthResponseImpl _value, $Res Function(_$OAuthResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? instanceUrl = freezed,
    Object? refreshToken = freezed,
    Object? id = freezed,
    Object? issuedAt = freezed,
    Object? signature = freezed,
    Object? scope = freezed,
    Object? tokenType = freezed,
    Object? orgId = freezed,
    Object? userId = freezed,
  }) {
    return _then(_$OAuthResponseImpl(
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      instanceUrl: freezed == instanceUrl
          ? _value.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      refreshToken: freezed == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      issuedAt: freezed == issuedAt
          ? _value.issuedAt
          : issuedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      signature: freezed == signature
          ? _value.signature
          : signature // ignore: cast_nullable_to_non_nullable
              as String?,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
      tokenType: freezed == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String?,
      orgId: freezed == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$OAuthResponseImpl implements _OAuthResponse {
  const _$OAuthResponseImpl(
      {@JsonKey(name: 'access_token') this.accessToken,
      @JsonKey(name: 'instance_url') this.instanceUrl,
      @JsonKey(name: 'refresh_token') this.refreshToken,
      this.id,
      @JsonKey(name: 'issued_at') this.issuedAt,
      this.signature,
      this.scope,
      @JsonKey(name: 'token_type') this.tokenType,
      this.orgId,
      this.userId});

  factory _$OAuthResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OAuthResponseImplFromJson(json);

  @override
  @JsonKey(name: 'access_token')
  final String? accessToken;
  @override
  @JsonKey(name: 'instance_url')
  final String? instanceUrl;
  @override
  @JsonKey(name: 'refresh_token')
  final String? refreshToken;
  @override
  final String? id;
  @override
  @JsonKey(name: 'issued_at')
  final String? issuedAt;
  @override
  final String? signature;
  @override
  final String? scope;
  @override
  @JsonKey(name: 'token_type')
  final String? tokenType;
  @override
  final String? orgId;
  @override
  final String? userId;

  @override
  String toString() {
    return 'OAuthResponse(accessToken: $accessToken, instanceUrl: $instanceUrl, refreshToken: $refreshToken, id: $id, issuedAt: $issuedAt, signature: $signature, scope: $scope, tokenType: $tokenType, orgId: $orgId, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OAuthResponseImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.issuedAt, issuedAt) ||
                other.issuedAt == issuedAt) &&
            (identical(other.signature, signature) ||
                other.signature == signature) &&
            (identical(other.scope, scope) || other.scope == scope) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, accessToken, instanceUrl,
      refreshToken, id, issuedAt, signature, scope, tokenType, orgId, userId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OAuthResponseImplCopyWith<_$OAuthResponseImpl> get copyWith =>
      __$$OAuthResponseImplCopyWithImpl<_$OAuthResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OAuthResponseImplToJson(
      this,
    );
  }
}

abstract class _OAuthResponse implements OAuthResponse {
  const factory _OAuthResponse(
      {@JsonKey(name: 'access_token') final String? accessToken,
      @JsonKey(name: 'instance_url') final String? instanceUrl,
      @JsonKey(name: 'refresh_token') final String? refreshToken,
      final String? id,
      @JsonKey(name: 'issued_at') final String? issuedAt,
      final String? signature,
      final String? scope,
      @JsonKey(name: 'token_type') final String? tokenType,
      final String? orgId,
      final String? userId}) = _$OAuthResponseImpl;

  factory _OAuthResponse.fromJson(Map<String, dynamic> json) =
      _$OAuthResponseImpl.fromJson;

  @override
  @JsonKey(name: 'access_token')
  String? get accessToken;
  @override
  @JsonKey(name: 'instance_url')
  String? get instanceUrl;
  @override
  @JsonKey(name: 'refresh_token')
  String? get refreshToken;
  @override
  String? get id;
  @override
  @JsonKey(name: 'issued_at')
  String? get issuedAt;
  @override
  String? get signature;
  @override
  String? get scope;
  @override
  @JsonKey(name: 'token_type')
  String? get tokenType;
  @override
  String? get orgId;
  @override
  String? get userId;
  @override
  @JsonKey(ignore: true)
  _$$OAuthResponseImplCopyWith<_$OAuthResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
