import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_records_response.freezed.dart';
part 'search_records_response.g.dart';

/* Example of returned object:
  {
  searchRecords: [
    {
          attributes: {type: Contact, url: /services/data/v60.0/sobjects/Contact/003Hn00002dA5CXIA0},
          Id: "003Hn00002dA5CXIA0",
          Name: "<PERSON>",
          Email: "<EMAIL>",
          MobilePhone: "(*************",
          PhotoUrl: "/services/images/photo/003Hn00002dA5CXIA0",
          Title: "Sales Engineer"
    },
    {
          attributes: {type: Contact, url: /services/data/v60.0/sobjects/Contact/003Hn00002dA5CgIAK},
          Id: "003Hn00002dA5CgIAK",
          Name: "<PERSON>",
          Email: "<EMAIL>",
          MobilePhone: "(*************",
          PhotoUrl: "/services/images/photo/003Hn00002dA5CgIAK",
          Title: "Director of Sales"
    },
    {
          attributes: {type: Contact, url: /services/data/v60.0/sobjects/Contact/003Hn00002dA5CfIAK},
          Id: "003Hn00002dA5CfIAK",
          Name: "Sarah <PERSON>",
          Email: "<EMAIL>",
          MobilePhone: null,
          PhotoUrl: "/services/images/photo/003Hn00002dA5CfIAK",
          Title: "Account Executive"
    },
    {
          attributes: {type: Contact, url: /services/data/v60.0/sobjects/Contact/003Hn00002dA5CeIAK},
          Id: "003Hn00002dA5CeIAK",
          Name: "Adam Mercer",
          Email: "<EMAIL>",
          MobilePhone: "(*************",
          PhotoUrl: "/services/images/photo/003Hn00002dA5CeIAK",
          Title: "Account Executive"
    }
  ]
}

or just an empoty array if no search results are found
{
    "searchRecords": []
}

*/

@freezed
class SearchRecordsResponse with _$SearchRecordsResponse {
  const factory SearchRecordsResponse(
      {required List<SearchRecord> searchRecords}) = _SearchRecordsResponse;

  factory SearchRecordsResponse.fromJson(Map<String, dynamic> json) =>
      _$SearchRecordsResponseFromJson(json);
}

@freezed
class SearchRecord with _$SearchRecord {
  const factory SearchRecord(
      {required Attributes attributes,
      @JsonKey(name: 'Name') String? name,
      @JsonKey(name: 'FirstName') String? firstName,
      @JsonKey(name: 'LastName') String? lastName,
      @JsonKey(name: 'PhotoUrl') String? photoUrl,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Email') String? email,
      @JsonKey(name: 'MobilePhone') String? mobilePhone,
      @JsonKey(name: 'Title') String? title}) = _SearchRecord;

  factory SearchRecord.fromJson(Map<String, dynamic> json) =>
      _$SearchRecordFromJson(json);
}

@freezed
class Attributes with _$Attributes {
  const factory Attributes({required String type, required String url}) =
      _Attributes;

  factory Attributes.fromJson(Map<String, dynamic> json) =>
      _$AttributesFromJson(json);
}
