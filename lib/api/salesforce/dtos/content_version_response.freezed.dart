// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'content_version_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ContentVersionResponse _$ContentVersionResponseFromJson(
    Map<String, dynamic> json) {
  return _ContentVersionResponse.fromJson(json);
}

/// @nodoc
mixin _$ContentVersionResponse {
  @JsonKey(name: 'Id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'ContentDocumentId')
  String? get contentDocumentId => throw _privateConstructorUsedError;
  @JsonKey(name: 'IsLatest')
  bool? get isLatest => throw _privateConstructorUsedError;
  @JsonKey(name: 'ContentBodyId')
  String? get contentBodyId => throw _privateConstructorUsedError;
  @JsonKey(name: 'VersionNumber')
  String? get versionNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'Title')
  String? get title => throw _privateConstructorUsedError;
  @JsonKey(name: 'SharingOption')
  String? get sharingOption => throw _privateConstructorUsedError;
  @JsonKey(name: 'SharingPrivacy')
  String? get sharingPrivacy => throw _privateConstructorUsedError;
  @JsonKey(name: 'PathOnClient')
  String? get pathOnClient => throw _privateConstructorUsedError;
  @JsonKey(name: 'RatingCount')
  int? get ratingCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'IsDeleted')
  bool? get isDeleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'ContentModifiedDate')
  String? get contentModifiedDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'ContentModifiedById')
  String? get contentModifiedById => throw _privateConstructorUsedError;
  @JsonKey(name: 'Language')
  String? get language => throw _privateConstructorUsedError;
  @JsonKey(name: 'PositiveRatingCount')
  int? get positiveRatingCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'NegativeRatingCount')
  int? get negativeRatingCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'OwnerId')
  String? get ownerId => throw _privateConstructorUsedError;
  @JsonKey(name: 'CreatedById')
  String? get createdById => throw _privateConstructorUsedError;
  @JsonKey(name: 'CreatedDate')
  String? get createdDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'LastModifiedById')
  String? get lastModifiedById => throw _privateConstructorUsedError;
  @JsonKey(name: 'LastModifiedDate')
  String? get lastModifiedDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'SystemModstamp')
  String? get systemModstamp => throw _privateConstructorUsedError;
  @JsonKey(name: 'FileType')
  String? get fileType => throw _privateConstructorUsedError;
  @JsonKey(name: 'PublishStatus')
  String? get publishStatus => throw _privateConstructorUsedError;
  @JsonKey(name: 'VersionData')
  String? get versionData => throw _privateConstructorUsedError;
  @JsonKey(name: 'ContentSize')
  int? get contentSize => throw _privateConstructorUsedError;
  @JsonKey(name: 'FileExtension')
  String? get fileExtension => throw _privateConstructorUsedError;
  @JsonKey(name: 'FirstPublishLocationId')
  String? get firstPublishLocationId => throw _privateConstructorUsedError;
  @JsonKey(name: 'Origin')
  String? get origin => throw _privateConstructorUsedError;
  @JsonKey(name: 'ContentLocation')
  String? get contentLocation => throw _privateConstructorUsedError;
  @JsonKey(name: 'VersionDataUrl')
  String? get versionDataUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContentVersionResponseCopyWith<ContentVersionResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentVersionResponseCopyWith<$Res> {
  factory $ContentVersionResponseCopyWith(ContentVersionResponse value,
          $Res Function(ContentVersionResponse) then) =
      _$ContentVersionResponseCopyWithImpl<$Res, ContentVersionResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'ContentDocumentId') String? contentDocumentId,
      @JsonKey(name: 'IsLatest') bool? isLatest,
      @JsonKey(name: 'ContentBodyId') String? contentBodyId,
      @JsonKey(name: 'VersionNumber') String? versionNumber,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'SharingOption') String? sharingOption,
      @JsonKey(name: 'SharingPrivacy') String? sharingPrivacy,
      @JsonKey(name: 'PathOnClient') String? pathOnClient,
      @JsonKey(name: 'RatingCount') int? ratingCount,
      @JsonKey(name: 'IsDeleted') bool? isDeleted,
      @JsonKey(name: 'ContentModifiedDate') String? contentModifiedDate,
      @JsonKey(name: 'ContentModifiedById') String? contentModifiedById,
      @JsonKey(name: 'Language') String? language,
      @JsonKey(name: 'PositiveRatingCount') int? positiveRatingCount,
      @JsonKey(name: 'NegativeRatingCount') int? negativeRatingCount,
      @JsonKey(name: 'OwnerId') String? ownerId,
      @JsonKey(name: 'CreatedById') String? createdById,
      @JsonKey(name: 'CreatedDate') String? createdDate,
      @JsonKey(name: 'LastModifiedById') String? lastModifiedById,
      @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
      @JsonKey(name: 'SystemModstamp') String? systemModstamp,
      @JsonKey(name: 'FileType') String? fileType,
      @JsonKey(name: 'PublishStatus') String? publishStatus,
      @JsonKey(name: 'VersionData') String? versionData,
      @JsonKey(name: 'ContentSize') int? contentSize,
      @JsonKey(name: 'FileExtension') String? fileExtension,
      @JsonKey(name: 'FirstPublishLocationId') String? firstPublishLocationId,
      @JsonKey(name: 'Origin') String? origin,
      @JsonKey(name: 'ContentLocation') String? contentLocation,
      @JsonKey(name: 'VersionDataUrl') String? versionDataUrl});
}

/// @nodoc
class _$ContentVersionResponseCopyWithImpl<$Res,
        $Val extends ContentVersionResponse>
    implements $ContentVersionResponseCopyWith<$Res> {
  _$ContentVersionResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? contentDocumentId = freezed,
    Object? isLatest = freezed,
    Object? contentBodyId = freezed,
    Object? versionNumber = freezed,
    Object? title = freezed,
    Object? sharingOption = freezed,
    Object? sharingPrivacy = freezed,
    Object? pathOnClient = freezed,
    Object? ratingCount = freezed,
    Object? isDeleted = freezed,
    Object? contentModifiedDate = freezed,
    Object? contentModifiedById = freezed,
    Object? language = freezed,
    Object? positiveRatingCount = freezed,
    Object? negativeRatingCount = freezed,
    Object? ownerId = freezed,
    Object? createdById = freezed,
    Object? createdDate = freezed,
    Object? lastModifiedById = freezed,
    Object? lastModifiedDate = freezed,
    Object? systemModstamp = freezed,
    Object? fileType = freezed,
    Object? publishStatus = freezed,
    Object? versionData = freezed,
    Object? contentSize = freezed,
    Object? fileExtension = freezed,
    Object? firstPublishLocationId = freezed,
    Object? origin = freezed,
    Object? contentLocation = freezed,
    Object? versionDataUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      contentDocumentId: freezed == contentDocumentId
          ? _value.contentDocumentId
          : contentDocumentId // ignore: cast_nullable_to_non_nullable
              as String?,
      isLatest: freezed == isLatest
          ? _value.isLatest
          : isLatest // ignore: cast_nullable_to_non_nullable
              as bool?,
      contentBodyId: freezed == contentBodyId
          ? _value.contentBodyId
          : contentBodyId // ignore: cast_nullable_to_non_nullable
              as String?,
      versionNumber: freezed == versionNumber
          ? _value.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      sharingOption: freezed == sharingOption
          ? _value.sharingOption
          : sharingOption // ignore: cast_nullable_to_non_nullable
              as String?,
      sharingPrivacy: freezed == sharingPrivacy
          ? _value.sharingPrivacy
          : sharingPrivacy // ignore: cast_nullable_to_non_nullable
              as String?,
      pathOnClient: freezed == pathOnClient
          ? _value.pathOnClient
          : pathOnClient // ignore: cast_nullable_to_non_nullable
              as String?,
      ratingCount: freezed == ratingCount
          ? _value.ratingCount
          : ratingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isDeleted: freezed == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      contentModifiedDate: freezed == contentModifiedDate
          ? _value.contentModifiedDate
          : contentModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contentModifiedById: freezed == contentModifiedById
          ? _value.contentModifiedById
          : contentModifiedById // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      positiveRatingCount: freezed == positiveRatingCount
          ? _value.positiveRatingCount
          : positiveRatingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      negativeRatingCount: freezed == negativeRatingCount
          ? _value.negativeRatingCount
          : negativeRatingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdById: freezed == createdById
          ? _value.createdById
          : createdById // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedById: freezed == lastModifiedById
          ? _value.lastModifiedById
          : lastModifiedById // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _value.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      systemModstamp: freezed == systemModstamp
          ? _value.systemModstamp
          : systemModstamp // ignore: cast_nullable_to_non_nullable
              as String?,
      fileType: freezed == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String?,
      publishStatus: freezed == publishStatus
          ? _value.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      versionData: freezed == versionData
          ? _value.versionData
          : versionData // ignore: cast_nullable_to_non_nullable
              as String?,
      contentSize: freezed == contentSize
          ? _value.contentSize
          : contentSize // ignore: cast_nullable_to_non_nullable
              as int?,
      fileExtension: freezed == fileExtension
          ? _value.fileExtension
          : fileExtension // ignore: cast_nullable_to_non_nullable
              as String?,
      firstPublishLocationId: freezed == firstPublishLocationId
          ? _value.firstPublishLocationId
          : firstPublishLocationId // ignore: cast_nullable_to_non_nullable
              as String?,
      origin: freezed == origin
          ? _value.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as String?,
      contentLocation: freezed == contentLocation
          ? _value.contentLocation
          : contentLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      versionDataUrl: freezed == versionDataUrl
          ? _value.versionDataUrl
          : versionDataUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContentVersionResponseImplCopyWith<$Res>
    implements $ContentVersionResponseCopyWith<$Res> {
  factory _$$ContentVersionResponseImplCopyWith(
          _$ContentVersionResponseImpl value,
          $Res Function(_$ContentVersionResponseImpl) then) =
      __$$ContentVersionResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'ContentDocumentId') String? contentDocumentId,
      @JsonKey(name: 'IsLatest') bool? isLatest,
      @JsonKey(name: 'ContentBodyId') String? contentBodyId,
      @JsonKey(name: 'VersionNumber') String? versionNumber,
      @JsonKey(name: 'Title') String? title,
      @JsonKey(name: 'SharingOption') String? sharingOption,
      @JsonKey(name: 'SharingPrivacy') String? sharingPrivacy,
      @JsonKey(name: 'PathOnClient') String? pathOnClient,
      @JsonKey(name: 'RatingCount') int? ratingCount,
      @JsonKey(name: 'IsDeleted') bool? isDeleted,
      @JsonKey(name: 'ContentModifiedDate') String? contentModifiedDate,
      @JsonKey(name: 'ContentModifiedById') String? contentModifiedById,
      @JsonKey(name: 'Language') String? language,
      @JsonKey(name: 'PositiveRatingCount') int? positiveRatingCount,
      @JsonKey(name: 'NegativeRatingCount') int? negativeRatingCount,
      @JsonKey(name: 'OwnerId') String? ownerId,
      @JsonKey(name: 'CreatedById') String? createdById,
      @JsonKey(name: 'CreatedDate') String? createdDate,
      @JsonKey(name: 'LastModifiedById') String? lastModifiedById,
      @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
      @JsonKey(name: 'SystemModstamp') String? systemModstamp,
      @JsonKey(name: 'FileType') String? fileType,
      @JsonKey(name: 'PublishStatus') String? publishStatus,
      @JsonKey(name: 'VersionData') String? versionData,
      @JsonKey(name: 'ContentSize') int? contentSize,
      @JsonKey(name: 'FileExtension') String? fileExtension,
      @JsonKey(name: 'FirstPublishLocationId') String? firstPublishLocationId,
      @JsonKey(name: 'Origin') String? origin,
      @JsonKey(name: 'ContentLocation') String? contentLocation,
      @JsonKey(name: 'VersionDataUrl') String? versionDataUrl});
}

/// @nodoc
class __$$ContentVersionResponseImplCopyWithImpl<$Res>
    extends _$ContentVersionResponseCopyWithImpl<$Res,
        _$ContentVersionResponseImpl>
    implements _$$ContentVersionResponseImplCopyWith<$Res> {
  __$$ContentVersionResponseImplCopyWithImpl(
      _$ContentVersionResponseImpl _value,
      $Res Function(_$ContentVersionResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? contentDocumentId = freezed,
    Object? isLatest = freezed,
    Object? contentBodyId = freezed,
    Object? versionNumber = freezed,
    Object? title = freezed,
    Object? sharingOption = freezed,
    Object? sharingPrivacy = freezed,
    Object? pathOnClient = freezed,
    Object? ratingCount = freezed,
    Object? isDeleted = freezed,
    Object? contentModifiedDate = freezed,
    Object? contentModifiedById = freezed,
    Object? language = freezed,
    Object? positiveRatingCount = freezed,
    Object? negativeRatingCount = freezed,
    Object? ownerId = freezed,
    Object? createdById = freezed,
    Object? createdDate = freezed,
    Object? lastModifiedById = freezed,
    Object? lastModifiedDate = freezed,
    Object? systemModstamp = freezed,
    Object? fileType = freezed,
    Object? publishStatus = freezed,
    Object? versionData = freezed,
    Object? contentSize = freezed,
    Object? fileExtension = freezed,
    Object? firstPublishLocationId = freezed,
    Object? origin = freezed,
    Object? contentLocation = freezed,
    Object? versionDataUrl = freezed,
  }) {
    return _then(_$ContentVersionResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      contentDocumentId: freezed == contentDocumentId
          ? _value.contentDocumentId
          : contentDocumentId // ignore: cast_nullable_to_non_nullable
              as String?,
      isLatest: freezed == isLatest
          ? _value.isLatest
          : isLatest // ignore: cast_nullable_to_non_nullable
              as bool?,
      contentBodyId: freezed == contentBodyId
          ? _value.contentBodyId
          : contentBodyId // ignore: cast_nullable_to_non_nullable
              as String?,
      versionNumber: freezed == versionNumber
          ? _value.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      sharingOption: freezed == sharingOption
          ? _value.sharingOption
          : sharingOption // ignore: cast_nullable_to_non_nullable
              as String?,
      sharingPrivacy: freezed == sharingPrivacy
          ? _value.sharingPrivacy
          : sharingPrivacy // ignore: cast_nullable_to_non_nullable
              as String?,
      pathOnClient: freezed == pathOnClient
          ? _value.pathOnClient
          : pathOnClient // ignore: cast_nullable_to_non_nullable
              as String?,
      ratingCount: freezed == ratingCount
          ? _value.ratingCount
          : ratingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isDeleted: freezed == isDeleted
          ? _value.isDeleted
          : isDeleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      contentModifiedDate: freezed == contentModifiedDate
          ? _value.contentModifiedDate
          : contentModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      contentModifiedById: freezed == contentModifiedById
          ? _value.contentModifiedById
          : contentModifiedById // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      positiveRatingCount: freezed == positiveRatingCount
          ? _value.positiveRatingCount
          : positiveRatingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      negativeRatingCount: freezed == negativeRatingCount
          ? _value.negativeRatingCount
          : negativeRatingCount // ignore: cast_nullable_to_non_nullable
              as int?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      createdById: freezed == createdById
          ? _value.createdById
          : createdById // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedById: freezed == lastModifiedById
          ? _value.lastModifiedById
          : lastModifiedById // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _value.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      systemModstamp: freezed == systemModstamp
          ? _value.systemModstamp
          : systemModstamp // ignore: cast_nullable_to_non_nullable
              as String?,
      fileType: freezed == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String?,
      publishStatus: freezed == publishStatus
          ? _value.publishStatus
          : publishStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      versionData: freezed == versionData
          ? _value.versionData
          : versionData // ignore: cast_nullable_to_non_nullable
              as String?,
      contentSize: freezed == contentSize
          ? _value.contentSize
          : contentSize // ignore: cast_nullable_to_non_nullable
              as int?,
      fileExtension: freezed == fileExtension
          ? _value.fileExtension
          : fileExtension // ignore: cast_nullable_to_non_nullable
              as String?,
      firstPublishLocationId: freezed == firstPublishLocationId
          ? _value.firstPublishLocationId
          : firstPublishLocationId // ignore: cast_nullable_to_non_nullable
              as String?,
      origin: freezed == origin
          ? _value.origin
          : origin // ignore: cast_nullable_to_non_nullable
              as String?,
      contentLocation: freezed == contentLocation
          ? _value.contentLocation
          : contentLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      versionDataUrl: freezed == versionDataUrl
          ? _value.versionDataUrl
          : versionDataUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentVersionResponseImpl implements _ContentVersionResponse {
  const _$ContentVersionResponseImpl(
      {@JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'ContentDocumentId') this.contentDocumentId,
      @JsonKey(name: 'IsLatest') this.isLatest,
      @JsonKey(name: 'ContentBodyId') this.contentBodyId,
      @JsonKey(name: 'VersionNumber') this.versionNumber,
      @JsonKey(name: 'Title') this.title,
      @JsonKey(name: 'SharingOption') this.sharingOption,
      @JsonKey(name: 'SharingPrivacy') this.sharingPrivacy,
      @JsonKey(name: 'PathOnClient') this.pathOnClient,
      @JsonKey(name: 'RatingCount') this.ratingCount,
      @JsonKey(name: 'IsDeleted') this.isDeleted,
      @JsonKey(name: 'ContentModifiedDate') this.contentModifiedDate,
      @JsonKey(name: 'ContentModifiedById') this.contentModifiedById,
      @JsonKey(name: 'Language') this.language,
      @JsonKey(name: 'PositiveRatingCount') this.positiveRatingCount,
      @JsonKey(name: 'NegativeRatingCount') this.negativeRatingCount,
      @JsonKey(name: 'OwnerId') this.ownerId,
      @JsonKey(name: 'CreatedById') this.createdById,
      @JsonKey(name: 'CreatedDate') this.createdDate,
      @JsonKey(name: 'LastModifiedById') this.lastModifiedById,
      @JsonKey(name: 'LastModifiedDate') this.lastModifiedDate,
      @JsonKey(name: 'SystemModstamp') this.systemModstamp,
      @JsonKey(name: 'FileType') this.fileType,
      @JsonKey(name: 'PublishStatus') this.publishStatus,
      @JsonKey(name: 'VersionData') this.versionData,
      @JsonKey(name: 'ContentSize') this.contentSize,
      @JsonKey(name: 'FileExtension') this.fileExtension,
      @JsonKey(name: 'FirstPublishLocationId') this.firstPublishLocationId,
      @JsonKey(name: 'Origin') this.origin,
      @JsonKey(name: 'ContentLocation') this.contentLocation,
      @JsonKey(name: 'VersionDataUrl') this.versionDataUrl});

  factory _$ContentVersionResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentVersionResponseImplFromJson(json);

  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'ContentDocumentId')
  final String? contentDocumentId;
  @override
  @JsonKey(name: 'IsLatest')
  final bool? isLatest;
  @override
  @JsonKey(name: 'ContentBodyId')
  final String? contentBodyId;
  @override
  @JsonKey(name: 'VersionNumber')
  final String? versionNumber;
  @override
  @JsonKey(name: 'Title')
  final String? title;
  @override
  @JsonKey(name: 'SharingOption')
  final String? sharingOption;
  @override
  @JsonKey(name: 'SharingPrivacy')
  final String? sharingPrivacy;
  @override
  @JsonKey(name: 'PathOnClient')
  final String? pathOnClient;
  @override
  @JsonKey(name: 'RatingCount')
  final int? ratingCount;
  @override
  @JsonKey(name: 'IsDeleted')
  final bool? isDeleted;
  @override
  @JsonKey(name: 'ContentModifiedDate')
  final String? contentModifiedDate;
  @override
  @JsonKey(name: 'ContentModifiedById')
  final String? contentModifiedById;
  @override
  @JsonKey(name: 'Language')
  final String? language;
  @override
  @JsonKey(name: 'PositiveRatingCount')
  final int? positiveRatingCount;
  @override
  @JsonKey(name: 'NegativeRatingCount')
  final int? negativeRatingCount;
  @override
  @JsonKey(name: 'OwnerId')
  final String? ownerId;
  @override
  @JsonKey(name: 'CreatedById')
  final String? createdById;
  @override
  @JsonKey(name: 'CreatedDate')
  final String? createdDate;
  @override
  @JsonKey(name: 'LastModifiedById')
  final String? lastModifiedById;
  @override
  @JsonKey(name: 'LastModifiedDate')
  final String? lastModifiedDate;
  @override
  @JsonKey(name: 'SystemModstamp')
  final String? systemModstamp;
  @override
  @JsonKey(name: 'FileType')
  final String? fileType;
  @override
  @JsonKey(name: 'PublishStatus')
  final String? publishStatus;
  @override
  @JsonKey(name: 'VersionData')
  final String? versionData;
  @override
  @JsonKey(name: 'ContentSize')
  final int? contentSize;
  @override
  @JsonKey(name: 'FileExtension')
  final String? fileExtension;
  @override
  @JsonKey(name: 'FirstPublishLocationId')
  final String? firstPublishLocationId;
  @override
  @JsonKey(name: 'Origin')
  final String? origin;
  @override
  @JsonKey(name: 'ContentLocation')
  final String? contentLocation;
  @override
  @JsonKey(name: 'VersionDataUrl')
  final String? versionDataUrl;

  @override
  String toString() {
    return 'ContentVersionResponse(id: $id, contentDocumentId: $contentDocumentId, isLatest: $isLatest, contentBodyId: $contentBodyId, versionNumber: $versionNumber, title: $title, sharingOption: $sharingOption, sharingPrivacy: $sharingPrivacy, pathOnClient: $pathOnClient, ratingCount: $ratingCount, isDeleted: $isDeleted, contentModifiedDate: $contentModifiedDate, contentModifiedById: $contentModifiedById, language: $language, positiveRatingCount: $positiveRatingCount, negativeRatingCount: $negativeRatingCount, ownerId: $ownerId, createdById: $createdById, createdDate: $createdDate, lastModifiedById: $lastModifiedById, lastModifiedDate: $lastModifiedDate, systemModstamp: $systemModstamp, fileType: $fileType, publishStatus: $publishStatus, versionData: $versionData, contentSize: $contentSize, fileExtension: $fileExtension, firstPublishLocationId: $firstPublishLocationId, origin: $origin, contentLocation: $contentLocation, versionDataUrl: $versionDataUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentVersionResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contentDocumentId, contentDocumentId) ||
                other.contentDocumentId == contentDocumentId) &&
            (identical(other.isLatest, isLatest) ||
                other.isLatest == isLatest) &&
            (identical(other.contentBodyId, contentBodyId) ||
                other.contentBodyId == contentBodyId) &&
            (identical(other.versionNumber, versionNumber) ||
                other.versionNumber == versionNumber) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.sharingOption, sharingOption) ||
                other.sharingOption == sharingOption) &&
            (identical(other.sharingPrivacy, sharingPrivacy) ||
                other.sharingPrivacy == sharingPrivacy) &&
            (identical(other.pathOnClient, pathOnClient) ||
                other.pathOnClient == pathOnClient) &&
            (identical(other.ratingCount, ratingCount) ||
                other.ratingCount == ratingCount) &&
            (identical(other.isDeleted, isDeleted) ||
                other.isDeleted == isDeleted) &&
            (identical(other.contentModifiedDate, contentModifiedDate) ||
                other.contentModifiedDate == contentModifiedDate) &&
            (identical(other.contentModifiedById, contentModifiedById) ||
                other.contentModifiedById == contentModifiedById) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.positiveRatingCount, positiveRatingCount) ||
                other.positiveRatingCount == positiveRatingCount) &&
            (identical(other.negativeRatingCount, negativeRatingCount) ||
                other.negativeRatingCount == negativeRatingCount) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.createdById, createdById) ||
                other.createdById == createdById) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate) &&
            (identical(other.lastModifiedById, lastModifiedById) ||
                other.lastModifiedById == lastModifiedById) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.systemModstamp, systemModstamp) ||
                other.systemModstamp == systemModstamp) &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.publishStatus, publishStatus) ||
                other.publishStatus == publishStatus) &&
            (identical(other.versionData, versionData) ||
                other.versionData == versionData) &&
            (identical(other.contentSize, contentSize) ||
                other.contentSize == contentSize) &&
            (identical(other.fileExtension, fileExtension) ||
                other.fileExtension == fileExtension) &&
            (identical(other.firstPublishLocationId, firstPublishLocationId) ||
                other.firstPublishLocationId == firstPublishLocationId) &&
            (identical(other.origin, origin) || other.origin == origin) &&
            (identical(other.contentLocation, contentLocation) ||
                other.contentLocation == contentLocation) &&
            (identical(other.versionDataUrl, versionDataUrl) ||
                other.versionDataUrl == versionDataUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        contentDocumentId,
        isLatest,
        contentBodyId,
        versionNumber,
        title,
        sharingOption,
        sharingPrivacy,
        pathOnClient,
        ratingCount,
        isDeleted,
        contentModifiedDate,
        contentModifiedById,
        language,
        positiveRatingCount,
        negativeRatingCount,
        ownerId,
        createdById,
        createdDate,
        lastModifiedById,
        lastModifiedDate,
        systemModstamp,
        fileType,
        publishStatus,
        versionData,
        contentSize,
        fileExtension,
        firstPublishLocationId,
        origin,
        contentLocation,
        versionDataUrl
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentVersionResponseImplCopyWith<_$ContentVersionResponseImpl>
      get copyWith => __$$ContentVersionResponseImplCopyWithImpl<
          _$ContentVersionResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentVersionResponseImplToJson(
      this,
    );
  }
}

abstract class _ContentVersionResponse implements ContentVersionResponse {
  const factory _ContentVersionResponse(
      {@JsonKey(name: 'Id') final String? id,
      @JsonKey(name: 'ContentDocumentId') final String? contentDocumentId,
      @JsonKey(name: 'IsLatest') final bool? isLatest,
      @JsonKey(name: 'ContentBodyId') final String? contentBodyId,
      @JsonKey(name: 'VersionNumber') final String? versionNumber,
      @JsonKey(name: 'Title') final String? title,
      @JsonKey(name: 'SharingOption') final String? sharingOption,
      @JsonKey(name: 'SharingPrivacy') final String? sharingPrivacy,
      @JsonKey(name: 'PathOnClient') final String? pathOnClient,
      @JsonKey(name: 'RatingCount') final int? ratingCount,
      @JsonKey(name: 'IsDeleted') final bool? isDeleted,
      @JsonKey(name: 'ContentModifiedDate') final String? contentModifiedDate,
      @JsonKey(name: 'ContentModifiedById') final String? contentModifiedById,
      @JsonKey(name: 'Language') final String? language,
      @JsonKey(name: 'PositiveRatingCount') final int? positiveRatingCount,
      @JsonKey(name: 'NegativeRatingCount') final int? negativeRatingCount,
      @JsonKey(name: 'OwnerId') final String? ownerId,
      @JsonKey(name: 'CreatedById') final String? createdById,
      @JsonKey(name: 'CreatedDate') final String? createdDate,
      @JsonKey(name: 'LastModifiedById') final String? lastModifiedById,
      @JsonKey(name: 'LastModifiedDate') final String? lastModifiedDate,
      @JsonKey(name: 'SystemModstamp') final String? systemModstamp,
      @JsonKey(name: 'FileType') final String? fileType,
      @JsonKey(name: 'PublishStatus') final String? publishStatus,
      @JsonKey(name: 'VersionData') final String? versionData,
      @JsonKey(name: 'ContentSize') final int? contentSize,
      @JsonKey(name: 'FileExtension') final String? fileExtension,
      @JsonKey(name: 'FirstPublishLocationId')
      final String? firstPublishLocationId,
      @JsonKey(name: 'Origin') final String? origin,
      @JsonKey(name: 'ContentLocation') final String? contentLocation,
      @JsonKey(name: 'VersionDataUrl')
      final String? versionDataUrl}) = _$ContentVersionResponseImpl;

  factory _ContentVersionResponse.fromJson(Map<String, dynamic> json) =
      _$ContentVersionResponseImpl.fromJson;

  @override
  @JsonKey(name: 'Id')
  String? get id;
  @override
  @JsonKey(name: 'ContentDocumentId')
  String? get contentDocumentId;
  @override
  @JsonKey(name: 'IsLatest')
  bool? get isLatest;
  @override
  @JsonKey(name: 'ContentBodyId')
  String? get contentBodyId;
  @override
  @JsonKey(name: 'VersionNumber')
  String? get versionNumber;
  @override
  @JsonKey(name: 'Title')
  String? get title;
  @override
  @JsonKey(name: 'SharingOption')
  String? get sharingOption;
  @override
  @JsonKey(name: 'SharingPrivacy')
  String? get sharingPrivacy;
  @override
  @JsonKey(name: 'PathOnClient')
  String? get pathOnClient;
  @override
  @JsonKey(name: 'RatingCount')
  int? get ratingCount;
  @override
  @JsonKey(name: 'IsDeleted')
  bool? get isDeleted;
  @override
  @JsonKey(name: 'ContentModifiedDate')
  String? get contentModifiedDate;
  @override
  @JsonKey(name: 'ContentModifiedById')
  String? get contentModifiedById;
  @override
  @JsonKey(name: 'Language')
  String? get language;
  @override
  @JsonKey(name: 'PositiveRatingCount')
  int? get positiveRatingCount;
  @override
  @JsonKey(name: 'NegativeRatingCount')
  int? get negativeRatingCount;
  @override
  @JsonKey(name: 'OwnerId')
  String? get ownerId;
  @override
  @JsonKey(name: 'CreatedById')
  String? get createdById;
  @override
  @JsonKey(name: 'CreatedDate')
  String? get createdDate;
  @override
  @JsonKey(name: 'LastModifiedById')
  String? get lastModifiedById;
  @override
  @JsonKey(name: 'LastModifiedDate')
  String? get lastModifiedDate;
  @override
  @JsonKey(name: 'SystemModstamp')
  String? get systemModstamp;
  @override
  @JsonKey(name: 'FileType')
  String? get fileType;
  @override
  @JsonKey(name: 'PublishStatus')
  String? get publishStatus;
  @override
  @JsonKey(name: 'VersionData')
  String? get versionData;
  @override
  @JsonKey(name: 'ContentSize')
  int? get contentSize;
  @override
  @JsonKey(name: 'FileExtension')
  String? get fileExtension;
  @override
  @JsonKey(name: 'FirstPublishLocationId')
  String? get firstPublishLocationId;
  @override
  @JsonKey(name: 'Origin')
  String? get origin;
  @override
  @JsonKey(name: 'ContentLocation')
  String? get contentLocation;
  @override
  @JsonKey(name: 'VersionDataUrl')
  String? get versionDataUrl;
  @override
  @JsonKey(ignore: true)
  _$$ContentVersionResponseImplCopyWith<_$ContentVersionResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
