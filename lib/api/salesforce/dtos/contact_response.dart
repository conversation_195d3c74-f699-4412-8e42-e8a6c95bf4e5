import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/salesforce/dtos/search_records_response.dart';

part 'contact_response.freezed.dart';
part 'contact_response.g.dart';

// TODO: update to match actual response
@freezed
class ContactResponse with _$ContactResponse {
  const factory ContactResponse({
      Attributes? attributes,
    @Json<PERSON>ey(name: 'Id') String? id,
    @<PERSON>son<PERSON><PERSON>(name: 'AccountId') String? accountId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'IndividualId') String? individualId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'PhotoUrl') String? photoUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'OwnerId') String? ownerId,
    @J<PERSON><PERSON><PERSON>(name: 'Department') String? department,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Title') String? title,
    @Json<PERSON>ey(name: 'Email') String? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'Phone') String? phone,
    @<PERSON>son<PERSON>ey(name: 'MobilePhone') String? mobilePhone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'LastName') String? lastName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'FirstName') String? firstName,
    @JsonKey(name: 'Name') String? name
  }) = _ContactResponse;

  factory ContactResponse.fromJson(Map<String, dynamic> json) =>
      _$ContactResponseFromJson(json);
}