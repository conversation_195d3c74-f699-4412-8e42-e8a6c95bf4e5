import 'package:freezed_annotation/freezed_annotation.dart';

part 'rs_l_consumer_response.freezed.dart';
part 'rs_l_consumer_response.g.dart';

// TODO: update to match actual response
@freezed
class RSLConsumerResponse with _$RSLConsumerResponse {
  const factory RSLConsumerResponse({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? city,
    String? state,
    String? zip,
    String? country,
    String? status,
    String? createdDate,
    String? lastModifiedDate,
  }) = _RSLConsumerResponse;

  factory RSLConsumerResponse.fromJson(Map<String, dynamic> json) =>
      _$RSLConsumerResponseFromJson(json);
}