import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/api/salesforce/salesforce_interceptor.dart';
import 'package:x1440/models/app_error_model.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_event.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';

import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/utils/json_utils.dart';

const authEndpoint = '/shim-service/auth/salesforce/obtain-token';
const sfLogoutEndpoint = '/services/oauth2/revoke';
const closeWorkEndpoint = '/shim-service/presence/actions/close-work';
const getUnacknowledgedNotificationsEndpoint =
    '/shim-service/notifications';

const authorizationTokenKey = 'accessToken';
const sessionTokenKey = 'sessionToken';
const sessionTokenHeaderKey = 'X-1440-Session-Token';
RegExp startSessionRegExp =
    RegExp(r'^/shim-service/organizations/([^/]+)/sessions$');
RegExp endSessionRegExp =
    RegExp(r'^/shim-service/organizations/([^/]+)/sessions/([^/]+)$');
const accessTokenExpired = 'AccessTokenExpired';
const notAuthorized = 'NotAuthorized';
const sessionGone = 'SessionGone';
const sessionsPath = '/sessions/';
const orgIdNotProvisioned = 'OrgIdNotProvisioned';

final isLoggingOutAllowList = [
  /// for closing standard sessions before logging out
  RegExp(closeWorkEndpoint),
  RegExp(sfLogoutEndpoint),
  RegExp(getUnacknowledgedNotificationsEndpoint),
  endSessionRegExp
];

class AuthInterceptor extends Interceptor {
  final LocalStorageRepository _localStorageRepository;
  final RemoteLogger _logger;

  AuthInterceptor(this._localStorageRepository, this._logger);

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final authUseCase = GetIt.instance<AuthUseCase>();
    if (authUseCase.isLoggingOut) {
      bool isAllowed = isLoggingOutAllowList.any((regExp) {
        return regExp.hasMatch(options.path);
      });
      if (!isAllowed) {
        _logger.info(
            'authInterceptor - rejecting ${options.path} - due to: Logging out');
        try {
          throw const AppError(
            message: 'Logging out',
          );
        } catch(e, stacktrace) {
          handler.reject(DioException(
            requestOptions: options,
            error: e,
            stackTrace: stacktrace,
          ));
        }
        return;
      }
    }
    if (options.path.startsWith(sfEndpoint)) {
      super.onRequest(options, handler);
      return;
    }
    final credentials = await _localStorageRepository.getCredentials();

    if (credentials.authorizationToken != null) {
      options.headers["Authorization"] = await _generateAuthHeader();
    }

    // Inject the 1440 session token if it exists
    final String? sessionToken = credentials.sessionToken;
    if (sessionToken != null) {
      options.headers[sessionTokenHeaderKey] = sessionToken;
    }

    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    if (response.requestOptions.path.startsWith(sfEndpoint)) {
      super.onResponse(response, handler);
      return;
    }
    if (response.data is Map) {
      if (response.requestOptions.path == authEndpoint &&
          (response.data as Map).containsKey(authorizationTokenKey)) {
        final authorizationToken = response.data[authorizationTokenKey];

        final credentials = await _localStorageRepository.getCredentials();
        await _localStorageRepository.setCredentials(credentials.copyWith(
          authorizationToken: authorizationToken,
        ));

        await Utils.saveDataToSecureStorage(
            'shim_access_token', authorizationToken);
      } else {
        if ((response.data as Map).containsKey(sessionTokenKey)) {
          bool isMatch =
              startSessionRegExp.hasMatch(response.requestOptions.path);
          String? sessionToken = response.data[sessionTokenKey];
          if (isMatch && sessionToken != null) {
            final credentials = await _localStorageRepository.getCredentials();
            await _localStorageRepository.setCredentials(credentials.copyWith(
              sessionToken: sessionToken,
            ));
            await Utils.saveDataToSecureStorage('session_token', sessionToken);
          }
        }
      }
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response != null &&
        err.response!.requestOptions.path.startsWith(sfEndpoint)) {
      super.onError(err, handler);
      return;
    }
    final apiError = (err.response?.data is ResponseBody)
        ? ApiError.fromResponseBody(err.response!.data)
        : ApiError.fromJson(safeJsonDecode(err.response?.data));

    if (err.response?.data is Map<String, dynamic> &&
        err.response?.statusCode == 401) {
      final authUseCase = GetIt.instance<AuthUseCase>();
      if (apiError.code == accessTokenExpired) {
        if (err.requestOptions.path == '/shim-service/notifications' ||
            err.requestOptions.path ==
                '/shim-service/auth/salesforce/obtain-token') {
          _logger.info(
              'AuthInterceptor - onError - 401 - accessTokenExpired - notifications');
          GetIt.I<AuthBloc>().add(LogoutEvent());
          return;
        }

        if (!authUseCase.isLoggingOut) {
          _logger.info(
              'AuthInterceptor - refreshToken: authUseCase.loginShimWithSalesForce');
          final loginSuccess = await authUseCase.loginShimWithSalesForce();

          if (!loginSuccess) {
            _logger.info(
                'AuthInterceptor - onError - 401 - loginShimWithSalesForce - failed');
            return;
          }
          // Update the authorization header with the new token
          err.requestOptions.headers["Authorization"] =
              await _generateAuthHeader();
          // Repeat the request with the updated header
          Dio dio = GetIt.instance<Dio>();
          return handler.resolve(await dio.fetch(err.requestOptions));
        } else if (endSessionRegExp.hasMatch(err.requestOptions.path)) {
          _logger.info(
              'AuthInterceptor - onError - 401 - accessTokenExpired - endSession - refresh access token');
          final loginSuccess = await authUseCase.loginShimWithSalesForce();
          if (loginSuccess) {
            final credentials = await _localStorageRepository.getCredentials();
            err.requestOptions.headers[sessionTokenHeaderKey] =
                credentials.sessionToken;
            Dio dio = GetIt.instance<Dio>();
            return handler.resolve(await dio.fetch(err.requestOptions));
          }
        }
      }
      _logger.info(
          'AuthInterceptor - onError - 401 - ${apiError.code} - failed to handle in interceptor');
    }
    if (err.response?.statusCode == 410) {
      final authUseCase = GetIt.instance<AuthUseCase>();
      if (!authUseCase.isLoggingOut) {
        final processedError = await _handleSessionGone(err);
        Dio dio = GetIt.instance<Dio>();
        return handler.resolve(await dio.fetch(processedError.requestOptions));
      }
    } else {
      if (err.response?.statusCode == 400 && err.error is! AppError) {
        final apiError = ApiError.fromJson(err.response?.data);

        if (apiError.code == sessionGone) {
          final processedError = await _handleSessionGone(err);
          Dio dio = GetIt.instance<Dio>();
          return handler
              .resolve(await dio.fetch(processedError.requestOptions));
        } else if (apiError.code == orgIdNotProvisioned) {
          GetIt.I<AppErrorBloc>()
              .add(ReportAppErrorEvent(orgNotProvisionedAppError));
        }
      }
      if (err.response?.statusCode == 409 &&
          err.response?.headers.value(sessionTokenHeaderKey) != null) {
        _logger.info(
            'AuthInterceptor - onError - 409 - no sessionToken; ending session');
        final sessionUseCase = GetIt.instance<SessionUseCase>();
        await sessionUseCase
            .endSession(err.response!.headers.value(sessionTokenHeaderKey)!);

        // Repeat the request with the updated header
        Dio dio = GetIt.instance<Dio>();
        return handler.resolve(await dio.fetch(err.requestOptions));
      }
    }
    // We did not process any known errors, so we pass the error to the next handler
    super.onError(err, handler);
    return;
  }

  Future<String> _generateAuthHeader() async {
    final credentials = await _localStorageRepository.getCredentials();
    return "Bearer ${credentials.authorizationToken}";
  }

  static String swapSessionTokenIfNeeded(String url, String newToken) {
    if (url.contains('/sessions/')) {
      RegExp sessionTokenRegExp = RegExp(r'/sessions/([^/]+)');
      return url.replaceFirstMapped(sessionTokenRegExp, (match) {
        return '/sessions/$newToken';
      });
    }
    return url;
  }

  Future<DioException> _handleSessionGone(DioException err) async {
    final authUseCase = GetIt.instance<AuthUseCase>();
    if (authUseCase.isLoggingOut) {
      return err;
    }

    Credentials credentials = await _localStorageRepository.getCredentials();
    final sessionUseCase = GetIt.instance<SessionUseCase>();
    if (credentials.userId != null && credentials.instanceUrl != null) {
      await sessionUseCase.startSession();

      credentials = await _localStorageRepository.getCredentials();
      if (credentials.sessionToken != null) {
        err.requestOptions.headers[sessionTokenHeaderKey] =
            credentials.sessionToken;
        await Utils.saveDataToSecureStorage(
            'session_token', credentials.sessionToken);
        // verify if the endpoint contains a session token
        // and replace the stale token with the new one
        err.requestOptions.path = swapSessionTokenIfNeeded(
            err.requestOptions.path, credentials.sessionToken!);
      }
    }
    return err;
  }
}
