// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_sessions_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ActiveSessionsResponse _$ActiveSessionsResponseFromJson(
    Map<String, dynamic> json) {
  return _ActiveSessionsResponse.fromJson(json);
}

/// @nodoc
mixin _$ActiveSessionsResponse {
  List<SessionData>? get sessions => throw _privateConstructorUsedError;
  String get key => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ActiveSessionsResponseCopyWith<ActiveSessionsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActiveSessionsResponseCopyWith<$Res> {
  factory $ActiveSessionsResponseCopyWith(ActiveSessionsResponse value,
          $Res Function(ActiveSessionsResponse) then) =
      _$ActiveSessionsResponseCopyWithImpl<$Res, ActiveSessionsResponse>;
  @useResult
  $Res call({List<SessionData>? sessions, String key});
}

/// @nodoc
class _$ActiveSessionsResponseCopyWithImpl<$Res,
        $Val extends ActiveSessionsResponse>
    implements $ActiveSessionsResponseCopyWith<$Res> {
  _$ActiveSessionsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessions = freezed,
    Object? key = null,
  }) {
    return _then(_value.copyWith(
      sessions: freezed == sessions
          ? _value.sessions
          : sessions // ignore: cast_nullable_to_non_nullable
              as List<SessionData>?,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ActiveSessionsResponseImplCopyWith<$Res>
    implements $ActiveSessionsResponseCopyWith<$Res> {
  factory _$$ActiveSessionsResponseImplCopyWith(
          _$ActiveSessionsResponseImpl value,
          $Res Function(_$ActiveSessionsResponseImpl) then) =
      __$$ActiveSessionsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SessionData>? sessions, String key});
}

/// @nodoc
class __$$ActiveSessionsResponseImplCopyWithImpl<$Res>
    extends _$ActiveSessionsResponseCopyWithImpl<$Res,
        _$ActiveSessionsResponseImpl>
    implements _$$ActiveSessionsResponseImplCopyWith<$Res> {
  __$$ActiveSessionsResponseImplCopyWithImpl(
      _$ActiveSessionsResponseImpl _value,
      $Res Function(_$ActiveSessionsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessions = freezed,
    Object? key = null,
  }) {
    return _then(_$ActiveSessionsResponseImpl(
      sessions: freezed == sessions
          ? _value._sessions
          : sessions // ignore: cast_nullable_to_non_nullable
              as List<SessionData>?,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ActiveSessionsResponseImpl implements _ActiveSessionsResponse {
  const _$ActiveSessionsResponseImpl(
      {final List<SessionData>? sessions, required this.key})
      : _sessions = sessions;

  factory _$ActiveSessionsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ActiveSessionsResponseImplFromJson(json);

  final List<SessionData>? _sessions;
  @override
  List<SessionData>? get sessions {
    final value = _sessions;
    if (value == null) return null;
    if (_sessions is EqualUnmodifiableListView) return _sessions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String key;

  @override
  String toString() {
    return 'ActiveSessionsResponse(sessions: $sessions, key: $key)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActiveSessionsResponseImpl &&
            const DeepCollectionEquality().equals(other._sessions, _sessions) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_sessions), key);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ActiveSessionsResponseImplCopyWith<_$ActiveSessionsResponseImpl>
      get copyWith => __$$ActiveSessionsResponseImplCopyWithImpl<
          _$ActiveSessionsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ActiveSessionsResponseImplToJson(
      this,
    );
  }
}

abstract class _ActiveSessionsResponse implements ActiveSessionsResponse {
  const factory _ActiveSessionsResponse(
      {final List<SessionData>? sessions,
      required final String key}) = _$ActiveSessionsResponseImpl;

  factory _ActiveSessionsResponse.fromJson(Map<String, dynamic> json) =
      _$ActiveSessionsResponseImpl.fromJson;

  @override
  List<SessionData>? get sessions;
  @override
  String get key;
  @override
  @JsonKey(ignore: true)
  _$$ActiveSessionsResponseImplCopyWith<_$ActiveSessionsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
