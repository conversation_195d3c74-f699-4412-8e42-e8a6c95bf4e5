import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'presence_status.freezed.dart';

part 'presence_status.g.dart';

enum PresenceStatusOption { online, offline, busy }

@freezed
@collectionOnFreezed
class PresenceStatus with _$PresenceStatus {
  Id get isarId => fastHash(id);

  const PresenceStatus._();

  @ignore
  bool get isOnline =>
      statusOption == PresenceStatusOption.online ||
      statusOption == PresenceStatusOption.busy;

  const factory PresenceStatus({
  required String id,
  required String label,
      @enumerated
      required PresenceStatusOption statusOption}) = _PresenceStatus;

  factory PresenceStatus.fromJson(Map<String, dynamic> json) =>
      _$PresenceStatusFromJson(json);
}
