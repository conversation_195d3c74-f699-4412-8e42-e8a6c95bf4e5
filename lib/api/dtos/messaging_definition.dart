import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:collection/collection.dart';
import 'package:x1440/models/sf_id.dart';
part 'messaging_definition.freezed.dart';
part 'messaging_definition.g.dart';

enum MessagingChannelType {
  whatsApp('WhatsApp'),
  text('Text');
  // Add other channels as needed

  final String value;
  const MessagingChannelType(this.value);

  static MessagingChannelType? fromString(String? str) {
    return MessagingChannelType.values.firstWhereOrNull(
      (channel) => channel.value.toLowerCase() == str?.toLowerCase(),
    );
  }
}

@freezed
class MessagingDefinition with _$MessagingDefinition {
  const MessagingDefinition._();
  const factory MessagingDefinition({
    @ParseSfIdConverter() SfId? id,
    String? name,
    String? internalName,
    @Default('') String? description,
    bool? hasRequiredParameters,
  }) = _MessagingDefinition;

  MessagingChannelType? get messagingChannel =>
      MessagingChannelType.fromString(name);

  factory MessagingDefinition.fromJson(Map<String, dynamic> json) =>
      _$MessagingDefinitionFromJson(json);
}
