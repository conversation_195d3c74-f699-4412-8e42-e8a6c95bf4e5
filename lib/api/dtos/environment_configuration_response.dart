import 'package:freezed_annotation/freezed_annotation.dart';

part 'environment_configuration_response.freezed.dart';
part 'environment_configuration_response.g.dart';

@freezed
class EnvironmentConfigurationResponse with _$EnvironmentConfigurationResponse {
  const factory EnvironmentConfigurationResponse({
    required Map<String, dynamic> config,
  }) = _EnvironmentConfigurationResponse;

  factory EnvironmentConfigurationResponse.fromJson(
          Map<String, dynamic> json) =>
      _$EnvironmentConfigurationResponseFromJson(json);
}
