import 'package:freezed_annotation/freezed_annotation.dart';

part 'messaging_end_user_status_response.freezed.dart';
part 'messaging_end_user_status_response.g.dart';

@freezed
class MessagingEndUserStatusResponse with _$MessagingEndUserStatusResponse {
  const MessagingEndUserStatusResponse._();

  const factory MessagingEndUserStatusResponse({
    required bool canSendOutbound
  }) = _MessagingEndUserStatusResponse;

  factory MessagingEndUserStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserStatusResponseFromJson(json);
}