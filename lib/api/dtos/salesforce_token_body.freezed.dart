// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'salesforce_token_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SalesforceTokenBody _$SalesforceTokenBodyFromJson(Map<String, dynamic> json) {
  return _SalesforceTokenBody.fromJson(json);
}

/// @nodoc
mixin _$SalesforceTokenBody {
  String get orgId => throw _privateConstructorUsedError;
  String get accessToken => throw _privateConstructorUsedError;
  int? get expirationSeconds => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SalesforceTokenBodyCopyWith<SalesforceTokenBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SalesforceTokenBodyCopyWith<$Res> {
  factory $SalesforceTokenBodyCopyWith(
          SalesforceTokenBody value, $Res Function(SalesforceTokenBody) then) =
      _$SalesforceTokenBodyCopyWithImpl<$Res, SalesforceTokenBody>;
  @useResult
  $Res call({String orgId, String accessToken, int? expirationSeconds});
}

/// @nodoc
class _$SalesforceTokenBodyCopyWithImpl<$Res, $Val extends SalesforceTokenBody>
    implements $SalesforceTokenBodyCopyWith<$Res> {
  _$SalesforceTokenBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orgId = null,
    Object? accessToken = null,
    Object? expirationSeconds = freezed,
  }) {
    return _then(_value.copyWith(
      orgId: null == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      expirationSeconds: freezed == expirationSeconds
          ? _value.expirationSeconds
          : expirationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SalesforceTokenBodyImplCopyWith<$Res>
    implements $SalesforceTokenBodyCopyWith<$Res> {
  factory _$$SalesforceTokenBodyImplCopyWith(_$SalesforceTokenBodyImpl value,
          $Res Function(_$SalesforceTokenBodyImpl) then) =
      __$$SalesforceTokenBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String orgId, String accessToken, int? expirationSeconds});
}

/// @nodoc
class __$$SalesforceTokenBodyImplCopyWithImpl<$Res>
    extends _$SalesforceTokenBodyCopyWithImpl<$Res, _$SalesforceTokenBodyImpl>
    implements _$$SalesforceTokenBodyImplCopyWith<$Res> {
  __$$SalesforceTokenBodyImplCopyWithImpl(_$SalesforceTokenBodyImpl _value,
      $Res Function(_$SalesforceTokenBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orgId = null,
    Object? accessToken = null,
    Object? expirationSeconds = freezed,
  }) {
    return _then(_$SalesforceTokenBodyImpl(
      orgId: null == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      expirationSeconds: freezed == expirationSeconds
          ? _value.expirationSeconds
          : expirationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SalesforceTokenBodyImpl implements _SalesforceTokenBody {
  const _$SalesforceTokenBodyImpl(
      {required this.orgId, required this.accessToken, this.expirationSeconds});

  factory _$SalesforceTokenBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$SalesforceTokenBodyImplFromJson(json);

  @override
  final String orgId;
  @override
  final String accessToken;
  @override
  final int? expirationSeconds;

  @override
  String toString() {
    return 'SalesforceTokenBody(orgId: $orgId, accessToken: $accessToken, expirationSeconds: $expirationSeconds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SalesforceTokenBodyImpl &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.expirationSeconds, expirationSeconds) ||
                other.expirationSeconds == expirationSeconds));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, orgId, accessToken, expirationSeconds);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SalesforceTokenBodyImplCopyWith<_$SalesforceTokenBodyImpl> get copyWith =>
      __$$SalesforceTokenBodyImplCopyWithImpl<_$SalesforceTokenBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SalesforceTokenBodyImplToJson(
      this,
    );
  }
}

abstract class _SalesforceTokenBody implements SalesforceTokenBody {
  const factory _SalesforceTokenBody(
      {required final String orgId,
      required final String accessToken,
      final int? expirationSeconds}) = _$SalesforceTokenBodyImpl;

  factory _SalesforceTokenBody.fromJson(Map<String, dynamic> json) =
      _$SalesforceTokenBodyImpl.fromJson;

  @override
  String get orgId;
  @override
  String get accessToken;
  @override
  int? get expirationSeconds;
  @override
  @JsonKey(ignore: true)
  _$$SalesforceTokenBodyImplCopyWith<_$SalesforceTokenBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
