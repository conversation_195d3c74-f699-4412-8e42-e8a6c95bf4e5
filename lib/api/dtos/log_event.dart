import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/api/dtos/log_metadata.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'log_event.freezed.dart';
part 'log_event.g.dart';

@freezed
@collectionOnFreezed
class LogEvent with _$LogEvent {
  Id get localDbId => Isar.autoIncrement;

  const LogEvent._();
  const factory LogEvent({
    required int timestamp,
    required String packageName,
    required String level,
    required LogMetadata metadata,
    required String message,
  }) = _LogEvent;

  factory LogEvent.fromJson(Map<String, dynamic> json) =>
      _$LogEventFromJson(json);
}
