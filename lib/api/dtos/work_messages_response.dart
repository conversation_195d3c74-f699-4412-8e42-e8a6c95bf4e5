import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/send_failure.dart';

part 'work_messages_response.freezed.dart';
part 'work_messages_response.g.dart';

@freezed
class WorkMessagesResponse with _$WorkMessagesResponse {
  const factory WorkMessagesResponse({
    List<SendFailure>? failures,
  }) = _WorkMessagesResponse;

  factory WorkMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkMessagesResponseFromJson(json);
}
