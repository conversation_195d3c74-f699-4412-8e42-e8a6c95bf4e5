// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_channel_entry.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingChannelEntryImpl _$$MessagingChannelEntryImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingChannelEntryImpl(
      channelId: const ParseSfIdConverter().fromJson(json['id']),
      messageType: json['messageType'] as String,
      name: json['name'] as String,
      isFavorite: json['isFavorite'] as bool,
      messagingEndUserId:
          const ParseSfIdConverter().fromJson(json['messagingEndUserId']),
      messagingEndUserTimestamp:
          (json['messagingEndUserTimestamp'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$MessagingChannelEntryImplToJson(
        _$MessagingChannelEntryImpl instance) =>
    <String, dynamic>{
      'id': const ParseSfIdConverter().toJson(instance.channelId),
      'messageType': instance.messageType,
      'name': instance.name,
      'isFavorite': instance.isFavorite,
      'messagingEndUserId': _$JsonConverterToJson<Object?, SfId>(
          instance.messagingEndUserId, const ParseSfIdConverter().toJson),
      'messagingEndUserTimestamp': instance.messagingEndUserTimestamp,
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
