import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'message_id_and_session_id.freezed.dart';
part 'message_id_and_session_id.g.dart';

@freezed
class MessageIdAndSessionId with _$MessageIdAndSessionId {
  const factory MessageIdAndSessionId({
    @ParseSfIdConverter() required SfId messageId,
    @ParseSfIdConverter() required SfId messagingSessionId,
  }) = _MessageIdAndSessionId;

  factory MessageIdAndSessionId.fromJson(Map<String, dynamic> json) =>
      _$MessageIdAndSessionIdFromJson(json);
}
