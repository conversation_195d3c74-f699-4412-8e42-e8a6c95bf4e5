import 'package:dio/dio.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/utils/json_utils.dart';

part 'api_error.freezed.dart';
part 'api_error.g.dart';

@freezed
class ApiError with _$ApiError {
  const factory ApiError({
    int? statusCode,
    String? code,
    String? message,
    String? errorMessage,
  }) = _ApiError;

  factory ApiError.fromJson(Map<String, dynamic> json) =>
      _$ApiErrorFromJson(json);

  static ApiError createError(Exception exception) {
    ApiError apiError =
        ApiError(code: 'unknown', message: exception.toString());
    if (exception is DioException) {
      apiError = apiError.copyWith(statusCode: exception.response?.statusCode);
      if (exception.type == DioExceptionType.badResponse) {
        if (exception.response != null && exception.response!.data != null) {
          apiError = ApiError.fromJson(safeJsonDecode(exception.response!.data))
              .copyWith(
            statusCode: exception.response!.statusCode,
            errorMessage: exception.response!.data is Map &&
                    (exception.response!.data as Map).containsKey('message')
                ? exception.response!.data['message']
                : 'unknown',
            message: exception.message,
          );
        }
      }
    } else {
      apiError = ApiError(code: 'unknown', message: exception.toString());
    }
    return apiError;
  }

  static fromResponseBody(ResponseBody responseBody) => ApiError(
        statusCode: responseBody.statusCode,
        code: responseBody.statusMessage ?? 'unknown',
        message: responseBody.toString(),
      );
}
