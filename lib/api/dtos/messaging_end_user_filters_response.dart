import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/messaging_end_user_filter.dart';

part 'messaging_end_user_filters_response.freezed.dart';
part 'messaging_end_user_filters_response.g.dart';

@freezed
class MessagingEndUserFiltersResponse with _$MessagingEndUserFiltersResponse {
  const factory MessagingEndUserFiltersResponse({
    required List<MessagingEndUserFilter> filters,
  }) = _MessagingEndUserFiltersResponse;

  factory MessagingEndUserFiltersResponse.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserFiltersResponseFromJson(json);
}
