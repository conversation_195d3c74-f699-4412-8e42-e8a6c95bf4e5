// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_configuration.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PresenceConfiguration _$PresenceConfigurationFromJson(
    Map<String, dynamic> json) {
  return _PresenceConfiguration.fromJson(json);
}

/// @nodoc
mixin _$PresenceConfiguration {
  bool get autoAcceptEnabled => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresenceConfigurationCopyWith<PresenceConfiguration> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresenceConfigurationCopyWith<$Res> {
  factory $PresenceConfigurationCopyWith(PresenceConfiguration value,
          $Res Function(PresenceConfiguration) then) =
      _$PresenceConfigurationCopyWithImpl<$Res, PresenceConfiguration>;
  @useResult
  $Res call({bool autoAcceptEnabled});
}

/// @nodoc
class _$PresenceConfigurationCopyWithImpl<$Res,
        $Val extends PresenceConfiguration>
    implements $PresenceConfigurationCopyWith<$Res> {
  _$PresenceConfigurationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? autoAcceptEnabled = null,
  }) {
    return _then(_value.copyWith(
      autoAcceptEnabled: null == autoAcceptEnabled
          ? _value.autoAcceptEnabled
          : autoAcceptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PresenceConfigurationImplCopyWith<$Res>
    implements $PresenceConfigurationCopyWith<$Res> {
  factory _$$PresenceConfigurationImplCopyWith(
          _$PresenceConfigurationImpl value,
          $Res Function(_$PresenceConfigurationImpl) then) =
      __$$PresenceConfigurationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool autoAcceptEnabled});
}

/// @nodoc
class __$$PresenceConfigurationImplCopyWithImpl<$Res>
    extends _$PresenceConfigurationCopyWithImpl<$Res,
        _$PresenceConfigurationImpl>
    implements _$$PresenceConfigurationImplCopyWith<$Res> {
  __$$PresenceConfigurationImplCopyWithImpl(_$PresenceConfigurationImpl _value,
      $Res Function(_$PresenceConfigurationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? autoAcceptEnabled = null,
  }) {
    return _then(_$PresenceConfigurationImpl(
      autoAcceptEnabled: null == autoAcceptEnabled
          ? _value.autoAcceptEnabled
          : autoAcceptEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresenceConfigurationImpl implements _PresenceConfiguration {
  const _$PresenceConfigurationImpl({required this.autoAcceptEnabled});

  factory _$PresenceConfigurationImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresenceConfigurationImplFromJson(json);

  @override
  final bool autoAcceptEnabled;

  @override
  String toString() {
    return 'PresenceConfiguration(autoAcceptEnabled: $autoAcceptEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresenceConfigurationImpl &&
            (identical(other.autoAcceptEnabled, autoAcceptEnabled) ||
                other.autoAcceptEnabled == autoAcceptEnabled));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, autoAcceptEnabled);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresenceConfigurationImplCopyWith<_$PresenceConfigurationImpl>
      get copyWith => __$$PresenceConfigurationImplCopyWithImpl<
          _$PresenceConfigurationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresenceConfigurationImplToJson(
      this,
    );
  }
}

abstract class _PresenceConfiguration implements PresenceConfiguration {
  const factory _PresenceConfiguration(
      {required final bool autoAcceptEnabled}) = _$PresenceConfigurationImpl;

  factory _PresenceConfiguration.fromJson(Map<String, dynamic> json) =
      _$PresenceConfigurationImpl.fromJson;

  @override
  bool get autoAcceptEnabled;
  @override
  @JsonKey(ignore: true)
  _$$PresenceConfigurationImplCopyWith<_$PresenceConfigurationImpl>
      get copyWith => throw _privateConstructorUsedError;
}
