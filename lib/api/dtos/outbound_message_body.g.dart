// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'outbound_message_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OutboundMessageBodyImpl _$$OutboundMessageBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$OutboundMessageBodyImpl(
      messageId: json['messageId'] as String,
      messagingEndUserId:
          const ParseSfIdConverter().from<PERSON>son(json['messagingEndUserId']),
      messageBody: json['messageBody'] as String?,
      messagingDefinitionParameters: json['messagingDefinitionParameters'] ==
              null
          ? null
          : MessagingDefinitionParameters.fromJson(
              json['messagingDefinitionParameters'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$OutboundMessageBodyImplToJson(
        _$OutboundMessageBodyImpl instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'messagingEndUserId':
          const ParseSfIdConverter().toJson(instance.messagingEndUserId),
      'messageBody': instance.messageBody,
      'messagingDefinitionParameters':
          instance.messagingDefinitionParameters?.toJson(),
    };
