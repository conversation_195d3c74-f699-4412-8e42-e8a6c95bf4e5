import 'package:freezed_annotation/freezed_annotation.dart';

part 'presence_configuration.freezed.dart';
part 'presence_configuration.g.dart';

@freezed
class PresenceConfiguration with _$PresenceConfiguration {
  const factory PresenceConfiguration({
    required bool autoAcceptEnabled,
  }) = _PresenceConfiguration;

  factory PresenceConfiguration.fromJson(Map<String, dynamic> json) =>
      _$PresenceConfigurationFromJson(json);
}
