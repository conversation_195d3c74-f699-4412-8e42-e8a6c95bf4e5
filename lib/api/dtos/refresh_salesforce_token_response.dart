import 'package:freezed_annotation/freezed_annotation.dart';

part 'refresh_salesforce_token_response.freezed.dart';
part 'refresh_salesforce_token_response.g.dart';

@freezed
class RefreshSalesforceTokenResponse with _$RefreshSalesforceTokenResponse {
  const factory RefreshSalesforceTokenResponse({required String accessToken}) =
      _RefreshSalesforceTokenResponse;

  factory RefreshSalesforceTokenResponse.fromJson(Map<String, dynamic> json) =>
      _$RefreshSalesforceTokenResponseFromJson(json);
}
