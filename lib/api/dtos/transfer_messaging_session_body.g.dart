// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_messaging_session_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TransferMessagingSessionBodyImpl _$$TransferMessagingSessionBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$TransferMessagingSessionBodyImpl(
      destinationType: $enumDecode(
          _$TransferDestinationTypeEnumMap, json['destinationType']),
      destinationId: const ParseSfIdConverter().from<PERSON>son(json['destinationId']),
    );

Map<String, dynamic> _$$TransferMessagingSessionBodyImplToJson(
        _$TransferMessagingSessionBodyImpl instance) =>
    <String, dynamic>{
      'destinationType':
          _$TransferDestinationTypeEnumMap[instance.destinationType]!,
      'destinationId':
          const ParseSfIdConverter().toJson(instance.destinationId),
    };

const _$TransferDestinationTypeEnumMap = {
  TransferDestinationType.queue: 'queue',
  TransferDestinationType.agent: 'agent',
  TransferDestinationType.flow: 'flow',
};
