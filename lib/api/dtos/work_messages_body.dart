import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/work_message_body.dart';

part 'work_messages_body.freezed.dart';
part 'work_messages_body.g.dart';

@freezed
class WorkMessagesBody with _$WorkMessagesBody {
  const factory WorkMessagesBody({
    required List<WorkMessageBody> messages,
  }) = _WorkMessagesBody;

  factory WorkMessagesBody.fromJson(Map<String, dynamic> json) =>
      _$WorkMessagesBodyFromJson(json);
}
