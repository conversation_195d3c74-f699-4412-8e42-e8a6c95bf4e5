import 'package:freezed_annotation/freezed_annotation.dart';

part 'session_data.freezed.dart';
part 'session_data.g.dart';

@freezed
class SessionData with _$SessionData {
  const factory SessionData(
      {required int tenantId,
      required String sessionId,
      required int timeCreated,
      required int timeUpdated,
      required int expiresAt,
      required List<String> channelPlatformTypes,
      required String userId}) = _SessionData;

  factory SessionData.fromJson(Map<String, dynamic> json) =>
      _$SessionDataFromJson(json);
}
