import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'decline_work_body.freezed.dart';
part 'decline_work_body.g.dart';

@freezed
class DeclineWorkBody with _$DeclineWorkBody {
  const factory DeclineWorkBody(
      {required String requestId,
        @ParseSfIdConverter() required SfId workId,
        @ParseSfIdConverter() required SfId workTargetId,
      String? declineReason}) = _DeclineWorkBody;

  factory DeclineWorkBody.fromJson(Map<String, dynamic> json) =>
      _$DeclineWorkBodyFromJson(json);
}
