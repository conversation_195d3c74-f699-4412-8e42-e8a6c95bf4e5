import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'work_body.freezed.dart';
part 'work_body.g.dart';

@freezed
class WorkBody with _$WorkBody {
  const factory WorkBody({
    required String requestId,
    @ParseSfIdConverter() required SfId workId,
    @ParseSfIdConverter() required SfId workTargetId,
  }) = _WorkBody;

  factory WorkBody.fromJson(Map<String, dynamic> json) =>
      _$WorkBodyFromJson(json);
}
