// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_end_user_filter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingEndUserFilterImpl _$$MessagingEndUserFilterImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingEndUserFilterImpl(
      messageType: json['messageType'] as String,
      consentStatus: json['consentStatus'] as String,
      channelName: json['channelName'] as String,
      channelId: const ParseSfIdConverter().fromJson(json['channelId']),
      enforceMessagingComponent: json['enforceMessagingComponent'] as bool,
    );

Map<String, dynamic> _$$MessagingEndUserFilterImplToJson(
        _$MessagingEndUserFilterImpl instance) =>
    <String, dynamic>{
      'messageType': instance.messageType,
      'consentStatus': instance.consentStatus,
      'channelName': instance.channelName,
      'channelId': const ParseSfIdConverter().toJson(instance.channelId),
      'enforceMessagingComponent': instance.enforceMessagingComponent,
    };
