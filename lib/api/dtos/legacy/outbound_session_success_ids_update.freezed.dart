// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_session_success_ids_update.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OutboundSessionSuccessIdsUpdate _$OutboundSessionSuccessIdsUpdateFromJson(
    Map<String, dynamic> json) {
  return _OutboundSessionSuccessIdsUpdate.fromJson(json);
}

/// @nodoc
mixin _$OutboundSessionSuccessIdsUpdate {
  String get messageId => throw _privateConstructorUsedError;
  String get messagingEndUserId => throw _privateConstructorUsedError;
  String get messagingSessionId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OutboundSessionSuccessIdsUpdateCopyWith<OutboundSessionSuccessIdsUpdate>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutboundSessionSuccessIdsUpdateCopyWith<$Res> {
  factory $OutboundSessionSuccessIdsUpdateCopyWith(
          OutboundSessionSuccessIdsUpdate value,
          $Res Function(OutboundSessionSuccessIdsUpdate) then) =
      _$OutboundSessionSuccessIdsUpdateCopyWithImpl<$Res,
          OutboundSessionSuccessIdsUpdate>;
  @useResult
  $Res call(
      {String messageId, String messagingEndUserId, String messagingSessionId});
}

/// @nodoc
class _$OutboundSessionSuccessIdsUpdateCopyWithImpl<$Res,
        $Val extends OutboundSessionSuccessIdsUpdate>
    implements $OutboundSessionSuccessIdsUpdateCopyWith<$Res> {
  _$OutboundSessionSuccessIdsUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingEndUserId = null,
    Object? messagingSessionId = null,
  }) {
    return _then(_value.copyWith(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingEndUserId: null == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingSessionId: null == messagingSessionId
          ? _value.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OutboundSessionSuccessIdsUpdateImplCopyWith<$Res>
    implements $OutboundSessionSuccessIdsUpdateCopyWith<$Res> {
  factory _$$OutboundSessionSuccessIdsUpdateImplCopyWith(
          _$OutboundSessionSuccessIdsUpdateImpl value,
          $Res Function(_$OutboundSessionSuccessIdsUpdateImpl) then) =
      __$$OutboundSessionSuccessIdsUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String messageId, String messagingEndUserId, String messagingSessionId});
}

/// @nodoc
class __$$OutboundSessionSuccessIdsUpdateImplCopyWithImpl<$Res>
    extends _$OutboundSessionSuccessIdsUpdateCopyWithImpl<$Res,
        _$OutboundSessionSuccessIdsUpdateImpl>
    implements _$$OutboundSessionSuccessIdsUpdateImplCopyWith<$Res> {
  __$$OutboundSessionSuccessIdsUpdateImplCopyWithImpl(
      _$OutboundSessionSuccessIdsUpdateImpl _value,
      $Res Function(_$OutboundSessionSuccessIdsUpdateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingEndUserId = null,
    Object? messagingSessionId = null,
  }) {
    return _then(_$OutboundSessionSuccessIdsUpdateImpl(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingEndUserId: null == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingSessionId: null == messagingSessionId
          ? _value.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OutboundSessionSuccessIdsUpdateImpl
    implements _OutboundSessionSuccessIdsUpdate {
  const _$OutboundSessionSuccessIdsUpdateImpl(
      {required this.messageId,
      required this.messagingEndUserId,
      required this.messagingSessionId});

  factory _$OutboundSessionSuccessIdsUpdateImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$OutboundSessionSuccessIdsUpdateImplFromJson(json);

  @override
  final String messageId;
  @override
  final String messagingEndUserId;
  @override
  final String messagingSessionId;

  @override
  String toString() {
    return 'OutboundSessionSuccessIdsUpdate(messageId: $messageId, messagingEndUserId: $messagingEndUserId, messagingSessionId: $messagingSessionId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OutboundSessionSuccessIdsUpdateImpl &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, messageId, messagingEndUserId, messagingSessionId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OutboundSessionSuccessIdsUpdateImplCopyWith<
          _$OutboundSessionSuccessIdsUpdateImpl>
      get copyWith => __$$OutboundSessionSuccessIdsUpdateImplCopyWithImpl<
          _$OutboundSessionSuccessIdsUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OutboundSessionSuccessIdsUpdateImplToJson(
      this,
    );
  }
}

abstract class _OutboundSessionSuccessIdsUpdate
    implements OutboundSessionSuccessIdsUpdate {
  const factory _OutboundSessionSuccessIdsUpdate(
          {required final String messageId,
          required final String messagingEndUserId,
          required final String messagingSessionId}) =
      _$OutboundSessionSuccessIdsUpdateImpl;

  factory _OutboundSessionSuccessIdsUpdate.fromJson(Map<String, dynamic> json) =
      _$OutboundSessionSuccessIdsUpdateImpl.fromJson;

  @override
  String get messageId;
  @override
  String get messagingEndUserId;
  @override
  String get messagingSessionId;
  @override
  @JsonKey(ignore: true)
  _$$OutboundSessionSuccessIdsUpdateImplCopyWith<
          _$OutboundSessionSuccessIdsUpdateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
