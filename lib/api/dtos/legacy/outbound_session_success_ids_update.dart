import 'package:freezed_annotation/freezed_annotation.dart';

part 'outbound_session_success_ids_update.freezed.dart';
part 'outbound_session_success_ids_update.g.dart';

@Deprecated('to update legacy Conversations View Model from new CLEAN architecture & apis')
@freezed
class OutboundSessionSuccessIdsUpdate with _$OutboundSessionSuccessIdsUpdate {
  const factory OutboundSessionSuccessIdsUpdate({
    required String messageId,
    required String messagingEndUserId,
    required String messagingSessionId
  }) = _OutboundSessionSuccessIdsUpdate;

  factory OutboundSessionSuccessIdsUpdate.fromJson(Map<String, dynamic> json) =>
      _$OutboundSessionSuccessIdsUpdateFromJson(json);
}