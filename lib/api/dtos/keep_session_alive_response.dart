import 'package:freezed_annotation/freezed_annotation.dart';

part 'keep_session_alive_response.freezed.dart';
part 'keep_session_alive_response.g.dart';

@freezed
class KeepSessionAliveResponse with _$KeepSessionAliveResponse {
  const factory KeepSessionAliveResponse({
    required int expirationTime,
  }) = _KeepSessionAliveResponse;

  factory KeepSessionAliveResponse.fromJson(Map<String, dynamic> json) =>
      _$KeepSessionAliveResponseFromJson(json);
}
