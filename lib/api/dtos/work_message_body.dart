import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:universal_io/io.dart';
import 'package:x1440/api/dtos/message_attachment.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';

part 'work_message_body.freezed.dart';
part 'work_message_body.g.dart';

/// WorkMessageBody for SendWorkMessage, SendWorkMessages endpoints is a different body:
/// SendWorkMessages uses messageId instead of Id
/// SendWorkMessages includes the workTargetId in the body, SendWorkMessage uses it in the endpoint path
@freezed
class WorkMessageBody with _$WorkMessageBody {
  const factory WorkMessageBody({
    required String messageId,
    String? messageBody,
    @ParseSfIdConverter() SfId? workTargetId,
    @Default(<MessageAttachment>[]) List<MessageAttachment> attachments,
  }) = _WorkMessageBody;

  factory WorkMessageBody.fromJson(Map<String, dynamic> json) =>
      _$WorkMessageBodyFromJson(json);

  factory WorkMessageBody.fromQueueSendMessage({required QueueSendMessage message,
    Map<String, File> messageFileMap = const {}
  }) => WorkMessageBody(
    messageId: message.messageId,
    messageBody: message.messageContent,
    workTargetId: message.workTargetId,
      attachments: messageFileMap.keys.map((key) => MessageAttachment(key: 'contentVersionId', value: key)).whereType<MessageAttachment>().toList()
  );
}
