import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'messaging_channel_entry.freezed.dart';
part 'messaging_channel_entry.g.dart';

@freezed
@collectionOnFreezed
class MessagingChannelEntry with _$MessagingChannelEntry {
  /// Isar ID for indexing — Setting this one to '0' for singleton item in collection; otherwise default use here is (can also use custom indices -- see isar docs):
  final Id id = 0;
  const MessagingChannelEntry._();

  const factory MessagingChannelEntry({
    @JsonKey(name: 'id') @ParseSfIdConverter()  required SfId channelId,
    required String messageType,
    required String name,
    required bool isFavorite,
    @ParseSfIdConverter() SfId? messagingEndUserId,
    int? messagingEndUserTimestamp,
  }) = _MessagingChannelEntry;

  bool isWhatsApp() => messageType.toLowerCase() == 'whatsapp';

  factory MessagingChannelEntry.fromJson(Map<String, dynamic> json) =>
      _$MessagingChannelEntryFromJson(json);
}
