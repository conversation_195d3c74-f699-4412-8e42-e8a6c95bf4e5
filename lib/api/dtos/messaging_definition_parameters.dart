import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'messaging_definition_parameters.freezed.dart';
part 'messaging_definition_parameters.g.dart';

@freezed
class MessagingDefinitionParameters with _$MessagingDefinitionParameters {
  const factory MessagingDefinitionParameters({
    @ParseSfIdConverter() required SfId id,
    List<String>? parameters,
  }) = _MessagingDefinitionParameters;

  factory MessagingDefinitionParameters.fromJson(Map<String, dynamic> json) =>
      _$MessagingDefinitionParametersFromJson(json);
}
