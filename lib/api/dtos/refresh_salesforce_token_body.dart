import 'package:freezed_annotation/freezed_annotation.dart';

part 'refresh_salesforce_token_body.freezed.dart';
part 'refresh_salesforce_token_body.g.dart';

@freezed
class RefreshSalesforceTokenBody with _$RefreshSalesforceTokenBody {
  const factory RefreshSalesforceTokenBody({
    required String refreshToken,
    required String orgId,
    required String instanceUrl,
  }) = _RefreshSalesforceTokenBody;

  factory RefreshSalesforceTokenBody.fromJson(Map<String, dynamic> json) =>
      _$RefreshSalesforceTokenBodyFromJson(json);
}
