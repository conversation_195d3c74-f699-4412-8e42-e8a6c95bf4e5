// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unacknowledged_notifications_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UnacknowledgedNotificationsResponse
    _$UnacknowledgedNotificationsResponseFromJson(Map<String, dynamic> json) {
  return _UnacknowledgedNotificationsResponse.fromJson(json);
}

/// @nodoc
mixin _$UnacknowledgedNotificationsResponse {
  List<NotificationMessage>? get notifications =>
      throw _privateConstructorUsedError;
  String? get nextToken => throw _privateConstructorUsedError;
  bool? get sessionActive => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UnacknowledgedNotificationsResponseCopyWith<
          UnacknowledgedNotificationsResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnacknowledgedNotificationsResponseCopyWith<$Res> {
  factory $UnacknowledgedNotificationsResponseCopyWith(
          UnacknowledgedNotificationsResponse value,
          $Res Function(UnacknowledgedNotificationsResponse) then) =
      _$UnacknowledgedNotificationsResponseCopyWithImpl<$Res,
          UnacknowledgedNotificationsResponse>;
  @useResult
  $Res call(
      {List<NotificationMessage>? notifications,
      String? nextToken,
      bool? sessionActive});
}

/// @nodoc
class _$UnacknowledgedNotificationsResponseCopyWithImpl<$Res,
        $Val extends UnacknowledgedNotificationsResponse>
    implements $UnacknowledgedNotificationsResponseCopyWith<$Res> {
  _$UnacknowledgedNotificationsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notifications = freezed,
    Object? nextToken = freezed,
    Object? sessionActive = freezed,
  }) {
    return _then(_value.copyWith(
      notifications: freezed == notifications
          ? _value.notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationMessage>?,
      nextToken: freezed == nextToken
          ? _value.nextToken
          : nextToken // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionActive: freezed == sessionActive
          ? _value.sessionActive
          : sessionActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UnacknowledgedNotificationsResponseImplCopyWith<$Res>
    implements $UnacknowledgedNotificationsResponseCopyWith<$Res> {
  factory _$$UnacknowledgedNotificationsResponseImplCopyWith(
          _$UnacknowledgedNotificationsResponseImpl value,
          $Res Function(_$UnacknowledgedNotificationsResponseImpl) then) =
      __$$UnacknowledgedNotificationsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<NotificationMessage>? notifications,
      String? nextToken,
      bool? sessionActive});
}

/// @nodoc
class __$$UnacknowledgedNotificationsResponseImplCopyWithImpl<$Res>
    extends _$UnacknowledgedNotificationsResponseCopyWithImpl<$Res,
        _$UnacknowledgedNotificationsResponseImpl>
    implements _$$UnacknowledgedNotificationsResponseImplCopyWith<$Res> {
  __$$UnacknowledgedNotificationsResponseImplCopyWithImpl(
      _$UnacknowledgedNotificationsResponseImpl _value,
      $Res Function(_$UnacknowledgedNotificationsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notifications = freezed,
    Object? nextToken = freezed,
    Object? sessionActive = freezed,
  }) {
    return _then(_$UnacknowledgedNotificationsResponseImpl(
      notifications: freezed == notifications
          ? _value._notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationMessage>?,
      nextToken: freezed == nextToken
          ? _value.nextToken
          : nextToken // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionActive: freezed == sessionActive
          ? _value.sessionActive
          : sessionActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UnacknowledgedNotificationsResponseImpl
    implements _UnacknowledgedNotificationsResponse {
  const _$UnacknowledgedNotificationsResponseImpl(
      {final List<NotificationMessage>? notifications,
      this.nextToken,
      this.sessionActive})
      : _notifications = notifications;

  factory _$UnacknowledgedNotificationsResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$UnacknowledgedNotificationsResponseImplFromJson(json);

  final List<NotificationMessage>? _notifications;
  @override
  List<NotificationMessage>? get notifications {
    final value = _notifications;
    if (value == null) return null;
    if (_notifications is EqualUnmodifiableListView) return _notifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? nextToken;
  @override
  final bool? sessionActive;

  @override
  String toString() {
    return 'UnacknowledgedNotificationsResponse(notifications: $notifications, nextToken: $nextToken, sessionActive: $sessionActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnacknowledgedNotificationsResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._notifications, _notifications) &&
            (identical(other.nextToken, nextToken) ||
                other.nextToken == nextToken) &&
            (identical(other.sessionActive, sessionActive) ||
                other.sessionActive == sessionActive));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_notifications),
      nextToken,
      sessionActive);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UnacknowledgedNotificationsResponseImplCopyWith<
          _$UnacknowledgedNotificationsResponseImpl>
      get copyWith => __$$UnacknowledgedNotificationsResponseImplCopyWithImpl<
          _$UnacknowledgedNotificationsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UnacknowledgedNotificationsResponseImplToJson(
      this,
    );
  }
}

abstract class _UnacknowledgedNotificationsResponse
    implements UnacknowledgedNotificationsResponse {
  const factory _UnacknowledgedNotificationsResponse(
      {final List<NotificationMessage>? notifications,
      final String? nextToken,
      final bool? sessionActive}) = _$UnacknowledgedNotificationsResponseImpl;

  factory _UnacknowledgedNotificationsResponse.fromJson(
          Map<String, dynamic> json) =
      _$UnacknowledgedNotificationsResponseImpl.fromJson;

  @override
  List<NotificationMessage>? get notifications;
  @override
  String? get nextToken;
  @override
  bool? get sessionActive;
  @override
  @JsonKey(ignore: true)
  _$$UnacknowledgedNotificationsResponseImplCopyWith<
          _$UnacknowledgedNotificationsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
