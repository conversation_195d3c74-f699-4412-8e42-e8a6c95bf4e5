import 'package:freezed_annotation/freezed_annotation.dart';

part 'presence_status_body.freezed.dart';
part 'presence_status_body.g.dart';

@freezed
class PresenceStatusBody with _$PresenceStatusBody {
  const factory PresenceStatusBody({
    required String id,
  }) = _PresenceStatusBody;

  factory PresenceStatusBody.fromJson(Map<String, dynamic> json) =>
      _$PresenceStatusBodyFromJson(json);
}
