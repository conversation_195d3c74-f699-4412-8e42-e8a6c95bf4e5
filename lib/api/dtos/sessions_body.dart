import 'package:freezed_annotation/freezed_annotation.dart';

part 'sessions_body.freezed.dart';
part 'sessions_body.g.dart';

/// Used to start a session with the service. If the user already has an active session
/// it will be updated, otherwise a new one will be created.
/// For TESTING PURPOSES (AND NOT FOR PRODUCTION), you can set the expirationSeconds to
/// a low value to test session expiration. It won’t let you set it to anything under 120
/// or greater than 7200. You can also set the pingMinutes to a low value to test a ping
/// notification coming in.
@freezed
class SessionsBody with _$SessionsBody {
  const factory SessionsBody(
      {required String userId,
      required String instanceUrl,
      required String? deviceToken,
      required String accessToken,
      required List<String> channelPlatformTypes,
      @JsonKey(name: 'localeCode') required final String locale,
      final String? deviceType,
      final int? expirationSeconds,
      final Map<String, dynamic>? metadata}) = _SessionsBody;

  factory SessionsBody.fromJson(Map<String, dynamic> json) =>
      _$<PERSON>(json);
}
