import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/notification_message.dart';

part 'unacknowledged_notifications_response.freezed.dart';
part 'unacknowledged_notifications_response.g.dart';

@freezed
class UnacknowledgedNotificationsResponse
    with _$UnacknowledgedNotificationsResponse {
  const factory UnacknowledgedNotificationsResponse({
    List<NotificationMessage>? notifications,
    String? nextToken,
    bool? sessionActive,
  }) = _UnacknowledgedNotificationsResponse;

  factory UnacknowledgedNotificationsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$UnacknowledgedNotificationsResponseFromJson(json);
}
