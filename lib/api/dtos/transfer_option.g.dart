// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_option.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TransferOptionImpl _$$TransferOptionImplFromJson(Map<String, dynamic> json) =>
    _$TransferOptionImpl(
      destinationType: $enumDecode(
          _$TransferDestinationTypeEnumMap, json['destinationType']),
      destinations: (json['destinations'] as List<dynamic>?)
              ?.map((e) =>
                  TransferDestination.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const <TransferDestination>[],
    );

Map<String, dynamic> _$$TransferOptionImplToJson(
        _$TransferOptionImpl instance) =>
    <String, dynamic>{
      'destinationType':
          _$TransferDestinationTypeEnumMap[instance.destinationType]!,
      'destinations': instance.destinations.map((e) => e.toJson()).toList(),
    };

const _$TransferDestinationTypeEnumMap = {
  TransferDestinationType.queue: 'queue',
  TransferDestinationType.agent: 'agent',
  TransferDestinationType.flow: 'flow',
};
