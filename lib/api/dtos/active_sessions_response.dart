import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/session_data.dart';

part 'active_sessions_response.freezed.dart';
part 'active_sessions_response.g.dart';

@freezed
class ActiveSessionsResponse with _$ActiveSessionsResponse {
  const factory ActiveSessionsResponse(
      {List<SessionData>? sessions,
      required String key}) = _ActiveSessionsResponse;

  factory ActiveSessionsResponse.fromJson(Map<String, dynamic> json) =>
      _$ActiveSessionsResponseFromJson(json);
}
