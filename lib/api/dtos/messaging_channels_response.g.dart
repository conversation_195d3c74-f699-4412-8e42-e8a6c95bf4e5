// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_channels_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingChannelsResponseImpl _$$MessagingChannelsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingChannelsResponseImpl(
      channelSelectionEntries: (json['channelSelectionEntries']
              as List<dynamic>)
          .map((e) => MessagingChannelEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
      otherChannels: (json['otherChannels'] as List<dynamic>)
          .map((e) => MessagingChannelEntry.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$MessagingChannelsResponseImplToJson(
        _$MessagingChannelsResponseImpl instance) =>
    <String, dynamic>{
      'channelSelectionEntries':
          instance.channelSelectionEntries.map((e) => e.toJson()).toList(),
      'otherChannels': instance.otherChannels.map((e) => e.toJson()).toList(),
    };
