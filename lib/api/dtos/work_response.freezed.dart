// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkResponse _$WorkResponseFromJson(Map<String, dynamic> json) {
  return _WorkResponse.fromJson(json);
}

/// @nodoc
mixin _$WorkResponse {
  String? get conversationIdentifier => throw _privateConstructorUsedError;
  List<PresenceChatMessage>? get messages => throw _privateConstructorUsedError;
  bool get canTransfer => throw _privateConstructorUsedError;
  bool get canRaiseFlag => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WorkResponseCopyWith<WorkResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkResponseCopyWith<$Res> {
  factory $WorkResponseCopyWith(
          WorkResponse value, $Res Function(WorkResponse) then) =
      _$WorkResponseCopyWithImpl<$Res, WorkResponse>;
  @useResult
  $Res call(
      {String? conversationIdentifier,
      List<PresenceChatMessage>? messages,
      bool canTransfer,
      bool canRaiseFlag});
}

/// @nodoc
class _$WorkResponseCopyWithImpl<$Res, $Val extends WorkResponse>
    implements $WorkResponseCopyWith<$Res> {
  _$WorkResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversationIdentifier = freezed,
    Object? messages = freezed,
    Object? canTransfer = null,
    Object? canRaiseFlag = null,
  }) {
    return _then(_value.copyWith(
      conversationIdentifier: freezed == conversationIdentifier
          ? _value.conversationIdentifier
          : conversationIdentifier // ignore: cast_nullable_to_non_nullable
              as String?,
      messages: freezed == messages
          ? _value.messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<PresenceChatMessage>?,
      canTransfer: null == canTransfer
          ? _value.canTransfer
          : canTransfer // ignore: cast_nullable_to_non_nullable
              as bool,
      canRaiseFlag: null == canRaiseFlag
          ? _value.canRaiseFlag
          : canRaiseFlag // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkResponseImplCopyWith<$Res>
    implements $WorkResponseCopyWith<$Res> {
  factory _$$WorkResponseImplCopyWith(
          _$WorkResponseImpl value, $Res Function(_$WorkResponseImpl) then) =
      __$$WorkResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? conversationIdentifier,
      List<PresenceChatMessage>? messages,
      bool canTransfer,
      bool canRaiseFlag});
}

/// @nodoc
class __$$WorkResponseImplCopyWithImpl<$Res>
    extends _$WorkResponseCopyWithImpl<$Res, _$WorkResponseImpl>
    implements _$$WorkResponseImplCopyWith<$Res> {
  __$$WorkResponseImplCopyWithImpl(
      _$WorkResponseImpl _value, $Res Function(_$WorkResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversationIdentifier = freezed,
    Object? messages = freezed,
    Object? canTransfer = null,
    Object? canRaiseFlag = null,
  }) {
    return _then(_$WorkResponseImpl(
      conversationIdentifier: freezed == conversationIdentifier
          ? _value.conversationIdentifier
          : conversationIdentifier // ignore: cast_nullable_to_non_nullable
              as String?,
      messages: freezed == messages
          ? _value._messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<PresenceChatMessage>?,
      canTransfer: null == canTransfer
          ? _value.canTransfer
          : canTransfer // ignore: cast_nullable_to_non_nullable
              as bool,
      canRaiseFlag: null == canRaiseFlag
          ? _value.canRaiseFlag
          : canRaiseFlag // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkResponseImpl implements _WorkResponse {
  const _$WorkResponseImpl(
      {this.conversationIdentifier,
      final List<PresenceChatMessage>? messages,
      this.canTransfer = false,
      this.canRaiseFlag = false})
      : _messages = messages;

  factory _$WorkResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkResponseImplFromJson(json);

  @override
  final String? conversationIdentifier;
  final List<PresenceChatMessage>? _messages;
  @override
  List<PresenceChatMessage>? get messages {
    final value = _messages;
    if (value == null) return null;
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool canTransfer;
  @override
  @JsonKey()
  final bool canRaiseFlag;

  @override
  String toString() {
    return 'WorkResponse(conversationIdentifier: $conversationIdentifier, messages: $messages, canTransfer: $canTransfer, canRaiseFlag: $canRaiseFlag)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkResponseImpl &&
            (identical(other.conversationIdentifier, conversationIdentifier) ||
                other.conversationIdentifier == conversationIdentifier) &&
            const DeepCollectionEquality().equals(other._messages, _messages) &&
            (identical(other.canTransfer, canTransfer) ||
                other.canTransfer == canTransfer) &&
            (identical(other.canRaiseFlag, canRaiseFlag) ||
                other.canRaiseFlag == canRaiseFlag));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      conversationIdentifier,
      const DeepCollectionEquality().hash(_messages),
      canTransfer,
      canRaiseFlag);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkResponseImplCopyWith<_$WorkResponseImpl> get copyWith =>
      __$$WorkResponseImplCopyWithImpl<_$WorkResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkResponseImplToJson(
      this,
    );
  }
}

abstract class _WorkResponse implements WorkResponse {
  const factory _WorkResponse(
      {final String? conversationIdentifier,
      final List<PresenceChatMessage>? messages,
      final bool canTransfer,
      final bool canRaiseFlag}) = _$WorkResponseImpl;

  factory _WorkResponse.fromJson(Map<String, dynamic> json) =
      _$WorkResponseImpl.fromJson;

  @override
  String? get conversationIdentifier;
  @override
  List<PresenceChatMessage>? get messages;
  @override
  bool get canTransfer;
  @override
  bool get canRaiseFlag;
  @override
  @JsonKey(ignore: true)
  _$$WorkResponseImplCopyWith<_$WorkResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
