// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_definition_parameters.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingDefinitionParameters _$MessagingDefinitionParametersFromJson(
    Map<String, dynamic> json) {
  return _MessagingDefinitionParameters.fromJson(json);
}

/// @nodoc
mixin _$MessagingDefinitionParameters {
  @ParseSfIdConverter()
  SfId get id => throw _privateConstructorUsedError;
  List<String>? get parameters => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingDefinitionParametersCopyWith<MessagingDefinitionParameters>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingDefinitionParametersCopyWith<$Res> {
  factory $MessagingDefinitionParametersCopyWith(
          MessagingDefinitionParameters value,
          $Res Function(MessagingDefinitionParameters) then) =
      _$MessagingDefinitionParametersCopyWithImpl<$Res,
          MessagingDefinitionParameters>;
  @useResult
  $Res call({@ParseSfIdConverter() SfId id, List<String>? parameters});

  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class _$MessagingDefinitionParametersCopyWithImpl<$Res,
        $Val extends MessagingDefinitionParameters>
    implements $MessagingDefinitionParametersCopyWith<$Res> {
  _$MessagingDefinitionParametersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? parameters = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      parameters: freezed == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_value.id, (value) {
      return _then(_value.copyWith(id: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingDefinitionParametersImplCopyWith<$Res>
    implements $MessagingDefinitionParametersCopyWith<$Res> {
  factory _$$MessagingDefinitionParametersImplCopyWith(
          _$MessagingDefinitionParametersImpl value,
          $Res Function(_$MessagingDefinitionParametersImpl) then) =
      __$$MessagingDefinitionParametersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@ParseSfIdConverter() SfId id, List<String>? parameters});

  @override
  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class __$$MessagingDefinitionParametersImplCopyWithImpl<$Res>
    extends _$MessagingDefinitionParametersCopyWithImpl<$Res,
        _$MessagingDefinitionParametersImpl>
    implements _$$MessagingDefinitionParametersImplCopyWith<$Res> {
  __$$MessagingDefinitionParametersImplCopyWithImpl(
      _$MessagingDefinitionParametersImpl _value,
      $Res Function(_$MessagingDefinitionParametersImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? parameters = freezed,
  }) {
    return _then(_$MessagingDefinitionParametersImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      parameters: freezed == parameters
          ? _value._parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingDefinitionParametersImpl
    implements _MessagingDefinitionParameters {
  const _$MessagingDefinitionParametersImpl(
      {@ParseSfIdConverter() required this.id, final List<String>? parameters})
      : _parameters = parameters;

  factory _$MessagingDefinitionParametersImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MessagingDefinitionParametersImplFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId id;
  final List<String>? _parameters;
  @override
  List<String>? get parameters {
    final value = _parameters;
    if (value == null) return null;
    if (_parameters is EqualUnmodifiableListView) return _parameters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MessagingDefinitionParameters(id: $id, parameters: $parameters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingDefinitionParametersImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other._parameters, _parameters));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_parameters));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingDefinitionParametersImplCopyWith<
          _$MessagingDefinitionParametersImpl>
      get copyWith => __$$MessagingDefinitionParametersImplCopyWithImpl<
          _$MessagingDefinitionParametersImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingDefinitionParametersImplToJson(
      this,
    );
  }
}

abstract class _MessagingDefinitionParameters
    implements MessagingDefinitionParameters {
  const factory _MessagingDefinitionParameters(
      {@ParseSfIdConverter() required final SfId id,
      final List<String>? parameters}) = _$MessagingDefinitionParametersImpl;

  factory _MessagingDefinitionParameters.fromJson(Map<String, dynamic> json) =
      _$MessagingDefinitionParametersImpl.fromJson;

  @override
  @ParseSfIdConverter()
  SfId get id;
  @override
  List<String>? get parameters;
  @override
  @JsonKey(ignore: true)
  _$$MessagingDefinitionParametersImplCopyWith<
          _$MessagingDefinitionParametersImpl>
      get copyWith => throw _privateConstructorUsedError;
}
