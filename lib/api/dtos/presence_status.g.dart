// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'presence_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PresenceStatusImpl _$$PresenceStatusImplFromJson(Map<String, dynamic> json) =>
    _$PresenceStatusImpl(
      id: json['id'] as String,
      label: json['label'] as String,
      statusOption:
          $enumDecode(_$PresenceStatusOptionEnumMap, json['statusOption']),
    );

Map<String, dynamic> _$$PresenceStatusImplToJson(
        _$PresenceStatusImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'label': instance.label,
      'statusOption': _$PresenceStatusOptionEnumMap[instance.statusOption]!,
    };

const _$PresenceStatusOptionEnumMap = {
  PresenceStatusOption.online: 'online',
  PresenceStatusOption.offline: 'offline',
  PresenceStatusOption.busy: 'busy',
};
