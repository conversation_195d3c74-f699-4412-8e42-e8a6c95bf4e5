// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sessions_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SessionsResponseImpl _$$SessionsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SessionsResponseImpl(
      sessionToken: json['sessionToken'] as String,
      sessionId: json['sessionId'] as String,
      expirationTime: (json['expirationTime'] as num).toInt(),
      presenceStatuses: (json['presenceStatuses'] as List<dynamic>?)
          ?.map((e) => PresenceStatus.fromJson(e as Map<String, dynamic>))
          .toList(),
      presenceStatusId: json['presenceStatusId'] as String?,
      scrtAccessToken: json['scrtAccessToken'] as String?,
      scrtHost: json['scrtHost'] as String?,
      webSocketUrl: json['webSocketUrl'] as String,
    );

Map<String, dynamic> _$$SessionsResponseImplToJson(
        _$SessionsResponseImpl instance) =>
    <String, dynamic>{
      'sessionToken': instance.sessionToken,
      'sessionId': instance.sessionId,
      'expirationTime': instance.expirationTime,
      'presenceStatuses':
          instance.presenceStatuses?.map((e) => e.toJson()).toList(),
      'presenceStatusId': instance.presenceStatusId,
      'scrtAccessToken': instance.scrtAccessToken,
      'scrtHost': instance.scrtHost,
      'webSocketUrl': instance.webSocketUrl,
    };
