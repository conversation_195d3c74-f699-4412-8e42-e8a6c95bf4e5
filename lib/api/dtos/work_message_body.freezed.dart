// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_message_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkMessageBody _$WorkMessageBodyFromJson(Map<String, dynamic> json) {
  return _WorkMessageBody.fromJson(json);
}

/// @nodoc
mixin _$WorkMessageBody {
  String get messageId => throw _privateConstructorUsedError;
  String? get messageBody => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get workTargetId => throw _privateConstructorUsedError;
  List<MessageAttachment> get attachments => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WorkMessageBodyCopyWith<WorkMessageBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkMessageBodyCopyWith<$Res> {
  factory $WorkMessageBodyCopyWith(
          WorkMessageBody value, $Res Function(WorkMessageBody) then) =
      _$WorkMessageBodyCopyWithImpl<$Res, WorkMessageBody>;
  @useResult
  $Res call(
      {String messageId,
      String? messageBody,
      @ParseSfIdConverter() SfId? workTargetId,
      List<MessageAttachment> attachments});

  $SfIdCopyWith<$Res>? get workTargetId;
}

/// @nodoc
class _$WorkMessageBodyCopyWithImpl<$Res, $Val extends WorkMessageBody>
    implements $WorkMessageBodyCopyWith<$Res> {
  _$WorkMessageBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messageBody = freezed,
    Object? workTargetId = freezed,
    Object? attachments = null,
  }) {
    return _then(_value.copyWith(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messageBody: freezed == messageBody
          ? _value.messageBody
          : messageBody // ignore: cast_nullable_to_non_nullable
              as String?,
      workTargetId: freezed == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      attachments: null == attachments
          ? _value.attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<MessageAttachment>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_value.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.workTargetId!, (value) {
      return _then(_value.copyWith(workTargetId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WorkMessageBodyImplCopyWith<$Res>
    implements $WorkMessageBodyCopyWith<$Res> {
  factory _$$WorkMessageBodyImplCopyWith(_$WorkMessageBodyImpl value,
          $Res Function(_$WorkMessageBodyImpl) then) =
      __$$WorkMessageBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String messageId,
      String? messageBody,
      @ParseSfIdConverter() SfId? workTargetId,
      List<MessageAttachment> attachments});

  @override
  $SfIdCopyWith<$Res>? get workTargetId;
}

/// @nodoc
class __$$WorkMessageBodyImplCopyWithImpl<$Res>
    extends _$WorkMessageBodyCopyWithImpl<$Res, _$WorkMessageBodyImpl>
    implements _$$WorkMessageBodyImplCopyWith<$Res> {
  __$$WorkMessageBodyImplCopyWithImpl(
      _$WorkMessageBodyImpl _value, $Res Function(_$WorkMessageBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messageBody = freezed,
    Object? workTargetId = freezed,
    Object? attachments = null,
  }) {
    return _then(_$WorkMessageBodyImpl(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messageBody: freezed == messageBody
          ? _value.messageBody
          : messageBody // ignore: cast_nullable_to_non_nullable
              as String?,
      workTargetId: freezed == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      attachments: null == attachments
          ? _value._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<MessageAttachment>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkMessageBodyImpl implements _WorkMessageBody {
  const _$WorkMessageBodyImpl(
      {required this.messageId,
      this.messageBody,
      @ParseSfIdConverter() this.workTargetId,
      final List<MessageAttachment> attachments = const <MessageAttachment>[]})
      : _attachments = attachments;

  factory _$WorkMessageBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkMessageBodyImplFromJson(json);

  @override
  final String messageId;
  @override
  final String? messageBody;
  @override
  @ParseSfIdConverter()
  final SfId? workTargetId;
  final List<MessageAttachment> _attachments;
  @override
  @JsonKey()
  List<MessageAttachment> get attachments {
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachments);
  }

  @override
  String toString() {
    return 'WorkMessageBody(messageId: $messageId, messageBody: $messageBody, workTargetId: $workTargetId, attachments: $attachments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkMessageBodyImpl &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messageBody, messageBody) ||
                other.messageBody == messageBody) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messageBody,
      workTargetId, const DeepCollectionEquality().hash(_attachments));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkMessageBodyImplCopyWith<_$WorkMessageBodyImpl> get copyWith =>
      __$$WorkMessageBodyImplCopyWithImpl<_$WorkMessageBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkMessageBodyImplToJson(
      this,
    );
  }
}

abstract class _WorkMessageBody implements WorkMessageBody {
  const factory _WorkMessageBody(
      {required final String messageId,
      final String? messageBody,
      @ParseSfIdConverter() final SfId? workTargetId,
      final List<MessageAttachment> attachments}) = _$WorkMessageBodyImpl;

  factory _WorkMessageBody.fromJson(Map<String, dynamic> json) =
      _$WorkMessageBodyImpl.fromJson;

  @override
  String get messageId;
  @override
  String? get messageBody;
  @override
  @ParseSfIdConverter()
  SfId? get workTargetId;
  @override
  List<MessageAttachment> get attachments;
  @override
  @JsonKey(ignore: true)
  _$$WorkMessageBodyImplCopyWith<_$WorkMessageBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
