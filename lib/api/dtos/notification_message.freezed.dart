// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationMessage _$NotificationMessageFromJson(Map<String, dynamic> json) {
  return _NotificationMessage.fromJson(json);
}

/// @nodoc
mixin _$NotificationMessage {
  @TimestampIntConverter()
  int get timestamp => throw _privateConstructorUsedError;
  String get notificationId => throw _privateConstructorUsedError;
  String get sessionId => throw _privateConstructorUsedError;
  String get messageCategory => throw _privateConstructorUsedError;
  Map<String, dynamic> get payload => throw _privateConstructorUsedError;
  bool get sentToWebSocket => throw _privateConstructorUsedError;
  bool get pushed => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationMessageCopyWith<NotificationMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationMessageCopyWith<$Res> {
  factory $NotificationMessageCopyWith(
          NotificationMessage value, $Res Function(NotificationMessage) then) =
      _$NotificationMessageCopyWithImpl<$Res, NotificationMessage>;
  @useResult
  $Res call(
      {@TimestampIntConverter() int timestamp,
      String notificationId,
      String sessionId,
      String messageCategory,
      Map<String, dynamic> payload,
      bool sentToWebSocket,
      bool pushed});
}

/// @nodoc
class _$NotificationMessageCopyWithImpl<$Res, $Val extends NotificationMessage>
    implements $NotificationMessageCopyWith<$Res> {
  _$NotificationMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? sessionId = null,
    Object? messageCategory = null,
    Object? payload = null,
    Object? sentToWebSocket = null,
    Object? pushed = null,
  }) {
    return _then(_value.copyWith(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      messageCategory: null == messageCategory
          ? _value.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      sentToWebSocket: null == sentToWebSocket
          ? _value.sentToWebSocket
          : sentToWebSocket // ignore: cast_nullable_to_non_nullable
              as bool,
      pushed: null == pushed
          ? _value.pushed
          : pushed // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationMessageImplCopyWith<$Res>
    implements $NotificationMessageCopyWith<$Res> {
  factory _$$NotificationMessageImplCopyWith(_$NotificationMessageImpl value,
          $Res Function(_$NotificationMessageImpl) then) =
      __$$NotificationMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@TimestampIntConverter() int timestamp,
      String notificationId,
      String sessionId,
      String messageCategory,
      Map<String, dynamic> payload,
      bool sentToWebSocket,
      bool pushed});
}

/// @nodoc
class __$$NotificationMessageImplCopyWithImpl<$Res>
    extends _$NotificationMessageCopyWithImpl<$Res, _$NotificationMessageImpl>
    implements _$$NotificationMessageImplCopyWith<$Res> {
  __$$NotificationMessageImplCopyWithImpl(_$NotificationMessageImpl _value,
      $Res Function(_$NotificationMessageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? sessionId = null,
    Object? messageCategory = null,
    Object? payload = null,
    Object? sentToWebSocket = null,
    Object? pushed = null,
  }) {
    return _then(_$NotificationMessageImpl(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      messageCategory: null == messageCategory
          ? _value.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _value._payload
          : payload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      sentToWebSocket: null == sentToWebSocket
          ? _value.sentToWebSocket
          : sentToWebSocket // ignore: cast_nullable_to_non_nullable
              as bool,
      pushed: null == pushed
          ? _value.pushed
          : pushed // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationMessageImpl implements _NotificationMessage {
  const _$NotificationMessageImpl(
      {@TimestampIntConverter() required this.timestamp,
      required this.notificationId,
      required this.sessionId,
      required this.messageCategory,
      required final Map<String, dynamic> payload,
      required this.sentToWebSocket,
      required this.pushed})
      : _payload = payload;

  factory _$NotificationMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationMessageImplFromJson(json);

  @override
  @TimestampIntConverter()
  final int timestamp;
  @override
  final String notificationId;
  @override
  final String sessionId;
  @override
  final String messageCategory;
  final Map<String, dynamic> _payload;
  @override
  Map<String, dynamic> get payload {
    if (_payload is EqualUnmodifiableMapView) return _payload;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_payload);
  }

  @override
  final bool sentToWebSocket;
  @override
  final bool pushed;

  @override
  String toString() {
    return 'NotificationMessage(timestamp: $timestamp, notificationId: $notificationId, sessionId: $sessionId, messageCategory: $messageCategory, payload: $payload, sentToWebSocket: $sentToWebSocket, pushed: $pushed)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationMessageImpl &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            const DeepCollectionEquality().equals(other._payload, _payload) &&
            (identical(other.sentToWebSocket, sentToWebSocket) ||
                other.sentToWebSocket == sentToWebSocket) &&
            (identical(other.pushed, pushed) || other.pushed == pushed));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      sessionId,
      messageCategory,
      const DeepCollectionEquality().hash(_payload),
      sentToWebSocket,
      pushed);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationMessageImplCopyWith<_$NotificationMessageImpl> get copyWith =>
      __$$NotificationMessageImplCopyWithImpl<_$NotificationMessageImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationMessageImplToJson(
      this,
    );
  }
}

abstract class _NotificationMessage implements NotificationMessage {
  const factory _NotificationMessage(
      {@TimestampIntConverter() required final int timestamp,
      required final String notificationId,
      required final String sessionId,
      required final String messageCategory,
      required final Map<String, dynamic> payload,
      required final bool sentToWebSocket,
      required final bool pushed}) = _$NotificationMessageImpl;

  factory _NotificationMessage.fromJson(Map<String, dynamic> json) =
      _$NotificationMessageImpl.fromJson;

  @override
  @TimestampIntConverter()
  int get timestamp;
  @override
  String get notificationId;
  @override
  String get sessionId;
  @override
  String get messageCategory;
  @override
  Map<String, dynamic> get payload;
  @override
  bool get sentToWebSocket;
  @override
  bool get pushed;
  @override
  @JsonKey(ignore: true)
  _$$NotificationMessageImplCopyWith<_$NotificationMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
