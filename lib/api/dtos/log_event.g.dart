// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LogEventImpl _$$LogEventImplFromJson(Map<String, dynamic> json) =>
    _$LogEventImpl(
      timestamp: (json['timestamp'] as num).toInt(),
      packageName: json['packageName'] as String,
      level: json['level'] as String,
      metadata: LogMetadata.fromJson(json['metadata'] as Map<String, dynamic>),
      message: json['message'] as String,
    );

Map<String, dynamic> _$$LogEventImplToJson(_$LogEventImpl instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp,
      'packageName': instance.packageName,
      'level': instance.level,
      'metadata': instance.metadata.toJson(),
      'message': instance.message,
    };
