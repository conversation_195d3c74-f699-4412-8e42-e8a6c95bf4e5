// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_metadata.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LogMetadataImpl _$$LogMetadataImplFromJson(Map<String, dynamic> json) =>
    _$LogMetadataImpl(
      buildNumber: json['buildNumber'] as String?,
      versionNumber: json['versionNumber'] as String?,
      osVersion: json['osVersion'] as String?,
      osName: $enumDecodeNullable(_$OSNameEnumMap, json['osName']),
      manufacturer: json['manufacturer'] as String?,
      model: json['model'] as String?,
      deviceType: $enumDecodeNullable(_$DeviceTypeEnumMap, json['deviceType']),
      carrier: json['carrier'] as String?,
      batteryLevel: (json['batteryLevel'] as num?)?.toInt(),
      userId: json['userId'] as String?,
      sessionId: json['sessionId'] as String?,
      debugMode: json['debugMode'] as bool?,
      profileMode: json['profileMode'] as bool?,
      releaseMode: json['releaseMode'] as bool?,
    );

Map<String, dynamic> _$$LogMetadataImplToJson(_$LogMetadataImpl instance) =>
    <String, dynamic>{
      'buildNumber': instance.buildNumber,
      'versionNumber': instance.versionNumber,
      'osVersion': instance.osVersion,
      'osName': _$OSNameEnumMap[instance.osName],
      'manufacturer': instance.manufacturer,
      'model': instance.model,
      'deviceType': _$DeviceTypeEnumMap[instance.deviceType],
      'carrier': instance.carrier,
      'batteryLevel': instance.batteryLevel,
      'userId': instance.userId,
      'sessionId': instance.sessionId,
      'debugMode': instance.debugMode,
      'profileMode': instance.profileMode,
      'releaseMode': instance.releaseMode,
    };

const _$OSNameEnumMap = {
  OSName.ios: 'ios',
  OSName.android: 'android',
};

const _$DeviceTypeEnumMap = {
  DeviceType.physical: 'physical',
  DeviceType.simulator: 'simulator',
};
