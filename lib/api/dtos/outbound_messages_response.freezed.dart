// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_messages_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OutboundMessagesResponse _$OutboundMessagesResponseFromJson(
    Map<String, dynamic> json) {
  return _OutboundMessagesResponse.fromJson(json);
}

/// @nodoc
mixin _$OutboundMessagesResponse {
  List<MessageIdAndSessionId>? get messageAndSessionIds =>
      throw _privateConstructorUsedError;
  List<SendFailure>? get failures => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OutboundMessagesResponseCopyWith<OutboundMessagesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutboundMessagesResponseCopyWith<$Res> {
  factory $OutboundMessagesResponseCopyWith(OutboundMessagesResponse value,
          $Res Function(OutboundMessagesResponse) then) =
      _$OutboundMessagesResponseCopyWithImpl<$Res, OutboundMessagesResponse>;
  @useResult
  $Res call(
      {List<MessageIdAndSessionId>? messageAndSessionIds,
      List<SendFailure>? failures});
}

/// @nodoc
class _$OutboundMessagesResponseCopyWithImpl<$Res,
        $Val extends OutboundMessagesResponse>
    implements $OutboundMessagesResponseCopyWith<$Res> {
  _$OutboundMessagesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageAndSessionIds = freezed,
    Object? failures = freezed,
  }) {
    return _then(_value.copyWith(
      messageAndSessionIds: freezed == messageAndSessionIds
          ? _value.messageAndSessionIds
          : messageAndSessionIds // ignore: cast_nullable_to_non_nullable
              as List<MessageIdAndSessionId>?,
      failures: freezed == failures
          ? _value.failures
          : failures // ignore: cast_nullable_to_non_nullable
              as List<SendFailure>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OutboundMessagesResponseImplCopyWith<$Res>
    implements $OutboundMessagesResponseCopyWith<$Res> {
  factory _$$OutboundMessagesResponseImplCopyWith(
          _$OutboundMessagesResponseImpl value,
          $Res Function(_$OutboundMessagesResponseImpl) then) =
      __$$OutboundMessagesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<MessageIdAndSessionId>? messageAndSessionIds,
      List<SendFailure>? failures});
}

/// @nodoc
class __$$OutboundMessagesResponseImplCopyWithImpl<$Res>
    extends _$OutboundMessagesResponseCopyWithImpl<$Res,
        _$OutboundMessagesResponseImpl>
    implements _$$OutboundMessagesResponseImplCopyWith<$Res> {
  __$$OutboundMessagesResponseImplCopyWithImpl(
      _$OutboundMessagesResponseImpl _value,
      $Res Function(_$OutboundMessagesResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageAndSessionIds = freezed,
    Object? failures = freezed,
  }) {
    return _then(_$OutboundMessagesResponseImpl(
      messageAndSessionIds: freezed == messageAndSessionIds
          ? _value._messageAndSessionIds
          : messageAndSessionIds // ignore: cast_nullable_to_non_nullable
              as List<MessageIdAndSessionId>?,
      failures: freezed == failures
          ? _value._failures
          : failures // ignore: cast_nullable_to_non_nullable
              as List<SendFailure>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OutboundMessagesResponseImpl implements _OutboundMessagesResponse {
  const _$OutboundMessagesResponseImpl(
      {final List<MessageIdAndSessionId>? messageAndSessionIds,
      final List<SendFailure>? failures})
      : _messageAndSessionIds = messageAndSessionIds,
        _failures = failures;

  factory _$OutboundMessagesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OutboundMessagesResponseImplFromJson(json);

  final List<MessageIdAndSessionId>? _messageAndSessionIds;
  @override
  List<MessageIdAndSessionId>? get messageAndSessionIds {
    final value = _messageAndSessionIds;
    if (value == null) return null;
    if (_messageAndSessionIds is EqualUnmodifiableListView)
      return _messageAndSessionIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<SendFailure>? _failures;
  @override
  List<SendFailure>? get failures {
    final value = _failures;
    if (value == null) return null;
    if (_failures is EqualUnmodifiableListView) return _failures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'OutboundMessagesResponse(messageAndSessionIds: $messageAndSessionIds, failures: $failures)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OutboundMessagesResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._messageAndSessionIds, _messageAndSessionIds) &&
            const DeepCollectionEquality().equals(other._failures, _failures));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_messageAndSessionIds),
      const DeepCollectionEquality().hash(_failures));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OutboundMessagesResponseImplCopyWith<_$OutboundMessagesResponseImpl>
      get copyWith => __$$OutboundMessagesResponseImplCopyWithImpl<
          _$OutboundMessagesResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OutboundMessagesResponseImplToJson(
      this,
    );
  }
}

abstract class _OutboundMessagesResponse implements OutboundMessagesResponse {
  const factory _OutboundMessagesResponse(
      {final List<MessageIdAndSessionId>? messageAndSessionIds,
      final List<SendFailure>? failures}) = _$OutboundMessagesResponseImpl;

  factory _OutboundMessagesResponse.fromJson(Map<String, dynamic> json) =
      _$OutboundMessagesResponseImpl.fromJson;

  @override
  List<MessageIdAndSessionId>? get messageAndSessionIds;
  @override
  List<SendFailure>? get failures;
  @override
  @JsonKey(ignore: true)
  _$$OutboundMessagesResponseImplCopyWith<_$OutboundMessagesResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
