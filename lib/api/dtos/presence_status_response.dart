import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/presence_configuration.dart';

part 'presence_status_response.freezed.dart';
part 'presence_status_response.g.dart';

@freezed
class PresenceStatusResponse with _$PresenceStatusResponse {
  const factory PresenceStatusResponse({
    PresenceConfiguration? presenceConfiguration,
    String? scrtAccessToken
  }) = _PresenceStatusResponse;

  factory PresenceStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$PresenceStatusResponseFromJson(json);
}
