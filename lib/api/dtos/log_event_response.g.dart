// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'log_event_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LogEventResponseImpl _$$LogEventResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$LogEventResponseImpl(
      success: json['success'] as bool,
      tooNewStartIndex: (json['tooNewStartIndex'] as num?)?.toInt(),
      tooOldEndIndex: (json['tooOldEndIndex'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$LogEventResponseImplToJson(
        _$LogEventResponseImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'tooNewStartIndex': instance.tooNewStartIndex,
      'tooOldEndIndex': instance.tooOldEndIndex,
    };
