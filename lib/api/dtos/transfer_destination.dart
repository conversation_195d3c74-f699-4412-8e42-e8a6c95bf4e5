import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'transfer_destination.freezed.dart';
part 'transfer_destination.g.dart';

@freezed
class TransferDestination with _$TransferDestination {
  const factory TransferDestination({
    @ParseSfIdConverter() required SfId id,
    required String label,
    @Default(null) String? smallPhotoUrl,

    /// seconds
    @Default(null) int? estimatedWait,
  }) = _TransferDestination;

  factory TransferDestination.fromJson(Map<String, dynamic> json) =>
      _$TransferDestinationFromJson(json);
}
