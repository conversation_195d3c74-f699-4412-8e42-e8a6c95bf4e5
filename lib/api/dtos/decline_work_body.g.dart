// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'decline_work_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeclineWorkBodyImpl _$$DeclineWorkBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$DeclineWorkBodyImpl(
      requestId: json['requestId'] as String,
      workId: const ParseSfIdConverter().from<PERSON>son(json['workId']),
      workTargetId: const ParseSfIdConverter().from<PERSON>son(json['workTargetId']),
      declineReason: json['declineReason'] as String?,
    );

Map<String, dynamic> _$$DeclineWorkBodyImplToJson(
        _$DeclineWorkBodyImpl instance) =>
    <String, dynamic>{
      'requestId': instance.requestId,
      'workId': const ParseSfIdConverter().toJson(instance.workId),
      'workTargetId': const ParseSfIdConverter().toJson(instance.workTargetId),
      'declineReason': instance.declineReason,
    };
