// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_message_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationMessageResponse _$NotificationMessageResponseFromJson(
    Map<String, dynamic> json) {
  return _NotificationMessageResponse.fromJson(json);
}

/// @nodoc
mixin _$NotificationMessageResponse {
  String? get message => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationMessageResponseCopyWith<NotificationMessageResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationMessageResponseCopyWith<$Res> {
  factory $NotificationMessageResponseCopyWith(
          NotificationMessageResponse value,
          $Res Function(NotificationMessageResponse) then) =
      _$NotificationMessageResponseCopyWithImpl<$Res,
          NotificationMessageResponse>;
  @useResult
  $Res call({String? message});
}

/// @nodoc
class _$NotificationMessageResponseCopyWithImpl<$Res,
        $Val extends NotificationMessageResponse>
    implements $NotificationMessageResponseCopyWith<$Res> {
  _$NotificationMessageResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationMessageResponseImplCopyWith<$Res>
    implements $NotificationMessageResponseCopyWith<$Res> {
  factory _$$NotificationMessageResponseImplCopyWith(
          _$NotificationMessageResponseImpl value,
          $Res Function(_$NotificationMessageResponseImpl) then) =
      __$$NotificationMessageResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$NotificationMessageResponseImplCopyWithImpl<$Res>
    extends _$NotificationMessageResponseCopyWithImpl<$Res,
        _$NotificationMessageResponseImpl>
    implements _$$NotificationMessageResponseImplCopyWith<$Res> {
  __$$NotificationMessageResponseImplCopyWithImpl(
      _$NotificationMessageResponseImpl _value,
      $Res Function(_$NotificationMessageResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
  }) {
    return _then(_$NotificationMessageResponseImpl(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationMessageResponseImpl
    implements _NotificationMessageResponse {
  const _$NotificationMessageResponseImpl({required this.message});

  factory _$NotificationMessageResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$NotificationMessageResponseImplFromJson(json);

  @override
  final String? message;

  @override
  String toString() {
    return 'NotificationMessageResponse(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationMessageResponseImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationMessageResponseImplCopyWith<_$NotificationMessageResponseImpl>
      get copyWith => __$$NotificationMessageResponseImplCopyWithImpl<
          _$NotificationMessageResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationMessageResponseImplToJson(
      this,
    );
  }
}

abstract class _NotificationMessageResponse
    implements NotificationMessageResponse {
  const factory _NotificationMessageResponse({required final String? message}) =
      _$NotificationMessageResponseImpl;

  factory _NotificationMessageResponse.fromJson(Map<String, dynamic> json) =
      _$NotificationMessageResponseImpl.fromJson;

  @override
  String? get message;
  @override
  @JsonKey(ignore: true)
  _$$NotificationMessageResponseImplCopyWith<_$NotificationMessageResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
