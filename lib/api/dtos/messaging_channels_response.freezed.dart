// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_channels_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingChannelsResponse _$MessagingChannelsResponseFromJson(
    Map<String, dynamic> json) {
  return _MessagingChannelsResponse.fromJson(json);
}

/// @nodoc
mixin _$MessagingChannelsResponse {
  List<MessagingChannelEntry> get channelSelectionEntries =>
      throw _privateConstructorUsedError;
  List<MessagingChannelEntry> get otherChannels =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingChannelsResponseCopyWith<MessagingChannelsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingChannelsResponseCopyWith<$Res> {
  factory $MessagingChannelsResponseCopyWith(MessagingChannelsResponse value,
          $Res Function(MessagingChannelsResponse) then) =
      _$MessagingChannelsResponseCopyWithImpl<$Res, MessagingChannelsResponse>;
  @useResult
  $Res call(
      {List<MessagingChannelEntry> channelSelectionEntries,
      List<MessagingChannelEntry> otherChannels});
}

/// @nodoc
class _$MessagingChannelsResponseCopyWithImpl<$Res,
        $Val extends MessagingChannelsResponse>
    implements $MessagingChannelsResponseCopyWith<$Res> {
  _$MessagingChannelsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelSelectionEntries = null,
    Object? otherChannels = null,
  }) {
    return _then(_value.copyWith(
      channelSelectionEntries: null == channelSelectionEntries
          ? _value.channelSelectionEntries
          : channelSelectionEntries // ignore: cast_nullable_to_non_nullable
              as List<MessagingChannelEntry>,
      otherChannels: null == otherChannels
          ? _value.otherChannels
          : otherChannels // ignore: cast_nullable_to_non_nullable
              as List<MessagingChannelEntry>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessagingChannelsResponseImplCopyWith<$Res>
    implements $MessagingChannelsResponseCopyWith<$Res> {
  factory _$$MessagingChannelsResponseImplCopyWith(
          _$MessagingChannelsResponseImpl value,
          $Res Function(_$MessagingChannelsResponseImpl) then) =
      __$$MessagingChannelsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<MessagingChannelEntry> channelSelectionEntries,
      List<MessagingChannelEntry> otherChannels});
}

/// @nodoc
class __$$MessagingChannelsResponseImplCopyWithImpl<$Res>
    extends _$MessagingChannelsResponseCopyWithImpl<$Res,
        _$MessagingChannelsResponseImpl>
    implements _$$MessagingChannelsResponseImplCopyWith<$Res> {
  __$$MessagingChannelsResponseImplCopyWithImpl(
      _$MessagingChannelsResponseImpl _value,
      $Res Function(_$MessagingChannelsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelSelectionEntries = null,
    Object? otherChannels = null,
  }) {
    return _then(_$MessagingChannelsResponseImpl(
      channelSelectionEntries: null == channelSelectionEntries
          ? _value._channelSelectionEntries
          : channelSelectionEntries // ignore: cast_nullable_to_non_nullable
              as List<MessagingChannelEntry>,
      otherChannels: null == otherChannels
          ? _value._otherChannels
          : otherChannels // ignore: cast_nullable_to_non_nullable
              as List<MessagingChannelEntry>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingChannelsResponseImpl extends _MessagingChannelsResponse {
  const _$MessagingChannelsResponseImpl(
      {required final List<MessagingChannelEntry> channelSelectionEntries,
      required final List<MessagingChannelEntry> otherChannels})
      : _channelSelectionEntries = channelSelectionEntries,
        _otherChannels = otherChannels,
        super._();

  factory _$MessagingChannelsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingChannelsResponseImplFromJson(json);

  final List<MessagingChannelEntry> _channelSelectionEntries;
  @override
  List<MessagingChannelEntry> get channelSelectionEntries {
    if (_channelSelectionEntries is EqualUnmodifiableListView)
      return _channelSelectionEntries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_channelSelectionEntries);
  }

  final List<MessagingChannelEntry> _otherChannels;
  @override
  List<MessagingChannelEntry> get otherChannels {
    if (_otherChannels is EqualUnmodifiableListView) return _otherChannels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_otherChannels);
  }

  @override
  String toString() {
    return 'MessagingChannelsResponse(channelSelectionEntries: $channelSelectionEntries, otherChannels: $otherChannels)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingChannelsResponseImpl &&
            const DeepCollectionEquality().equals(
                other._channelSelectionEntries, _channelSelectionEntries) &&
            const DeepCollectionEquality()
                .equals(other._otherChannels, _otherChannels));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_channelSelectionEntries),
      const DeepCollectionEquality().hash(_otherChannels));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingChannelsResponseImplCopyWith<_$MessagingChannelsResponseImpl>
      get copyWith => __$$MessagingChannelsResponseImplCopyWithImpl<
          _$MessagingChannelsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingChannelsResponseImplToJson(
      this,
    );
  }
}

abstract class _MessagingChannelsResponse extends MessagingChannelsResponse {
  const factory _MessagingChannelsResponse(
          {required final List<MessagingChannelEntry> channelSelectionEntries,
          required final List<MessagingChannelEntry> otherChannels}) =
      _$MessagingChannelsResponseImpl;
  const _MessagingChannelsResponse._() : super._();

  factory _MessagingChannelsResponse.fromJson(Map<String, dynamic> json) =
      _$MessagingChannelsResponseImpl.fromJson;

  @override
  List<MessagingChannelEntry> get channelSelectionEntries;
  @override
  List<MessagingChannelEntry> get otherChannels;
  @override
  @JsonKey(ignore: true)
  _$$MessagingChannelsResponseImplCopyWith<_$MessagingChannelsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
