import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_message_response.freezed.dart';
part 'notification_message_response.g.dart';

@freezed
class NotificationMessageResponse with _$NotificationMessageResponse {
  const factory NotificationMessageResponse({
    required String? message,
  }) = _NotificationMessageResponse;

  factory NotificationMessageResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationMessageResponseFromJson(json);
}
