// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging_definition.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessagingDefinitionImpl _$$MessagingDefinitionImplFromJson(
        Map<String, dynamic> json) =>
    _$MessagingDefinitionImpl(
      id: const ParseSfIdConverter().fromJson(json['id']),
      name: json['name'] as String?,
      internalName: json['internalName'] as String?,
      description: json['description'] as String? ?? '',
      hasRequiredParameters: json['hasRequiredParameters'] as bool?,
    );

Map<String, dynamic> _$$MessagingDefinitionImplToJson(
        _$MessagingDefinitionImpl instance) =>
    <String, dynamic>{
      'id': _$JsonConverterToJson<Object?, SfId>(
          instance.id, const ParseSfIdConverter().toJson),
      'name': instance.name,
      'internalName': instance.internalName,
      'description': instance.description,
      'hasRequiredParameters': instance.hasRequiredParameters,
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
