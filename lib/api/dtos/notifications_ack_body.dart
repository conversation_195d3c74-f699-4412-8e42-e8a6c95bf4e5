import 'package:freezed_annotation/freezed_annotation.dart';

part 'notifications_ack_body.freezed.dart';
part 'notifications_ack_body.g.dart';

@freezed
class NotificationsAckBody with _$NotificationsAckBody {
  const factory NotificationsAckBody({
    required List<String> notificationIds,
  }) = _NotificationsAckBody;

  factory NotificationsAckBody.fromJson(Map<String, dynamic> json) =>
      _$NotificationsAckBodyFromJson(json);
}
