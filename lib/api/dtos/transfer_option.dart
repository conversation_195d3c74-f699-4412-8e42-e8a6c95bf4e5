import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/transfer_messaging_session_body.dart';

import 'transfer_destination.dart';

part 'transfer_option.freezed.dart';
part 'transfer_option.g.dart';

@freezed
class TransferOption with _$TransferOption {
  const factory TransferOption({
    required TransferDestinationType destinationType,
    @Default(<TransferDestination>[]) List<TransferDestination> destinations,
  }) = _TransferOption;

  factory TransferOption.fromJson(Map<String, dynamic> json) =>
      _$TransferOptionFromJson(json);
}
