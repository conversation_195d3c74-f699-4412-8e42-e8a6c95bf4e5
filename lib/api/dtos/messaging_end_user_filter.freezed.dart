// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user_filter.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingEndUserFilter _$MessagingEndUserFilterFromJson(
    Map<String, dynamic> json) {
  return _MessagingEndUserFilter.fromJson(json);
}

/// @nodoc
mixin _$MessagingEndUserFilter {
  String get messageType => throw _privateConstructorUsedError;
  String get consentStatus => throw _privateConstructorUsedError;
  String get channelName => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get channelId => throw _privateConstructorUsedError;
  bool get enforceMessagingComponent => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingEndUserFilterCopyWith<MessagingEndUserFilter> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingEndUserFilterCopyWith<$Res> {
  factory $MessagingEndUserFilterCopyWith(MessagingEndUserFilter value,
          $Res Function(MessagingEndUserFilter) then) =
      _$MessagingEndUserFilterCopyWithImpl<$Res, MessagingEndUserFilter>;
  @useResult
  $Res call(
      {String messageType,
      String consentStatus,
      String channelName,
      @ParseSfIdConverter() SfId channelId,
      bool enforceMessagingComponent});

  $SfIdCopyWith<$Res> get channelId;
}

/// @nodoc
class _$MessagingEndUserFilterCopyWithImpl<$Res,
        $Val extends MessagingEndUserFilter>
    implements $MessagingEndUserFilterCopyWith<$Res> {
  _$MessagingEndUserFilterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageType = null,
    Object? consentStatus = null,
    Object? channelName = null,
    Object? channelId = null,
    Object? enforceMessagingComponent = null,
  }) {
    return _then(_value.copyWith(
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      consentStatus: null == consentStatus
          ? _value.consentStatus
          : consentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      channelName: null == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String,
      channelId: null == channelId
          ? _value.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      enforceMessagingComponent: null == enforceMessagingComponent
          ? _value.enforceMessagingComponent
          : enforceMessagingComponent // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get channelId {
    return $SfIdCopyWith<$Res>(_value.channelId, (value) {
      return _then(_value.copyWith(channelId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingEndUserFilterImplCopyWith<$Res>
    implements $MessagingEndUserFilterCopyWith<$Res> {
  factory _$$MessagingEndUserFilterImplCopyWith(
          _$MessagingEndUserFilterImpl value,
          $Res Function(_$MessagingEndUserFilterImpl) then) =
      __$$MessagingEndUserFilterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String messageType,
      String consentStatus,
      String channelName,
      @ParseSfIdConverter() SfId channelId,
      bool enforceMessagingComponent});

  @override
  $SfIdCopyWith<$Res> get channelId;
}

/// @nodoc
class __$$MessagingEndUserFilterImplCopyWithImpl<$Res>
    extends _$MessagingEndUserFilterCopyWithImpl<$Res,
        _$MessagingEndUserFilterImpl>
    implements _$$MessagingEndUserFilterImplCopyWith<$Res> {
  __$$MessagingEndUserFilterImplCopyWithImpl(
      _$MessagingEndUserFilterImpl _value,
      $Res Function(_$MessagingEndUserFilterImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageType = null,
    Object? consentStatus = null,
    Object? channelName = null,
    Object? channelId = null,
    Object? enforceMessagingComponent = null,
  }) {
    return _then(_$MessagingEndUserFilterImpl(
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      consentStatus: null == consentStatus
          ? _value.consentStatus
          : consentStatus // ignore: cast_nullable_to_non_nullable
              as String,
      channelName: null == channelName
          ? _value.channelName
          : channelName // ignore: cast_nullable_to_non_nullable
              as String,
      channelId: null == channelId
          ? _value.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      enforceMessagingComponent: null == enforceMessagingComponent
          ? _value.enforceMessagingComponent
          : enforceMessagingComponent // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingEndUserFilterImpl implements _MessagingEndUserFilter {
  const _$MessagingEndUserFilterImpl(
      {required this.messageType,
      required this.consentStatus,
      required this.channelName,
      @ParseSfIdConverter() required this.channelId,
      required this.enforceMessagingComponent});

  factory _$MessagingEndUserFilterImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingEndUserFilterImplFromJson(json);

  @override
  final String messageType;
  @override
  final String consentStatus;
  @override
  final String channelName;
  @override
  @ParseSfIdConverter()
  final SfId channelId;
  @override
  final bool enforceMessagingComponent;

  @override
  String toString() {
    return 'MessagingEndUserFilter(messageType: $messageType, consentStatus: $consentStatus, channelName: $channelName, channelId: $channelId, enforceMessagingComponent: $enforceMessagingComponent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingEndUserFilterImpl &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.consentStatus, consentStatus) ||
                other.consentStatus == consentStatus) &&
            (identical(other.channelName, channelName) ||
                other.channelName == channelName) &&
            (identical(other.channelId, channelId) ||
                other.channelId == channelId) &&
            (identical(other.enforceMessagingComponent,
                    enforceMessagingComponent) ||
                other.enforceMessagingComponent == enforceMessagingComponent));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, messageType, consentStatus,
      channelName, channelId, enforceMessagingComponent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingEndUserFilterImplCopyWith<_$MessagingEndUserFilterImpl>
      get copyWith => __$$MessagingEndUserFilterImplCopyWithImpl<
          _$MessagingEndUserFilterImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingEndUserFilterImplToJson(
      this,
    );
  }
}

abstract class _MessagingEndUserFilter implements MessagingEndUserFilter {
  const factory _MessagingEndUserFilter(
          {required final String messageType,
          required final String consentStatus,
          required final String channelName,
          @ParseSfIdConverter() required final SfId channelId,
          required final bool enforceMessagingComponent}) =
      _$MessagingEndUserFilterImpl;

  factory _MessagingEndUserFilter.fromJson(Map<String, dynamic> json) =
      _$MessagingEndUserFilterImpl.fromJson;

  @override
  String get messageType;
  @override
  String get consentStatus;
  @override
  String get channelName;
  @override
  @ParseSfIdConverter()
  SfId get channelId;
  @override
  bool get enforceMessagingComponent;
  @override
  @JsonKey(ignore: true)
  _$$MessagingEndUserFilterImplCopyWith<_$MessagingEndUserFilterImpl>
      get copyWith => throw _privateConstructorUsedError;
}
