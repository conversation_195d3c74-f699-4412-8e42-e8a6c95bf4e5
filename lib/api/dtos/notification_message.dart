import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/converters/timestamp_int_converter.dart';

part 'notification_message.freezed.dart';
part 'notification_message.g.dart';

@freezed
class NotificationMessage with _$NotificationMessage {

  const factory NotificationMessage({
    @TimestampIntConverter() required int timestamp,
    required String notificationId,
    required String sessionId,
    required String messageCategory,
    required Map<String, dynamic> payload,
    required bool sentToWebSocket,
    required bool pushed,
  }) = _NotificationMessage;

  factory NotificationMessage.fromJson(Map<String, dynamic> json) =>
      _$NotificationMessageFromJson(json);
}
