// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_option.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TransferOption _$TransferOptionFromJson(Map<String, dynamic> json) {
  return _TransferOption.fromJson(json);
}

/// @nodoc
mixin _$TransferOption {
  TransferDestinationType get destinationType =>
      throw _privateConstructorUsedError;
  List<TransferDestination> get destinations =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TransferOptionCopyWith<TransferOption> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransferOptionCopyWith<$Res> {
  factory $TransferOptionCopyWith(
          TransferOption value, $Res Function(TransferOption) then) =
      _$TransferOptionCopyWithImpl<$Res, TransferOption>;
  @useResult
  $Res call(
      {TransferDestinationType destinationType,
      List<TransferDestination> destinations});
}

/// @nodoc
class _$TransferOptionCopyWithImpl<$Res, $Val extends TransferOption>
    implements $TransferOptionCopyWith<$Res> {
  _$TransferOptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? destinationType = null,
    Object? destinations = null,
  }) {
    return _then(_value.copyWith(
      destinationType: null == destinationType
          ? _value.destinationType
          : destinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType,
      destinations: null == destinations
          ? _value.destinations
          : destinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransferOptionImplCopyWith<$Res>
    implements $TransferOptionCopyWith<$Res> {
  factory _$$TransferOptionImplCopyWith(_$TransferOptionImpl value,
          $Res Function(_$TransferOptionImpl) then) =
      __$$TransferOptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TransferDestinationType destinationType,
      List<TransferDestination> destinations});
}

/// @nodoc
class __$$TransferOptionImplCopyWithImpl<$Res>
    extends _$TransferOptionCopyWithImpl<$Res, _$TransferOptionImpl>
    implements _$$TransferOptionImplCopyWith<$Res> {
  __$$TransferOptionImplCopyWithImpl(
      _$TransferOptionImpl _value, $Res Function(_$TransferOptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? destinationType = null,
    Object? destinations = null,
  }) {
    return _then(_$TransferOptionImpl(
      destinationType: null == destinationType
          ? _value.destinationType
          : destinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType,
      destinations: null == destinations
          ? _value._destinations
          : destinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TransferOptionImpl implements _TransferOption {
  const _$TransferOptionImpl(
      {required this.destinationType,
      final List<TransferDestination> destinations =
          const <TransferDestination>[]})
      : _destinations = destinations;

  factory _$TransferOptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$TransferOptionImplFromJson(json);

  @override
  final TransferDestinationType destinationType;
  final List<TransferDestination> _destinations;
  @override
  @JsonKey()
  List<TransferDestination> get destinations {
    if (_destinations is EqualUnmodifiableListView) return _destinations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_destinations);
  }

  @override
  String toString() {
    return 'TransferOption(destinationType: $destinationType, destinations: $destinations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferOptionImpl &&
            (identical(other.destinationType, destinationType) ||
                other.destinationType == destinationType) &&
            const DeepCollectionEquality()
                .equals(other._destinations, _destinations));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, destinationType,
      const DeepCollectionEquality().hash(_destinations));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferOptionImplCopyWith<_$TransferOptionImpl> get copyWith =>
      __$$TransferOptionImplCopyWithImpl<_$TransferOptionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TransferOptionImplToJson(
      this,
    );
  }
}

abstract class _TransferOption implements TransferOption {
  const factory _TransferOption(
      {required final TransferDestinationType destinationType,
      final List<TransferDestination> destinations}) = _$TransferOptionImpl;

  factory _TransferOption.fromJson(Map<String, dynamic> json) =
      _$TransferOptionImpl.fromJson;

  @override
  TransferDestinationType get destinationType;
  @override
  List<TransferDestination> get destinations;
  @override
  @JsonKey(ignore: true)
  _$$TransferOptionImplCopyWith<_$TransferOptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
