// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_destination.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TransferDestination _$TransferDestinationFromJson(Map<String, dynamic> json) {
  return _TransferDestination.fromJson(json);
}

/// @nodoc
mixin _$TransferDestination {
  @ParseSfIdConverter()
  SfId get id => throw _privateConstructorUsedError;
  String get label => throw _privateConstructorUsedError;
  String? get smallPhotoUrl => throw _privateConstructorUsedError;

  /// seconds
  int? get estimatedWait => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TransferDestinationCopyWith<TransferDestination> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransferDestinationCopyWith<$Res> {
  factory $TransferDestinationCopyWith(
          TransferDestination value, $Res Function(TransferDestination) then) =
      _$TransferDestinationCopyWithImpl<$Res, TransferDestination>;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId id,
      String label,
      String? smallPhotoUrl,
      int? estimatedWait});

  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class _$TransferDestinationCopyWithImpl<$Res, $Val extends TransferDestination>
    implements $TransferDestinationCopyWith<$Res> {
  _$TransferDestinationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? smallPhotoUrl = freezed,
    Object? estimatedWait = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      smallPhotoUrl: freezed == smallPhotoUrl
          ? _value.smallPhotoUrl
          : smallPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedWait: freezed == estimatedWait
          ? _value.estimatedWait
          : estimatedWait // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get id {
    return $SfIdCopyWith<$Res>(_value.id, (value) {
      return _then(_value.copyWith(id: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TransferDestinationImplCopyWith<$Res>
    implements $TransferDestinationCopyWith<$Res> {
  factory _$$TransferDestinationImplCopyWith(_$TransferDestinationImpl value,
          $Res Function(_$TransferDestinationImpl) then) =
      __$$TransferDestinationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId id,
      String label,
      String? smallPhotoUrl,
      int? estimatedWait});

  @override
  $SfIdCopyWith<$Res> get id;
}

/// @nodoc
class __$$TransferDestinationImplCopyWithImpl<$Res>
    extends _$TransferDestinationCopyWithImpl<$Res, _$TransferDestinationImpl>
    implements _$$TransferDestinationImplCopyWith<$Res> {
  __$$TransferDestinationImplCopyWithImpl(_$TransferDestinationImpl _value,
      $Res Function(_$TransferDestinationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? smallPhotoUrl = freezed,
    Object? estimatedWait = freezed,
  }) {
    return _then(_$TransferDestinationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      smallPhotoUrl: freezed == smallPhotoUrl
          ? _value.smallPhotoUrl
          : smallPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedWait: freezed == estimatedWait
          ? _value.estimatedWait
          : estimatedWait // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TransferDestinationImpl implements _TransferDestination {
  const _$TransferDestinationImpl(
      {@ParseSfIdConverter() required this.id,
      required this.label,
      this.smallPhotoUrl = null,
      this.estimatedWait = null});

  factory _$TransferDestinationImpl.fromJson(Map<String, dynamic> json) =>
      _$$TransferDestinationImplFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId id;
  @override
  final String label;
  @override
  @JsonKey()
  final String? smallPhotoUrl;

  /// seconds
  @override
  @JsonKey()
  final int? estimatedWait;

  @override
  String toString() {
    return 'TransferDestination(id: $id, label: $label, smallPhotoUrl: $smallPhotoUrl, estimatedWait: $estimatedWait)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferDestinationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.smallPhotoUrl, smallPhotoUrl) ||
                other.smallPhotoUrl == smallPhotoUrl) &&
            (identical(other.estimatedWait, estimatedWait) ||
                other.estimatedWait == estimatedWait));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, label, smallPhotoUrl, estimatedWait);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferDestinationImplCopyWith<_$TransferDestinationImpl> get copyWith =>
      __$$TransferDestinationImplCopyWithImpl<_$TransferDestinationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TransferDestinationImplToJson(
      this,
    );
  }
}

abstract class _TransferDestination implements TransferDestination {
  const factory _TransferDestination(
      {@ParseSfIdConverter() required final SfId id,
      required final String label,
      final String? smallPhotoUrl,
      final int? estimatedWait}) = _$TransferDestinationImpl;

  factory _TransferDestination.fromJson(Map<String, dynamic> json) =
      _$TransferDestinationImpl.fromJson;

  @override
  @ParseSfIdConverter()
  SfId get id;
  @override
  String get label;
  @override
  String? get smallPhotoUrl;
  @override

  /// seconds
  int? get estimatedWait;
  @override
  @JsonKey(ignore: true)
  _$$TransferDestinationImplCopyWith<_$TransferDestinationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
