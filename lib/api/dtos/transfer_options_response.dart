import 'package:freezed_annotation/freezed_annotation.dart';

import 'transfer_option.dart';

part 'transfer_options_response.freezed.dart';
part 'transfer_options_response.g.dart';

@freezed
class TransferOptionsResponse with _$TransferOptionsResponse {
  const factory TransferOptionsResponse({
    required List<TransferOption> options,
  }) = _TransferOptionsResponse;

  factory TransferOptionsResponse.fromJson(Map<String, dynamic> json) =>
      _$TransferOptionsResponseFromJson(json);
}
