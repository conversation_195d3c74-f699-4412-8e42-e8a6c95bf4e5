// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'send_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SendFailure _$SendFailureFromJson(Map<String, dynamic> json) {
  return _SendFailure.fromJson(json);
}

/// @nodoc
mixin _$SendFailure {
  String get messageId => throw _privateConstructorUsedError;
  String get reason => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SendFailureCopyWith<SendFailure> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendFailureCopyWith<$Res> {
  factory $SendFailureCopyWith(
          SendFailure value, $Res Function(SendFailure) then) =
      _$SendFailureCopyWithImpl<$Res, SendFailure>;
  @useResult
  $Res call({String messageId, String reason});
}

/// @nodoc
class _$SendFailureCopyWithImpl<$Res, $Val extends SendFailure>
    implements $SendFailureCopyWith<$Res> {
  _$SendFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? reason = null,
  }) {
    return _then(_value.copyWith(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SendFailureImplCopyWith<$Res>
    implements $SendFailureCopyWith<$Res> {
  factory _$$SendFailureImplCopyWith(
          _$SendFailureImpl value, $Res Function(_$SendFailureImpl) then) =
      __$$SendFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String messageId, String reason});
}

/// @nodoc
class __$$SendFailureImplCopyWithImpl<$Res>
    extends _$SendFailureCopyWithImpl<$Res, _$SendFailureImpl>
    implements _$$SendFailureImplCopyWith<$Res> {
  __$$SendFailureImplCopyWithImpl(
      _$SendFailureImpl _value, $Res Function(_$SendFailureImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? reason = null,
  }) {
    return _then(_$SendFailureImpl(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SendFailureImpl implements _SendFailure {
  const _$SendFailureImpl({required this.messageId, required this.reason});

  factory _$SendFailureImpl.fromJson(Map<String, dynamic> json) =>
      _$$SendFailureImplFromJson(json);

  @override
  final String messageId;
  @override
  final String reason;

  @override
  String toString() {
    return 'SendFailure(messageId: $messageId, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendFailureImpl &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, reason);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SendFailureImplCopyWith<_$SendFailureImpl> get copyWith =>
      __$$SendFailureImplCopyWithImpl<_$SendFailureImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SendFailureImplToJson(
      this,
    );
  }
}

abstract class _SendFailure implements SendFailure {
  const factory _SendFailure(
      {required final String messageId,
      required final String reason}) = _$SendFailureImpl;

  factory _SendFailure.fromJson(Map<String, dynamic> json) =
      _$SendFailureImpl.fromJson;

  @override
  String get messageId;
  @override
  String get reason;
  @override
  @JsonKey(ignore: true)
  _$$SendFailureImplCopyWith<_$SendFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
