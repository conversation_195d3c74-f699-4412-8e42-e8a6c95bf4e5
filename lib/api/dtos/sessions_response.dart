import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/presence_status.dart';

part 'sessions_response.freezed.dart';
part 'sessions_response.g.dart';

@freezed
class SessionsResponse with _$SessionsResponse {
  const factory SessionsResponse({
    required String sessionToken,
    required String sessionId,
    required int expirationTime,
    List<PresenceStatus>? presenceStatuses,
    String? presenceStatusId,
    String? scrtAccessToken,
    String? scrtHost,
    required String webSocketUrl,
  }) = _SessionsResponse;

  factory SessionsResponse.fromJson(Map<String, dynamic> json) =>
      _$SessionsResponseFromJson(json);
}
