// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notifications_ack_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationsAckBody _$NotificationsAckBodyFromJson(Map<String, dynamic> json) {
  return _NotificationsAckBody.fromJson(json);
}

/// @nodoc
mixin _$NotificationsAckBody {
  List<String> get notificationIds => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NotificationsAckBodyCopyWith<NotificationsAckBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationsAckBodyCopyWith<$Res> {
  factory $NotificationsAckBodyCopyWith(NotificationsAckBody value,
          $Res Function(NotificationsAckBody) then) =
      _$NotificationsAckBodyCopyWithImpl<$Res, NotificationsAckBody>;
  @useResult
  $Res call({List<String> notificationIds});
}

/// @nodoc
class _$NotificationsAckBodyCopyWithImpl<$Res,
        $Val extends NotificationsAckBody>
    implements $NotificationsAckBodyCopyWith<$Res> {
  _$NotificationsAckBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationIds = null,
  }) {
    return _then(_value.copyWith(
      notificationIds: null == notificationIds
          ? _value.notificationIds
          : notificationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationsAckBodyImplCopyWith<$Res>
    implements $NotificationsAckBodyCopyWith<$Res> {
  factory _$$NotificationsAckBodyImplCopyWith(_$NotificationsAckBodyImpl value,
          $Res Function(_$NotificationsAckBodyImpl) then) =
      __$$NotificationsAckBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<String> notificationIds});
}

/// @nodoc
class __$$NotificationsAckBodyImplCopyWithImpl<$Res>
    extends _$NotificationsAckBodyCopyWithImpl<$Res, _$NotificationsAckBodyImpl>
    implements _$$NotificationsAckBodyImplCopyWith<$Res> {
  __$$NotificationsAckBodyImplCopyWithImpl(_$NotificationsAckBodyImpl _value,
      $Res Function(_$NotificationsAckBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationIds = null,
  }) {
    return _then(_$NotificationsAckBodyImpl(
      notificationIds: null == notificationIds
          ? _value._notificationIds
          : notificationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationsAckBodyImpl implements _NotificationsAckBody {
  const _$NotificationsAckBodyImpl(
      {required final List<String> notificationIds})
      : _notificationIds = notificationIds;

  factory _$NotificationsAckBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationsAckBodyImplFromJson(json);

  final List<String> _notificationIds;
  @override
  List<String> get notificationIds {
    if (_notificationIds is EqualUnmodifiableListView) return _notificationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_notificationIds);
  }

  @override
  String toString() {
    return 'NotificationsAckBody(notificationIds: $notificationIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationsAckBodyImpl &&
            const DeepCollectionEquality()
                .equals(other._notificationIds, _notificationIds));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_notificationIds));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationsAckBodyImplCopyWith<_$NotificationsAckBodyImpl>
      get copyWith =>
          __$$NotificationsAckBodyImplCopyWithImpl<_$NotificationsAckBodyImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationsAckBodyImplToJson(
      this,
    );
  }
}

abstract class _NotificationsAckBody implements NotificationsAckBody {
  const factory _NotificationsAckBody(
          {required final List<String> notificationIds}) =
      _$NotificationsAckBodyImpl;

  factory _NotificationsAckBody.fromJson(Map<String, dynamic> json) =
      _$NotificationsAckBodyImpl.fromJson;

  @override
  List<String> get notificationIds;
  @override
  @JsonKey(ignore: true)
  _$$NotificationsAckBodyImplCopyWith<_$NotificationsAckBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
