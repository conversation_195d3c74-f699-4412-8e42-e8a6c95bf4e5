// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_event_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LogEventResponse _$LogEventResponseFromJson(Map<String, dynamic> json) {
  return _LogEventResponse.fromJson(json);
}

/// @nodoc
mixin _$LogEventResponse {
  bool get success => throw _privateConstructorUsedError;
  int? get tooNewStartIndex => throw _privateConstructorUsedError;
  int? get tooOldEndIndex => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogEventResponseCopyWith<LogEventResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogEventResponseCopyWith<$Res> {
  factory $LogEventResponseCopyWith(
          LogEventResponse value, $Res Function(LogEventResponse) then) =
      _$LogEventResponseCopyWithImpl<$Res, LogEventResponse>;
  @useResult
  $Res call({bool success, int? tooNewStartIndex, int? tooOldEndIndex});
}

/// @nodoc
class _$LogEventResponseCopyWithImpl<$Res, $Val extends LogEventResponse>
    implements $LogEventResponseCopyWith<$Res> {
  _$LogEventResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? tooNewStartIndex = freezed,
    Object? tooOldEndIndex = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      tooNewStartIndex: freezed == tooNewStartIndex
          ? _value.tooNewStartIndex
          : tooNewStartIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      tooOldEndIndex: freezed == tooOldEndIndex
          ? _value.tooOldEndIndex
          : tooOldEndIndex // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LogEventResponseImplCopyWith<$Res>
    implements $LogEventResponseCopyWith<$Res> {
  factory _$$LogEventResponseImplCopyWith(_$LogEventResponseImpl value,
          $Res Function(_$LogEventResponseImpl) then) =
      __$$LogEventResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, int? tooNewStartIndex, int? tooOldEndIndex});
}

/// @nodoc
class __$$LogEventResponseImplCopyWithImpl<$Res>
    extends _$LogEventResponseCopyWithImpl<$Res, _$LogEventResponseImpl>
    implements _$$LogEventResponseImplCopyWith<$Res> {
  __$$LogEventResponseImplCopyWithImpl(_$LogEventResponseImpl _value,
      $Res Function(_$LogEventResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? tooNewStartIndex = freezed,
    Object? tooOldEndIndex = freezed,
  }) {
    return _then(_$LogEventResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      tooNewStartIndex: freezed == tooNewStartIndex
          ? _value.tooNewStartIndex
          : tooNewStartIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      tooOldEndIndex: freezed == tooOldEndIndex
          ? _value.tooOldEndIndex
          : tooOldEndIndex // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LogEventResponseImpl implements _LogEventResponse {
  const _$LogEventResponseImpl(
      {required this.success, this.tooNewStartIndex, this.tooOldEndIndex});

  factory _$LogEventResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$LogEventResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final int? tooNewStartIndex;
  @override
  final int? tooOldEndIndex;

  @override
  String toString() {
    return 'LogEventResponse(success: $success, tooNewStartIndex: $tooNewStartIndex, tooOldEndIndex: $tooOldEndIndex)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LogEventResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.tooNewStartIndex, tooNewStartIndex) ||
                other.tooNewStartIndex == tooNewStartIndex) &&
            (identical(other.tooOldEndIndex, tooOldEndIndex) ||
                other.tooOldEndIndex == tooOldEndIndex));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, tooNewStartIndex, tooOldEndIndex);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LogEventResponseImplCopyWith<_$LogEventResponseImpl> get copyWith =>
      __$$LogEventResponseImplCopyWithImpl<_$LogEventResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LogEventResponseImplToJson(
      this,
    );
  }
}

abstract class _LogEventResponse implements LogEventResponse {
  const factory _LogEventResponse(
      {required final bool success,
      final int? tooNewStartIndex,
      final int? tooOldEndIndex}) = _$LogEventResponseImpl;

  factory _LogEventResponse.fromJson(Map<String, dynamic> json) =
      _$LogEventResponseImpl.fromJson;

  @override
  bool get success;
  @override
  int? get tooNewStartIndex;
  @override
  int? get tooOldEndIndex;
  @override
  @JsonKey(ignore: true)
  _$$LogEventResponseImplCopyWith<_$LogEventResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
