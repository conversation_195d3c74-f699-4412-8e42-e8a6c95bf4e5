// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user_filters_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingEndUserFiltersResponse _$MessagingEndUserFiltersResponseFromJson(
    Map<String, dynamic> json) {
  return _MessagingEndUserFiltersResponse.fromJson(json);
}

/// @nodoc
mixin _$MessagingEndUserFiltersResponse {
  List<MessagingEndUserFilter> get filters =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingEndUserFiltersResponseCopyWith<MessagingEndUserFiltersResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingEndUserFiltersResponseCopyWith<$Res> {
  factory $MessagingEndUserFiltersResponseCopyWith(
          MessagingEndUserFiltersResponse value,
          $Res Function(MessagingEndUserFiltersResponse) then) =
      _$MessagingEndUserFiltersResponseCopyWithImpl<$Res,
          MessagingEndUserFiltersResponse>;
  @useResult
  $Res call({List<MessagingEndUserFilter> filters});
}

/// @nodoc
class _$MessagingEndUserFiltersResponseCopyWithImpl<$Res,
        $Val extends MessagingEndUserFiltersResponse>
    implements $MessagingEndUserFiltersResponseCopyWith<$Res> {
  _$MessagingEndUserFiltersResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filters = null,
  }) {
    return _then(_value.copyWith(
      filters: null == filters
          ? _value.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as List<MessagingEndUserFilter>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessagingEndUserFiltersResponseImplCopyWith<$Res>
    implements $MessagingEndUserFiltersResponseCopyWith<$Res> {
  factory _$$MessagingEndUserFiltersResponseImplCopyWith(
          _$MessagingEndUserFiltersResponseImpl value,
          $Res Function(_$MessagingEndUserFiltersResponseImpl) then) =
      __$$MessagingEndUserFiltersResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<MessagingEndUserFilter> filters});
}

/// @nodoc
class __$$MessagingEndUserFiltersResponseImplCopyWithImpl<$Res>
    extends _$MessagingEndUserFiltersResponseCopyWithImpl<$Res,
        _$MessagingEndUserFiltersResponseImpl>
    implements _$$MessagingEndUserFiltersResponseImplCopyWith<$Res> {
  __$$MessagingEndUserFiltersResponseImplCopyWithImpl(
      _$MessagingEndUserFiltersResponseImpl _value,
      $Res Function(_$MessagingEndUserFiltersResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filters = null,
  }) {
    return _then(_$MessagingEndUserFiltersResponseImpl(
      filters: null == filters
          ? _value._filters
          : filters // ignore: cast_nullable_to_non_nullable
              as List<MessagingEndUserFilter>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingEndUserFiltersResponseImpl
    implements _MessagingEndUserFiltersResponse {
  const _$MessagingEndUserFiltersResponseImpl(
      {required final List<MessagingEndUserFilter> filters})
      : _filters = filters;

  factory _$MessagingEndUserFiltersResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MessagingEndUserFiltersResponseImplFromJson(json);

  final List<MessagingEndUserFilter> _filters;
  @override
  List<MessagingEndUserFilter> get filters {
    if (_filters is EqualUnmodifiableListView) return _filters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filters);
  }

  @override
  String toString() {
    return 'MessagingEndUserFiltersResponse(filters: $filters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingEndUserFiltersResponseImpl &&
            const DeepCollectionEquality().equals(other._filters, _filters));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_filters));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingEndUserFiltersResponseImplCopyWith<
          _$MessagingEndUserFiltersResponseImpl>
      get copyWith => __$$MessagingEndUserFiltersResponseImplCopyWithImpl<
          _$MessagingEndUserFiltersResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingEndUserFiltersResponseImplToJson(
      this,
    );
  }
}

abstract class _MessagingEndUserFiltersResponse
    implements MessagingEndUserFiltersResponse {
  const factory _MessagingEndUserFiltersResponse(
          {required final List<MessagingEndUserFilter> filters}) =
      _$MessagingEndUserFiltersResponseImpl;

  factory _MessagingEndUserFiltersResponse.fromJson(Map<String, dynamic> json) =
      _$MessagingEndUserFiltersResponseImpl.fromJson;

  @override
  List<MessagingEndUserFilter> get filters;
  @override
  @JsonKey(ignore: true)
  _$$MessagingEndUserFiltersResponseImplCopyWith<
          _$MessagingEndUserFiltersResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
