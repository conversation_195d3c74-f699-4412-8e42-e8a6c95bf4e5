// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PresenceStatus _$PresenceStatusFromJson(Map<String, dynamic> json) {
  return _PresenceStatus.fromJson(json);
}

/// @nodoc
mixin _$PresenceStatus {
  String get id => throw _privateConstructorUsedError;
  String get label => throw _privateConstructorUsedError;
  @enumerated
  PresenceStatusOption get statusOption => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresenceStatusCopyWith<PresenceStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresenceStatusCopyWith<$Res> {
  factory $PresenceStatusCopyWith(
          PresenceStatus value, $Res Function(PresenceStatus) then) =
      _$PresenceStatusCopyWithImpl<$Res, PresenceStatus>;
  @useResult
  $Res call(
      {String id, String label, @enumerated PresenceStatusOption statusOption});
}

/// @nodoc
class _$PresenceStatusCopyWithImpl<$Res, $Val extends PresenceStatus>
    implements $PresenceStatusCopyWith<$Res> {
  _$PresenceStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? statusOption = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      statusOption: null == statusOption
          ? _value.statusOption
          : statusOption // ignore: cast_nullable_to_non_nullable
              as PresenceStatusOption,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PresenceStatusImplCopyWith<$Res>
    implements $PresenceStatusCopyWith<$Res> {
  factory _$$PresenceStatusImplCopyWith(_$PresenceStatusImpl value,
          $Res Function(_$PresenceStatusImpl) then) =
      __$$PresenceStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String label, @enumerated PresenceStatusOption statusOption});
}

/// @nodoc
class __$$PresenceStatusImplCopyWithImpl<$Res>
    extends _$PresenceStatusCopyWithImpl<$Res, _$PresenceStatusImpl>
    implements _$$PresenceStatusImplCopyWith<$Res> {
  __$$PresenceStatusImplCopyWithImpl(
      _$PresenceStatusImpl _value, $Res Function(_$PresenceStatusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? label = null,
    Object? statusOption = null,
  }) {
    return _then(_$PresenceStatusImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      statusOption: null == statusOption
          ? _value.statusOption
          : statusOption // ignore: cast_nullable_to_non_nullable
              as PresenceStatusOption,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresenceStatusImpl extends _PresenceStatus {
  const _$PresenceStatusImpl(
      {required this.id,
      required this.label,
      @enumerated required this.statusOption})
      : super._();

  factory _$PresenceStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresenceStatusImplFromJson(json);

  @override
  final String id;
  @override
  final String label;
  @override
  @enumerated
  final PresenceStatusOption statusOption;

  @override
  String toString() {
    return 'PresenceStatus(id: $id, label: $label, statusOption: $statusOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresenceStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.statusOption, statusOption) ||
                other.statusOption == statusOption));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, label, statusOption);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresenceStatusImplCopyWith<_$PresenceStatusImpl> get copyWith =>
      __$$PresenceStatusImplCopyWithImpl<_$PresenceStatusImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresenceStatusImplToJson(
      this,
    );
  }
}

abstract class _PresenceStatus extends PresenceStatus {
  const factory _PresenceStatus(
          {required final String id,
          required final String label,
          @enumerated required final PresenceStatusOption statusOption}) =
      _$PresenceStatusImpl;
  const _PresenceStatus._() : super._();

  factory _PresenceStatus.fromJson(Map<String, dynamic> json) =
      _$PresenceStatusImpl.fromJson;

  @override
  String get id;
  @override
  String get label;
  @override
  @enumerated
  PresenceStatusOption get statusOption;
  @override
  @JsonKey(ignore: true)
  _$$PresenceStatusImplCopyWith<_$PresenceStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
