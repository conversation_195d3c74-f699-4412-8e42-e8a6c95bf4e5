import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/messaging_definition.dart';

part 'channel_messaging_definitions_response.freezed.dart';
part 'channel_messaging_definitions_response.g.dart';

@freezed
class ChannelMessagingDefinitionsResponse
    with _$ChannelMessagingDefinitionsResponse {
  const factory ChannelMessagingDefinitionsResponse({
    required List<MessagingDefinition> definitions,
  }) = _ChannelMessagingDefinitionsResponse;

  factory ChannelMessagingDefinitionsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$ChannelMessagingDefinitionsResponseFromJson(json);
}
