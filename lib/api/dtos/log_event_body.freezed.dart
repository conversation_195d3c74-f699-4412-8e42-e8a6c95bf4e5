// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_event_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LogEventBody _$LogEventBodyFromJson(Map<String, dynamic> json) {
  return _LogEventBody.fromJson(json);
}

/// @nodoc
mixin _$LogEventBody {
  List<LogEvent> get events => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogEventBodyCopyWith<LogEventBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogEventBodyCopyWith<$Res> {
  factory $LogEventBodyCopyWith(
          LogEventBody value, $Res Function(LogEventBody) then) =
      _$LogEventBodyCopyWithImpl<$Res, LogEventBody>;
  @useResult
  $Res call({List<LogEvent> events});
}

/// @nodoc
class _$LogEventBodyCopyWithImpl<$Res, $Val extends LogEventBody>
    implements $LogEventBodyCopyWith<$Res> {
  _$LogEventBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? events = null,
  }) {
    return _then(_value.copyWith(
      events: null == events
          ? _value.events
          : events // ignore: cast_nullable_to_non_nullable
              as List<LogEvent>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LogEventBodyImplCopyWith<$Res>
    implements $LogEventBodyCopyWith<$Res> {
  factory _$$LogEventBodyImplCopyWith(
          _$LogEventBodyImpl value, $Res Function(_$LogEventBodyImpl) then) =
      __$$LogEventBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<LogEvent> events});
}

/// @nodoc
class __$$LogEventBodyImplCopyWithImpl<$Res>
    extends _$LogEventBodyCopyWithImpl<$Res, _$LogEventBodyImpl>
    implements _$$LogEventBodyImplCopyWith<$Res> {
  __$$LogEventBodyImplCopyWithImpl(
      _$LogEventBodyImpl _value, $Res Function(_$LogEventBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? events = null,
  }) {
    return _then(_$LogEventBodyImpl(
      events: null == events
          ? _value._events
          : events // ignore: cast_nullable_to_non_nullable
              as List<LogEvent>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LogEventBodyImpl implements _LogEventBody {
  const _$LogEventBodyImpl({required final List<LogEvent> events})
      : _events = events;

  factory _$LogEventBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$LogEventBodyImplFromJson(json);

  final List<LogEvent> _events;
  @override
  List<LogEvent> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  @override
  String toString() {
    return 'LogEventBody(events: $events)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LogEventBodyImpl &&
            const DeepCollectionEquality().equals(other._events, _events));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_events));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LogEventBodyImplCopyWith<_$LogEventBodyImpl> get copyWith =>
      __$$LogEventBodyImplCopyWithImpl<_$LogEventBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LogEventBodyImplToJson(
      this,
    );
  }
}

abstract class _LogEventBody implements LogEventBody {
  const factory _LogEventBody({required final List<LogEvent> events}) =
      _$LogEventBodyImpl;

  factory _LogEventBody.fromJson(Map<String, dynamic> json) =
      _$LogEventBodyImpl.fromJson;

  @override
  List<LogEvent> get events;
  @override
  @JsonKey(ignore: true)
  _$$LogEventBodyImplCopyWith<_$LogEventBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
