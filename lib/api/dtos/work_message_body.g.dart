// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_message_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WorkMessageBodyImpl _$$WorkMessageBodyImplFromJson(
        Map<String, dynamic> json) =>
    _$WorkMessageBodyImpl(
      messageId: json['messageId'] as String,
      messageBody: json['messageBody'] as String?,
      workTargetId: const ParseSfIdConverter().from<PERSON>son(json['workTargetId']),
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map(
                  (e) => MessageAttachment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const <MessageAttachment>[],
    );

Map<String, dynamic> _$$WorkMessageBodyImplToJson(
        _$WorkMessageBodyImpl instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
      'messageBody': instance.messageBody,
      'workTargetId': _$JsonConverterToJson<Object?, SfId>(
          instance.workTargetId, const ParseSfIdConverter().toJson),
      'attachments': instance.attachments.map((e) => e.toJson()).toList(),
    };

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
