import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'messaging_end_user_filter.freezed.dart';
part 'messaging_end_user_filter.g.dart';

@freezed
class MessagingEndUserFilter with _$MessagingEndUserFilter {
  const factory MessagingEndUserFilter({
    required String messageType,
    required String consentStatus,
    required String channelName,
    @ParseSfIdConverter() required SfId channelId,
    required bool enforceMessagingComponent,
  }) = _MessagingEndUserFilter;

  factory MessagingEndUserFilter.fromJson(Map<String, dynamic> json) =>
      _$MessagingEndUserFilterFromJson(json);
}
