import 'package:freezed_annotation/freezed_annotation.dart';

part 'presence_chat_attachment.freezed.dart';
part 'presence_chat_attachment.g.dart';

@freezed
class PresenceChatAttachment with _$PresenceChatAttachment {
  const factory PresenceChatAttachment({
    required String contentVersionId,
  }) = _PresenceChatAttachment;

  factory PresenceChatAttachment.fromJson(Map<String, dynamic> json) =>
      _$PresenceChatAttachmentFromJson(json);
}
