// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_channel_entry.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingChannelEntry _$MessagingChannelEntryFromJson(
    Map<String, dynamic> json) {
  return _MessagingChannelEntry.fromJson(json);
}

/// @nodoc
mixin _$MessagingChannelEntry {
  @JsonKey(name: 'id')
  @ParseSfIdConverter()
  SfId get channelId => throw _privateConstructorUsedError;
  String get messageType => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  bool get isFavorite => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get messagingEndUserId => throw _privateConstructorUsedError;
  int? get messagingEndUserTimestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingChannelEntryCopyWith<MessagingChannelEntry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingChannelEntryCopyWith<$Res> {
  factory $MessagingChannelEntryCopyWith(MessagingChannelEntry value,
          $Res Function(MessagingChannelEntry) then) =
      _$MessagingChannelEntryCopyWithImpl<$Res, MessagingChannelEntry>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') @ParseSfIdConverter() SfId channelId,
      String messageType,
      String name,
      bool isFavorite,
      @ParseSfIdConverter() SfId? messagingEndUserId,
      int? messagingEndUserTimestamp});

  $SfIdCopyWith<$Res> get channelId;
  $SfIdCopyWith<$Res>? get messagingEndUserId;
}

/// @nodoc
class _$MessagingChannelEntryCopyWithImpl<$Res,
        $Val extends MessagingChannelEntry>
    implements $MessagingChannelEntryCopyWith<$Res> {
  _$MessagingChannelEntryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelId = null,
    Object? messageType = null,
    Object? name = null,
    Object? isFavorite = null,
    Object? messagingEndUserId = freezed,
    Object? messagingEndUserTimestamp = freezed,
  }) {
    return _then(_value.copyWith(
      channelId: null == channelId
          ? _value.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      isFavorite: null == isFavorite
          ? _value.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingEndUserId: freezed == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingEndUserTimestamp: freezed == messagingEndUserTimestamp
          ? _value.messagingEndUserTimestamp
          : messagingEndUserTimestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get channelId {
    return $SfIdCopyWith<$Res>(_value.channelId, (value) {
      return _then(_value.copyWith(channelId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_value.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.messagingEndUserId!, (value) {
      return _then(_value.copyWith(messagingEndUserId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingChannelEntryImplCopyWith<$Res>
    implements $MessagingChannelEntryCopyWith<$Res> {
  factory _$$MessagingChannelEntryImplCopyWith(
          _$MessagingChannelEntryImpl value,
          $Res Function(_$MessagingChannelEntryImpl) then) =
      __$$MessagingChannelEntryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') @ParseSfIdConverter() SfId channelId,
      String messageType,
      String name,
      bool isFavorite,
      @ParseSfIdConverter() SfId? messagingEndUserId,
      int? messagingEndUserTimestamp});

  @override
  $SfIdCopyWith<$Res> get channelId;
  @override
  $SfIdCopyWith<$Res>? get messagingEndUserId;
}

/// @nodoc
class __$$MessagingChannelEntryImplCopyWithImpl<$Res>
    extends _$MessagingChannelEntryCopyWithImpl<$Res,
        _$MessagingChannelEntryImpl>
    implements _$$MessagingChannelEntryImplCopyWith<$Res> {
  __$$MessagingChannelEntryImplCopyWithImpl(_$MessagingChannelEntryImpl _value,
      $Res Function(_$MessagingChannelEntryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? channelId = null,
    Object? messageType = null,
    Object? name = null,
    Object? isFavorite = null,
    Object? messagingEndUserId = freezed,
    Object? messagingEndUserTimestamp = freezed,
  }) {
    return _then(_$MessagingChannelEntryImpl(
      channelId: null == channelId
          ? _value.channelId
          : channelId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      isFavorite: null == isFavorite
          ? _value.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingEndUserId: freezed == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingEndUserTimestamp: freezed == messagingEndUserTimestamp
          ? _value.messagingEndUserTimestamp
          : messagingEndUserTimestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingChannelEntryImpl extends _MessagingChannelEntry {
  const _$MessagingChannelEntryImpl(
      {@JsonKey(name: 'id') @ParseSfIdConverter() required this.channelId,
      required this.messageType,
      required this.name,
      required this.isFavorite,
      @ParseSfIdConverter() this.messagingEndUserId,
      this.messagingEndUserTimestamp})
      : super._();

  factory _$MessagingChannelEntryImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingChannelEntryImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  @ParseSfIdConverter()
  final SfId channelId;
  @override
  final String messageType;
  @override
  final String name;
  @override
  final bool isFavorite;
  @override
  @ParseSfIdConverter()
  final SfId? messagingEndUserId;
  @override
  final int? messagingEndUserTimestamp;

  @override
  String toString() {
    return 'MessagingChannelEntry(channelId: $channelId, messageType: $messageType, name: $name, isFavorite: $isFavorite, messagingEndUserId: $messagingEndUserId, messagingEndUserTimestamp: $messagingEndUserTimestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingChannelEntryImpl &&
            (identical(other.channelId, channelId) ||
                other.channelId == channelId) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.isFavorite, isFavorite) ||
                other.isFavorite == isFavorite) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingEndUserTimestamp,
                    messagingEndUserTimestamp) ||
                other.messagingEndUserTimestamp == messagingEndUserTimestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, channelId, messageType, name,
      isFavorite, messagingEndUserId, messagingEndUserTimestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingChannelEntryImplCopyWith<_$MessagingChannelEntryImpl>
      get copyWith => __$$MessagingChannelEntryImplCopyWithImpl<
          _$MessagingChannelEntryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingChannelEntryImplToJson(
      this,
    );
  }
}

abstract class _MessagingChannelEntry extends MessagingChannelEntry {
  const factory _MessagingChannelEntry(
      {@JsonKey(name: 'id') @ParseSfIdConverter() required final SfId channelId,
      required final String messageType,
      required final String name,
      required final bool isFavorite,
      @ParseSfIdConverter() final SfId? messagingEndUserId,
      final int? messagingEndUserTimestamp}) = _$MessagingChannelEntryImpl;
  const _MessagingChannelEntry._() : super._();

  factory _MessagingChannelEntry.fromJson(Map<String, dynamic> json) =
      _$MessagingChannelEntryImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  @ParseSfIdConverter()
  SfId get channelId;
  @override
  String get messageType;
  @override
  String get name;
  @override
  bool get isFavorite;
  @override
  @ParseSfIdConverter()
  SfId? get messagingEndUserId;
  @override
  int? get messagingEndUserTimestamp;
  @override
  @JsonKey(ignore: true)
  _$$MessagingChannelEntryImplCopyWith<_$MessagingChannelEntryImpl>
      get copyWith => throw _privateConstructorUsedError;
}
