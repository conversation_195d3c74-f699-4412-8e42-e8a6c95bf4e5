// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_options_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TransferOptionsResponse _$TransferOptionsResponseFromJson(
    Map<String, dynamic> json) {
  return _TransferOptionsResponse.fromJson(json);
}

/// @nodoc
mixin _$TransferOptionsResponse {
  List<TransferOption> get options => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TransferOptionsResponseCopyWith<TransferOptionsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransferOptionsResponseCopyWith<$Res> {
  factory $TransferOptionsResponseCopyWith(TransferOptionsResponse value,
          $Res Function(TransferOptionsResponse) then) =
      _$TransferOptionsResponseCopyWithImpl<$Res, TransferOptionsResponse>;
  @useResult
  $Res call({List<TransferOption> options});
}

/// @nodoc
class _$TransferOptionsResponseCopyWithImpl<$Res,
        $Val extends TransferOptionsResponse>
    implements $TransferOptionsResponseCopyWith<$Res> {
  _$TransferOptionsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? options = null,
  }) {
    return _then(_value.copyWith(
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<TransferOption>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransferOptionsResponseImplCopyWith<$Res>
    implements $TransferOptionsResponseCopyWith<$Res> {
  factory _$$TransferOptionsResponseImplCopyWith(
          _$TransferOptionsResponseImpl value,
          $Res Function(_$TransferOptionsResponseImpl) then) =
      __$$TransferOptionsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<TransferOption> options});
}

/// @nodoc
class __$$TransferOptionsResponseImplCopyWithImpl<$Res>
    extends _$TransferOptionsResponseCopyWithImpl<$Res,
        _$TransferOptionsResponseImpl>
    implements _$$TransferOptionsResponseImplCopyWith<$Res> {
  __$$TransferOptionsResponseImplCopyWithImpl(
      _$TransferOptionsResponseImpl _value,
      $Res Function(_$TransferOptionsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? options = null,
  }) {
    return _then(_$TransferOptionsResponseImpl(
      options: null == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<TransferOption>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TransferOptionsResponseImpl implements _TransferOptionsResponse {
  const _$TransferOptionsResponseImpl(
      {required final List<TransferOption> options})
      : _options = options;

  factory _$TransferOptionsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TransferOptionsResponseImplFromJson(json);

  final List<TransferOption> _options;
  @override
  List<TransferOption> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  @override
  String toString() {
    return 'TransferOptionsResponse(options: $options)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferOptionsResponseImpl &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_options));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferOptionsResponseImplCopyWith<_$TransferOptionsResponseImpl>
      get copyWith => __$$TransferOptionsResponseImplCopyWithImpl<
          _$TransferOptionsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TransferOptionsResponseImplToJson(
      this,
    );
  }
}

abstract class _TransferOptionsResponse implements TransferOptionsResponse {
  const factory _TransferOptionsResponse(
          {required final List<TransferOption> options}) =
      _$TransferOptionsResponseImpl;

  factory _TransferOptionsResponse.fromJson(Map<String, dynamic> json) =
      _$TransferOptionsResponseImpl.fromJson;

  @override
  List<TransferOption> get options;
  @override
  @JsonKey(ignore: true)
  _$$TransferOptionsResponseImplCopyWith<_$TransferOptionsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
