import 'package:freezed_annotation/freezed_annotation.dart';

part 'log_event_response.freezed.dart';
part 'log_event_response.g.dart';

@freezed
class LogEventResponse with _$LogEventResponse {
  const factory LogEventResponse({
    required bool success,
    int? tooNewStartIndex,
    int? tooOldEndIndex,
  }) = _LogEventResponse;

  factory LogEventResponse.fromJson(Map<String, dynamic> json) =>
      _$LogEventResponseFromJson(json);
}
