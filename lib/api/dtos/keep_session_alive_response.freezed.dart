// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'keep_session_alive_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

KeepSessionAliveResponse _$KeepSessionAliveResponseFromJson(
    Map<String, dynamic> json) {
  return _KeepSessionAliveResponse.fromJson(json);
}

/// @nodoc
mixin _$KeepSessionAliveResponse {
  int get expirationTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $KeepSessionAliveResponseCopyWith<KeepSessionAliveResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KeepSessionAliveResponseCopyWith<$Res> {
  factory $KeepSessionAliveResponseCopyWith(KeepSessionAliveResponse value,
          $Res Function(KeepSessionAliveResponse) then) =
      _$KeepSessionAliveResponseCopyWithImpl<$Res, KeepSessionAliveResponse>;
  @useResult
  $Res call({int expirationTime});
}

/// @nodoc
class _$KeepSessionAliveResponseCopyWithImpl<$Res,
        $Val extends KeepSessionAliveResponse>
    implements $KeepSessionAliveResponseCopyWith<$Res> {
  _$KeepSessionAliveResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expirationTime = null,
  }) {
    return _then(_value.copyWith(
      expirationTime: null == expirationTime
          ? _value.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$KeepSessionAliveResponseImplCopyWith<$Res>
    implements $KeepSessionAliveResponseCopyWith<$Res> {
  factory _$$KeepSessionAliveResponseImplCopyWith(
          _$KeepSessionAliveResponseImpl value,
          $Res Function(_$KeepSessionAliveResponseImpl) then) =
      __$$KeepSessionAliveResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int expirationTime});
}

/// @nodoc
class __$$KeepSessionAliveResponseImplCopyWithImpl<$Res>
    extends _$KeepSessionAliveResponseCopyWithImpl<$Res,
        _$KeepSessionAliveResponseImpl>
    implements _$$KeepSessionAliveResponseImplCopyWith<$Res> {
  __$$KeepSessionAliveResponseImplCopyWithImpl(
      _$KeepSessionAliveResponseImpl _value,
      $Res Function(_$KeepSessionAliveResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expirationTime = null,
  }) {
    return _then(_$KeepSessionAliveResponseImpl(
      expirationTime: null == expirationTime
          ? _value.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$KeepSessionAliveResponseImpl implements _KeepSessionAliveResponse {
  const _$KeepSessionAliveResponseImpl({required this.expirationTime});

  factory _$KeepSessionAliveResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$KeepSessionAliveResponseImplFromJson(json);

  @override
  final int expirationTime;

  @override
  String toString() {
    return 'KeepSessionAliveResponse(expirationTime: $expirationTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$KeepSessionAliveResponseImpl &&
            (identical(other.expirationTime, expirationTime) ||
                other.expirationTime == expirationTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, expirationTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$KeepSessionAliveResponseImplCopyWith<_$KeepSessionAliveResponseImpl>
      get copyWith => __$$KeepSessionAliveResponseImplCopyWithImpl<
          _$KeepSessionAliveResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$KeepSessionAliveResponseImplToJson(
      this,
    );
  }
}

abstract class _KeepSessionAliveResponse implements KeepSessionAliveResponse {
  const factory _KeepSessionAliveResponse({required final int expirationTime}) =
      _$KeepSessionAliveResponseImpl;

  factory _KeepSessionAliveResponse.fromJson(Map<String, dynamic> json) =
      _$KeepSessionAliveResponseImpl.fromJson;

  @override
  int get expirationTime;
  @override
  @JsonKey(ignore: true)
  _$$KeepSessionAliveResponseImplCopyWith<_$KeepSessionAliveResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
