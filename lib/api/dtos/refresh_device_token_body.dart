import 'package:freezed_annotation/freezed_annotation.dart';

part 'refresh_device_token_body.freezed.dart';
part 'refresh_device_token_body.g.dart';

@freezed
class RefreshDeviceTokenBody with _$RefreshDeviceTokenBody {
  const factory RefreshDeviceTokenBody({
    required String deviceToken,
  }) = _RefreshDeviceTokenBody;

  factory RefreshDeviceTokenBody.fromJson(Map<String, dynamic> json) =>
      _$RefreshDeviceTokenBodyFromJson(json);
}
