import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'log_metadata.freezed.dart';
part 'log_metadata.g.dart';

enum DeviceType { physical, simulator }

enum OSName { ios, android }

@freezed
@embeddedOnFreezed
class LogMetadata with _$LogMetadata {
  const LogMetadata._();
  const factory LogMetadata(
      {String? buildNumber,
      String? versionNumber,
      String? osVersion,
        @nullableEnumerated
      OSName? osName,
      String? manufacturer,
      String? model,
        @nullableEnumerated
      DeviceType? deviceType,
      String? carrier,
      int? batteryLevel,
      String? userId,
      String? sessionId,
          bool? debugMode,
          bool? profileMode,
          bool? releaseMode,
      }) = _LogMetadata;

  factory LogMetadata.fromJson(Map<String, dynamic> json) =>
      _$LogMetadataFromJson(json);
}
