// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_message_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OutboundMessageBody _$OutboundMessageBodyFromJson(Map<String, dynamic> json) {
  return _OutboundMessageBody.fromJson(json);
}

/// @nodoc
mixin _$OutboundMessageBody {
  String get messageId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get messagingEndUserId => throw _privateConstructorUsedError;
  String? get messageBody =>
      throw _privateConstructorUsedError; // Message Body MUST NOT be set if we pass in a messagingDefinitionParameters
  MessagingDefinitionParameters? get messagingDefinitionParameters =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OutboundMessageBodyCopyWith<OutboundMessageBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutboundMessageBodyCopyWith<$Res> {
  factory $OutboundMessageBodyCopyWith(
          OutboundMessageBody value, $Res Function(OutboundMessageBody) then) =
      _$OutboundMessageBodyCopyWithImpl<$Res, OutboundMessageBody>;
  @useResult
  $Res call(
      {String messageId,
      @ParseSfIdConverter() SfId messagingEndUserId,
      String? messageBody,
      MessagingDefinitionParameters? messagingDefinitionParameters});

  $SfIdCopyWith<$Res> get messagingEndUserId;
  $MessagingDefinitionParametersCopyWith<$Res>?
      get messagingDefinitionParameters;
}

/// @nodoc
class _$OutboundMessageBodyCopyWithImpl<$Res, $Val extends OutboundMessageBody>
    implements $OutboundMessageBodyCopyWith<$Res> {
  _$OutboundMessageBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingEndUserId = null,
    Object? messageBody = freezed,
    Object? messagingDefinitionParameters = freezed,
  }) {
    return _then(_value.copyWith(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingEndUserId: null == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageBody: freezed == messageBody
          ? _value.messageBody
          : messageBody // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingDefinitionParameters: freezed == messagingDefinitionParameters
          ? _value.messagingDefinitionParameters
          : messagingDefinitionParameters // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionParameters?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingEndUserId {
    return $SfIdCopyWith<$Res>(_value.messagingEndUserId, (value) {
      return _then(_value.copyWith(messagingEndUserId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionParametersCopyWith<$Res>?
      get messagingDefinitionParameters {
    if (_value.messagingDefinitionParameters == null) {
      return null;
    }

    return $MessagingDefinitionParametersCopyWith<$Res>(
        _value.messagingDefinitionParameters!, (value) {
      return _then(
          _value.copyWith(messagingDefinitionParameters: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OutboundMessageBodyImplCopyWith<$Res>
    implements $OutboundMessageBodyCopyWith<$Res> {
  factory _$$OutboundMessageBodyImplCopyWith(_$OutboundMessageBodyImpl value,
          $Res Function(_$OutboundMessageBodyImpl) then) =
      __$$OutboundMessageBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String messageId,
      @ParseSfIdConverter() SfId messagingEndUserId,
      String? messageBody,
      MessagingDefinitionParameters? messagingDefinitionParameters});

  @override
  $SfIdCopyWith<$Res> get messagingEndUserId;
  @override
  $MessagingDefinitionParametersCopyWith<$Res>?
      get messagingDefinitionParameters;
}

/// @nodoc
class __$$OutboundMessageBodyImplCopyWithImpl<$Res>
    extends _$OutboundMessageBodyCopyWithImpl<$Res, _$OutboundMessageBodyImpl>
    implements _$$OutboundMessageBodyImplCopyWith<$Res> {
  __$$OutboundMessageBodyImplCopyWithImpl(_$OutboundMessageBodyImpl _value,
      $Res Function(_$OutboundMessageBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingEndUserId = null,
    Object? messageBody = freezed,
    Object? messagingDefinitionParameters = freezed,
  }) {
    return _then(_$OutboundMessageBodyImpl(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messagingEndUserId: null == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messageBody: freezed == messageBody
          ? _value.messageBody
          : messageBody // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingDefinitionParameters: freezed == messagingDefinitionParameters
          ? _value.messagingDefinitionParameters
          : messagingDefinitionParameters // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionParameters?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OutboundMessageBodyImpl implements _OutboundMessageBody {
  const _$OutboundMessageBodyImpl(
      {required this.messageId,
      @ParseSfIdConverter() required this.messagingEndUserId,
      this.messageBody,
      this.messagingDefinitionParameters});

  factory _$OutboundMessageBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$OutboundMessageBodyImplFromJson(json);

  @override
  final String messageId;
  @override
  @ParseSfIdConverter()
  final SfId messagingEndUserId;
  @override
  final String? messageBody;
// Message Body MUST NOT be set if we pass in a messagingDefinitionParameters
  @override
  final MessagingDefinitionParameters? messagingDefinitionParameters;

  @override
  String toString() {
    return 'OutboundMessageBody(messageId: $messageId, messagingEndUserId: $messagingEndUserId, messageBody: $messageBody, messagingDefinitionParameters: $messagingDefinitionParameters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OutboundMessageBodyImpl &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messageBody, messageBody) ||
                other.messageBody == messageBody) &&
            (identical(other.messagingDefinitionParameters,
                    messagingDefinitionParameters) ||
                other.messagingDefinitionParameters ==
                    messagingDefinitionParameters));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messagingEndUserId,
      messageBody, messagingDefinitionParameters);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OutboundMessageBodyImplCopyWith<_$OutboundMessageBodyImpl> get copyWith =>
      __$$OutboundMessageBodyImplCopyWithImpl<_$OutboundMessageBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OutboundMessageBodyImplToJson(
      this,
    );
  }
}

abstract class _OutboundMessageBody implements OutboundMessageBody {
  const factory _OutboundMessageBody(
          {required final String messageId,
          @ParseSfIdConverter() required final SfId messagingEndUserId,
          final String? messageBody,
          final MessagingDefinitionParameters? messagingDefinitionParameters}) =
      _$OutboundMessageBodyImpl;

  factory _OutboundMessageBody.fromJson(Map<String, dynamic> json) =
      _$OutboundMessageBodyImpl.fromJson;

  @override
  String get messageId;
  @override
  @ParseSfIdConverter()
  SfId get messagingEndUserId;
  @override
  String? get messageBody;
  @override // Message Body MUST NOT be set if we pass in a messagingDefinitionParameters
  MessagingDefinitionParameters? get messagingDefinitionParameters;
  @override
  @JsonKey(ignore: true)
  _$$OutboundMessageBodyImplCopyWith<_$OutboundMessageBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
