// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_id_and_session_id.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessageIdAndSessionId _$MessageIdAndSessionIdFromJson(
    Map<String, dynamic> json) {
  return _MessageIdAndSessionId.fromJson(json);
}

/// @nodoc
mixin _$MessageIdAndSessionId {
  @ParseSfIdConverter()
  SfId get messageId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get messagingSessionId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessageIdAndSessionIdCopyWith<MessageIdAndSessionId> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageIdAndSessionIdCopyWith<$Res> {
  factory $MessageIdAndSessionIdCopyWith(MessageIdAndSessionId value,
          $Res Function(MessageIdAndSessionId) then) =
      _$MessageIdAndSessionIdCopyWithImpl<$Res, MessageIdAndSessionId>;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId messageId,
      @ParseSfIdConverter() SfId messagingSessionId});

  $SfIdCopyWith<$Res> get messageId;
  $SfIdCopyWith<$Res> get messagingSessionId;
}

/// @nodoc
class _$MessageIdAndSessionIdCopyWithImpl<$Res,
        $Val extends MessageIdAndSessionId>
    implements $MessageIdAndSessionIdCopyWith<$Res> {
  _$MessageIdAndSessionIdCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingSessionId = null,
  }) {
    return _then(_value.copyWith(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingSessionId: null == messagingSessionId
          ? _value.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messageId {
    return $SfIdCopyWith<$Res>(_value.messageId, (value) {
      return _then(_value.copyWith(messageId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get messagingSessionId {
    return $SfIdCopyWith<$Res>(_value.messagingSessionId, (value) {
      return _then(_value.copyWith(messagingSessionId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessageIdAndSessionIdImplCopyWith<$Res>
    implements $MessageIdAndSessionIdCopyWith<$Res> {
  factory _$$MessageIdAndSessionIdImplCopyWith(
          _$MessageIdAndSessionIdImpl value,
          $Res Function(_$MessageIdAndSessionIdImpl) then) =
      __$$MessageIdAndSessionIdImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId messageId,
      @ParseSfIdConverter() SfId messagingSessionId});

  @override
  $SfIdCopyWith<$Res> get messageId;
  @override
  $SfIdCopyWith<$Res> get messagingSessionId;
}

/// @nodoc
class __$$MessageIdAndSessionIdImplCopyWithImpl<$Res>
    extends _$MessageIdAndSessionIdCopyWithImpl<$Res,
        _$MessageIdAndSessionIdImpl>
    implements _$$MessageIdAndSessionIdImplCopyWith<$Res> {
  __$$MessageIdAndSessionIdImplCopyWithImpl(_$MessageIdAndSessionIdImpl _value,
      $Res Function(_$MessageIdAndSessionIdImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? messagingSessionId = null,
  }) {
    return _then(_$MessageIdAndSessionIdImpl(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as SfId,
      messagingSessionId: null == messagingSessionId
          ? _value.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessageIdAndSessionIdImpl implements _MessageIdAndSessionId {
  const _$MessageIdAndSessionIdImpl(
      {@ParseSfIdConverter() required this.messageId,
      @ParseSfIdConverter() required this.messagingSessionId});

  factory _$MessageIdAndSessionIdImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessageIdAndSessionIdImplFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId messageId;
  @override
  @ParseSfIdConverter()
  final SfId messagingSessionId;

  @override
  String toString() {
    return 'MessageIdAndSessionId(messageId: $messageId, messagingSessionId: $messagingSessionId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessageIdAndSessionIdImpl &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, messageId, messagingSessionId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessageIdAndSessionIdImplCopyWith<_$MessageIdAndSessionIdImpl>
      get copyWith => __$$MessageIdAndSessionIdImplCopyWithImpl<
          _$MessageIdAndSessionIdImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessageIdAndSessionIdImplToJson(
      this,
    );
  }
}

abstract class _MessageIdAndSessionId implements MessageIdAndSessionId {
  const factory _MessageIdAndSessionId(
          {@ParseSfIdConverter() required final SfId messageId,
          @ParseSfIdConverter() required final SfId messagingSessionId}) =
      _$MessageIdAndSessionIdImpl;

  factory _MessageIdAndSessionId.fromJson(Map<String, dynamic> json) =
      _$MessageIdAndSessionIdImpl.fromJson;

  @override
  @ParseSfIdConverter()
  SfId get messageId;
  @override
  @ParseSfIdConverter()
  SfId get messagingSessionId;
  @override
  @JsonKey(ignore: true)
  _$$MessageIdAndSessionIdImplCopyWith<_$MessageIdAndSessionIdImpl>
      get copyWith => throw _privateConstructorUsedError;
}
