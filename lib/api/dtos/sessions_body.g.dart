// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sessions_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SessionsBodyImpl _$$SessionsBodyImplFromJson(Map<String, dynamic> json) =>
    _$SessionsBodyImpl(
      userId: json['userId'] as String,
      instanceUrl: json['instanceUrl'] as String,
      deviceToken: json['deviceToken'] as String?,
      accessToken: json['accessToken'] as String,
      channelPlatformTypes: (json['channelPlatformTypes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      locale: json['localeCode'] as String,
      deviceType: json['deviceType'] as String?,
      expirationSeconds: (json['expirationSeconds'] as num?)?.toInt(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$SessionsBodyImplToJson(_$SessionsBodyImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'instanceUrl': instance.instanceUrl,
      'deviceToken': instance.deviceToken,
      'accessToken': instance.accessToken,
      'channelPlatformTypes': instance.channelPlatformTypes,
      'localeCode': instance.locale,
      'deviceType': instance.deviceType,
      'expirationSeconds': instance.expirationSeconds,
      'metadata': instance.metadata,
    };
