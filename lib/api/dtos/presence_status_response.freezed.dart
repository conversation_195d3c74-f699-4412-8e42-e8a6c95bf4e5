// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_status_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PresenceStatusResponse _$PresenceStatusResponseFromJson(
    Map<String, dynamic> json) {
  return _PresenceStatusResponse.fromJson(json);
}

/// @nodoc
mixin _$PresenceStatusResponse {
  PresenceConfiguration? get presenceConfiguration =>
      throw _privateConstructorUsedError;
  String? get scrtAccessToken => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresenceStatusResponseCopyWith<PresenceStatusResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresenceStatusResponseCopyWith<$Res> {
  factory $PresenceStatusResponseCopyWith(PresenceStatusResponse value,
          $Res Function(PresenceStatusResponse) then) =
      _$PresenceStatusResponseCopyWithImpl<$Res, PresenceStatusResponse>;
  @useResult
  $Res call(
      {PresenceConfiguration? presenceConfiguration, String? scrtAccessToken});

  $PresenceConfigurationCopyWith<$Res>? get presenceConfiguration;
}

/// @nodoc
class _$PresenceStatusResponseCopyWithImpl<$Res,
        $Val extends PresenceStatusResponse>
    implements $PresenceStatusResponseCopyWith<$Res> {
  _$PresenceStatusResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presenceConfiguration = freezed,
    Object? scrtAccessToken = freezed,
  }) {
    return _then(_value.copyWith(
      presenceConfiguration: freezed == presenceConfiguration
          ? _value.presenceConfiguration
          : presenceConfiguration // ignore: cast_nullable_to_non_nullable
              as PresenceConfiguration?,
      scrtAccessToken: freezed == scrtAccessToken
          ? _value.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PresenceConfigurationCopyWith<$Res>? get presenceConfiguration {
    if (_value.presenceConfiguration == null) {
      return null;
    }

    return $PresenceConfigurationCopyWith<$Res>(_value.presenceConfiguration!,
        (value) {
      return _then(_value.copyWith(presenceConfiguration: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PresenceStatusResponseImplCopyWith<$Res>
    implements $PresenceStatusResponseCopyWith<$Res> {
  factory _$$PresenceStatusResponseImplCopyWith(
          _$PresenceStatusResponseImpl value,
          $Res Function(_$PresenceStatusResponseImpl) then) =
      __$$PresenceStatusResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {PresenceConfiguration? presenceConfiguration, String? scrtAccessToken});

  @override
  $PresenceConfigurationCopyWith<$Res>? get presenceConfiguration;
}

/// @nodoc
class __$$PresenceStatusResponseImplCopyWithImpl<$Res>
    extends _$PresenceStatusResponseCopyWithImpl<$Res,
        _$PresenceStatusResponseImpl>
    implements _$$PresenceStatusResponseImplCopyWith<$Res> {
  __$$PresenceStatusResponseImplCopyWithImpl(
      _$PresenceStatusResponseImpl _value,
      $Res Function(_$PresenceStatusResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? presenceConfiguration = freezed,
    Object? scrtAccessToken = freezed,
  }) {
    return _then(_$PresenceStatusResponseImpl(
      presenceConfiguration: freezed == presenceConfiguration
          ? _value.presenceConfiguration
          : presenceConfiguration // ignore: cast_nullable_to_non_nullable
              as PresenceConfiguration?,
      scrtAccessToken: freezed == scrtAccessToken
          ? _value.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresenceStatusResponseImpl implements _PresenceStatusResponse {
  const _$PresenceStatusResponseImpl(
      {this.presenceConfiguration, this.scrtAccessToken});

  factory _$PresenceStatusResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresenceStatusResponseImplFromJson(json);

  @override
  final PresenceConfiguration? presenceConfiguration;
  @override
  final String? scrtAccessToken;

  @override
  String toString() {
    return 'PresenceStatusResponse(presenceConfiguration: $presenceConfiguration, scrtAccessToken: $scrtAccessToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresenceStatusResponseImpl &&
            (identical(other.presenceConfiguration, presenceConfiguration) ||
                other.presenceConfiguration == presenceConfiguration) &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, presenceConfiguration, scrtAccessToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresenceStatusResponseImplCopyWith<_$PresenceStatusResponseImpl>
      get copyWith => __$$PresenceStatusResponseImplCopyWithImpl<
          _$PresenceStatusResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresenceStatusResponseImplToJson(
      this,
    );
  }
}

abstract class _PresenceStatusResponse implements PresenceStatusResponse {
  const factory _PresenceStatusResponse(
      {final PresenceConfiguration? presenceConfiguration,
      final String? scrtAccessToken}) = _$PresenceStatusResponseImpl;

  factory _PresenceStatusResponse.fromJson(Map<String, dynamic> json) =
      _$PresenceStatusResponseImpl.fromJson;

  @override
  PresenceConfiguration? get presenceConfiguration;
  @override
  String? get scrtAccessToken;
  @override
  @JsonKey(ignore: true)
  _$$PresenceStatusResponseImplCopyWith<_$PresenceStatusResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
