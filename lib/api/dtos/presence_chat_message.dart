import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/presence_chat_attachment.dart';

part 'presence_chat_message.freezed.dart';
part 'presence_chat_message.g.dart';

@freezed
class PresenceChatMessage with _$PresenceChatMessage {
  const factory PresenceChatMessage({
    String? content,
    List<PresenceChatAttachment>? attachments,
    int? sequence,
    String? messageId,
    int? timestamp,
  }) = _PresenceChatMessage;

  factory PresenceChatMessage.fromJson(Map<String, dynamic> json) =>
      _$PresenceChatMessageFromJson(json);
}
