// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'session_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SessionData _$SessionDataFromJson(Map<String, dynamic> json) {
  return _SessionData.fromJson(json);
}

/// @nodoc
mixin _$SessionData {
  int get tenantId => throw _privateConstructorUsedError;
  String get sessionId => throw _privateConstructorUsedError;
  int get timeCreated => throw _privateConstructorUsedError;
  int get timeUpdated => throw _privateConstructorUsedError;
  int get expiresAt => throw _privateConstructorUsedError;
  List<String> get channelPlatformTypes => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionDataCopyWith<SessionData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionDataCopyWith<$Res> {
  factory $SessionDataCopyWith(
          SessionData value, $Res Function(SessionData) then) =
      _$SessionDataCopyWithImpl<$Res, SessionData>;
  @useResult
  $Res call(
      {int tenantId,
      String sessionId,
      int timeCreated,
      int timeUpdated,
      int expiresAt,
      List<String> channelPlatformTypes,
      String userId});
}

/// @nodoc
class _$SessionDataCopyWithImpl<$Res, $Val extends SessionData>
    implements $SessionDataCopyWith<$Res> {
  _$SessionDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tenantId = null,
    Object? sessionId = null,
    Object? timeCreated = null,
    Object? timeUpdated = null,
    Object? expiresAt = null,
    Object? channelPlatformTypes = null,
    Object? userId = null,
  }) {
    return _then(_value.copyWith(
      tenantId: null == tenantId
          ? _value.tenantId
          : tenantId // ignore: cast_nullable_to_non_nullable
              as int,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      timeCreated: null == timeCreated
          ? _value.timeCreated
          : timeCreated // ignore: cast_nullable_to_non_nullable
              as int,
      timeUpdated: null == timeUpdated
          ? _value.timeUpdated
          : timeUpdated // ignore: cast_nullable_to_non_nullable
              as int,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as int,
      channelPlatformTypes: null == channelPlatformTypes
          ? _value.channelPlatformTypes
          : channelPlatformTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionDataImplCopyWith<$Res>
    implements $SessionDataCopyWith<$Res> {
  factory _$$SessionDataImplCopyWith(
          _$SessionDataImpl value, $Res Function(_$SessionDataImpl) then) =
      __$$SessionDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int tenantId,
      String sessionId,
      int timeCreated,
      int timeUpdated,
      int expiresAt,
      List<String> channelPlatformTypes,
      String userId});
}

/// @nodoc
class __$$SessionDataImplCopyWithImpl<$Res>
    extends _$SessionDataCopyWithImpl<$Res, _$SessionDataImpl>
    implements _$$SessionDataImplCopyWith<$Res> {
  __$$SessionDataImplCopyWithImpl(
      _$SessionDataImpl _value, $Res Function(_$SessionDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tenantId = null,
    Object? sessionId = null,
    Object? timeCreated = null,
    Object? timeUpdated = null,
    Object? expiresAt = null,
    Object? channelPlatformTypes = null,
    Object? userId = null,
  }) {
    return _then(_$SessionDataImpl(
      tenantId: null == tenantId
          ? _value.tenantId
          : tenantId // ignore: cast_nullable_to_non_nullable
              as int,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      timeCreated: null == timeCreated
          ? _value.timeCreated
          : timeCreated // ignore: cast_nullable_to_non_nullable
              as int,
      timeUpdated: null == timeUpdated
          ? _value.timeUpdated
          : timeUpdated // ignore: cast_nullable_to_non_nullable
              as int,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as int,
      channelPlatformTypes: null == channelPlatformTypes
          ? _value._channelPlatformTypes
          : channelPlatformTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionDataImpl implements _SessionData {
  const _$SessionDataImpl(
      {required this.tenantId,
      required this.sessionId,
      required this.timeCreated,
      required this.timeUpdated,
      required this.expiresAt,
      required final List<String> channelPlatformTypes,
      required this.userId})
      : _channelPlatformTypes = channelPlatformTypes;

  factory _$SessionDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionDataImplFromJson(json);

  @override
  final int tenantId;
  @override
  final String sessionId;
  @override
  final int timeCreated;
  @override
  final int timeUpdated;
  @override
  final int expiresAt;
  final List<String> _channelPlatformTypes;
  @override
  List<String> get channelPlatformTypes {
    if (_channelPlatformTypes is EqualUnmodifiableListView)
      return _channelPlatformTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_channelPlatformTypes);
  }

  @override
  final String userId;

  @override
  String toString() {
    return 'SessionData(tenantId: $tenantId, sessionId: $sessionId, timeCreated: $timeCreated, timeUpdated: $timeUpdated, expiresAt: $expiresAt, channelPlatformTypes: $channelPlatformTypes, userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionDataImpl &&
            (identical(other.tenantId, tenantId) ||
                other.tenantId == tenantId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.timeCreated, timeCreated) ||
                other.timeCreated == timeCreated) &&
            (identical(other.timeUpdated, timeUpdated) ||
                other.timeUpdated == timeUpdated) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            const DeepCollectionEquality()
                .equals(other._channelPlatformTypes, _channelPlatformTypes) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      tenantId,
      sessionId,
      timeCreated,
      timeUpdated,
      expiresAt,
      const DeepCollectionEquality().hash(_channelPlatformTypes),
      userId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionDataImplCopyWith<_$SessionDataImpl> get copyWith =>
      __$$SessionDataImplCopyWithImpl<_$SessionDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionDataImplToJson(
      this,
    );
  }
}

abstract class _SessionData implements SessionData {
  const factory _SessionData(
      {required final int tenantId,
      required final String sessionId,
      required final int timeCreated,
      required final int timeUpdated,
      required final int expiresAt,
      required final List<String> channelPlatformTypes,
      required final String userId}) = _$SessionDataImpl;

  factory _SessionData.fromJson(Map<String, dynamic> json) =
      _$SessionDataImpl.fromJson;

  @override
  int get tenantId;
  @override
  String get sessionId;
  @override
  int get timeCreated;
  @override
  int get timeUpdated;
  @override
  int get expiresAt;
  @override
  List<String> get channelPlatformTypes;
  @override
  String get userId;
  @override
  @JsonKey(ignore: true)
  _$$SessionDataImplCopyWith<_$SessionDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
