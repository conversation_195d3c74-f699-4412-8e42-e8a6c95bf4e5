// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_sessions_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ActiveSessionsBody _$ActiveSessionsBodyFromJson(Map<String, dynamic> json) {
  return _ActiveSessionsBody.fromJson(json);
}

/// @nodoc
mixin _$ActiveSessionsBody {
  int get tenantId => throw _privateConstructorUsedError;
  int get limit => throw _privateConstructorUsedError;
  String get key => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ActiveSessionsBodyCopyWith<ActiveSessionsBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActiveSessionsBodyCopyWith<$Res> {
  factory $ActiveSessionsBodyCopyWith(
          ActiveSessionsBody value, $Res Function(ActiveSessionsBody) then) =
      _$ActiveSessionsBodyCopyWithImpl<$Res, ActiveSessionsBody>;
  @useResult
  $Res call({int tenantId, int limit, String key});
}

/// @nodoc
class _$ActiveSessionsBodyCopyWithImpl<$Res, $Val extends ActiveSessionsBody>
    implements $ActiveSessionsBodyCopyWith<$Res> {
  _$ActiveSessionsBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tenantId = null,
    Object? limit = null,
    Object? key = null,
  }) {
    return _then(_value.copyWith(
      tenantId: null == tenantId
          ? _value.tenantId
          : tenantId // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ActiveSessionsBodyImplCopyWith<$Res>
    implements $ActiveSessionsBodyCopyWith<$Res> {
  factory _$$ActiveSessionsBodyImplCopyWith(_$ActiveSessionsBodyImpl value,
          $Res Function(_$ActiveSessionsBodyImpl) then) =
      __$$ActiveSessionsBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int tenantId, int limit, String key});
}

/// @nodoc
class __$$ActiveSessionsBodyImplCopyWithImpl<$Res>
    extends _$ActiveSessionsBodyCopyWithImpl<$Res, _$ActiveSessionsBodyImpl>
    implements _$$ActiveSessionsBodyImplCopyWith<$Res> {
  __$$ActiveSessionsBodyImplCopyWithImpl(_$ActiveSessionsBodyImpl _value,
      $Res Function(_$ActiveSessionsBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tenantId = null,
    Object? limit = null,
    Object? key = null,
  }) {
    return _then(_$ActiveSessionsBodyImpl(
      tenantId: null == tenantId
          ? _value.tenantId
          : tenantId // ignore: cast_nullable_to_non_nullable
              as int,
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ActiveSessionsBodyImpl implements _ActiveSessionsBody {
  const _$ActiveSessionsBodyImpl(
      {required this.tenantId, required this.limit, required this.key});

  factory _$ActiveSessionsBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$ActiveSessionsBodyImplFromJson(json);

  @override
  final int tenantId;
  @override
  final int limit;
  @override
  final String key;

  @override
  String toString() {
    return 'ActiveSessionsBody(tenantId: $tenantId, limit: $limit, key: $key)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActiveSessionsBodyImpl &&
            (identical(other.tenantId, tenantId) ||
                other.tenantId == tenantId) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, tenantId, limit, key);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ActiveSessionsBodyImplCopyWith<_$ActiveSessionsBodyImpl> get copyWith =>
      __$$ActiveSessionsBodyImplCopyWithImpl<_$ActiveSessionsBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ActiveSessionsBodyImplToJson(
      this,
    );
  }
}

abstract class _ActiveSessionsBody implements ActiveSessionsBody {
  const factory _ActiveSessionsBody(
      {required final int tenantId,
      required final int limit,
      required final String key}) = _$ActiveSessionsBodyImpl;

  factory _ActiveSessionsBody.fromJson(Map<String, dynamic> json) =
      _$ActiveSessionsBodyImpl.fromJson;

  @override
  int get tenantId;
  @override
  int get limit;
  @override
  String get key;
  @override
  @JsonKey(ignore: true)
  _$$ActiveSessionsBodyImplCopyWith<_$ActiveSessionsBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
