import 'package:freezed_annotation/freezed_annotation.dart';

part 'outbound_message_response.freezed.dart';
part 'outbound_message_response.g.dart';

@freezed
class OutboundMessageResponse with _$OutboundMessageResponse {
  const factory OutboundMessageResponse({
    required String messagingSessionId,
  }) = _OutboundMessageResponse;

  factory OutboundMessageResponse.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessageResponseFromJson(json);
}
