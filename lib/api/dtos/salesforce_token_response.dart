import 'package:freezed_annotation/freezed_annotation.dart';

part 'salesforce_token_response.freezed.dart';
part 'salesforce_token_response.g.dart';

@freezed
class SalesforceTokenResponse with _$SalesforceTokenResponse {
  const factory SalesforceTokenResponse(
      {String? accessToken,
      int? statusCode,
      final int? expiresAt}) = _SalesforceTokenResponse;

  factory SalesforceTokenResponse.fromJson(Map<String, dynamic> json) =>
      _$SalesforceTokenResponseFromJson(json);
}
