// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'presence_chat_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PresenceChatMessageImpl _$$PresenceChatMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$PresenceChatMessageImpl(
      content: json['content'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map(
              (e) => PresenceChatAttachment.fromJson(e as Map<String, dynamic>))
          .toList(),
      sequence: (json['sequence'] as num?)?.toInt(),
      messageId: json['messageId'] as String?,
      timestamp: (json['timestamp'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$PresenceChatMessageImplToJson(
        _$PresenceChatMessageImpl instance) =>
    <String, dynamic>{
      'content': instance.content,
      'attachments': instance.attachments?.map((e) => e.toJson()).toList(),
      'sequence': instance.sequence,
      'messageId': instance.messageId,
      'timestamp': instance.timestamp,
    };
