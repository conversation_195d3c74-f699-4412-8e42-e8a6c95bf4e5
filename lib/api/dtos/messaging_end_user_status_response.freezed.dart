// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_end_user_status_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingEndUserStatusResponse _$MessagingEndUserStatusResponseFromJson(
    Map<String, dynamic> json) {
  return _MessagingEndUserStatusResponse.fromJson(json);
}

/// @nodoc
mixin _$MessagingEndUserStatusResponse {
  bool get canSendOutbound => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingEndUserStatusResponseCopyWith<MessagingEndUserStatusResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingEndUserStatusResponseCopyWith<$Res> {
  factory $MessagingEndUserStatusResponseCopyWith(
          MessagingEndUserStatusResponse value,
          $Res Function(MessagingEndUserStatusResponse) then) =
      _$MessagingEndUserStatusResponseCopyWithImpl<$Res,
          MessagingEndUserStatusResponse>;
  @useResult
  $Res call({bool canSendOutbound});
}

/// @nodoc
class _$MessagingEndUserStatusResponseCopyWithImpl<$Res,
        $Val extends MessagingEndUserStatusResponse>
    implements $MessagingEndUserStatusResponseCopyWith<$Res> {
  _$MessagingEndUserStatusResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? canSendOutbound = null,
  }) {
    return _then(_value.copyWith(
      canSendOutbound: null == canSendOutbound
          ? _value.canSendOutbound
          : canSendOutbound // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MessagingEndUserStatusResponseImplCopyWith<$Res>
    implements $MessagingEndUserStatusResponseCopyWith<$Res> {
  factory _$$MessagingEndUserStatusResponseImplCopyWith(
          _$MessagingEndUserStatusResponseImpl value,
          $Res Function(_$MessagingEndUserStatusResponseImpl) then) =
      __$$MessagingEndUserStatusResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool canSendOutbound});
}

/// @nodoc
class __$$MessagingEndUserStatusResponseImplCopyWithImpl<$Res>
    extends _$MessagingEndUserStatusResponseCopyWithImpl<$Res,
        _$MessagingEndUserStatusResponseImpl>
    implements _$$MessagingEndUserStatusResponseImplCopyWith<$Res> {
  __$$MessagingEndUserStatusResponseImplCopyWithImpl(
      _$MessagingEndUserStatusResponseImpl _value,
      $Res Function(_$MessagingEndUserStatusResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? canSendOutbound = null,
  }) {
    return _then(_$MessagingEndUserStatusResponseImpl(
      canSendOutbound: null == canSendOutbound
          ? _value.canSendOutbound
          : canSendOutbound // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingEndUserStatusResponseImpl
    extends _MessagingEndUserStatusResponse {
  const _$MessagingEndUserStatusResponseImpl({required this.canSendOutbound})
      : super._();

  factory _$MessagingEndUserStatusResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$MessagingEndUserStatusResponseImplFromJson(json);

  @override
  final bool canSendOutbound;

  @override
  String toString() {
    return 'MessagingEndUserStatusResponse(canSendOutbound: $canSendOutbound)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingEndUserStatusResponseImpl &&
            (identical(other.canSendOutbound, canSendOutbound) ||
                other.canSendOutbound == canSendOutbound));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, canSendOutbound);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingEndUserStatusResponseImplCopyWith<
          _$MessagingEndUserStatusResponseImpl>
      get copyWith => __$$MessagingEndUserStatusResponseImplCopyWithImpl<
          _$MessagingEndUserStatusResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingEndUserStatusResponseImplToJson(
      this,
    );
  }
}

abstract class _MessagingEndUserStatusResponse
    extends MessagingEndUserStatusResponse {
  const factory _MessagingEndUserStatusResponse(
          {required final bool canSendOutbound}) =
      _$MessagingEndUserStatusResponseImpl;
  const _MessagingEndUserStatusResponse._() : super._();

  factory _MessagingEndUserStatusResponse.fromJson(Map<String, dynamic> json) =
      _$MessagingEndUserStatusResponseImpl.fromJson;

  @override
  bool get canSendOutbound;
  @override
  @JsonKey(ignore: true)
  _$$MessagingEndUserStatusResponseImplCopyWith<
          _$MessagingEndUserStatusResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
