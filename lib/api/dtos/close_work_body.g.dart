// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_work_body.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CloseWorkBodyImpl _$$CloseWorkBodyImplFromJson(Map<String, dynamic> json) =>
    _$CloseWorkBodyImpl(
      requestId: json['requestId'] as String,
      endConversation: json['endConversation'] as bool?,
      workTargetId: const ParseSfIdConverter().from<PERSON>son(json['workTargetId']),
    );

Map<String, dynamic> _$$CloseWorkBodyImplToJson(_$CloseWorkBodyImpl instance) =>
    <String, dynamic>{
      'requestId': instance.requestId,
      'endConversation': instance.endConversation,
      'workTargetId': const ParseSfIdConverter().toJson(instance.workTargetId),
    };
