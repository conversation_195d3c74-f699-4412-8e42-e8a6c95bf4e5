// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'close_work_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CloseWorkBody _$CloseWorkBodyFromJson(Map<String, dynamic> json) {
  return _CloseWorkBody.fromJson(json);
}

/// @nodoc
mixin _$CloseWorkBody {
  String get requestId => throw _privateConstructorUsedError;
  bool? get endConversation => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get workTargetId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CloseWorkBodyCopyWith<CloseWorkBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CloseWorkBodyCopyWith<$Res> {
  factory $CloseWorkBodyCopyWith(
          CloseWorkBody value, $Res Function(CloseWorkBody) then) =
      _$CloseWorkBodyCopyWithImpl<$Res, CloseWorkBody>;
  @useResult
  $Res call(
      {String requestId,
      bool? endConversation,
      @ParseSfIdConverter() SfId workTargetId});

  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class _$CloseWorkBodyCopyWithImpl<$Res, $Val extends CloseWorkBody>
    implements $CloseWorkBodyCopyWith<$Res> {
  _$CloseWorkBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? endConversation = freezed,
    Object? workTargetId = null,
  }) {
    return _then(_value.copyWith(
      requestId: null == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      endConversation: freezed == endConversation
          ? _value.endConversation
          : endConversation // ignore: cast_nullable_to_non_nullable
              as bool?,
      workTargetId: null == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_value.workTargetId, (value) {
      return _then(_value.copyWith(workTargetId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CloseWorkBodyImplCopyWith<$Res>
    implements $CloseWorkBodyCopyWith<$Res> {
  factory _$$CloseWorkBodyImplCopyWith(
          _$CloseWorkBodyImpl value, $Res Function(_$CloseWorkBodyImpl) then) =
      __$$CloseWorkBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String requestId,
      bool? endConversation,
      @ParseSfIdConverter() SfId workTargetId});

  @override
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class __$$CloseWorkBodyImplCopyWithImpl<$Res>
    extends _$CloseWorkBodyCopyWithImpl<$Res, _$CloseWorkBodyImpl>
    implements _$$CloseWorkBodyImplCopyWith<$Res> {
  __$$CloseWorkBodyImplCopyWithImpl(
      _$CloseWorkBodyImpl _value, $Res Function(_$CloseWorkBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? endConversation = freezed,
    Object? workTargetId = null,
  }) {
    return _then(_$CloseWorkBodyImpl(
      requestId: null == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      endConversation: freezed == endConversation
          ? _value.endConversation
          : endConversation // ignore: cast_nullable_to_non_nullable
              as bool?,
      workTargetId: null == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CloseWorkBodyImpl implements _CloseWorkBody {
  const _$CloseWorkBodyImpl(
      {required this.requestId,
      this.endConversation,
      @ParseSfIdConverter() required this.workTargetId});

  factory _$CloseWorkBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$CloseWorkBodyImplFromJson(json);

  @override
  final String requestId;
  @override
  final bool? endConversation;
  @override
  @ParseSfIdConverter()
  final SfId workTargetId;

  @override
  String toString() {
    return 'CloseWorkBody(requestId: $requestId, endConversation: $endConversation, workTargetId: $workTargetId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CloseWorkBodyImpl &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.endConversation, endConversation) ||
                other.endConversation == endConversation) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, requestId, endConversation, workTargetId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CloseWorkBodyImplCopyWith<_$CloseWorkBodyImpl> get copyWith =>
      __$$CloseWorkBodyImplCopyWithImpl<_$CloseWorkBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CloseWorkBodyImplToJson(
      this,
    );
  }
}

abstract class _CloseWorkBody implements CloseWorkBody {
  const factory _CloseWorkBody(
          {required final String requestId,
          final bool? endConversation,
          @ParseSfIdConverter() required final SfId workTargetId}) =
      _$CloseWorkBodyImpl;

  factory _CloseWorkBody.fromJson(Map<String, dynamic> json) =
      _$CloseWorkBodyImpl.fromJson;

  @override
  String get requestId;
  @override
  bool? get endConversation;
  @override
  @ParseSfIdConverter()
  SfId get workTargetId;
  @override
  @JsonKey(ignore: true)
  _$$CloseWorkBodyImplCopyWith<_$CloseWorkBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
