// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_messages_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkMessagesBody _$WorkMessagesBodyFromJson(Map<String, dynamic> json) {
  return _WorkMessagesBody.fromJson(json);
}

/// @nodoc
mixin _$WorkMessagesBody {
  List<WorkMessageBody> get messages => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WorkMessagesBodyCopyWith<WorkMessagesBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkMessagesBodyCopyWith<$Res> {
  factory $WorkMessagesBodyCopyWith(
          WorkMessagesBody value, $Res Function(WorkMessagesBody) then) =
      _$WorkMessagesBodyCopyWithImpl<$Res, WorkMessagesBody>;
  @useResult
  $Res call({List<WorkMessageBody> messages});
}

/// @nodoc
class _$WorkMessagesBodyCopyWithImpl<$Res, $Val extends WorkMessagesBody>
    implements $WorkMessagesBodyCopyWith<$Res> {
  _$WorkMessagesBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messages = null,
  }) {
    return _then(_value.copyWith(
      messages: null == messages
          ? _value.messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<WorkMessageBody>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkMessagesBodyImplCopyWith<$Res>
    implements $WorkMessagesBodyCopyWith<$Res> {
  factory _$$WorkMessagesBodyImplCopyWith(_$WorkMessagesBodyImpl value,
          $Res Function(_$WorkMessagesBodyImpl) then) =
      __$$WorkMessagesBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<WorkMessageBody> messages});
}

/// @nodoc
class __$$WorkMessagesBodyImplCopyWithImpl<$Res>
    extends _$WorkMessagesBodyCopyWithImpl<$Res, _$WorkMessagesBodyImpl>
    implements _$$WorkMessagesBodyImplCopyWith<$Res> {
  __$$WorkMessagesBodyImplCopyWithImpl(_$WorkMessagesBodyImpl _value,
      $Res Function(_$WorkMessagesBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messages = null,
  }) {
    return _then(_$WorkMessagesBodyImpl(
      messages: null == messages
          ? _value._messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<WorkMessageBody>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkMessagesBodyImpl implements _WorkMessagesBody {
  const _$WorkMessagesBodyImpl({required final List<WorkMessageBody> messages})
      : _messages = messages;

  factory _$WorkMessagesBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkMessagesBodyImplFromJson(json);

  final List<WorkMessageBody> _messages;
  @override
  List<WorkMessageBody> get messages {
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messages);
  }

  @override
  String toString() {
    return 'WorkMessagesBody(messages: $messages)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkMessagesBodyImpl &&
            const DeepCollectionEquality().equals(other._messages, _messages));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_messages));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkMessagesBodyImplCopyWith<_$WorkMessagesBodyImpl> get copyWith =>
      __$$WorkMessagesBodyImplCopyWithImpl<_$WorkMessagesBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkMessagesBodyImplToJson(
      this,
    );
  }
}

abstract class _WorkMessagesBody implements WorkMessagesBody {
  const factory _WorkMessagesBody(
      {required final List<WorkMessageBody> messages}) = _$WorkMessagesBodyImpl;

  factory _WorkMessagesBody.fromJson(Map<String, dynamic> json) =
      _$WorkMessagesBodyImpl.fromJson;

  @override
  List<WorkMessageBody> get messages;
  @override
  @JsonKey(ignore: true)
  _$$WorkMessagesBodyImplCopyWith<_$WorkMessagesBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
