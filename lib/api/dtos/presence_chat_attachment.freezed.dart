// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_chat_attachment.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PresenceChatAttachment _$PresenceChatAttachmentFromJson(
    Map<String, dynamic> json) {
  return _PresenceChatAttachment.fromJson(json);
}

/// @nodoc
mixin _$PresenceChatAttachment {
  String get contentVersionId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresenceChatAttachmentCopyWith<PresenceChatAttachment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresenceChatAttachmentCopyWith<$Res> {
  factory $PresenceChatAttachmentCopyWith(PresenceChatAttachment value,
          $Res Function(PresenceChatAttachment) then) =
      _$PresenceChatAttachmentCopyWithImpl<$Res, PresenceChatAttachment>;
  @useResult
  $Res call({String contentVersionId});
}

/// @nodoc
class _$PresenceChatAttachmentCopyWithImpl<$Res,
        $Val extends PresenceChatAttachment>
    implements $PresenceChatAttachmentCopyWith<$Res> {
  _$PresenceChatAttachmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contentVersionId = null,
  }) {
    return _then(_value.copyWith(
      contentVersionId: null == contentVersionId
          ? _value.contentVersionId
          : contentVersionId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PresenceChatAttachmentImplCopyWith<$Res>
    implements $PresenceChatAttachmentCopyWith<$Res> {
  factory _$$PresenceChatAttachmentImplCopyWith(
          _$PresenceChatAttachmentImpl value,
          $Res Function(_$PresenceChatAttachmentImpl) then) =
      __$$PresenceChatAttachmentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String contentVersionId});
}

/// @nodoc
class __$$PresenceChatAttachmentImplCopyWithImpl<$Res>
    extends _$PresenceChatAttachmentCopyWithImpl<$Res,
        _$PresenceChatAttachmentImpl>
    implements _$$PresenceChatAttachmentImplCopyWith<$Res> {
  __$$PresenceChatAttachmentImplCopyWithImpl(
      _$PresenceChatAttachmentImpl _value,
      $Res Function(_$PresenceChatAttachmentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contentVersionId = null,
  }) {
    return _then(_$PresenceChatAttachmentImpl(
      contentVersionId: null == contentVersionId
          ? _value.contentVersionId
          : contentVersionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresenceChatAttachmentImpl implements _PresenceChatAttachment {
  const _$PresenceChatAttachmentImpl({required this.contentVersionId});

  factory _$PresenceChatAttachmentImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresenceChatAttachmentImplFromJson(json);

  @override
  final String contentVersionId;

  @override
  String toString() {
    return 'PresenceChatAttachment(contentVersionId: $contentVersionId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresenceChatAttachmentImpl &&
            (identical(other.contentVersionId, contentVersionId) ||
                other.contentVersionId == contentVersionId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, contentVersionId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresenceChatAttachmentImplCopyWith<_$PresenceChatAttachmentImpl>
      get copyWith => __$$PresenceChatAttachmentImplCopyWithImpl<
          _$PresenceChatAttachmentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresenceChatAttachmentImplToJson(
      this,
    );
  }
}

abstract class _PresenceChatAttachment implements PresenceChatAttachment {
  const factory _PresenceChatAttachment(
      {required final String contentVersionId}) = _$PresenceChatAttachmentImpl;

  factory _PresenceChatAttachment.fromJson(Map<String, dynamic> json) =
      _$PresenceChatAttachmentImpl.fromJson;

  @override
  String get contentVersionId;
  @override
  @JsonKey(ignore: true)
  _$$PresenceChatAttachmentImplCopyWith<_$PresenceChatAttachmentImpl>
      get copyWith => throw _privateConstructorUsedError;
}
