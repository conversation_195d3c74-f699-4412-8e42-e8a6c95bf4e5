// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sessions_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SessionsBody _$SessionsBodyFromJson(Map<String, dynamic> json) {
  return _SessionsBody.fromJson(json);
}

/// @nodoc
mixin _$SessionsBody {
  String get userId => throw _privateConstructorUsedError;
  String get instanceUrl => throw _privateConstructorUsedError;
  String? get deviceToken => throw _privateConstructorUsedError;
  String get accessToken => throw _privateConstructorUsedError;
  List<String> get channelPlatformTypes => throw _privateConstructorUsedError;
  @JsonKey(name: 'localeCode')
  String get locale => throw _privateConstructorUsedError;
  String? get deviceType => throw _privateConstructorUsedError;
  int? get expirationSeconds => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionsBodyCopyWith<SessionsBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionsBodyCopyWith<$Res> {
  factory $SessionsBodyCopyWith(
          SessionsBody value, $Res Function(SessionsBody) then) =
      _$SessionsBodyCopyWithImpl<$Res, SessionsBody>;
  @useResult
  $Res call(
      {String userId,
      String instanceUrl,
      String? deviceToken,
      String accessToken,
      List<String> channelPlatformTypes,
      @JsonKey(name: 'localeCode') String locale,
      String? deviceType,
      int? expirationSeconds,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$SessionsBodyCopyWithImpl<$Res, $Val extends SessionsBody>
    implements $SessionsBodyCopyWith<$Res> {
  _$SessionsBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? instanceUrl = null,
    Object? deviceToken = freezed,
    Object? accessToken = null,
    Object? channelPlatformTypes = null,
    Object? locale = null,
    Object? deviceType = freezed,
    Object? expirationSeconds = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      instanceUrl: null == instanceUrl
          ? _value.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String,
      deviceToken: freezed == deviceToken
          ? _value.deviceToken
          : deviceToken // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      channelPlatformTypes: null == channelPlatformTypes
          ? _value.channelPlatformTypes
          : channelPlatformTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      locale: null == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      expirationSeconds: freezed == expirationSeconds
          ? _value.expirationSeconds
          : expirationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionsBodyImplCopyWith<$Res>
    implements $SessionsBodyCopyWith<$Res> {
  factory _$$SessionsBodyImplCopyWith(
          _$SessionsBodyImpl value, $Res Function(_$SessionsBodyImpl) then) =
      __$$SessionsBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String userId,
      String instanceUrl,
      String? deviceToken,
      String accessToken,
      List<String> channelPlatformTypes,
      @JsonKey(name: 'localeCode') String locale,
      String? deviceType,
      int? expirationSeconds,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$SessionsBodyImplCopyWithImpl<$Res>
    extends _$SessionsBodyCopyWithImpl<$Res, _$SessionsBodyImpl>
    implements _$$SessionsBodyImplCopyWith<$Res> {
  __$$SessionsBodyImplCopyWithImpl(
      _$SessionsBodyImpl _value, $Res Function(_$SessionsBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? instanceUrl = null,
    Object? deviceToken = freezed,
    Object? accessToken = null,
    Object? channelPlatformTypes = null,
    Object? locale = null,
    Object? deviceType = freezed,
    Object? expirationSeconds = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$SessionsBodyImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      instanceUrl: null == instanceUrl
          ? _value.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String,
      deviceToken: freezed == deviceToken
          ? _value.deviceToken
          : deviceToken // ignore: cast_nullable_to_non_nullable
              as String?,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      channelPlatformTypes: null == channelPlatformTypes
          ? _value._channelPlatformTypes
          : channelPlatformTypes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      locale: null == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      expirationSeconds: freezed == expirationSeconds
          ? _value.expirationSeconds
          : expirationSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionsBodyImpl implements _SessionsBody {
  const _$SessionsBodyImpl(
      {required this.userId,
      required this.instanceUrl,
      required this.deviceToken,
      required this.accessToken,
      required final List<String> channelPlatformTypes,
      @JsonKey(name: 'localeCode') required this.locale,
      this.deviceType,
      this.expirationSeconds,
      final Map<String, dynamic>? metadata})
      : _channelPlatformTypes = channelPlatformTypes,
        _metadata = metadata;

  factory _$SessionsBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionsBodyImplFromJson(json);

  @override
  final String userId;
  @override
  final String instanceUrl;
  @override
  final String? deviceToken;
  @override
  final String accessToken;
  final List<String> _channelPlatformTypes;
  @override
  List<String> get channelPlatformTypes {
    if (_channelPlatformTypes is EqualUnmodifiableListView)
      return _channelPlatformTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_channelPlatformTypes);
  }

  @override
  @JsonKey(name: 'localeCode')
  final String locale;
  @override
  final String? deviceType;
  @override
  final int? expirationSeconds;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'SessionsBody(userId: $userId, instanceUrl: $instanceUrl, deviceToken: $deviceToken, accessToken: $accessToken, channelPlatformTypes: $channelPlatformTypes, locale: $locale, deviceType: $deviceType, expirationSeconds: $expirationSeconds, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionsBodyImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl) &&
            (identical(other.deviceToken, deviceToken) ||
                other.deviceToken == deviceToken) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            const DeepCollectionEquality()
                .equals(other._channelPlatformTypes, _channelPlatformTypes) &&
            (identical(other.locale, locale) || other.locale == locale) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.expirationSeconds, expirationSeconds) ||
                other.expirationSeconds == expirationSeconds) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      instanceUrl,
      deviceToken,
      accessToken,
      const DeepCollectionEquality().hash(_channelPlatformTypes),
      locale,
      deviceType,
      expirationSeconds,
      const DeepCollectionEquality().hash(_metadata));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionsBodyImplCopyWith<_$SessionsBodyImpl> get copyWith =>
      __$$SessionsBodyImplCopyWithImpl<_$SessionsBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionsBodyImplToJson(
      this,
    );
  }
}

abstract class _SessionsBody implements SessionsBody {
  const factory _SessionsBody(
      {required final String userId,
      required final String instanceUrl,
      required final String? deviceToken,
      required final String accessToken,
      required final List<String> channelPlatformTypes,
      @JsonKey(name: 'localeCode') required final String locale,
      final String? deviceType,
      final int? expirationSeconds,
      final Map<String, dynamic>? metadata}) = _$SessionsBodyImpl;

  factory _SessionsBody.fromJson(Map<String, dynamic> json) =
      _$SessionsBodyImpl.fromJson;

  @override
  String get userId;
  @override
  String get instanceUrl;
  @override
  String? get deviceToken;
  @override
  String get accessToken;
  @override
  List<String> get channelPlatformTypes;
  @override
  @JsonKey(name: 'localeCode')
  String get locale;
  @override
  String? get deviceType;
  @override
  int? get expirationSeconds;
  @override
  Map<String, dynamic>? get metadata;
  @override
  @JsonKey(ignore: true)
  _$$SessionsBodyImplCopyWith<_$SessionsBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
