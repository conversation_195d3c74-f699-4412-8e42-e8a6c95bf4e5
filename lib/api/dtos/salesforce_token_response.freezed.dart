// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'salesforce_token_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SalesforceTokenResponse _$SalesforceTokenResponseFromJson(
    Map<String, dynamic> json) {
  return _SalesforceTokenResponse.fromJson(json);
}

/// @nodoc
mixin _$SalesforceTokenResponse {
  String? get accessToken => throw _privateConstructorUsedError;
  int? get statusCode => throw _privateConstructorUsedError;
  int? get expiresAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SalesforceTokenResponseCopyWith<SalesforceTokenResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SalesforceTokenResponseCopyWith<$Res> {
  factory $SalesforceTokenResponseCopyWith(SalesforceTokenResponse value,
          $Res Function(SalesforceTokenResponse) then) =
      _$SalesforceTokenResponseCopyWithImpl<$Res, SalesforceTokenResponse>;
  @useResult
  $Res call({String? accessToken, int? statusCode, int? expiresAt});
}

/// @nodoc
class _$SalesforceTokenResponseCopyWithImpl<$Res,
        $Val extends SalesforceTokenResponse>
    implements $SalesforceTokenResponseCopyWith<$Res> {
  _$SalesforceTokenResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? statusCode = freezed,
    Object? expiresAt = freezed,
  }) {
    return _then(_value.copyWith(
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SalesforceTokenResponseImplCopyWith<$Res>
    implements $SalesforceTokenResponseCopyWith<$Res> {
  factory _$$SalesforceTokenResponseImplCopyWith(
          _$SalesforceTokenResponseImpl value,
          $Res Function(_$SalesforceTokenResponseImpl) then) =
      __$$SalesforceTokenResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? accessToken, int? statusCode, int? expiresAt});
}

/// @nodoc
class __$$SalesforceTokenResponseImplCopyWithImpl<$Res>
    extends _$SalesforceTokenResponseCopyWithImpl<$Res,
        _$SalesforceTokenResponseImpl>
    implements _$$SalesforceTokenResponseImplCopyWith<$Res> {
  __$$SalesforceTokenResponseImplCopyWithImpl(
      _$SalesforceTokenResponseImpl _value,
      $Res Function(_$SalesforceTokenResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = freezed,
    Object? statusCode = freezed,
    Object? expiresAt = freezed,
  }) {
    return _then(_$SalesforceTokenResponseImpl(
      accessToken: freezed == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SalesforceTokenResponseImpl implements _SalesforceTokenResponse {
  const _$SalesforceTokenResponseImpl(
      {this.accessToken, this.statusCode, this.expiresAt});

  factory _$SalesforceTokenResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SalesforceTokenResponseImplFromJson(json);

  @override
  final String? accessToken;
  @override
  final int? statusCode;
  @override
  final int? expiresAt;

  @override
  String toString() {
    return 'SalesforceTokenResponse(accessToken: $accessToken, statusCode: $statusCode, expiresAt: $expiresAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SalesforceTokenResponseImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, accessToken, statusCode, expiresAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SalesforceTokenResponseImplCopyWith<_$SalesforceTokenResponseImpl>
      get copyWith => __$$SalesforceTokenResponseImplCopyWithImpl<
          _$SalesforceTokenResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SalesforceTokenResponseImplToJson(
      this,
    );
  }
}

abstract class _SalesforceTokenResponse implements SalesforceTokenResponse {
  const factory _SalesforceTokenResponse(
      {final String? accessToken,
      final int? statusCode,
      final int? expiresAt}) = _$SalesforceTokenResponseImpl;

  factory _SalesforceTokenResponse.fromJson(Map<String, dynamic> json) =
      _$SalesforceTokenResponseImpl.fromJson;

  @override
  String? get accessToken;
  @override
  int? get statusCode;
  @override
  int? get expiresAt;
  @override
  @JsonKey(ignore: true)
  _$$SalesforceTokenResponseImplCopyWith<_$SalesforceTokenResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
