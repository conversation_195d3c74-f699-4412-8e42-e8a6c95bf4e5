import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/presence_chat_message.dart';

part 'work_response.freezed.dart';
part 'work_response.g.dart';

/// Shim Service "AcceptWork" response
@freezed
class WorkResponse with _$WorkResponse {
  const factory WorkResponse({
    String? conversationIdentifier,
    List<PresenceChatMessage>? messages,
    @Default(false) bool canTransfer,
    @Default(false) bool canRaiseFlag,
  }) = _WorkResponse;

  factory WorkResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkResponseFromJson(json);
}
