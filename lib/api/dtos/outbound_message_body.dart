import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/messaging_definition_parameters.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';

part 'outbound_message_body.freezed.dart';
part 'outbound_message_body.g.dart';

@freezed
class OutboundMessageBody with _$OutboundMessageBody {
  const factory OutboundMessageBody({
    required String messageId,
    @ParseSfIdConverter() required SfId messagingEndUserId,
    String?
        messageBody, // Message Body MUST NOT be set if we pass in a messagingDefinitionParameters

    MessagingDefinitionParameters?
        messagingDefinitionParameters, // Messaging Definition Parameters MUST NOT be set if we pass in a messageBody
  }) = _OutboundMessageBody;

  factory OutboundMessageBody.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessageBodyFromJson(json);

  factory OutboundMessageBody.fromQueueSendMessage(
      {required QueueSendMessage message,
      required Map<String, File> messageFileMap}) {
    if (message.definitionId != null && message.definitionId != null) {
      final messageDefinitionParameters =
          MessagingDefinitionParameters(id: message.definitionId!);
      return OutboundMessageBody(
        messageId: message.messageId,
        messagingEndUserId: message.messagingEndUserId!,
        messagingDefinitionParameters: messageDefinitionParameters,
      );
    }
    return OutboundMessageBody(
      messageId: message.messageId,
      messagingEndUserId: message.messagingEndUserId!,
      messageBody: message.messageContent ?? '',
    );
  }
}
