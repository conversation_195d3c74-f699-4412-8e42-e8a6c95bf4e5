// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_messages_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkMessagesResponse _$WorkMessagesResponseFromJson(Map<String, dynamic> json) {
  return _WorkMessagesResponse.fromJson(json);
}

/// @nodoc
mixin _$WorkMessagesResponse {
  List<SendFailure>? get failures => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WorkMessagesResponseCopyWith<WorkMessagesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkMessagesResponseCopyWith<$Res> {
  factory $WorkMessagesResponseCopyWith(WorkMessagesResponse value,
          $Res Function(WorkMessagesResponse) then) =
      _$WorkMessagesResponseCopyWithImpl<$Res, WorkMessagesResponse>;
  @useResult
  $Res call({List<SendFailure>? failures});
}

/// @nodoc
class _$WorkMessagesResponseCopyWithImpl<$Res,
        $Val extends WorkMessagesResponse>
    implements $WorkMessagesResponseCopyWith<$Res> {
  _$WorkMessagesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failures = freezed,
  }) {
    return _then(_value.copyWith(
      failures: freezed == failures
          ? _value.failures
          : failures // ignore: cast_nullable_to_non_nullable
              as List<SendFailure>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkMessagesResponseImplCopyWith<$Res>
    implements $WorkMessagesResponseCopyWith<$Res> {
  factory _$$WorkMessagesResponseImplCopyWith(_$WorkMessagesResponseImpl value,
          $Res Function(_$WorkMessagesResponseImpl) then) =
      __$$WorkMessagesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SendFailure>? failures});
}

/// @nodoc
class __$$WorkMessagesResponseImplCopyWithImpl<$Res>
    extends _$WorkMessagesResponseCopyWithImpl<$Res, _$WorkMessagesResponseImpl>
    implements _$$WorkMessagesResponseImplCopyWith<$Res> {
  __$$WorkMessagesResponseImplCopyWithImpl(_$WorkMessagesResponseImpl _value,
      $Res Function(_$WorkMessagesResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failures = freezed,
  }) {
    return _then(_$WorkMessagesResponseImpl(
      failures: freezed == failures
          ? _value._failures
          : failures // ignore: cast_nullable_to_non_nullable
              as List<SendFailure>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkMessagesResponseImpl implements _WorkMessagesResponse {
  const _$WorkMessagesResponseImpl({final List<SendFailure>? failures})
      : _failures = failures;

  factory _$WorkMessagesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkMessagesResponseImplFromJson(json);

  final List<SendFailure>? _failures;
  @override
  List<SendFailure>? get failures {
    final value = _failures;
    if (value == null) return null;
    if (_failures is EqualUnmodifiableListView) return _failures;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'WorkMessagesResponse(failures: $failures)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkMessagesResponseImpl &&
            const DeepCollectionEquality().equals(other._failures, _failures));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_failures));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkMessagesResponseImplCopyWith<_$WorkMessagesResponseImpl>
      get copyWith =>
          __$$WorkMessagesResponseImplCopyWithImpl<_$WorkMessagesResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkMessagesResponseImplToJson(
      this,
    );
  }
}

abstract class _WorkMessagesResponse implements WorkMessagesResponse {
  const factory _WorkMessagesResponse({final List<SendFailure>? failures}) =
      _$WorkMessagesResponseImpl;

  factory _WorkMessagesResponse.fromJson(Map<String, dynamic> json) =
      _$WorkMessagesResponseImpl.fromJson;

  @override
  List<SendFailure>? get failures;
  @override
  @JsonKey(ignore: true)
  _$$WorkMessagesResponseImplCopyWith<_$WorkMessagesResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
