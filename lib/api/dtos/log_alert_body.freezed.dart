// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_alert_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LogAlertBody _$LogAlertBodyFromJson(Map<String, dynamic> json) {
  return _LogAlertBody.fromJson(json);
}

/// @nodoc
mixin _$LogAlertBody {
  String get message => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogAlertBodyCopyWith<LogAlertBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogAlertBodyCopyWith<$Res> {
  factory $LogAlertBodyCopyWith(
          LogAlertBody value, $Res Function(LogAlertBody) then) =
      _$LogAlertBodyCopyWithImpl<$Res, LogAlertBody>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class _$LogAlertBodyCopyWithImpl<$Res, $Val extends LogAlertBody>
    implements $LogAlertBodyCopyWith<$Res> {
  _$LogAlertBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LogAlertBodyImplCopyWith<$Res>
    implements $LogAlertBodyCopyWith<$Res> {
  factory _$$LogAlertBodyImplCopyWith(
          _$LogAlertBodyImpl value, $Res Function(_$LogAlertBodyImpl) then) =
      __$$LogAlertBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$LogAlertBodyImplCopyWithImpl<$Res>
    extends _$LogAlertBodyCopyWithImpl<$Res, _$LogAlertBodyImpl>
    implements _$$LogAlertBodyImplCopyWith<$Res> {
  __$$LogAlertBodyImplCopyWithImpl(
      _$LogAlertBodyImpl _value, $Res Function(_$LogAlertBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$LogAlertBodyImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LogAlertBodyImpl implements _LogAlertBody {
  const _$LogAlertBodyImpl({required this.message});

  factory _$LogAlertBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$LogAlertBodyImplFromJson(json);

  @override
  final String message;

  @override
  String toString() {
    return 'LogAlertBody(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LogAlertBodyImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LogAlertBodyImplCopyWith<_$LogAlertBodyImpl> get copyWith =>
      __$$LogAlertBodyImplCopyWithImpl<_$LogAlertBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LogAlertBodyImplToJson(
      this,
    );
  }
}

abstract class _LogAlertBody implements LogAlertBody {
  const factory _LogAlertBody({required final String message}) =
      _$LogAlertBodyImpl;

  factory _LogAlertBody.fromJson(Map<String, dynamic> json) =
      _$LogAlertBodyImpl.fromJson;

  @override
  String get message;
  @override
  @JsonKey(ignore: true)
  _$$LogAlertBodyImplCopyWith<_$LogAlertBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
