import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'transfer_messaging_session_body.freezed.dart';
part 'transfer_messaging_session_body.g.dart';

enum TransferDestinationType {
  @JsonValue("queue")
  queue,
  @JsonValue("agent")
  agent,
  @JsonValue("flow")
  flow
}

@freezed
class TransferMessagingSessionBody with _$TransferMessagingSessionBody {
  const TransferMessagingSessionBody._();

  const factory TransferMessagingSessionBody({
    required TransferDestinationType destinationType,
    @ParseSfIdConverter() required SfId destinationId,
  }) = _TransferMessagingSessionBody;

  factory TransferMessagingSessionBody.fromJson(Map<String, dynamic> json) =>
      _$TransferMessagingSessionBodyFromJson(json);
}
