// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_metadata.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LogMetadata _$LogMetadataFromJson(Map<String, dynamic> json) {
  return _LogMetadata.fromJson(json);
}

/// @nodoc
mixin _$LogMetadata {
  String? get buildNumber => throw _privateConstructorUsedError;
  String? get versionNumber => throw _privateConstructorUsedError;
  String? get osVersion => throw _privateConstructorUsedError;
  @nullableEnumerated
  OSName? get osName => throw _privateConstructorUsedError;
  String? get manufacturer => throw _privateConstructorUsedError;
  String? get model => throw _privateConstructorUsedError;
  @nullableEnumerated
  DeviceType? get deviceType => throw _privateConstructorUsedError;
  String? get carrier => throw _privateConstructorUsedError;
  int? get batteryLevel => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  bool? get debugMode => throw _privateConstructorUsedError;
  bool? get profileMode => throw _privateConstructorUsedError;
  bool? get releaseMode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogMetadataCopyWith<LogMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogMetadataCopyWith<$Res> {
  factory $LogMetadataCopyWith(
          LogMetadata value, $Res Function(LogMetadata) then) =
      _$LogMetadataCopyWithImpl<$Res, LogMetadata>;
  @useResult
  $Res call(
      {String? buildNumber,
      String? versionNumber,
      String? osVersion,
      @nullableEnumerated OSName? osName,
      String? manufacturer,
      String? model,
      @nullableEnumerated DeviceType? deviceType,
      String? carrier,
      int? batteryLevel,
      String? userId,
      String? sessionId,
      bool? debugMode,
      bool? profileMode,
      bool? releaseMode});
}

/// @nodoc
class _$LogMetadataCopyWithImpl<$Res, $Val extends LogMetadata>
    implements $LogMetadataCopyWith<$Res> {
  _$LogMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buildNumber = freezed,
    Object? versionNumber = freezed,
    Object? osVersion = freezed,
    Object? osName = freezed,
    Object? manufacturer = freezed,
    Object? model = freezed,
    Object? deviceType = freezed,
    Object? carrier = freezed,
    Object? batteryLevel = freezed,
    Object? userId = freezed,
    Object? sessionId = freezed,
    Object? debugMode = freezed,
    Object? profileMode = freezed,
    Object? releaseMode = freezed,
  }) {
    return _then(_value.copyWith(
      buildNumber: freezed == buildNumber
          ? _value.buildNumber
          : buildNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      versionNumber: freezed == versionNumber
          ? _value.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      osVersion: freezed == osVersion
          ? _value.osVersion
          : osVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      osName: freezed == osName
          ? _value.osName
          : osName // ignore: cast_nullable_to_non_nullable
              as OSName?,
      manufacturer: freezed == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      model: freezed == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as DeviceType?,
      carrier: freezed == carrier
          ? _value.carrier
          : carrier // ignore: cast_nullable_to_non_nullable
              as String?,
      batteryLevel: freezed == batteryLevel
          ? _value.batteryLevel
          : batteryLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      debugMode: freezed == debugMode
          ? _value.debugMode
          : debugMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      profileMode: freezed == profileMode
          ? _value.profileMode
          : profileMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      releaseMode: freezed == releaseMode
          ? _value.releaseMode
          : releaseMode // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LogMetadataImplCopyWith<$Res>
    implements $LogMetadataCopyWith<$Res> {
  factory _$$LogMetadataImplCopyWith(
          _$LogMetadataImpl value, $Res Function(_$LogMetadataImpl) then) =
      __$$LogMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? buildNumber,
      String? versionNumber,
      String? osVersion,
      @nullableEnumerated OSName? osName,
      String? manufacturer,
      String? model,
      @nullableEnumerated DeviceType? deviceType,
      String? carrier,
      int? batteryLevel,
      String? userId,
      String? sessionId,
      bool? debugMode,
      bool? profileMode,
      bool? releaseMode});
}

/// @nodoc
class __$$LogMetadataImplCopyWithImpl<$Res>
    extends _$LogMetadataCopyWithImpl<$Res, _$LogMetadataImpl>
    implements _$$LogMetadataImplCopyWith<$Res> {
  __$$LogMetadataImplCopyWithImpl(
      _$LogMetadataImpl _value, $Res Function(_$LogMetadataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buildNumber = freezed,
    Object? versionNumber = freezed,
    Object? osVersion = freezed,
    Object? osName = freezed,
    Object? manufacturer = freezed,
    Object? model = freezed,
    Object? deviceType = freezed,
    Object? carrier = freezed,
    Object? batteryLevel = freezed,
    Object? userId = freezed,
    Object? sessionId = freezed,
    Object? debugMode = freezed,
    Object? profileMode = freezed,
    Object? releaseMode = freezed,
  }) {
    return _then(_$LogMetadataImpl(
      buildNumber: freezed == buildNumber
          ? _value.buildNumber
          : buildNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      versionNumber: freezed == versionNumber
          ? _value.versionNumber
          : versionNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      osVersion: freezed == osVersion
          ? _value.osVersion
          : osVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      osName: freezed == osName
          ? _value.osName
          : osName // ignore: cast_nullable_to_non_nullable
              as OSName?,
      manufacturer: freezed == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      model: freezed == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as DeviceType?,
      carrier: freezed == carrier
          ? _value.carrier
          : carrier // ignore: cast_nullable_to_non_nullable
              as String?,
      batteryLevel: freezed == batteryLevel
          ? _value.batteryLevel
          : batteryLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      debugMode: freezed == debugMode
          ? _value.debugMode
          : debugMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      profileMode: freezed == profileMode
          ? _value.profileMode
          : profileMode // ignore: cast_nullable_to_non_nullable
              as bool?,
      releaseMode: freezed == releaseMode
          ? _value.releaseMode
          : releaseMode // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LogMetadataImpl extends _LogMetadata {
  const _$LogMetadataImpl(
      {this.buildNumber,
      this.versionNumber,
      this.osVersion,
      @nullableEnumerated this.osName,
      this.manufacturer,
      this.model,
      @nullableEnumerated this.deviceType,
      this.carrier,
      this.batteryLevel,
      this.userId,
      this.sessionId,
      this.debugMode,
      this.profileMode,
      this.releaseMode})
      : super._();

  factory _$LogMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$LogMetadataImplFromJson(json);

  @override
  final String? buildNumber;
  @override
  final String? versionNumber;
  @override
  final String? osVersion;
  @override
  @nullableEnumerated
  final OSName? osName;
  @override
  final String? manufacturer;
  @override
  final String? model;
  @override
  @nullableEnumerated
  final DeviceType? deviceType;
  @override
  final String? carrier;
  @override
  final int? batteryLevel;
  @override
  final String? userId;
  @override
  final String? sessionId;
  @override
  final bool? debugMode;
  @override
  final bool? profileMode;
  @override
  final bool? releaseMode;

  @override
  String toString() {
    return 'LogMetadata(buildNumber: $buildNumber, versionNumber: $versionNumber, osVersion: $osVersion, osName: $osName, manufacturer: $manufacturer, model: $model, deviceType: $deviceType, carrier: $carrier, batteryLevel: $batteryLevel, userId: $userId, sessionId: $sessionId, debugMode: $debugMode, profileMode: $profileMode, releaseMode: $releaseMode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LogMetadataImpl &&
            (identical(other.buildNumber, buildNumber) ||
                other.buildNumber == buildNumber) &&
            (identical(other.versionNumber, versionNumber) ||
                other.versionNumber == versionNumber) &&
            (identical(other.osVersion, osVersion) ||
                other.osVersion == osVersion) &&
            (identical(other.osName, osName) || other.osName == osName) &&
            (identical(other.manufacturer, manufacturer) ||
                other.manufacturer == manufacturer) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.carrier, carrier) || other.carrier == carrier) &&
            (identical(other.batteryLevel, batteryLevel) ||
                other.batteryLevel == batteryLevel) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.debugMode, debugMode) ||
                other.debugMode == debugMode) &&
            (identical(other.profileMode, profileMode) ||
                other.profileMode == profileMode) &&
            (identical(other.releaseMode, releaseMode) ||
                other.releaseMode == releaseMode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      buildNumber,
      versionNumber,
      osVersion,
      osName,
      manufacturer,
      model,
      deviceType,
      carrier,
      batteryLevel,
      userId,
      sessionId,
      debugMode,
      profileMode,
      releaseMode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LogMetadataImplCopyWith<_$LogMetadataImpl> get copyWith =>
      __$$LogMetadataImplCopyWithImpl<_$LogMetadataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LogMetadataImplToJson(
      this,
    );
  }
}

abstract class _LogMetadata extends LogMetadata {
  const factory _LogMetadata(
      {final String? buildNumber,
      final String? versionNumber,
      final String? osVersion,
      @nullableEnumerated final OSName? osName,
      final String? manufacturer,
      final String? model,
      @nullableEnumerated final DeviceType? deviceType,
      final String? carrier,
      final int? batteryLevel,
      final String? userId,
      final String? sessionId,
      final bool? debugMode,
      final bool? profileMode,
      final bool? releaseMode}) = _$LogMetadataImpl;
  const _LogMetadata._() : super._();

  factory _LogMetadata.fromJson(Map<String, dynamic> json) =
      _$LogMetadataImpl.fromJson;

  @override
  String? get buildNumber;
  @override
  String? get versionNumber;
  @override
  String? get osVersion;
  @override
  @nullableEnumerated
  OSName? get osName;
  @override
  String? get manufacturer;
  @override
  String? get model;
  @override
  @nullableEnumerated
  DeviceType? get deviceType;
  @override
  String? get carrier;
  @override
  int? get batteryLevel;
  @override
  String? get userId;
  @override
  String? get sessionId;
  @override
  bool? get debugMode;
  @override
  bool? get profileMode;
  @override
  bool? get releaseMode;
  @override
  @JsonKey(ignore: true)
  _$$LogMetadataImplCopyWith<_$LogMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
