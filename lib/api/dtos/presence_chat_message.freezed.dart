// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_chat_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PresenceChatMessage _$PresenceChatMessageFromJson(Map<String, dynamic> json) {
  return _PresenceChatMessage.fromJson(json);
}

/// @nodoc
mixin _$PresenceChatMessage {
  String? get content => throw _privateConstructorUsedError;
  List<PresenceChatAttachment>? get attachments =>
      throw _privateConstructorUsedError;
  int? get sequence => throw _privateConstructorUsedError;
  String? get messageId => throw _privateConstructorUsedError;
  int? get timestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresenceChatMessageCopyWith<PresenceChatMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresenceChatMessageCopyWith<$Res> {
  factory $PresenceChatMessageCopyWith(
          PresenceChatMessage value, $Res Function(PresenceChatMessage) then) =
      _$PresenceChatMessageCopyWithImpl<$Res, PresenceChatMessage>;
  @useResult
  $Res call(
      {String? content,
      List<PresenceChatAttachment>? attachments,
      int? sequence,
      String? messageId,
      int? timestamp});
}

/// @nodoc
class _$PresenceChatMessageCopyWithImpl<$Res, $Val extends PresenceChatMessage>
    implements $PresenceChatMessageCopyWith<$Res> {
  _$PresenceChatMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? attachments = freezed,
    Object? sequence = freezed,
    Object? messageId = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_value.copyWith(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      attachments: freezed == attachments
          ? _value.attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<PresenceChatAttachment>?,
      sequence: freezed == sequence
          ? _value.sequence
          : sequence // ignore: cast_nullable_to_non_nullable
              as int?,
      messageId: freezed == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PresenceChatMessageImplCopyWith<$Res>
    implements $PresenceChatMessageCopyWith<$Res> {
  factory _$$PresenceChatMessageImplCopyWith(_$PresenceChatMessageImpl value,
          $Res Function(_$PresenceChatMessageImpl) then) =
      __$$PresenceChatMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? content,
      List<PresenceChatAttachment>? attachments,
      int? sequence,
      String? messageId,
      int? timestamp});
}

/// @nodoc
class __$$PresenceChatMessageImplCopyWithImpl<$Res>
    extends _$PresenceChatMessageCopyWithImpl<$Res, _$PresenceChatMessageImpl>
    implements _$$PresenceChatMessageImplCopyWith<$Res> {
  __$$PresenceChatMessageImplCopyWithImpl(_$PresenceChatMessageImpl _value,
      $Res Function(_$PresenceChatMessageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = freezed,
    Object? attachments = freezed,
    Object? sequence = freezed,
    Object? messageId = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_$PresenceChatMessageImpl(
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      attachments: freezed == attachments
          ? _value._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<PresenceChatAttachment>?,
      sequence: freezed == sequence
          ? _value.sequence
          : sequence // ignore: cast_nullable_to_non_nullable
              as int?,
      messageId: freezed == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresenceChatMessageImpl implements _PresenceChatMessage {
  const _$PresenceChatMessageImpl(
      {this.content,
      final List<PresenceChatAttachment>? attachments,
      this.sequence,
      this.messageId,
      this.timestamp})
      : _attachments = attachments;

  factory _$PresenceChatMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresenceChatMessageImplFromJson(json);

  @override
  final String? content;
  final List<PresenceChatAttachment>? _attachments;
  @override
  List<PresenceChatAttachment>? get attachments {
    final value = _attachments;
    if (value == null) return null;
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? sequence;
  @override
  final String? messageId;
  @override
  final int? timestamp;

  @override
  String toString() {
    return 'PresenceChatMessage(content: $content, attachments: $attachments, sequence: $sequence, messageId: $messageId, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresenceChatMessageImpl &&
            (identical(other.content, content) || other.content == content) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments) &&
            (identical(other.sequence, sequence) ||
                other.sequence == sequence) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      content,
      const DeepCollectionEquality().hash(_attachments),
      sequence,
      messageId,
      timestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresenceChatMessageImplCopyWith<_$PresenceChatMessageImpl> get copyWith =>
      __$$PresenceChatMessageImplCopyWithImpl<_$PresenceChatMessageImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresenceChatMessageImplToJson(
      this,
    );
  }
}

abstract class _PresenceChatMessage implements PresenceChatMessage {
  const factory _PresenceChatMessage(
      {final String? content,
      final List<PresenceChatAttachment>? attachments,
      final int? sequence,
      final String? messageId,
      final int? timestamp}) = _$PresenceChatMessageImpl;

  factory _PresenceChatMessage.fromJson(Map<String, dynamic> json) =
      _$PresenceChatMessageImpl.fromJson;

  @override
  String? get content;
  @override
  List<PresenceChatAttachment>? get attachments;
  @override
  int? get sequence;
  @override
  String? get messageId;
  @override
  int? get timestamp;
  @override
  @JsonKey(ignore: true)
  _$$PresenceChatMessageImplCopyWith<_$PresenceChatMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
