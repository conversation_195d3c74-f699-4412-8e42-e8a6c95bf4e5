// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sessions_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SessionsResponse _$SessionsResponseFromJson(Map<String, dynamic> json) {
  return _SessionsResponse.fromJson(json);
}

/// @nodoc
mixin _$SessionsResponse {
  String get sessionToken => throw _privateConstructorUsedError;
  String get sessionId => throw _privateConstructorUsedError;
  int get expirationTime => throw _privateConstructorUsedError;
  List<PresenceStatus>? get presenceStatuses =>
      throw _privateConstructorUsedError;
  String? get presenceStatusId => throw _privateConstructorUsedError;
  String? get scrtAccessToken => throw _privateConstructorUsedError;
  String? get scrtHost => throw _privateConstructorUsedError;
  String get webSocketUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionsResponseCopyWith<SessionsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionsResponseCopyWith<$Res> {
  factory $SessionsResponseCopyWith(
          SessionsResponse value, $Res Function(SessionsResponse) then) =
      _$SessionsResponseCopyWithImpl<$Res, SessionsResponse>;
  @useResult
  $Res call(
      {String sessionToken,
      String sessionId,
      int expirationTime,
      List<PresenceStatus>? presenceStatuses,
      String? presenceStatusId,
      String? scrtAccessToken,
      String? scrtHost,
      String webSocketUrl});
}

/// @nodoc
class _$SessionsResponseCopyWithImpl<$Res, $Val extends SessionsResponse>
    implements $SessionsResponseCopyWith<$Res> {
  _$SessionsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionToken = null,
    Object? sessionId = null,
    Object? expirationTime = null,
    Object? presenceStatuses = freezed,
    Object? presenceStatusId = freezed,
    Object? scrtAccessToken = freezed,
    Object? scrtHost = freezed,
    Object? webSocketUrl = null,
  }) {
    return _then(_value.copyWith(
      sessionToken: null == sessionToken
          ? _value.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      expirationTime: null == expirationTime
          ? _value.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as int,
      presenceStatuses: freezed == presenceStatuses
          ? _value.presenceStatuses
          : presenceStatuses // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>?,
      presenceStatusId: freezed == presenceStatusId
          ? _value.presenceStatusId
          : presenceStatusId // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtAccessToken: freezed == scrtAccessToken
          ? _value.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtHost: freezed == scrtHost
          ? _value.scrtHost
          : scrtHost // ignore: cast_nullable_to_non_nullable
              as String?,
      webSocketUrl: null == webSocketUrl
          ? _value.webSocketUrl
          : webSocketUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionsResponseImplCopyWith<$Res>
    implements $SessionsResponseCopyWith<$Res> {
  factory _$$SessionsResponseImplCopyWith(_$SessionsResponseImpl value,
          $Res Function(_$SessionsResponseImpl) then) =
      __$$SessionsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String sessionToken,
      String sessionId,
      int expirationTime,
      List<PresenceStatus>? presenceStatuses,
      String? presenceStatusId,
      String? scrtAccessToken,
      String? scrtHost,
      String webSocketUrl});
}

/// @nodoc
class __$$SessionsResponseImplCopyWithImpl<$Res>
    extends _$SessionsResponseCopyWithImpl<$Res, _$SessionsResponseImpl>
    implements _$$SessionsResponseImplCopyWith<$Res> {
  __$$SessionsResponseImplCopyWithImpl(_$SessionsResponseImpl _value,
      $Res Function(_$SessionsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionToken = null,
    Object? sessionId = null,
    Object? expirationTime = null,
    Object? presenceStatuses = freezed,
    Object? presenceStatusId = freezed,
    Object? scrtAccessToken = freezed,
    Object? scrtHost = freezed,
    Object? webSocketUrl = null,
  }) {
    return _then(_$SessionsResponseImpl(
      sessionToken: null == sessionToken
          ? _value.sessionToken
          : sessionToken // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      expirationTime: null == expirationTime
          ? _value.expirationTime
          : expirationTime // ignore: cast_nullable_to_non_nullable
              as int,
      presenceStatuses: freezed == presenceStatuses
          ? _value._presenceStatuses
          : presenceStatuses // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>?,
      presenceStatusId: freezed == presenceStatusId
          ? _value.presenceStatusId
          : presenceStatusId // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtAccessToken: freezed == scrtAccessToken
          ? _value.scrtAccessToken
          : scrtAccessToken // ignore: cast_nullable_to_non_nullable
              as String?,
      scrtHost: freezed == scrtHost
          ? _value.scrtHost
          : scrtHost // ignore: cast_nullable_to_non_nullable
              as String?,
      webSocketUrl: null == webSocketUrl
          ? _value.webSocketUrl
          : webSocketUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionsResponseImpl implements _SessionsResponse {
  const _$SessionsResponseImpl(
      {required this.sessionToken,
      required this.sessionId,
      required this.expirationTime,
      final List<PresenceStatus>? presenceStatuses,
      this.presenceStatusId,
      this.scrtAccessToken,
      this.scrtHost,
      required this.webSocketUrl})
      : _presenceStatuses = presenceStatuses;

  factory _$SessionsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionsResponseImplFromJson(json);

  @override
  final String sessionToken;
  @override
  final String sessionId;
  @override
  final int expirationTime;
  final List<PresenceStatus>? _presenceStatuses;
  @override
  List<PresenceStatus>? get presenceStatuses {
    final value = _presenceStatuses;
    if (value == null) return null;
    if (_presenceStatuses is EqualUnmodifiableListView)
      return _presenceStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? presenceStatusId;
  @override
  final String? scrtAccessToken;
  @override
  final String? scrtHost;
  @override
  final String webSocketUrl;

  @override
  String toString() {
    return 'SessionsResponse(sessionToken: $sessionToken, sessionId: $sessionId, expirationTime: $expirationTime, presenceStatuses: $presenceStatuses, presenceStatusId: $presenceStatusId, scrtAccessToken: $scrtAccessToken, scrtHost: $scrtHost, webSocketUrl: $webSocketUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionsResponseImpl &&
            (identical(other.sessionToken, sessionToken) ||
                other.sessionToken == sessionToken) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.expirationTime, expirationTime) ||
                other.expirationTime == expirationTime) &&
            const DeepCollectionEquality()
                .equals(other._presenceStatuses, _presenceStatuses) &&
            (identical(other.presenceStatusId, presenceStatusId) ||
                other.presenceStatusId == presenceStatusId) &&
            (identical(other.scrtAccessToken, scrtAccessToken) ||
                other.scrtAccessToken == scrtAccessToken) &&
            (identical(other.scrtHost, scrtHost) ||
                other.scrtHost == scrtHost) &&
            (identical(other.webSocketUrl, webSocketUrl) ||
                other.webSocketUrl == webSocketUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionToken,
      sessionId,
      expirationTime,
      const DeepCollectionEquality().hash(_presenceStatuses),
      presenceStatusId,
      scrtAccessToken,
      scrtHost,
      webSocketUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionsResponseImplCopyWith<_$SessionsResponseImpl> get copyWith =>
      __$$SessionsResponseImplCopyWithImpl<_$SessionsResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionsResponseImplToJson(
      this,
    );
  }
}

abstract class _SessionsResponse implements SessionsResponse {
  const factory _SessionsResponse(
      {required final String sessionToken,
      required final String sessionId,
      required final int expirationTime,
      final List<PresenceStatus>? presenceStatuses,
      final String? presenceStatusId,
      final String? scrtAccessToken,
      final String? scrtHost,
      required final String webSocketUrl}) = _$SessionsResponseImpl;

  factory _SessionsResponse.fromJson(Map<String, dynamic> json) =
      _$SessionsResponseImpl.fromJson;

  @override
  String get sessionToken;
  @override
  String get sessionId;
  @override
  int get expirationTime;
  @override
  List<PresenceStatus>? get presenceStatuses;
  @override
  String? get presenceStatusId;
  @override
  String? get scrtAccessToken;
  @override
  String? get scrtHost;
  @override
  String get webSocketUrl;
  @override
  @JsonKey(ignore: true)
  _$$SessionsResponseImplCopyWith<_$SessionsResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
