// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_messages_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OutboundMessagesBody _$OutboundMessagesBodyFromJson(Map<String, dynamic> json) {
  return _OutboundMessagesBody.fromJson(json);
}

/// @nodoc
mixin _$OutboundMessagesBody {
  List<OutboundMessageBody> get messages => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OutboundMessagesBodyCopyWith<OutboundMessagesBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutboundMessagesBodyCopyWith<$Res> {
  factory $OutboundMessagesBodyCopyWith(OutboundMessagesBody value,
          $Res Function(OutboundMessagesBody) then) =
      _$OutboundMessagesBodyCopyWithImpl<$Res, OutboundMessagesBody>;
  @useResult
  $Res call({List<OutboundMessageBody> messages});
}

/// @nodoc
class _$OutboundMessagesBodyCopyWithImpl<$Res,
        $Val extends OutboundMessagesBody>
    implements $OutboundMessagesBodyCopyWith<$Res> {
  _$OutboundMessagesBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messages = null,
  }) {
    return _then(_value.copyWith(
      messages: null == messages
          ? _value.messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<OutboundMessageBody>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OutboundMessagesBodyImplCopyWith<$Res>
    implements $OutboundMessagesBodyCopyWith<$Res> {
  factory _$$OutboundMessagesBodyImplCopyWith(_$OutboundMessagesBodyImpl value,
          $Res Function(_$OutboundMessagesBodyImpl) then) =
      __$$OutboundMessagesBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<OutboundMessageBody> messages});
}

/// @nodoc
class __$$OutboundMessagesBodyImplCopyWithImpl<$Res>
    extends _$OutboundMessagesBodyCopyWithImpl<$Res, _$OutboundMessagesBodyImpl>
    implements _$$OutboundMessagesBodyImplCopyWith<$Res> {
  __$$OutboundMessagesBodyImplCopyWithImpl(_$OutboundMessagesBodyImpl _value,
      $Res Function(_$OutboundMessagesBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messages = null,
  }) {
    return _then(_$OutboundMessagesBodyImpl(
      messages: null == messages
          ? _value._messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<OutboundMessageBody>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OutboundMessagesBodyImpl implements _OutboundMessagesBody {
  const _$OutboundMessagesBodyImpl(
      {required final List<OutboundMessageBody> messages})
      : _messages = messages;

  factory _$OutboundMessagesBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$OutboundMessagesBodyImplFromJson(json);

  final List<OutboundMessageBody> _messages;
  @override
  List<OutboundMessageBody> get messages {
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messages);
  }

  @override
  String toString() {
    return 'OutboundMessagesBody(messages: $messages)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OutboundMessagesBodyImpl &&
            const DeepCollectionEquality().equals(other._messages, _messages));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_messages));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OutboundMessagesBodyImplCopyWith<_$OutboundMessagesBodyImpl>
      get copyWith =>
          __$$OutboundMessagesBodyImplCopyWithImpl<_$OutboundMessagesBodyImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OutboundMessagesBodyImplToJson(
      this,
    );
  }
}

abstract class _OutboundMessagesBody implements OutboundMessagesBody {
  const factory _OutboundMessagesBody(
          {required final List<OutboundMessageBody> messages}) =
      _$OutboundMessagesBodyImpl;

  factory _OutboundMessagesBody.fromJson(Map<String, dynamic> json) =
      _$OutboundMessagesBodyImpl.fromJson;

  @override
  List<OutboundMessageBody> get messages;
  @override
  @JsonKey(ignore: true)
  _$$OutboundMessagesBodyImplCopyWith<_$OutboundMessagesBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
