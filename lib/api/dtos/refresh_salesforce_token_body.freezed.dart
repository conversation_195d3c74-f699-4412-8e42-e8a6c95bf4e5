// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refresh_salesforce_token_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RefreshSalesforceTokenBody _$RefreshSalesforceTokenBodyFromJson(
    Map<String, dynamic> json) {
  return _RefreshSalesforceTokenBody.fromJson(json);
}

/// @nodoc
mixin _$RefreshSalesforceTokenBody {
  String get refreshToken => throw _privateConstructorUsedError;
  String get orgId => throw _privateConstructorUsedError;
  String get instanceUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RefreshSalesforceTokenBodyCopyWith<RefreshSalesforceTokenBody>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RefreshSalesforceTokenBodyCopyWith<$Res> {
  factory $RefreshSalesforceTokenBodyCopyWith(RefreshSalesforceTokenBody value,
          $Res Function(RefreshSalesforceTokenBody) then) =
      _$RefreshSalesforceTokenBodyCopyWithImpl<$Res,
          RefreshSalesforceTokenBody>;
  @useResult
  $Res call({String refreshToken, String orgId, String instanceUrl});
}

/// @nodoc
class _$RefreshSalesforceTokenBodyCopyWithImpl<$Res,
        $Val extends RefreshSalesforceTokenBody>
    implements $RefreshSalesforceTokenBodyCopyWith<$Res> {
  _$RefreshSalesforceTokenBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refreshToken = null,
    Object? orgId = null,
    Object? instanceUrl = null,
  }) {
    return _then(_value.copyWith(
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      orgId: null == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      instanceUrl: null == instanceUrl
          ? _value.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RefreshSalesforceTokenBodyImplCopyWith<$Res>
    implements $RefreshSalesforceTokenBodyCopyWith<$Res> {
  factory _$$RefreshSalesforceTokenBodyImplCopyWith(
          _$RefreshSalesforceTokenBodyImpl value,
          $Res Function(_$RefreshSalesforceTokenBodyImpl) then) =
      __$$RefreshSalesforceTokenBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String refreshToken, String orgId, String instanceUrl});
}

/// @nodoc
class __$$RefreshSalesforceTokenBodyImplCopyWithImpl<$Res>
    extends _$RefreshSalesforceTokenBodyCopyWithImpl<$Res,
        _$RefreshSalesforceTokenBodyImpl>
    implements _$$RefreshSalesforceTokenBodyImplCopyWith<$Res> {
  __$$RefreshSalesforceTokenBodyImplCopyWithImpl(
      _$RefreshSalesforceTokenBodyImpl _value,
      $Res Function(_$RefreshSalesforceTokenBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refreshToken = null,
    Object? orgId = null,
    Object? instanceUrl = null,
  }) {
    return _then(_$RefreshSalesforceTokenBodyImpl(
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
      orgId: null == orgId
          ? _value.orgId
          : orgId // ignore: cast_nullable_to_non_nullable
              as String,
      instanceUrl: null == instanceUrl
          ? _value.instanceUrl
          : instanceUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RefreshSalesforceTokenBodyImpl implements _RefreshSalesforceTokenBody {
  const _$RefreshSalesforceTokenBodyImpl(
      {required this.refreshToken,
      required this.orgId,
      required this.instanceUrl});

  factory _$RefreshSalesforceTokenBodyImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$RefreshSalesforceTokenBodyImplFromJson(json);

  @override
  final String refreshToken;
  @override
  final String orgId;
  @override
  final String instanceUrl;

  @override
  String toString() {
    return 'RefreshSalesforceTokenBody(refreshToken: $refreshToken, orgId: $orgId, instanceUrl: $instanceUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshSalesforceTokenBodyImpl &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.orgId, orgId) || other.orgId == orgId) &&
            (identical(other.instanceUrl, instanceUrl) ||
                other.instanceUrl == instanceUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, refreshToken, orgId, instanceUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RefreshSalesforceTokenBodyImplCopyWith<_$RefreshSalesforceTokenBodyImpl>
      get copyWith => __$$RefreshSalesforceTokenBodyImplCopyWithImpl<
          _$RefreshSalesforceTokenBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RefreshSalesforceTokenBodyImplToJson(
      this,
    );
  }
}

abstract class _RefreshSalesforceTokenBody
    implements RefreshSalesforceTokenBody {
  const factory _RefreshSalesforceTokenBody(
      {required final String refreshToken,
      required final String orgId,
      required final String instanceUrl}) = _$RefreshSalesforceTokenBodyImpl;

  factory _RefreshSalesforceTokenBody.fromJson(Map<String, dynamic> json) =
      _$RefreshSalesforceTokenBodyImpl.fromJson;

  @override
  String get refreshToken;
  @override
  String get orgId;
  @override
  String get instanceUrl;
  @override
  @JsonKey(ignore: true)
  _$$RefreshSalesforceTokenBodyImplCopyWith<_$RefreshSalesforceTokenBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
