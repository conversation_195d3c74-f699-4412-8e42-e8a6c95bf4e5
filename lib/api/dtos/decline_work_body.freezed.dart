// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'decline_work_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DeclineWorkBody _$DeclineWorkBodyFromJson(Map<String, dynamic> json) {
  return _DeclineWorkBody.fromJson(json);
}

/// @nodoc
mixin _$DeclineWorkBody {
  String get requestId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get workId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get workTargetId => throw _privateConstructorUsedError;
  String? get declineReason => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DeclineWorkBodyCopyWith<DeclineWorkBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeclineWorkBodyCopyWith<$Res> {
  factory $DeclineWorkBodyCopyWith(
          DeclineWorkBody value, $Res Function(DeclineWorkBody) then) =
      _$DeclineWorkBodyCopyWithImpl<$Res, DeclineWorkBody>;
  @useResult
  $Res call(
      {String requestId,
      @ParseSfIdConverter() SfId workId,
      @ParseSfIdConverter() SfId workTargetId,
      String? declineReason});

  $SfIdCopyWith<$Res> get workId;
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class _$DeclineWorkBodyCopyWithImpl<$Res, $Val extends DeclineWorkBody>
    implements $DeclineWorkBodyCopyWith<$Res> {
  _$DeclineWorkBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? workId = null,
    Object? workTargetId = null,
    Object? declineReason = freezed,
  }) {
    return _then(_value.copyWith(
      requestId: null == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      workId: null == workId
          ? _value.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
      declineReason: freezed == declineReason
          ? _value.declineReason
          : declineReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workId {
    return $SfIdCopyWith<$Res>(_value.workId, (value) {
      return _then(_value.copyWith(workId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_value.workTargetId, (value) {
      return _then(_value.copyWith(workTargetId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DeclineWorkBodyImplCopyWith<$Res>
    implements $DeclineWorkBodyCopyWith<$Res> {
  factory _$$DeclineWorkBodyImplCopyWith(_$DeclineWorkBodyImpl value,
          $Res Function(_$DeclineWorkBodyImpl) then) =
      __$$DeclineWorkBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String requestId,
      @ParseSfIdConverter() SfId workId,
      @ParseSfIdConverter() SfId workTargetId,
      String? declineReason});

  @override
  $SfIdCopyWith<$Res> get workId;
  @override
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class __$$DeclineWorkBodyImplCopyWithImpl<$Res>
    extends _$DeclineWorkBodyCopyWithImpl<$Res, _$DeclineWorkBodyImpl>
    implements _$$DeclineWorkBodyImplCopyWith<$Res> {
  __$$DeclineWorkBodyImplCopyWithImpl(
      _$DeclineWorkBodyImpl _value, $Res Function(_$DeclineWorkBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? workId = null,
    Object? workTargetId = null,
    Object? declineReason = freezed,
  }) {
    return _then(_$DeclineWorkBodyImpl(
      requestId: null == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      workId: null == workId
          ? _value.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
      declineReason: freezed == declineReason
          ? _value.declineReason
          : declineReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeclineWorkBodyImpl implements _DeclineWorkBody {
  const _$DeclineWorkBodyImpl(
      {required this.requestId,
      @ParseSfIdConverter() required this.workId,
      @ParseSfIdConverter() required this.workTargetId,
      this.declineReason});

  factory _$DeclineWorkBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeclineWorkBodyImplFromJson(json);

  @override
  final String requestId;
  @override
  @ParseSfIdConverter()
  final SfId workId;
  @override
  @ParseSfIdConverter()
  final SfId workTargetId;
  @override
  final String? declineReason;

  @override
  String toString() {
    return 'DeclineWorkBody(requestId: $requestId, workId: $workId, workTargetId: $workTargetId, declineReason: $declineReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeclineWorkBodyImpl &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.declineReason, declineReason) ||
                other.declineReason == declineReason));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, requestId, workId, workTargetId, declineReason);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DeclineWorkBodyImplCopyWith<_$DeclineWorkBodyImpl> get copyWith =>
      __$$DeclineWorkBodyImplCopyWithImpl<_$DeclineWorkBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeclineWorkBodyImplToJson(
      this,
    );
  }
}

abstract class _DeclineWorkBody implements DeclineWorkBody {
  const factory _DeclineWorkBody(
      {required final String requestId,
      @ParseSfIdConverter() required final SfId workId,
      @ParseSfIdConverter() required final SfId workTargetId,
      final String? declineReason}) = _$DeclineWorkBodyImpl;

  factory _DeclineWorkBody.fromJson(Map<String, dynamic> json) =
      _$DeclineWorkBodyImpl.fromJson;

  @override
  String get requestId;
  @override
  @ParseSfIdConverter()
  SfId get workId;
  @override
  @ParseSfIdConverter()
  SfId get workTargetId;
  @override
  String? get declineReason;
  @override
  @JsonKey(ignore: true)
  _$$DeclineWorkBodyImplCopyWith<_$DeclineWorkBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
