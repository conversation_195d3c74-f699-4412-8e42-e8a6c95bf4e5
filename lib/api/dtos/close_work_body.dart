import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'close_work_body.freezed.dart';
part 'close_work_body.g.dart';

@freezed
class CloseWorkBody with _$CloseWorkBody {
  const factory CloseWorkBody({
    required String requestId,
    bool? endConversation,
    @ParseSfIdConverter() required SfId workTargetId,
  }) = _CloseWorkBody;

  factory CloseWorkBody.fromJson(Map<String, dynamic> json) =>
      _$CloseWorkBodyFromJson(json);
}
