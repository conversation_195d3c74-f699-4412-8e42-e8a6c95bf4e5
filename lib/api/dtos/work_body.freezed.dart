// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'work_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WorkBody _$WorkBodyFromJson(Map<String, dynamic> json) {
  return _WorkBody.fromJson(json);
}

/// @nodoc
mixin _$WorkBody {
  String get requestId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get workId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get workTargetId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WorkBodyCopyWith<WorkBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkBodyCopyWith<$Res> {
  factory $WorkBodyCopyWith(WorkBody value, $Res Function(WorkBody) then) =
      _$WorkBodyCopyWithImpl<$Res, WorkBody>;
  @useResult
  $Res call(
      {String requestId,
      @ParseSfIdConverter() SfId workId,
      @ParseSfIdConverter() SfId workTargetId});

  $SfIdCopyWith<$Res> get workId;
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class _$WorkBodyCopyWithImpl<$Res, $Val extends WorkBody>
    implements $WorkBodyCopyWith<$Res> {
  _$WorkBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? workId = null,
    Object? workTargetId = null,
  }) {
    return _then(_value.copyWith(
      requestId: null == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      workId: null == workId
          ? _value.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workId {
    return $SfIdCopyWith<$Res>(_value.workId, (value) {
      return _then(_value.copyWith(workId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get workTargetId {
    return $SfIdCopyWith<$Res>(_value.workTargetId, (value) {
      return _then(_value.copyWith(workTargetId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WorkBodyImplCopyWith<$Res>
    implements $WorkBodyCopyWith<$Res> {
  factory _$$WorkBodyImplCopyWith(
          _$WorkBodyImpl value, $Res Function(_$WorkBodyImpl) then) =
      __$$WorkBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String requestId,
      @ParseSfIdConverter() SfId workId,
      @ParseSfIdConverter() SfId workTargetId});

  @override
  $SfIdCopyWith<$Res> get workId;
  @override
  $SfIdCopyWith<$Res> get workTargetId;
}

/// @nodoc
class __$$WorkBodyImplCopyWithImpl<$Res>
    extends _$WorkBodyCopyWithImpl<$Res, _$WorkBodyImpl>
    implements _$$WorkBodyImplCopyWith<$Res> {
  __$$WorkBodyImplCopyWithImpl(
      _$WorkBodyImpl _value, $Res Function(_$WorkBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestId = null,
    Object? workId = null,
    Object? workTargetId = null,
  }) {
    return _then(_$WorkBodyImpl(
      requestId: null == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String,
      workId: null == workId
          ? _value.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId,
      workTargetId: null == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkBodyImpl implements _WorkBody {
  const _$WorkBodyImpl(
      {required this.requestId,
      @ParseSfIdConverter() required this.workId,
      @ParseSfIdConverter() required this.workTargetId});

  factory _$WorkBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkBodyImplFromJson(json);

  @override
  final String requestId;
  @override
  @ParseSfIdConverter()
  final SfId workId;
  @override
  @ParseSfIdConverter()
  final SfId workTargetId;

  @override
  String toString() {
    return 'WorkBody(requestId: $requestId, workId: $workId, workTargetId: $workTargetId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkBodyImpl &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, requestId, workId, workTargetId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkBodyImplCopyWith<_$WorkBodyImpl> get copyWith =>
      __$$WorkBodyImplCopyWithImpl<_$WorkBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkBodyImplToJson(
      this,
    );
  }
}

abstract class _WorkBody implements WorkBody {
  const factory _WorkBody(
      {required final String requestId,
      @ParseSfIdConverter() required final SfId workId,
      @ParseSfIdConverter() required final SfId workTargetId}) = _$WorkBodyImpl;

  factory _WorkBody.fromJson(Map<String, dynamic> json) =
      _$WorkBodyImpl.fromJson;

  @override
  String get requestId;
  @override
  @ParseSfIdConverter()
  SfId get workId;
  @override
  @ParseSfIdConverter()
  SfId get workTargetId;
  @override
  @JsonKey(ignore: true)
  _$$WorkBodyImplCopyWith<_$WorkBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
