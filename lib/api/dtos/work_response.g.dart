// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WorkResponseImpl _$$WorkResponseImplFromJson(Map<String, dynamic> json) =>
    _$WorkResponseImpl(
      conversationIdentifier: json['conversationIdentifier'] as String?,
      messages: (json['messages'] as List<dynamic>?)
          ?.map((e) => PresenceChatMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
      canTransfer: json['canTransfer'] as bool? ?? false,
      canRaiseFlag: json['canRaiseFlag'] as bool? ?? false,
    );

Map<String, dynamic> _$$WorkResponseImplToJson(_$WorkResponseImpl instance) =>
    <String, dynamic>{
      'conversationIdentifier': instance.conversationIdentifier,
      'messages': instance.messages?.map((e) => e.toJson()).toList(),
      'canTransfer': instance.canTransfer,
      'canRaiseFlag': instance.canRaiseFlag,
    };
