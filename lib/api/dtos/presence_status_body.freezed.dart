// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_status_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PresenceStatusBody _$PresenceStatusBodyFromJson(Map<String, dynamic> json) {
  return _PresenceStatusBody.fromJson(json);
}

/// @nodoc
mixin _$PresenceStatusBody {
  String get id => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PresenceStatusBodyCopyWith<PresenceStatusBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresenceStatusBodyCopyWith<$Res> {
  factory $PresenceStatusBodyCopyWith(
          PresenceStatusBody value, $Res Function(PresenceStatusBody) then) =
      _$PresenceStatusBodyCopyWithImpl<$Res, PresenceStatusBody>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class _$PresenceStatusBodyCopyWithImpl<$Res, $Val extends PresenceStatusBody>
    implements $PresenceStatusBodyCopyWith<$Res> {
  _$PresenceStatusBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PresenceStatusBodyImplCopyWith<$Res>
    implements $PresenceStatusBodyCopyWith<$Res> {
  factory _$$PresenceStatusBodyImplCopyWith(_$PresenceStatusBodyImpl value,
          $Res Function(_$PresenceStatusBodyImpl) then) =
      __$$PresenceStatusBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$PresenceStatusBodyImplCopyWithImpl<$Res>
    extends _$PresenceStatusBodyCopyWithImpl<$Res, _$PresenceStatusBodyImpl>
    implements _$$PresenceStatusBodyImplCopyWith<$Res> {
  __$$PresenceStatusBodyImplCopyWithImpl(_$PresenceStatusBodyImpl _value,
      $Res Function(_$PresenceStatusBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$PresenceStatusBodyImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PresenceStatusBodyImpl implements _PresenceStatusBody {
  const _$PresenceStatusBodyImpl({required this.id});

  factory _$PresenceStatusBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$PresenceStatusBodyImplFromJson(json);

  @override
  final String id;

  @override
  String toString() {
    return 'PresenceStatusBody(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresenceStatusBodyImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresenceStatusBodyImplCopyWith<_$PresenceStatusBodyImpl> get copyWith =>
      __$$PresenceStatusBodyImplCopyWithImpl<_$PresenceStatusBodyImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PresenceStatusBodyImplToJson(
      this,
    );
  }
}

abstract class _PresenceStatusBody implements PresenceStatusBody {
  const factory _PresenceStatusBody({required final String id}) =
      _$PresenceStatusBodyImpl;

  factory _PresenceStatusBody.fromJson(Map<String, dynamic> json) =
      _$PresenceStatusBodyImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(ignore: true)
  _$$PresenceStatusBodyImplCopyWith<_$PresenceStatusBodyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
