import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/message_id_and_session_id.dart';
import 'package:x1440/api/dtos/send_failure.dart';

part 'outbound_messages_response.freezed.dart';
part 'outbound_messages_response.g.dart';

@freezed
class OutboundMessagesResponse with _$OutboundMessagesResponse {
  const factory OutboundMessagesResponse({
    List<MessageIdAndSessionId>? messageAndSessionIds,
    List<SendFailure>? failures,
  }) = _OutboundMessagesResponse;

  factory OutboundMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$OutboundMessagesResponseFromJson(json);
}