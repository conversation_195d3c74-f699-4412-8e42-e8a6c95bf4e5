// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unacknowledged_notifications_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UnacknowledgedNotificationsBody _$UnacknowledgedNotificationsBodyFromJson(
    Map<String, dynamic> json) {
  return _UnacknowledgedNotificationsBody.fromJson(json);
}

/// @nodoc
mixin _$UnacknowledgedNotificationsBody {
  int get limit => throw _privateConstructorUsedError;
  String get key => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UnacknowledgedNotificationsBodyCopyWith<UnacknowledgedNotificationsBody>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnacknowledgedNotificationsBodyCopyWith<$Res> {
  factory $UnacknowledgedNotificationsBodyCopyWith(
          UnacknowledgedNotificationsBody value,
          $Res Function(UnacknowledgedNotificationsBody) then) =
      _$UnacknowledgedNotificationsBodyCopyWithImpl<$Res,
          UnacknowledgedNotificationsBody>;
  @useResult
  $Res call({int limit, String key});
}

/// @nodoc
class _$UnacknowledgedNotificationsBodyCopyWithImpl<$Res,
        $Val extends UnacknowledgedNotificationsBody>
    implements $UnacknowledgedNotificationsBodyCopyWith<$Res> {
  _$UnacknowledgedNotificationsBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limit = null,
    Object? key = null,
  }) {
    return _then(_value.copyWith(
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UnacknowledgedNotificationsBodyImplCopyWith<$Res>
    implements $UnacknowledgedNotificationsBodyCopyWith<$Res> {
  factory _$$UnacknowledgedNotificationsBodyImplCopyWith(
          _$UnacknowledgedNotificationsBodyImpl value,
          $Res Function(_$UnacknowledgedNotificationsBodyImpl) then) =
      __$$UnacknowledgedNotificationsBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int limit, String key});
}

/// @nodoc
class __$$UnacknowledgedNotificationsBodyImplCopyWithImpl<$Res>
    extends _$UnacknowledgedNotificationsBodyCopyWithImpl<$Res,
        _$UnacknowledgedNotificationsBodyImpl>
    implements _$$UnacknowledgedNotificationsBodyImplCopyWith<$Res> {
  __$$UnacknowledgedNotificationsBodyImplCopyWithImpl(
      _$UnacknowledgedNotificationsBodyImpl _value,
      $Res Function(_$UnacknowledgedNotificationsBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limit = null,
    Object? key = null,
  }) {
    return _then(_$UnacknowledgedNotificationsBodyImpl(
      limit: null == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UnacknowledgedNotificationsBodyImpl
    implements _UnacknowledgedNotificationsBody {
  const _$UnacknowledgedNotificationsBodyImpl(
      {required this.limit, required this.key});

  factory _$UnacknowledgedNotificationsBodyImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$UnacknowledgedNotificationsBodyImplFromJson(json);

  @override
  final int limit;
  @override
  final String key;

  @override
  String toString() {
    return 'UnacknowledgedNotificationsBody(limit: $limit, key: $key)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnacknowledgedNotificationsBodyImpl &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, limit, key);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UnacknowledgedNotificationsBodyImplCopyWith<
          _$UnacknowledgedNotificationsBodyImpl>
      get copyWith => __$$UnacknowledgedNotificationsBodyImplCopyWithImpl<
          _$UnacknowledgedNotificationsBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UnacknowledgedNotificationsBodyImplToJson(
      this,
    );
  }
}

abstract class _UnacknowledgedNotificationsBody
    implements UnacknowledgedNotificationsBody {
  const factory _UnacknowledgedNotificationsBody(
      {required final int limit,
      required final String key}) = _$UnacknowledgedNotificationsBodyImpl;

  factory _UnacknowledgedNotificationsBody.fromJson(Map<String, dynamic> json) =
      _$UnacknowledgedNotificationsBodyImpl.fromJson;

  @override
  int get limit;
  @override
  String get key;
  @override
  @JsonKey(ignore: true)
  _$$UnacknowledgedNotificationsBodyImplCopyWith<
          _$UnacknowledgedNotificationsBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
