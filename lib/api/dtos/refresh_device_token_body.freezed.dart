// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refresh_device_token_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RefreshDeviceTokenBody _$RefreshDeviceTokenBodyFromJson(
    Map<String, dynamic> json) {
  return _RefreshDeviceTokenBody.fromJson(json);
}

/// @nodoc
mixin _$RefreshDeviceTokenBody {
  String get deviceToken => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RefreshDeviceTokenBodyCopyWith<RefreshDeviceTokenBody> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RefreshDeviceTokenBodyCopyWith<$Res> {
  factory $RefreshDeviceTokenBodyCopyWith(RefreshDeviceTokenBody value,
          $Res Function(RefreshDeviceTokenBody) then) =
      _$RefreshDeviceTokenBodyCopyWithImpl<$Res, RefreshDeviceTokenBody>;
  @useResult
  $Res call({String deviceToken});
}

/// @nodoc
class _$RefreshDeviceTokenBodyCopyWithImpl<$Res,
        $Val extends RefreshDeviceTokenBody>
    implements $RefreshDeviceTokenBodyCopyWith<$Res> {
  _$RefreshDeviceTokenBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceToken = null,
  }) {
    return _then(_value.copyWith(
      deviceToken: null == deviceToken
          ? _value.deviceToken
          : deviceToken // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RefreshDeviceTokenBodyImplCopyWith<$Res>
    implements $RefreshDeviceTokenBodyCopyWith<$Res> {
  factory _$$RefreshDeviceTokenBodyImplCopyWith(
          _$RefreshDeviceTokenBodyImpl value,
          $Res Function(_$RefreshDeviceTokenBodyImpl) then) =
      __$$RefreshDeviceTokenBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String deviceToken});
}

/// @nodoc
class __$$RefreshDeviceTokenBodyImplCopyWithImpl<$Res>
    extends _$RefreshDeviceTokenBodyCopyWithImpl<$Res,
        _$RefreshDeviceTokenBodyImpl>
    implements _$$RefreshDeviceTokenBodyImplCopyWith<$Res> {
  __$$RefreshDeviceTokenBodyImplCopyWithImpl(
      _$RefreshDeviceTokenBodyImpl _value,
      $Res Function(_$RefreshDeviceTokenBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceToken = null,
  }) {
    return _then(_$RefreshDeviceTokenBodyImpl(
      deviceToken: null == deviceToken
          ? _value.deviceToken
          : deviceToken // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RefreshDeviceTokenBodyImpl implements _RefreshDeviceTokenBody {
  const _$RefreshDeviceTokenBodyImpl({required this.deviceToken});

  factory _$RefreshDeviceTokenBodyImpl.fromJson(Map<String, dynamic> json) =>
      _$$RefreshDeviceTokenBodyImplFromJson(json);

  @override
  final String deviceToken;

  @override
  String toString() {
    return 'RefreshDeviceTokenBody(deviceToken: $deviceToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshDeviceTokenBodyImpl &&
            (identical(other.deviceToken, deviceToken) ||
                other.deviceToken == deviceToken));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, deviceToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RefreshDeviceTokenBodyImplCopyWith<_$RefreshDeviceTokenBodyImpl>
      get copyWith => __$$RefreshDeviceTokenBodyImplCopyWithImpl<
          _$RefreshDeviceTokenBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RefreshDeviceTokenBodyImplToJson(
      this,
    );
  }
}

abstract class _RefreshDeviceTokenBody implements RefreshDeviceTokenBody {
  const factory _RefreshDeviceTokenBody({required final String deviceToken}) =
      _$RefreshDeviceTokenBodyImpl;

  factory _RefreshDeviceTokenBody.fromJson(Map<String, dynamic> json) =
      _$RefreshDeviceTokenBodyImpl.fromJson;

  @override
  String get deviceToken;
  @override
  @JsonKey(ignore: true)
  _$$RefreshDeviceTokenBodyImplCopyWith<_$RefreshDeviceTokenBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
