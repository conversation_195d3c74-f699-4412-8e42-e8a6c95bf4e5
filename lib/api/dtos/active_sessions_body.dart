import 'package:freezed_annotation/freezed_annotation.dart';

part 'active_sessions_body.freezed.dart';
part 'active_sessions_body.g.dart';

@freezed
class ActiveSessionsBody with _$ActiveSessionsBody {
  const factory ActiveSessionsBody(
      {required int tenantId,
      required int limit,
      required String key}) = _ActiveSessionsBody;

  factory ActiveSessionsBody.fromJson(Map<String, dynamic> json) =>
      _$ActiveSessionsBodyFromJson(json);
}
