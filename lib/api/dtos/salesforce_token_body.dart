import 'package:freezed_annotation/freezed_annotation.dart';

part 'salesforce_token_body.freezed.dart';
part 'salesforce_token_body.g.dart';

@freezed
class SalesforceTokenBody with _$SalesforceTokenBody {
  const factory SalesforceTokenBody(
      {required String orgId,
      required String accessToken,
      final int? expirationSeconds}) = _SalesforceTokenBody;

  factory SalesforceTokenBody.fromJson(Map<String, dynamic> json) =>
      _$SalesforceTokenBodyFromJson(json);
}
