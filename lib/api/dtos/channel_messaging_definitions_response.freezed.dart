// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'channel_messaging_definitions_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ChannelMessagingDefinitionsResponse
    _$ChannelMessagingDefinitionsResponseFromJson(Map<String, dynamic> json) {
  return _ChannelMessagingDefinitionsResponse.fromJson(json);
}

/// @nodoc
mixin _$ChannelMessagingDefinitionsResponse {
  List<MessagingDefinition> get definitions =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChannelMessagingDefinitionsResponseCopyWith<
          ChannelMessagingDefinitionsResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChannelMessagingDefinitionsResponseCopyWith<$Res> {
  factory $ChannelMessagingDefinitionsResponseCopyWith(
          ChannelMessagingDefinitionsResponse value,
          $Res Function(ChannelMessagingDefinitionsResponse) then) =
      _$ChannelMessagingDefinitionsResponseCopyWithImpl<$Res,
          ChannelMessagingDefinitionsResponse>;
  @useResult
  $Res call({List<MessagingDefinition> definitions});
}

/// @nodoc
class _$ChannelMessagingDefinitionsResponseCopyWithImpl<$Res,
        $Val extends ChannelMessagingDefinitionsResponse>
    implements $ChannelMessagingDefinitionsResponseCopyWith<$Res> {
  _$ChannelMessagingDefinitionsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? definitions = null,
  }) {
    return _then(_value.copyWith(
      definitions: null == definitions
          ? _value.definitions
          : definitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChannelMessagingDefinitionsResponseImplCopyWith<$Res>
    implements $ChannelMessagingDefinitionsResponseCopyWith<$Res> {
  factory _$$ChannelMessagingDefinitionsResponseImplCopyWith(
          _$ChannelMessagingDefinitionsResponseImpl value,
          $Res Function(_$ChannelMessagingDefinitionsResponseImpl) then) =
      __$$ChannelMessagingDefinitionsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<MessagingDefinition> definitions});
}

/// @nodoc
class __$$ChannelMessagingDefinitionsResponseImplCopyWithImpl<$Res>
    extends _$ChannelMessagingDefinitionsResponseCopyWithImpl<$Res,
        _$ChannelMessagingDefinitionsResponseImpl>
    implements _$$ChannelMessagingDefinitionsResponseImplCopyWith<$Res> {
  __$$ChannelMessagingDefinitionsResponseImplCopyWithImpl(
      _$ChannelMessagingDefinitionsResponseImpl _value,
      $Res Function(_$ChannelMessagingDefinitionsResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? definitions = null,
  }) {
    return _then(_$ChannelMessagingDefinitionsResponseImpl(
      definitions: null == definitions
          ? _value._definitions
          : definitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChannelMessagingDefinitionsResponseImpl
    implements _ChannelMessagingDefinitionsResponse {
  const _$ChannelMessagingDefinitionsResponseImpl(
      {required final List<MessagingDefinition> definitions})
      : _definitions = definitions;

  factory _$ChannelMessagingDefinitionsResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ChannelMessagingDefinitionsResponseImplFromJson(json);

  final List<MessagingDefinition> _definitions;
  @override
  List<MessagingDefinition> get definitions {
    if (_definitions is EqualUnmodifiableListView) return _definitions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_definitions);
  }

  @override
  String toString() {
    return 'ChannelMessagingDefinitionsResponse(definitions: $definitions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChannelMessagingDefinitionsResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._definitions, _definitions));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_definitions));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChannelMessagingDefinitionsResponseImplCopyWith<
          _$ChannelMessagingDefinitionsResponseImpl>
      get copyWith => __$$ChannelMessagingDefinitionsResponseImplCopyWithImpl<
          _$ChannelMessagingDefinitionsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChannelMessagingDefinitionsResponseImplToJson(
      this,
    );
  }
}

abstract class _ChannelMessagingDefinitionsResponse
    implements ChannelMessagingDefinitionsResponse {
  const factory _ChannelMessagingDefinitionsResponse(
          {required final List<MessagingDefinition> definitions}) =
      _$ChannelMessagingDefinitionsResponseImpl;

  factory _ChannelMessagingDefinitionsResponse.fromJson(
          Map<String, dynamic> json) =
      _$ChannelMessagingDefinitionsResponseImpl.fromJson;

  @override
  List<MessagingDefinition> get definitions;
  @override
  @JsonKey(ignore: true)
  _$$ChannelMessagingDefinitionsResponseImplCopyWith<
          _$ChannelMessagingDefinitionsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
