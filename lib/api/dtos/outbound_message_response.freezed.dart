// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_message_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OutboundMessageResponse _$OutboundMessageResponseFromJson(
    Map<String, dynamic> json) {
  return _OutboundMessageResponse.fromJson(json);
}

/// @nodoc
mixin _$OutboundMessageResponse {
  String get messagingSessionId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OutboundMessageResponseCopyWith<OutboundMessageResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutboundMessageResponseCopyWith<$Res> {
  factory $OutboundMessageResponseCopyWith(OutboundMessageResponse value,
          $Res Function(OutboundMessageResponse) then) =
      _$OutboundMessageResponseCopyWithImpl<$Res, OutboundMessageResponse>;
  @useResult
  $Res call({String messagingSessionId});
}

/// @nodoc
class _$OutboundMessageResponseCopyWithImpl<$Res,
        $Val extends OutboundMessageResponse>
    implements $OutboundMessageResponseCopyWith<$Res> {
  _$OutboundMessageResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingSessionId = null,
  }) {
    return _then(_value.copyWith(
      messagingSessionId: null == messagingSessionId
          ? _value.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OutboundMessageResponseImplCopyWith<$Res>
    implements $OutboundMessageResponseCopyWith<$Res> {
  factory _$$OutboundMessageResponseImplCopyWith(
          _$OutboundMessageResponseImpl value,
          $Res Function(_$OutboundMessageResponseImpl) then) =
      __$$OutboundMessageResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String messagingSessionId});
}

/// @nodoc
class __$$OutboundMessageResponseImplCopyWithImpl<$Res>
    extends _$OutboundMessageResponseCopyWithImpl<$Res,
        _$OutboundMessageResponseImpl>
    implements _$$OutboundMessageResponseImplCopyWith<$Res> {
  __$$OutboundMessageResponseImplCopyWithImpl(
      _$OutboundMessageResponseImpl _value,
      $Res Function(_$OutboundMessageResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingSessionId = null,
  }) {
    return _then(_$OutboundMessageResponseImpl(
      messagingSessionId: null == messagingSessionId
          ? _value.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OutboundMessageResponseImpl implements _OutboundMessageResponse {
  const _$OutboundMessageResponseImpl({required this.messagingSessionId});

  factory _$OutboundMessageResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$OutboundMessageResponseImplFromJson(json);

  @override
  final String messagingSessionId;

  @override
  String toString() {
    return 'OutboundMessageResponse(messagingSessionId: $messagingSessionId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OutboundMessageResponseImpl &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, messagingSessionId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OutboundMessageResponseImplCopyWith<_$OutboundMessageResponseImpl>
      get copyWith => __$$OutboundMessageResponseImplCopyWithImpl<
          _$OutboundMessageResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OutboundMessageResponseImplToJson(
      this,
    );
  }
}

abstract class _OutboundMessageResponse implements OutboundMessageResponse {
  const factory _OutboundMessageResponse(
          {required final String messagingSessionId}) =
      _$OutboundMessageResponseImpl;

  factory _OutboundMessageResponse.fromJson(Map<String, dynamic> json) =
      _$OutboundMessageResponseImpl.fromJson;

  @override
  String get messagingSessionId;
  @override
  @JsonKey(ignore: true)
  _$$OutboundMessageResponseImplCopyWith<_$OutboundMessageResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
