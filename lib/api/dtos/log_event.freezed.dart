// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'log_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LogEvent _$LogEventFromJson(Map<String, dynamic> json) {
  return _LogEvent.fromJson(json);
}

/// @nodoc
mixin _$LogEvent {
  int get timestamp => throw _privateConstructorUsedError;
  String get packageName => throw _privateConstructorUsedError;
  String get level => throw _privateConstructorUsedError;
  LogMetadata get metadata => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LogEventCopyWith<LogEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LogEventCopyWith<$Res> {
  factory $LogEventCopyWith(LogEvent value, $Res Function(LogEvent) then) =
      _$LogEventCopyWithImpl<$Res, LogEvent>;
  @useResult
  $Res call(
      {int timestamp,
      String packageName,
      String level,
      LogMetadata metadata,
      String message});

  $LogMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class _$LogEventCopyWithImpl<$Res, $Val extends LogEvent>
    implements $LogEventCopyWith<$Res> {
  _$LogEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? packageName = null,
    Object? level = null,
    Object? metadata = null,
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      packageName: null == packageName
          ? _value.packageName
          : packageName // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as String,
      metadata: null == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as LogMetadata,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $LogMetadataCopyWith<$Res> get metadata {
    return $LogMetadataCopyWith<$Res>(_value.metadata, (value) {
      return _then(_value.copyWith(metadata: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LogEventImplCopyWith<$Res>
    implements $LogEventCopyWith<$Res> {
  factory _$$LogEventImplCopyWith(
          _$LogEventImpl value, $Res Function(_$LogEventImpl) then) =
      __$$LogEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int timestamp,
      String packageName,
      String level,
      LogMetadata metadata,
      String message});

  @override
  $LogMetadataCopyWith<$Res> get metadata;
}

/// @nodoc
class __$$LogEventImplCopyWithImpl<$Res>
    extends _$LogEventCopyWithImpl<$Res, _$LogEventImpl>
    implements _$$LogEventImplCopyWith<$Res> {
  __$$LogEventImplCopyWithImpl(
      _$LogEventImpl _value, $Res Function(_$LogEventImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? packageName = null,
    Object? level = null,
    Object? metadata = null,
    Object? message = null,
  }) {
    return _then(_$LogEventImpl(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      packageName: null == packageName
          ? _value.packageName
          : packageName // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as String,
      metadata: null == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as LogMetadata,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LogEventImpl extends _LogEvent {
  const _$LogEventImpl(
      {required this.timestamp,
      required this.packageName,
      required this.level,
      required this.metadata,
      required this.message})
      : super._();

  factory _$LogEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$LogEventImplFromJson(json);

  @override
  final int timestamp;
  @override
  final String packageName;
  @override
  final String level;
  @override
  final LogMetadata metadata;
  @override
  final String message;

  @override
  String toString() {
    return 'LogEvent(timestamp: $timestamp, packageName: $packageName, level: $level, metadata: $metadata, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LogEventImpl &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.packageName, packageName) ||
                other.packageName == packageName) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.metadata, metadata) ||
                other.metadata == metadata) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, timestamp, packageName, level, metadata, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LogEventImplCopyWith<_$LogEventImpl> get copyWith =>
      __$$LogEventImplCopyWithImpl<_$LogEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LogEventImplToJson(
      this,
    );
  }
}

abstract class _LogEvent extends LogEvent {
  const factory _LogEvent(
      {required final int timestamp,
      required final String packageName,
      required final String level,
      required final LogMetadata metadata,
      required final String message}) = _$LogEventImpl;
  const _LogEvent._() : super._();

  factory _LogEvent.fromJson(Map<String, dynamic> json) =
      _$LogEventImpl.fromJson;

  @override
  int get timestamp;
  @override
  String get packageName;
  @override
  String get level;
  @override
  LogMetadata get metadata;
  @override
  String get message;
  @override
  @JsonKey(ignore: true)
  _$$LogEventImplCopyWith<_$LogEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
