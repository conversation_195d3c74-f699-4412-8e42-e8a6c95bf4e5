// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refresh_salesforce_token_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RefreshSalesforceTokenResponse _$RefreshSalesforceTokenResponseFromJson(
    Map<String, dynamic> json) {
  return _RefreshSalesforceTokenResponse.fromJson(json);
}

/// @nodoc
mixin _$RefreshSalesforceTokenResponse {
  String get accessToken => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RefreshSalesforceTokenResponseCopyWith<RefreshSalesforceTokenResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RefreshSalesforceTokenResponseCopyWith<$Res> {
  factory $RefreshSalesforceTokenResponseCopyWith(
          RefreshSalesforceTokenResponse value,
          $Res Function(RefreshSalesforceTokenResponse) then) =
      _$RefreshSalesforceTokenResponseCopyWithImpl<$Res,
          RefreshSalesforceTokenResponse>;
  @useResult
  $Res call({String accessToken});
}

/// @nodoc
class _$RefreshSalesforceTokenResponseCopyWithImpl<$Res,
        $Val extends RefreshSalesforceTokenResponse>
    implements $RefreshSalesforceTokenResponseCopyWith<$Res> {
  _$RefreshSalesforceTokenResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
  }) {
    return _then(_value.copyWith(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RefreshSalesforceTokenResponseImplCopyWith<$Res>
    implements $RefreshSalesforceTokenResponseCopyWith<$Res> {
  factory _$$RefreshSalesforceTokenResponseImplCopyWith(
          _$RefreshSalesforceTokenResponseImpl value,
          $Res Function(_$RefreshSalesforceTokenResponseImpl) then) =
      __$$RefreshSalesforceTokenResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String accessToken});
}

/// @nodoc
class __$$RefreshSalesforceTokenResponseImplCopyWithImpl<$Res>
    extends _$RefreshSalesforceTokenResponseCopyWithImpl<$Res,
        _$RefreshSalesforceTokenResponseImpl>
    implements _$$RefreshSalesforceTokenResponseImplCopyWith<$Res> {
  __$$RefreshSalesforceTokenResponseImplCopyWithImpl(
      _$RefreshSalesforceTokenResponseImpl _value,
      $Res Function(_$RefreshSalesforceTokenResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
  }) {
    return _then(_$RefreshSalesforceTokenResponseImpl(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RefreshSalesforceTokenResponseImpl
    implements _RefreshSalesforceTokenResponse {
  const _$RefreshSalesforceTokenResponseImpl({required this.accessToken});

  factory _$RefreshSalesforceTokenResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$RefreshSalesforceTokenResponseImplFromJson(json);

  @override
  final String accessToken;

  @override
  String toString() {
    return 'RefreshSalesforceTokenResponse(accessToken: $accessToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshSalesforceTokenResponseImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, accessToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RefreshSalesforceTokenResponseImplCopyWith<
          _$RefreshSalesforceTokenResponseImpl>
      get copyWith => __$$RefreshSalesforceTokenResponseImplCopyWithImpl<
          _$RefreshSalesforceTokenResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RefreshSalesforceTokenResponseImplToJson(
      this,
    );
  }
}

abstract class _RefreshSalesforceTokenResponse
    implements RefreshSalesforceTokenResponse {
  const factory _RefreshSalesforceTokenResponse(
          {required final String accessToken}) =
      _$RefreshSalesforceTokenResponseImpl;

  factory _RefreshSalesforceTokenResponse.fromJson(Map<String, dynamic> json) =
      _$RefreshSalesforceTokenResponseImpl.fromJson;

  @override
  String get accessToken;
  @override
  @JsonKey(ignore: true)
  _$$RefreshSalesforceTokenResponseImplCopyWith<
          _$RefreshSalesforceTokenResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
