// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationMessageImpl _$$NotificationMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationMessageImpl(
      timestamp: const TimestampIntConverter().fromJson(json['timestamp']),
      notificationId: json['notificationId'] as String,
      sessionId: json['sessionId'] as String,
      messageCategory: json['messageCategory'] as String,
      payload: json['payload'] as Map<String, dynamic>,
      sentToWebSocket: json['sentToWebSocket'] as bool,
      pushed: json['pushed'] as bool,
    );

Map<String, dynamic> _$$NotificationMessageImplToJson(
        _$NotificationMessageImpl instance) =>
    <String, dynamic>{
      'timestamp': const TimestampIntConverter().toJson(instance.timestamp),
      'notificationId': instance.notificationId,
      'sessionId': instance.sessionId,
      'messageCategory': instance.messageCategory,
      'payload': instance.payload,
      'sentToWebSocket': instance.sentToWebSocket,
      'pushed': instance.pushed,
    };
