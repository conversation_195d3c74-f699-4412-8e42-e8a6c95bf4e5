import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/log_event.dart';

part 'log_event_body.freezed.dart';
part 'log_event_body.g.dart';

@freezed
class LogEventBody with _$LogEventBody {
  const factory LogEventBody({
    required List<LogEvent> events,
  }) = _LogEventBody;

  factory LogEventBody.fromJson(Map<String, dynamic> json) =>
      _$LogEventBodyFromJson(json);
}
