// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'session_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SessionDataImpl _$$SessionDataImplFromJson(Map<String, dynamic> json) =>
    _$SessionDataImpl(
      tenantId: (json['tenantId'] as num).toInt(),
      sessionId: json['sessionId'] as String,
      timeCreated: (json['timeCreated'] as num).toInt(),
      timeUpdated: (json['timeUpdated'] as num).toInt(),
      expiresAt: (json['expiresAt'] as num).toInt(),
      channelPlatformTypes: (json['channelPlatformTypes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      userId: json['userId'] as String,
    );

Map<String, dynamic> _$$SessionDataImplToJson(_$SessionDataImpl instance) =>
    <String, dynamic>{
      'tenantId': instance.tenantId,
      'sessionId': instance.sessionId,
      'timeCreated': instance.timeCreated,
      'timeUpdated': instance.timeUpdated,
      'expiresAt': instance.expiresAt,
      'channelPlatformTypes': instance.channelPlatformTypes,
      'userId': instance.userId,
    };
