// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_messaging_session_body.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TransferMessagingSessionBody _$TransferMessagingSessionBodyFromJson(
    Map<String, dynamic> json) {
  return _TransferMessagingSessionBody.fromJson(json);
}

/// @nodoc
mixin _$TransferMessagingSessionBody {
  TransferDestinationType get destinationType =>
      throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId get destinationId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TransferMessagingSessionBodyCopyWith<TransferMessagingSessionBody>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransferMessagingSessionBodyCopyWith<$Res> {
  factory $TransferMessagingSessionBodyCopyWith(
          TransferMessagingSessionBody value,
          $Res Function(TransferMessagingSessionBody) then) =
      _$TransferMessagingSessionBodyCopyWithImpl<$Res,
          TransferMessagingSessionBody>;
  @useResult
  $Res call(
      {TransferDestinationType destinationType,
      @ParseSfIdConverter() SfId destinationId});

  $SfIdCopyWith<$Res> get destinationId;
}

/// @nodoc
class _$TransferMessagingSessionBodyCopyWithImpl<$Res,
        $Val extends TransferMessagingSessionBody>
    implements $TransferMessagingSessionBodyCopyWith<$Res> {
  _$TransferMessagingSessionBodyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? destinationType = null,
    Object? destinationId = null,
  }) {
    return _then(_value.copyWith(
      destinationType: null == destinationType
          ? _value.destinationType
          : destinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType,
      destinationId: null == destinationId
          ? _value.destinationId
          : destinationId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get destinationId {
    return $SfIdCopyWith<$Res>(_value.destinationId, (value) {
      return _then(_value.copyWith(destinationId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TransferMessagingSessionBodyImplCopyWith<$Res>
    implements $TransferMessagingSessionBodyCopyWith<$Res> {
  factory _$$TransferMessagingSessionBodyImplCopyWith(
          _$TransferMessagingSessionBodyImpl value,
          $Res Function(_$TransferMessagingSessionBodyImpl) then) =
      __$$TransferMessagingSessionBodyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TransferDestinationType destinationType,
      @ParseSfIdConverter() SfId destinationId});

  @override
  $SfIdCopyWith<$Res> get destinationId;
}

/// @nodoc
class __$$TransferMessagingSessionBodyImplCopyWithImpl<$Res>
    extends _$TransferMessagingSessionBodyCopyWithImpl<$Res,
        _$TransferMessagingSessionBodyImpl>
    implements _$$TransferMessagingSessionBodyImplCopyWith<$Res> {
  __$$TransferMessagingSessionBodyImplCopyWithImpl(
      _$TransferMessagingSessionBodyImpl _value,
      $Res Function(_$TransferMessagingSessionBodyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? destinationType = null,
    Object? destinationId = null,
  }) {
    return _then(_$TransferMessagingSessionBodyImpl(
      destinationType: null == destinationType
          ? _value.destinationType
          : destinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType,
      destinationId: null == destinationId
          ? _value.destinationId
          : destinationId // ignore: cast_nullable_to_non_nullable
              as SfId,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TransferMessagingSessionBodyImpl extends _TransferMessagingSessionBody {
  const _$TransferMessagingSessionBodyImpl(
      {required this.destinationType,
      @ParseSfIdConverter() required this.destinationId})
      : super._();

  factory _$TransferMessagingSessionBodyImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$TransferMessagingSessionBodyImplFromJson(json);

  @override
  final TransferDestinationType destinationType;
  @override
  @ParseSfIdConverter()
  final SfId destinationId;

  @override
  String toString() {
    return 'TransferMessagingSessionBody(destinationType: $destinationType, destinationId: $destinationId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferMessagingSessionBodyImpl &&
            (identical(other.destinationType, destinationType) ||
                other.destinationType == destinationType) &&
            (identical(other.destinationId, destinationId) ||
                other.destinationId == destinationId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, destinationType, destinationId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferMessagingSessionBodyImplCopyWith<
          _$TransferMessagingSessionBodyImpl>
      get copyWith => __$$TransferMessagingSessionBodyImplCopyWithImpl<
          _$TransferMessagingSessionBodyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TransferMessagingSessionBodyImplToJson(
      this,
    );
  }
}

abstract class _TransferMessagingSessionBody
    extends TransferMessagingSessionBody {
  const factory _TransferMessagingSessionBody(
          {required final TransferDestinationType destinationType,
          @ParseSfIdConverter() required final SfId destinationId}) =
      _$TransferMessagingSessionBodyImpl;
  const _TransferMessagingSessionBody._() : super._();

  factory _TransferMessagingSessionBody.fromJson(Map<String, dynamic> json) =
      _$TransferMessagingSessionBodyImpl.fromJson;

  @override
  TransferDestinationType get destinationType;
  @override
  @ParseSfIdConverter()
  SfId get destinationId;
  @override
  @JsonKey(ignore: true)
  _$$TransferMessagingSessionBodyImplCopyWith<
          _$TransferMessagingSessionBodyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
