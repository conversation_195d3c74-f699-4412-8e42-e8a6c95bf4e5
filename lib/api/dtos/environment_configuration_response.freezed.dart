// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'environment_configuration_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EnvironmentConfigurationResponse _$EnvironmentConfigurationResponseFromJson(
    Map<String, dynamic> json) {
  return _EnvironmentConfigurationResponse.fromJson(json);
}

/// @nodoc
mixin _$EnvironmentConfigurationResponse {
  Map<String, dynamic> get config => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EnvironmentConfigurationResponseCopyWith<EnvironmentConfigurationResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EnvironmentConfigurationResponseCopyWith<$Res> {
  factory $EnvironmentConfigurationResponseCopyWith(
          EnvironmentConfigurationResponse value,
          $Res Function(EnvironmentConfigurationResponse) then) =
      _$EnvironmentConfigurationResponseCopyWithImpl<$Res,
          EnvironmentConfigurationResponse>;
  @useResult
  $Res call({Map<String, dynamic> config});
}

/// @nodoc
class _$EnvironmentConfigurationResponseCopyWithImpl<$Res,
        $Val extends EnvironmentConfigurationResponse>
    implements $EnvironmentConfigurationResponseCopyWith<$Res> {
  _$EnvironmentConfigurationResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = null,
  }) {
    return _then(_value.copyWith(
      config: null == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EnvironmentConfigurationResponseImplCopyWith<$Res>
    implements $EnvironmentConfigurationResponseCopyWith<$Res> {
  factory _$$EnvironmentConfigurationResponseImplCopyWith(
          _$EnvironmentConfigurationResponseImpl value,
          $Res Function(_$EnvironmentConfigurationResponseImpl) then) =
      __$$EnvironmentConfigurationResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<String, dynamic> config});
}

/// @nodoc
class __$$EnvironmentConfigurationResponseImplCopyWithImpl<$Res>
    extends _$EnvironmentConfigurationResponseCopyWithImpl<$Res,
        _$EnvironmentConfigurationResponseImpl>
    implements _$$EnvironmentConfigurationResponseImplCopyWith<$Res> {
  __$$EnvironmentConfigurationResponseImplCopyWithImpl(
      _$EnvironmentConfigurationResponseImpl _value,
      $Res Function(_$EnvironmentConfigurationResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = null,
  }) {
    return _then(_$EnvironmentConfigurationResponseImpl(
      config: null == config
          ? _value._config
          : config // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EnvironmentConfigurationResponseImpl
    implements _EnvironmentConfigurationResponse {
  const _$EnvironmentConfigurationResponseImpl(
      {required final Map<String, dynamic> config})
      : _config = config;

  factory _$EnvironmentConfigurationResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$EnvironmentConfigurationResponseImplFromJson(json);

  final Map<String, dynamic> _config;
  @override
  Map<String, dynamic> get config {
    if (_config is EqualUnmodifiableMapView) return _config;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_config);
  }

  @override
  String toString() {
    return 'EnvironmentConfigurationResponse(config: $config)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EnvironmentConfigurationResponseImpl &&
            const DeepCollectionEquality().equals(other._config, _config));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_config));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EnvironmentConfigurationResponseImplCopyWith<
          _$EnvironmentConfigurationResponseImpl>
      get copyWith => __$$EnvironmentConfigurationResponseImplCopyWithImpl<
          _$EnvironmentConfigurationResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EnvironmentConfigurationResponseImplToJson(
      this,
    );
  }
}

abstract class _EnvironmentConfigurationResponse
    implements EnvironmentConfigurationResponse {
  const factory _EnvironmentConfigurationResponse(
          {required final Map<String, dynamic> config}) =
      _$EnvironmentConfigurationResponseImpl;

  factory _EnvironmentConfigurationResponse.fromJson(
          Map<String, dynamic> json) =
      _$EnvironmentConfigurationResponseImpl.fromJson;

  @override
  Map<String, dynamic> get config;
  @override
  @JsonKey(ignore: true)
  _$$EnvironmentConfigurationResponseImplCopyWith<
          _$EnvironmentConfigurationResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
