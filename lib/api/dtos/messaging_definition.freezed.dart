// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_definition.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MessagingDefinition _$MessagingDefinitionFromJson(Map<String, dynamic> json) {
  return _MessagingDefinition.fromJson(json);
}

/// @nodoc
mixin _$MessagingDefinition {
  @ParseSfIdConverter()
  SfId? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get internalName => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  bool? get hasRequiredParameters => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessagingDefinitionCopyWith<MessagingDefinition> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingDefinitionCopyWith<$Res> {
  factory $MessagingDefinitionCopyWith(
          MessagingDefinition value, $Res Function(MessagingDefinition) then) =
      _$MessagingDefinitionCopyWithImpl<$Res, MessagingDefinition>;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId? id,
      String? name,
      String? internalName,
      String? description,
      bool? hasRequiredParameters});

  $SfIdCopyWith<$Res>? get id;
}

/// @nodoc
class _$MessagingDefinitionCopyWithImpl<$Res, $Val extends MessagingDefinition>
    implements $MessagingDefinitionCopyWith<$Res> {
  _$MessagingDefinitionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? internalName = freezed,
    Object? description = freezed,
    Object? hasRequiredParameters = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      internalName: freezed == internalName
          ? _value.internalName
          : internalName // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      hasRequiredParameters: freezed == hasRequiredParameters
          ? _value.hasRequiredParameters
          : hasRequiredParameters // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get id {
    if (_value.id == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.id!, (value) {
      return _then(_value.copyWith(id: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingDefinitionImplCopyWith<$Res>
    implements $MessagingDefinitionCopyWith<$Res> {
  factory _$$MessagingDefinitionImplCopyWith(_$MessagingDefinitionImpl value,
          $Res Function(_$MessagingDefinitionImpl) then) =
      __$$MessagingDefinitionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId? id,
      String? name,
      String? internalName,
      String? description,
      bool? hasRequiredParameters});

  @override
  $SfIdCopyWith<$Res>? get id;
}

/// @nodoc
class __$$MessagingDefinitionImplCopyWithImpl<$Res>
    extends _$MessagingDefinitionCopyWithImpl<$Res, _$MessagingDefinitionImpl>
    implements _$$MessagingDefinitionImplCopyWith<$Res> {
  __$$MessagingDefinitionImplCopyWithImpl(_$MessagingDefinitionImpl _value,
      $Res Function(_$MessagingDefinitionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? internalName = freezed,
    Object? description = freezed,
    Object? hasRequiredParameters = freezed,
  }) {
    return _then(_$MessagingDefinitionImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as SfId?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      internalName: freezed == internalName
          ? _value.internalName
          : internalName // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      hasRequiredParameters: freezed == hasRequiredParameters
          ? _value.hasRequiredParameters
          : hasRequiredParameters // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MessagingDefinitionImpl extends _MessagingDefinition {
  const _$MessagingDefinitionImpl(
      {@ParseSfIdConverter() this.id,
      this.name,
      this.internalName,
      this.description = '',
      this.hasRequiredParameters})
      : super._();

  factory _$MessagingDefinitionImpl.fromJson(Map<String, dynamic> json) =>
      _$$MessagingDefinitionImplFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId? id;
  @override
  final String? name;
  @override
  final String? internalName;
  @override
  @JsonKey()
  final String? description;
  @override
  final bool? hasRequiredParameters;

  @override
  String toString() {
    return 'MessagingDefinition(id: $id, name: $name, internalName: $internalName, description: $description, hasRequiredParameters: $hasRequiredParameters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingDefinitionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.internalName, internalName) ||
                other.internalName == internalName) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.hasRequiredParameters, hasRequiredParameters) ||
                other.hasRequiredParameters == hasRequiredParameters));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, internalName, description, hasRequiredParameters);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingDefinitionImplCopyWith<_$MessagingDefinitionImpl> get copyWith =>
      __$$MessagingDefinitionImplCopyWithImpl<_$MessagingDefinitionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MessagingDefinitionImplToJson(
      this,
    );
  }
}

abstract class _MessagingDefinition extends MessagingDefinition {
  const factory _MessagingDefinition(
      {@ParseSfIdConverter() final SfId? id,
      final String? name,
      final String? internalName,
      final String? description,
      final bool? hasRequiredParameters}) = _$MessagingDefinitionImpl;
  const _MessagingDefinition._() : super._();

  factory _MessagingDefinition.fromJson(Map<String, dynamic> json) =
      _$MessagingDefinitionImpl.fromJson;

  @override
  @ParseSfIdConverter()
  SfId? get id;
  @override
  String? get name;
  @override
  String? get internalName;
  @override
  String? get description;
  @override
  bool? get hasRequiredParameters;
  @override
  @JsonKey(ignore: true)
  _$$MessagingDefinitionImplCopyWith<_$MessagingDefinitionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
