import 'package:freezed_annotation/freezed_annotation.dart';

part 'unacknowledged_notifications_body.freezed.dart';
part 'unacknowledged_notifications_body.g.dart';

@freezed
class UnacknowledgedNotificationsBody with _$UnacknowledgedNotificationsBody {
  const factory UnacknowledgedNotificationsBody({
    required int limit,
    required String key,
  }) = _UnacknowledgedNotificationsBody;

  factory UnacknowledgedNotificationsBody.fromJson(Map<String, dynamic> json) =>
      _$UnacknowledgedNotificationsBodyFromJson(json);
}
