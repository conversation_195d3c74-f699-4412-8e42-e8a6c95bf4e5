import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:loggy/loggy.dart';
import 'package:x1440/use_cases/settings/settings_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/viewmodels/appwide_viewmodels.dart';
import 'di/di.dart';
import 'package:flutter_driver/driver_extension.dart';

import 'generated/l10n.dart';

void main() async {
  if (Utils.isIntegrationTest) {
    enableFlutterDriverExtension();
  }

  WidgetsFlutterBinding.ensureInitialized();

  /// IMPORTANT: this is required to support legacy view models that use S.current we've added in; TODO: remove this once all view models are removed => clean logic has localization strings handled on the UI layer!!!
  await S.load(Locale.fromSubtags(languageCode: Utils.getLanguage()));

  await configureDependencies();
  /// this must run after configureDependencies; it will set scopes if necessary ('demo') which can potentially require any/all dependencies to have been registered. // TODO: better way to handle this?
  GetIt.I<SettingsUseCase>().init();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget with UiLoggy {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) => getAppWideViewModels();
}
