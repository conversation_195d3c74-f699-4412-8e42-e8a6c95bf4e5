import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart';
import 'package:injectable/injectable.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:x1440/api/auth_interceptor.dart';
import 'package:x1440/api/logger_api.dart';
import 'package:x1440/api/logger_interceptor.dart';
import 'package:x1440/api/salesforce/salesforce_interceptor.dart';
import 'package:x1440/api/shim_service_api.dart';
import 'package:x1440/debug/global_bloc_observer.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/frameworks/settings/settings_manager.dart';
import 'package:x1440/services/interfaces/productivity_tools_viewmodel_interface.dart';
import 'package:x1440/services/security/encryption_service.dart';
import 'package:x1440/ui/blocs/messaging/messaging_event.dart';
import 'package:x1440/ui/blocs/org/org_bloc.dart';
import 'package:x1440/use_cases/ai_suggestions/ai_suggestions_use_case.dart';
import 'package:x1440/frameworks/crash_manager.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/use_cases/presence/presence_use_case.dart';
import 'package:x1440/frameworks/push_notification_manager.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/frameworks/settings/models/app_local_settings.dart';
import 'package:x1440/use_cases/org/org_use_case.dart';
import 'package:x1440/use_cases/settings/settings_use_case.dart';
import 'package:x1440/frameworks/typing_indicator/typing_indicator_manager.dart';
import 'package:x1440/repositories/auth/auth_repository.dart';
import 'package:x1440/repositories/auth/auth_repository_impl.dart';
import 'package:x1440/repositories/channels/channels_repository.dart';
import 'package:x1440/repositories/channels/channels_repository_impl.dart';
import 'package:x1440/repositories/contacts/contact_details_repository.dart';
import 'package:x1440/repositories/contacts/contacts_repository.dart';
import 'package:x1440/repositories/conversation/conversation_repository.dart';
import 'package:x1440/repositories/conversation/conversation_repository_impl.dart';
import 'package:x1440/repositories/environment_info/environment_info_repository.dart';

import 'package:x1440/repositories/graph_ql/graphql_repository.dart';
import 'package:x1440/repositories/logging/logging_repository.dart';
import 'package:x1440/repositories/logging/logging_repository_impl.dart';
import 'package:x1440/repositories/message_queue/message_queue_repository.dart';
import 'package:x1440/repositories/message_queue/message_queue_repository_impl.dart';
import 'package:x1440/repositories/messaging_sessions/messaging_sessions_repository.dart';
import 'package:x1440/repositories/notifications/notifications_repository.dart';
import 'package:x1440/repositories/notifications/notifications_repository_impl.dart';
import 'package:x1440/repositories/query/query_repository.dart';
import 'package:x1440/repositories/quick_text_repository/quick_text_repository.dart';
import 'package:x1440/repositories/salesforce_data_repository/salesforce_data_repository.dart';
import 'package:x1440/repositories/search/search_repository.dart';
import 'package:x1440/repositories/search/search_repository_impl.dart';
import 'package:x1440/repositories/session/session_repository.dart';
import 'package:x1440/repositories/session/session_repository_impl.dart';
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/repositories/storage/local_storage_repository_impl.dart';
import 'package:x1440/repositories/presence/presence_repository.dart';
import 'package:x1440/repositories/presence/presence_repository_impl.dart';
import 'package:x1440/frameworks/messaging_service.dart';
import 'package:x1440/repositories/transfer/transfer_repository.dart';
import 'package:x1440/repositories/websocket/messaging_definitions_repository.dart';
import 'package:x1440/repositories/websocket/messaging_definitions_repository_impl.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository_impl.dart';
import 'package:x1440/frameworks/conversations_service.dart';
import 'package:x1440/services/de_conversations_service.dart';
import 'package:x1440/services/interfaces/contact_viewmodel_interface.dart';
import 'package:x1440/services/shim_service/shim_service.dart';
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/channels/channels_bloc.dart';
import 'package:x1440/ui/blocs/chat/chat_bloc.dart';
import 'package:x1440/ui/blocs/contacts/contacts_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/image_size/image_size_bloc.dart';
import 'package:x1440/ui/blocs/messaging/definitions/messaging_definitions_bloc.dart';
import 'package:x1440/ui/blocs/messaging/messaging_bloc.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/use_cases/channels/channels_use_case.dart';
import 'package:x1440/use_cases/contacts/contacts_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/frameworks/message_queue/message_queue_manager.dart';
import 'package:x1440/use_cases/messaging/messaging_definitions_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/messaging_channels/messaging_channels_use_case.dart';
import 'package:x1440/use_cases/notifications/notifications_use_case.dart';
import 'package:x1440/use_cases/quick_actions/quick_actions_use_case.dart';
import 'package:x1440/use_cases/salesforce_data/salesforce_data_use_case.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';
import 'package:x1440/use_cases/typing_indicator/typing_indicator_use_case.dart';
import 'package:x1440/viewmodels/contacts_viewmodel.dart';
import 'package:x1440/viewmodels/conversations_viewmodel.dart';
import 'package:x1440/viewmodels/productivity_tools_viewmodel.dart';

@module
abstract class BaseModules {
  @preResolve
  @Singleton()
  Future<GlobalBlocObserver> globalBlocObserverFactory(
      LoggingUseCase loggingUseCase) async {
    final obs = GlobalBlocObserver(loggingUseCase.getRemoteLogger('BlocObs'));
    Bloc.observer = obs;
    return obs;
  }

  @Singleton()
  @Injectable()
  ChatBloc chatBlocFactory(
          ConversationsUseCase conversationsUseCase,
          TypingIndicatorManager typingIndicatorManager,
          ContactsUseCase contactsUseCase,
          MessagingUseCase messagingUseCase,
          MessagingDefinitionsUseCase messagingDefinitionsUseCase,
          LoggingUseCase loggingUseCase,
          QuickActionsUseCase quickActionsUseCase,
          AuthUseCase authUseCase) =>
      ChatBlocImpl(
          conversationsUseCase,
          typingIndicatorManager,
          contactsUseCase,
          messagingUseCase,
          messagingDefinitionsUseCase,
          loggingUseCase.getRemoteLogger('ChatBloc'),
          quickActionsUseCase,
          authUseCase);

  @Deprecated('legacy support')
  @preResolve
  @Singleton()
  Future<ShimService> shimServiceFactory() async {
    return ShimService();
  }

  @Deprecated('legacy support')
  @preResolve
  @Singleton()
  Future<DeConversationsService> DeConversationsServiceFactory(
      ShimService shimService,
      LocalStorageRepository localStorageRepository,
      LoggingUseCase loggingUseCase,
      ConversationsUseCase conversationsUseCase,
      GraphQlRepository graphQlRepository) async {
    return DeConversationsService(
        shimService,
        localStorageRepository,
        loggingUseCase.getRemoteLogger('DeConversationsService'),
        conversationsUseCase,
        graphQlRepository)
      ..init();
  }

  @preResolve
  @Singleton()
  Future<MessagingSessionsRepository> messagingSessionsRepositoryFactory(
      LocalStorageRepository localStorage) async {
    return MessagingSessionsRepository(localStorage);
  }

  @preResolve
  @Singleton()
  Future<GraphQlRepository> graphQlRepositoryFactory(
      LocalStorageRepository localStorageRepository) async {
    return GraphQlRepository(localStorageRepository)..init();
  }

  @preResolve
  @Singleton()
  Future<AppLifeCycleRepository> appLifeCycleRepositoryFactory() async {
    final appLifeCycleRepository = AppLifeCycleRepository();
    await appLifeCycleRepository.init();
    return Future.value(appLifeCycleRepository);
  }

  @preResolve
  @Singleton()
  Future<NotificationManager> notificationManagerFactory(
      RoutingManager routingManager,
      ContactsUseCase contactsUseCase,
      ConversationsUseCase conversationsUseCase,
      AppLifeCycleRepository appLifeCycleRepository,
      LoggingUseCase loggingUseCase) async {
    final notificationManager = NotificationManager(
        routingManager,
        contactsUseCase,
        conversationsUseCase,
        appLifeCycleRepository,
        loggingUseCase.getRemoteLogger('NotificationManager'));
    await notificationManager.init();
    return Future.value(notificationManager);
  }

  @preResolve
  @Singleton()
  Future<EnvironmentInfoRepository> environmentInfoRepositoryFactory(
      LocalStorageRepository localStorageRepository) async {
    final environmentInfoRepository =
        EnvironmentInfoRepository(localStorageRepository);
    return Future.value(environmentInfoRepository);
  }

  @preResolve
  @Singleton()
  Future<ShimWebsocketRepository> shimWebsocketRepositoryFactory(
    LoggingUseCase loggingUseCase,
    LocalStorageRepository localStorageRepository,
  ) async {
    final shimWebsocket = ShimWebsocketRepositoryImpl(
        loggingUseCase.getRemoteLogger('ShimWebSocket'),
        localStorageRepository);
    await shimWebsocket.init();
    return Future.value(shimWebsocket);
  }

  @preResolve
  @Singleton()
  Future<MessageQueueRepository> messageQueueRepositoryFactory(
      LoggingUseCase loggingUseCase,
      LocalStorageRepository localStorageRepository) async {
    return MessageQueueRepositoryImpl(
        loggingUseCase.getRemoteLogger('messageQueue'), localStorageRepository);
  }

  @preResolve
  @Singleton()
  Future<MessagingDefinitionsRepository> messagingRepositoryFactory(
      ShimServiceApi api) async {
    return MessagingDefinitionsRepositoryImpl(api);
  }

  @preResolve
  @Singleton()
  Future<ChannelsRepository> channelsRepositoryFactory(
      ShimServiceApi api) async {
    return ChannelsRepositoryImpl(api);
  }

  @preResolve
  @Singleton()
  Future<MessagingDefinitionsUseCase> messagingDefinitionsUseCaseFactory(
    LoggingUseCase loggingUseCase,
    MessagingDefinitionsRepository messagingDefinitionsRepository,
  ) async {
    return MessagingDefinitionsUseCase(
        loggingUseCase.getRemoteLogger('MessagingUseCase'),
        messagingDefinitionsRepository);
  }

  @preResolve
  @Singleton()
  Future<MessagingUseCase> messagingUseCaseFactory(
    LoggingUseCase loggingUseCase,
    ConversationRepository repo,
    MessageQueueRepository messageQueueRepository,
    ShimWebsocketRepository shimWebsocket,
    NotificationsRepository notificationsRepository,
    SalesforceDataRepository salesforceDataRepository,
    LocalStorageRepository localStorageRepository,
    AppLifeCycleRepository appLifeCycleRepository,
    EnvironmentInfoRepository environmentInfoRepository,
  ) async {
    return MessagingUseCase(
        loggingUseCase.getRemoteLogger('MessagingUseCase'),
        repo,
        messageQueueRepository,
        shimWebsocket,
        notificationsRepository,
        salesforceDataRepository,
        localStorageRepository,
        appLifeCycleRepository,
        environmentInfoRepository)
      ..init();
  }

  @preResolve
  @Singleton()
  Future<MessageQueueManager> messageQueueManagerFactory(
      MessageQueueRepository messageQueueRepository,
      AuthUseCase authUseCase,
      LoggingUseCase loggingUseCase) async {
    return MessageQueueManager(messageQueueRepository, authUseCase,
        loggingUseCase.getRemoteLogger('MessageQueueManager'))
      ..init();
  }

  @preResolve
  @Singleton()
  Future<PushNotificationManager> pushNotificationManagerFactory(
      ShimServiceApi api,
      LocalStorageRepository localStorageRepository,
      LoggingUseCase loggingUseCase) async {
    return PushNotificationManager(api, localStorageRepository,
        loggingUseCase.getRemoteLogger('PushNotificationManager'));
  }

  @preResolve
  @Singleton()
  @Injectable()
  Future<RoutingManager> routingManagerFactory(
      LoggingUseCase loggingUseCase) async {
    return RoutingManager(
      loggingUseCase.getRemoteLogger('RoutingUseCase'),
    );
  }

  @preResolve
  @Singleton()
  Future<SettingsUseCase> settingsUseCaseFactory(
      LocalStorageRepository localStorage) async {
    return SettingsUseCase(localStorage);
  }

  @preResolve
  @Singleton()
  Future<AppErrorBloc> appErrorBlocFactory() async {
    return AppErrorBloc();
  }

  @preResolve
  @Singleton()
  Future<SettingsManager> settingsManagerFactory(
      SettingsUseCase settingsUseCase) async {
    AppLocalSettings startingSettings = await settingsUseCase.appLocalSettings;
    return SettingsManager(settingsUseCase, startingSettings);
  }

  @preResolve
  @Singleton()
  Future<OrgUseCase> orgUseCaseFactory(
      LocalStorageRepository localStorage) async {
    return OrgUseCase(localStorage);
  }

  @preResolve
  @Singleton()
  Future<MessagingDefinitionsBloc> messagingDefinitionsBlocFactory(
      MessagingDefinitionsUseCase messagingDefinitionsUseCase) async {
    return MessagingDefinitionsBloc(messagingDefinitionsUseCase);
  }

  @preResolve
  @Singleton()
  @Injectable()
  Future<AuthBloc> authBlocFactory(
      AuthUseCase authUseCase,
      SessionUseCase sessionUseCase,
      ConversationsUseCase conversationsUseCase,
      MessagingUseCase messagingUseCase,
      AppLifeCycleRepository appLifeCycleRepository,
      LoggingUseCase loggingUseCase) async {
    return AuthBlocImpl(
        authUseCase,
        sessionUseCase,
        conversationsUseCase,
        messagingUseCase,
        appLifeCycleRepository,
        loggingUseCase.getRemoteLogger('AuthBloc'));
  }

  @preResolve
  @Singleton()
  @Injectable()
  Future<OrgBloc> orgBlocFactory(OrgUseCase orgUseCase) async {
    return OrgBlocImpl(orgUseCase);
  }

  @Singleton()
  @Injectable()
  MessagingBloc messagingBlocFactory(
      MessagingUseCase messagingUseCase,
      MessageQueueManager messageQueueManager,
      EnvironmentInfoRepository environmentInfoRepository) {
    return MessagingBloc(
        messagingUseCase, messageQueueManager, environmentInfoRepository)
      ..add(ConnectEvent());
  }

  @Singleton()
  @Injectable()
  ImageSizeBloc imageSizeBlocFactory() {
    return ImageSizeBloc();
  }

  @Singleton()
  @Injectable()
  ConversationsBloc conversationsBlocFactory(
      ConversationsUseCase conversationsUseCase,
      QuickActionsUseCase quickActionsUseCase,
      AuthUseCase authUseCase,
      LoggingUseCase loggingUseCase) {
    return ConversationsBlocImpl(conversationsUseCase, quickActionsUseCase,
        authUseCase, loggingUseCase.getRemoteLogger('ConversationsBloc'));
  }

  @preResolve
  @Singleton()
  Future<PresenceUseCase> presenceUseCaseFactory(
      LocalStorageRepository localStorageRepository,
      PresenceRepository presenceRepository) async {
    return PresenceUseCase(localStorageRepository, presenceRepository);
  }

  @preResolve
  @Singleton()
  @Injectable()
  Future<PresenceManager> presenceManagerFactory(
    PresenceUseCase presenceUseCase,
    SessionUseCase sessionUseCase,
    MessagingUseCase messagingUseCase,
  ) async {
    return PresenceManagerImpl(
        presenceUseCase, sessionUseCase, messagingUseCase);
  }

  @preResolve
  @Singleton()
  Future<LoggerInterceptor> loggerInterceptorFactory(
      LoggingUseCase loggingUseCase) async {
    return LoggerInterceptor(
        loggingUseCase.getRemoteLogger('lib.api.logger_interceptor'));
  }

  @preResolve
  @Singleton()
  Future<AuthInterceptor> authInterceptorFactory(
      LocalStorageRepository localStorageRepository,
      LoggingUseCase loggingUseCase) async {
    return AuthInterceptor(localStorageRepository,
        loggingUseCase.getRemoteLogger('AuthInterceptor'));
  }

  @preResolve
  @Singleton()
  Future<SalesforceInterceptor> salesforceInterceptorFactory(
      LocalStorageRepository localStorage) async {
    return SalesforceInterceptor(localStorage);
  }

  @preResolve
  @Singleton()
  Future<EncryptionService> encryptionServiceFactory() async {
    return EncryptionService();
  }

  @preResolve
  @preResolve
  @Singleton()
  Future<AuthRepository> authRepositoryFactory(
      ShimServiceApi api,
      LocalStorageRepository localStorage,
      FlutterWebAuth2Options webAuthOptions,
      EncryptionService encryptionService) async {
    return AuthRepositoryImpl(
        api, localStorage, webAuthOptions, encryptionService);
  }

  @preResolve
  @Singleton()
  Future<SearchRepository> searchRepositoryFactory(
      LocalStorageRepository localstorageRepository) async {
    return SearchRepositoryImpl(localstorageRepository);
  }

  @preResolve
  @Singleton()
  Future<ContactsRepository> contactsRepositoryFactory(
      LocalStorageRepository localStorageRepository) async {
    return ContactsRepository(localStorageRepository);
  }

  @preResolve
  @Singleton()
  Future<ContactDetailsRepository> contactDetailsRepositoryFactory(
      LocalStorageRepository localStorageRepository) async {
    return ContactDetailsRepository(localStorageRepository);
  }

  @preResolve
  @Singleton()
  Future<ContactsUseCase> contactsUseCaseFactory(
      AppConfig appConfig,
      SearchRepository searchRepository,
      ContactsRepository contactsRepository,
      ContactDetailsRepository contactDetailsRepository,
      LocalStorageRepository localStorageRepository,
      QueryRepository queryRepository) async {
    return ContactsUseCaseImpl(appConfig, searchRepository, contactsRepository,
        contactDetailsRepository, localStorageRepository, queryRepository);
  }

  @Singleton()
  @Injectable()
  ContactsBloc contactsBlocFactory(ContactsUseCase contactsUseCase,
      ConversationsUseCase conversationsUseCase) {
    return ContactsBloc(contactsUseCase, conversationsUseCase);
  }

  @preResolve
  @Singleton()
  Future<SessionRepository> sessionRepositoryFactory(ShimServiceApi api) async {
    return SessionRepositoryImpl(api);
  }

  @preResolve
  @Singleton()
  Future<PresenceRepository> presenceRepositoryFactory(
      ShimServiceApi api) async {
    return PresenceRepositoryImpl(api);
  }

  @preResolve
  @Singleton()
  Future<QuickTextRepository> quickTextRepositoryFactory(
      ShimServiceApi api) async {
    return QuickTextRepository(api);
  }

  @preResolve
  @Singleton()
  Future<LocalStorageRepository> localStorageRepositoryFactory() async {
    var localStorage = LocalStorageRepositoryImpl();
    await localStorage.init();
    return Future.value(localStorage);
  }

  @preResolve
  @Singleton()
  Future<QuickActionsUseCase> quickActionsUseCaseFactory(
      QueryRepository queryRepository,
      LocalStorageRepository localStorageRepository) async {
    return QuickActionsUseCase(queryRepository, localStorageRepository);
  }

  @preResolve
  @Singleton()
  Future<AuthUseCase> authUseCaseFactory(
      AuthRepository repo,
      LocalStorageRepository localStorage,
      AppConfig appConfig,
      NotificationManager notificationManager,
      LoggingUseCase loggingUseCase) async {
    return AuthUseCase(repo, localStorage, appConfig, notificationManager,
        loggingUseCase.getRemoteLogger('AuthUseCase'));
  }

  @preResolve
  @Singleton()
  Future<LoggingUseCase> loggingUseCaseFactory(
    LoggingRepository repo,
    LocalStorageRepository localStorage,
    AppLifeCycleRepository appLifeCycleRepository,
    AppConfig appConfig,
    EnvironmentInfoRepository environmentInfoRepository,
  ) async {
    LoggingUseCase useCase = LoggingUseCase(repo, localStorage,
        appLifeCycleRepository, appConfig, environmentInfoRepository);
    useCase.init();
    return useCase;
  }

  @preResolve
  @Singleton()
  Future<SessionUseCase> sessionUseCaseFactory(
      SessionRepository repo,
      LocalStorageRepository localStorage,
      EnvironmentInfoRepository environmentInfoRepository,
      AppConfig appConfig,
      LoggingUseCase loggingUseCase) async {
    return SessionUseCase(repo, localStorage, environmentInfoRepository,
        appConfig, loggingUseCase.getRemoteLogger('SessionUseCase'));
  }

  @preResolve
  @Singleton()
  Future<ConversationRepository> conversationRepositoryFactory(
      ShimServiceApi api, LocalStorageRepository localStorage) async {
    return ConversationRepositoryImpl(api, localStorage);
  }

  @preResolve
  @Singleton()
  Future<QueryRepository> queryRepositoryFactory(
      LocalStorageRepository localStorage) async {
    return QueryRepository(localStorage);
  }

  @preResolve
  @Singleton()
  Future<TransferRepository> transferRepositoryFactory(
      ShimServiceApi api) async {
    return TransferRepository(api);
  }

  @preResolve
  @Singleton()
  Future<ConversationsService> conversationsServiceFactory(
    ConversationsUseCase conversationsUseCase,
    NotificationsUseCase notificationsUseCase,
    SessionUseCase sessionUseCase,
    MessagingUseCase messagingUseCase,
    LoggingUseCase loggingUseCase,
  ) async {
    return ConversationsService(
        conversationsUseCase,
        notificationsUseCase,
        sessionUseCase,
        messagingUseCase,
        loggingUseCase.getRemoteLogger('ConversationsService'))
      ..init();
  }

  // TODO: make a 'factoryFactory' here or something? Right now, this is instantiated in the view -- where we also have to do a scope check for the Demo overrides
//   @preResolve
// //  @Singleton() TO BE TESTED!!
//   Future<TransferBloc> transferBlocFactory(
//       ConversationsUseCase conversationsUseCase) async {
//     return TransferBlocImpl(conversationsUseCase);
//   }

  @preResolve
  @Singleton()
  Future<ChannelsBloc> channelsBlocFactory(
      ChannelsUseCase channelsUseCase) async {
    return ChannelsBlocImpl(channelsUseCase);
  }

  @preResolve
  @Singleton()
  Future<AiSuggestionsUseCase> aiSuggestionsManagerFactory(
      LoggingUseCase loggingUseCase) async {
    return AiSuggestionsUseCase(
        loggingUseCase.getRemoteLogger('AiSuggestionsManager'));
  }

  @Singleton()
  @Injectable()
  AiSuggestionsBloc aiSuggestionsBlocFactory(
      AiSuggestionsUseCase aiSuggestionsManager,
      ConversationsUseCase conversationsUseCase) {
    return AiSuggestionsBlocImpl(aiSuggestionsManager, conversationsUseCase);
  }

  @preResolve
  @Singleton()
  @Injectable()
  Future<ConversationsUseCase> conversationsUseCaseFactory(
      QueryRepository queryRepository,
      ConversationRepository conversationRepository,
      TransferRepository transferRepository,
      LocalStorageRepository localStorageRepository,
      TypingIndicatorManager typingIndicatorManager,
      LoggingUseCase loggingUseCase) async {
    return ConversationsUseCase(
        queryRepository,
        conversationRepository,
        transferRepository,
        localStorageRepository,
        typingIndicatorManager,
        loggingUseCase.getRemoteLogger('ConversationsUseCase'));
  }

  @Singleton()
  @Injectable()
  ProductivityToolsViewmodelInterface productivityToolsViewmodelFactory(
      GraphQlRepository graphQlRepository) {
    return ProductivityToolsViewmodel(graphQlRepository);
  }

  @preResolve
  @Singleton()
  Future<NotificationsRepository> notificationsRepositoryFactory(
      ShimServiceApi api) async {
    return NotificationsRepositoryImpl(api);
  }

  @preResolve
  @Singleton()
  Future<NotificationsUseCase> notificationsUseCaseFactory(
      NotificationsRepository notificationsRepository,
      AppLifeCycleRepository appLifeCycleRepository,
      MessageQueueRepository messageQueueRepository,
      LocalStorageRepository localStorage,
      SessionUseCase sessionUseCase,
      LoggingUseCase loggingUseCase) async {
    return NotificationsUseCase(
        notificationsRepository,
        appLifeCycleRepository,
        messageQueueRepository,
        localStorage,
        sessionUseCase,
        loggingUseCase.getRemoteLogger('NotificationsUseCase'))
      ..init();
  }

  @preResolve
  @Singleton()
  Future<SalesforceDataRepository> salesforceDataRepositoryFactory(
      LoggingUseCase loggingUseCase,
      LocalStorageRepository localStorage) async {
    return SalesforceDataRepository(
        loggingUseCase.getRemoteLogger('salesforceData'), localStorage);
  }

  @preResolve
  @Singleton()
  Future<SalesforceDataUseCase> salesforceDataUseCaseFactory(
      LoggingUseCase loggingUseCase,
      SalesforceDataRepository salesforceDataRepository,
      LocalStorageRepository localStorageRepository) async {
    return SalesforceDataUseCase(
        loggingUseCase.getRemoteLogger('salesforceDataUseCase'),
        salesforceDataRepository,
        localStorageRepository);
  }

  @preResolve
  @Singleton()
  Future<ChannelsUseCase> channelsUseCaseFactory(
      ChannelsRepository channersRepository,
      LoggingUseCase loggingUseCase,
      LocalStorageRepository storageRepository) async {
    return ChannelsUseCase(
        channersRepository,
        loggingUseCase.getRemoteLogger('channelsUseCaseFactory'),
        storageRepository);
  }

  @preResolve
  @Singleton()
  Future<MessagingChannelsUseCase> messagingChannelsUseCaseFactory(
      QueryRepository queryRepository) async {
    return MessagingChannelsUseCase(queryRepository);
  }

  @preResolve
  @Singleton()
  Future<TypingIndicatorUseCase> typingIndicatorUseCaseFactory() async {
    return TypingIndicatorUseCase();
  }

  @preResolve
  @Singleton()
  Future<TypingIndicatorManager> typingManagerFactory(
      TypingIndicatorUseCase useCase) async {
    return TypingIndicatorManager(useCase);
  }

  @preResolve
  @Singleton()
  Future<MessagingService> messagingServiceFactory(
      MessageQueueManager messagingQueueUseCase,
      MessagingUseCase messagingUseCase,
      ConversationsUseCase conversationsUseCase,
      AuthUseCase authUseCase,
      LoggingUseCase loggingUseCase) async {
    final messagingService = MessagingService(
        messagingQueueUseCase,
        messagingUseCase,
        conversationsUseCase,
        authUseCase,
        loggingUseCase.getRemoteLogger('MessagingService'))
      ..init();
    return Future.value(messagingService);
  }

  @preResolve
  @Singleton()
  Future<CrashManager> crashManagerFactory(
      LoggingUseCase loggingUseCase) async {
    final crashManager = CrashManager(
        loggingUseCase.getRemoteLogger('lib.frameworks.crash_manager'))
      ..init();
    return Future.value(crashManager);
  }

  @preResolve
  @Singleton()
  Future<LoggingRepository> loggingRepositoryFactory(
      LocalStorageRepository localStorage) async {
    return LoggingRepositoryImpl(localStorage);
  }

  @preResolve
  @Singleton()
  Future<ShimServiceApi> shimServiceApiFactory(
      Dio dio, AppConfig appConfig) async {
    return ShimServiceApi(
      dio,
      baseUrl: appConfig.shimServiceUrl,
    );
  }

  @preResolve
  @Singleton()
  Future<LoggerApi> loggerApiFactory(Dio dio, AppConfig appConfig) async {
    return LoggerApi(
      dio,
      baseUrl: appConfig.shimServiceUrl,
    );
  }

  @preResolve
  @Deprecated('legacy')
  @Singleton()
  Future<ContactsViewmodelInterface> contactsViewmodelFactory(
          ContactsUseCase contactsUseCase) async =>
      ContactsViewmodel(contactsUseCase);

  @Deprecated('legacy')
  @preResolve
  @Singleton()
  Future<ConversationsViewmodel> conversationsViewmodelFactory(
    PresenceUseCase presenceManager,
    ConversationsUseCase conversationsUseCase,
  ) async {
    return ConversationsViewmodel(
      conversationsUseCase,
    );
  }

  @preResolve
  @Singleton()
  Future<Dio> dioFactory(
      AuthInterceptor authInterceptor,
      LoggerInterceptor loggerInterceptor,
      SalesforceInterceptor salesforceInterceptor,
      LoggingUseCase loggingUseCase) async {
    final dio = Dio(BaseOptions());
    dio.interceptors.add(authInterceptor);
    dio.interceptors.add(salesforceInterceptor);
    dio.interceptors.add(RetryInterceptor(
      dio: dio,
      logPrint: print,
      // specify log function (optional)
      retries: 3,
      // retry count (optional)
      // Remove 502 to handle Salesforce 502 error
      // List below is from: https://pub.dev/packages/dio_smart_retry
      retryEvaluator: DefaultRetryEvaluator({503, 504}).evaluate,
      retryDelays: const [
        // set delays between retries (optional)
        Duration(seconds: 1), // wait 1 sec before first retry
        Duration(seconds: 2), // wait 2 sec before second retry
        Duration(seconds: 3), // wait 3 sec before third retry
      ],
    ));
    dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90));
    dio.interceptors.add(loggerInterceptor);
    return dio;
  }
}
