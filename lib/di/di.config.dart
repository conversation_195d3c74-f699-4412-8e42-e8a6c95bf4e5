// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i45;
import 'package:firebase_remote_config/firebase_remote_config.dart' as _i6;
import 'package:flutter_web_auth_2/flutter_web_auth_2.dart' as _i7;
import 'package:get_it/get_it.dart' as _i1;
import 'package:injectable/injectable.dart' as _i2;
import 'package:x1440/api/auth_interceptor.dart' as _i39;
import 'package:x1440/api/logger_api.dart' as _i46;
import 'package:x1440/api/logger_interceptor.dart' as _i43;
import 'package:x1440/api/salesforce/salesforce_interceptor.dart' as _i15;
import 'package:x1440/api/shim_service_api.dart' as _i47;
import 'package:x1440/debug/global_bloc_observer.dart' as _i42;
import 'package:x1440/di/modules/base_modules.dart' as _i83;
import 'package:x1440/di/modules/config_module.dart' as _i85;
import 'package:x1440/di/modules/demo_module.dart' as _i84;
import 'package:x1440/frameworks/conversations_service.dart' as _i78;
import 'package:x1440/frameworks/crash_manager.dart' as _i40;
import 'package:x1440/frameworks/demo/end_user/demo_ai_end_user_service.dart'
    as _i82;
import 'package:x1440/frameworks/message_queue/message_queue_manager.dart'
    as _i71;
import 'package:x1440/frameworks/messaging_service.dart' as _i80;
import 'package:x1440/frameworks/notifications/notifications_manager.dart'
    as _i57;
import 'package:x1440/frameworks/presence/presence_manager.dart' as _i75;
import 'package:x1440/frameworks/push_notification_manager.dart' as _i61;
import 'package:x1440/frameworks/remote_config/app_config.dart' as _i20;
import 'package:x1440/frameworks/routing/routing_manager.dart' as _i32;
import 'package:x1440/frameworks/settings/settings_manager.dart' as _i35;
import 'package:x1440/frameworks/typing_indicator/typing_indicator_manager.dart'
    as _i37;
import 'package:x1440/repositories/auth/auth_repository.dart' as _i49;
import 'package:x1440/repositories/channels/channels_repository.dart' as _i50;
import 'package:x1440/repositories/contacts/contact_details_repository.dart'
    as _i21;
import 'package:x1440/repositories/contacts/contacts_repository.dart' as _i22;
import 'package:x1440/repositories/conversation/conversation_repository.dart'
    as _i52;
import 'package:x1440/repositories/environment_info/environment_info_repository.dart'
    as _i25;
import 'package:x1440/repositories/graph_ql/graphql_repository.dart' as _i26;
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart'
    as _i4;
import 'package:x1440/repositories/logging/logging_repository.dart' as _i10;
import 'package:x1440/repositories/message_queue/message_queue_repository.dart'
    as _i28;
import 'package:x1440/repositories/messaging_sessions/messaging_sessions_repository.dart'
    as _i11;
import 'package:x1440/repositories/notifications/notifications_repository.dart'
    as _i58;
import 'package:x1440/repositories/presence/presence_repository.dart' as _i59;
import 'package:x1440/repositories/query/query_repository.dart' as _i13;
import 'package:x1440/repositories/quick_text_repository/quick_text_repository.dart'
    as _i62;
import 'package:x1440/repositories/salesforce_data_repository/salesforce_data_repository.dart'
    as _i33;
import 'package:x1440/repositories/search/search_repository.dart' as _i16;
import 'package:x1440/repositories/session/session_repository.dart' as _i63;
import 'package:x1440/repositories/storage/local_storage_repository.dart'
    as _i9;
import 'package:x1440/repositories/transfer/transfer_repository.dart' as _i48;
import 'package:x1440/repositories/websocket/messaging_definitions_repository.dart'
    as _i55;
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart'
    as _i36;
import 'package:x1440/services/de_conversations_service.dart' as _i54;
import 'package:x1440/services/interfaces/contact_viewmodel_interface.dart'
    as _i24;
import 'package:x1440/services/interfaces/productivity_tools_viewmodel_interface.dart'
    as _i31;
import 'package:x1440/services/security/encryption_service.dart' as _i5;
import 'package:x1440/services/shim_service/shim_service.dart' as _i18;
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_bloc.dart' as _i65;
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart' as _i3;
import 'package:x1440/ui/blocs/auth/auth_bloc.dart' as _i76;
import 'package:x1440/ui/blocs/channels/channels_bloc.dart' as _i67;
import 'package:x1440/ui/blocs/chat/chat_bloc.dart' as _i77;
import 'package:x1440/ui/blocs/contacts/contacts_bloc.dart' as _i68;
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart' as _i69;
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_manager.dart' as _i44;
import 'package:x1440/ui/blocs/image_size/image_size_bloc.dart' as _i8;
import 'package:x1440/ui/blocs/messaging/definitions/messaging_definitions_bloc.dart'
    as _i72;
import 'package:x1440/ui/blocs/messaging/messaging_bloc.dart' as _i79;
import 'package:x1440/ui/blocs/org/org_bloc.dart' as _i30;
import 'package:x1440/use_cases/ai_suggestions/ai_suggestions_use_case.dart'
    as _i38;
import 'package:x1440/use_cases/auth/auth_use_case.dart' as _i66;
import 'package:x1440/use_cases/channels/channels_use_case.dart' as _i51;
import 'package:x1440/use_cases/contacts/contacts_use_case.dart' as _i23;
import 'package:x1440/use_cases/conversations/conversations_use_case.dart'
    as _i53;
import 'package:x1440/use_cases/demo/demo_ai_suggestions_use_case.dart' as _i81;
import 'package:x1440/use_cases/demo/demo_user_use_case.dart' as _i41;
import 'package:x1440/use_cases/logging/logging_use_case.dart' as _i27;
import 'package:x1440/use_cases/messaging/messaging_definitions_use_case.dart'
    as _i56;
import 'package:x1440/use_cases/messaging/messaging_use_case.dart' as _i73;
import 'package:x1440/use_cases/messaging_channels/messaging_channels_use_case.dart'
    as _i29;
import 'package:x1440/use_cases/notifications/notifications_use_case.dart'
    as _i74;
import 'package:x1440/use_cases/org/org_use_case.dart' as _i12;
import 'package:x1440/use_cases/presence/presence_use_case.dart' as _i60;
import 'package:x1440/use_cases/quick_actions/quick_actions_use_case.dart'
    as _i14;
import 'package:x1440/use_cases/salesforce_data/salesforce_data_use_case.dart'
    as _i34;
import 'package:x1440/use_cases/session/session_use_case.dart' as _i64;
import 'package:x1440/use_cases/settings/settings_use_case.dart' as _i17;
import 'package:x1440/use_cases/typing_indicator/typing_indicator_use_case.dart'
    as _i19;
import 'package:x1440/viewmodels/conversations_viewmodel.dart' as _i70;

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i1.GetIt> init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final baseModules = _$BaseModules();
    final moduleAppConfig = _$ModuleAppConfig();
    final demoModule = _$DemoModule();
    await gh.singletonAsync<_i3.AppErrorBloc>(
      () => baseModules.appErrorBlocFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i4.AppLifeCycleRepository>(
      () => baseModules.appLifeCycleRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i5.EncryptionService>(
      () => baseModules.encryptionServiceFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i6.FirebaseRemoteConfig>(
      () => moduleAppConfig.firebaseRemoteConfig(),
      preResolve: true,
    );
    await gh.singletonAsync<_i7.FlutterWebAuth2Options>(
      () => moduleAppConfig.flutterWebAuth2Options(),
      preResolve: true,
    );
    gh.singleton<_i8.ImageSizeBloc>(() => baseModules.imageSizeBlocFactory());
    await gh.singletonAsync<_i9.LocalStorageRepository>(
      () => baseModules.localStorageRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i10.LoggingRepository>(
      () => baseModules
          .loggingRepositoryFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i11.MessagingSessionsRepository>(
      () => baseModules
          .messagingSessionsRepositoryFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i12.OrgUseCase>(
      () => baseModules.orgUseCaseFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i13.QueryRepository>(
      () =>
          baseModules.queryRepositoryFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i14.QuickActionsUseCase>(
      () => baseModules.quickActionsUseCaseFactory(
        gh<_i13.QueryRepository>(),
        gh<_i9.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i15.SalesforceInterceptor>(
      () => baseModules
          .salesforceInterceptorFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i16.SearchRepository>(
      () =>
          baseModules.searchRepositoryFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i17.SettingsUseCase>(
      () =>
          baseModules.settingsUseCaseFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i18.ShimService>(
      () => baseModules.shimServiceFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i19.TypingIndicatorUseCase>(
      () => baseModules.typingIndicatorUseCaseFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i20.AppConfig>(
      () => moduleAppConfig.appConfig(gh<_i6.FirebaseRemoteConfig>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i21.ContactDetailsRepository>(
      () => baseModules
          .contactDetailsRepositoryFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i22.ContactsRepository>(
      () => baseModules
          .contactsRepositoryFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i23.ContactsUseCase>(
      () => baseModules.contactsUseCaseFactory(
        gh<_i20.AppConfig>(),
        gh<_i16.SearchRepository>(),
        gh<_i22.ContactsRepository>(),
        gh<_i21.ContactDetailsRepository>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i13.QueryRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i24.ContactsViewmodelInterface>(
      () => baseModules.contactsViewmodelFactory(gh<_i23.ContactsUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i25.EnvironmentInfoRepository>(
      () => baseModules
          .environmentInfoRepositoryFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i26.GraphQlRepository>(
      () => baseModules
          .graphQlRepositoryFactory(gh<_i9.LocalStorageRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i27.LoggingUseCase>(
      () => baseModules.loggingUseCaseFactory(
        gh<_i10.LoggingRepository>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i4.AppLifeCycleRepository>(),
        gh<_i20.AppConfig>(),
        gh<_i25.EnvironmentInfoRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i28.MessageQueueRepository>(
      () => baseModules.messageQueueRepositoryFactory(
        gh<_i27.LoggingUseCase>(),
        gh<_i9.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i29.MessagingChannelsUseCase>(
      () => baseModules
          .messagingChannelsUseCaseFactory(gh<_i13.QueryRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i30.OrgBloc>(
      () => baseModules.orgBlocFactory(gh<_i12.OrgUseCase>()),
      preResolve: true,
    );
    gh.singleton<_i31.ProductivityToolsViewmodelInterface>(() => baseModules
        .productivityToolsViewmodelFactory(gh<_i26.GraphQlRepository>()));
    await gh.singletonAsync<_i32.RoutingManager>(
      () => baseModules.routingManagerFactory(gh<_i27.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i33.SalesforceDataRepository>(
      () => baseModules.salesforceDataRepositoryFactory(
        gh<_i27.LoggingUseCase>(),
        gh<_i9.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i34.SalesforceDataUseCase>(
      () => baseModules.salesforceDataUseCaseFactory(
        gh<_i27.LoggingUseCase>(),
        gh<_i33.SalesforceDataRepository>(),
        gh<_i9.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i35.SettingsManager>(
      () => baseModules.settingsManagerFactory(gh<_i17.SettingsUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i36.ShimWebsocketRepository>(
      () => baseModules.shimWebsocketRepositoryFactory(
        gh<_i27.LoggingUseCase>(),
        gh<_i9.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i37.TypingIndicatorManager>(
      () => baseModules.typingManagerFactory(gh<_i19.TypingIndicatorUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i38.AiSuggestionsUseCase>(
      () => baseModules.aiSuggestionsManagerFactory(gh<_i27.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i39.AuthInterceptor>(
      () => baseModules.authInterceptorFactory(
        gh<_i9.LocalStorageRepository>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i40.CrashManager>(
      () => baseModules.crashManagerFactory(gh<_i27.LoggingUseCase>()),
      preResolve: true,
    );
    gh.singletonAsync<_i41.DemoUserUseCase>(
        () => demoModule.demoUserUseCaseFactory(
              gh<_i25.EnvironmentInfoRepository>(),
              gh<_i9.LocalStorageRepository>(),
              gh<_i27.LoggingUseCase>(),
            ));
    await gh.singletonAsync<_i42.GlobalBlocObserver>(
      () => baseModules.globalBlocObserverFactory(gh<_i27.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i43.LoggerInterceptor>(
      () => baseModules.loggerInterceptorFactory(gh<_i27.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i44.DemoModeManager>(
      () async => demoModule
          .demoModeManagerFactory(await getAsync<_i41.DemoUserUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i45.Dio>(
      () => baseModules.dioFactory(
        gh<_i39.AuthInterceptor>(),
        gh<_i43.LoggerInterceptor>(),
        gh<_i15.SalesforceInterceptor>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i46.LoggerApi>(
      () => baseModules.loggerApiFactory(
        gh<_i45.Dio>(),
        gh<_i20.AppConfig>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i47.ShimServiceApi>(
      () => baseModules.shimServiceApiFactory(
        gh<_i45.Dio>(),
        gh<_i20.AppConfig>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i48.TransferRepository>(
      () => baseModules.transferRepositoryFactory(gh<_i47.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i49.AuthRepository>(
      () => baseModules.authRepositoryFactory(
        gh<_i47.ShimServiceApi>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i7.FlutterWebAuth2Options>(),
        gh<_i5.EncryptionService>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i50.ChannelsRepository>(
      () => baseModules.channelsRepositoryFactory(gh<_i47.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i51.ChannelsUseCase>(
      () => baseModules.channelsUseCaseFactory(
        gh<_i50.ChannelsRepository>(),
        gh<_i27.LoggingUseCase>(),
        gh<_i9.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i52.ConversationRepository>(
      () => baseModules.conversationRepositoryFactory(
        gh<_i47.ShimServiceApi>(),
        gh<_i9.LocalStorageRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i53.ConversationsUseCase>(
      () => baseModules.conversationsUseCaseFactory(
        gh<_i13.QueryRepository>(),
        gh<_i52.ConversationRepository>(),
        gh<_i48.TransferRepository>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i37.TypingIndicatorManager>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i54.DeConversationsService>(
      () => baseModules.DeConversationsServiceFactory(
        gh<_i18.ShimService>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i27.LoggingUseCase>(),
        gh<_i53.ConversationsUseCase>(),
        gh<_i26.GraphQlRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i55.MessagingDefinitionsRepository>(
      () => baseModules.messagingRepositoryFactory(gh<_i47.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i56.MessagingDefinitionsUseCase>(
      () => baseModules.messagingDefinitionsUseCaseFactory(
        gh<_i27.LoggingUseCase>(),
        gh<_i55.MessagingDefinitionsRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i57.NotificationManager>(
      () => baseModules.notificationManagerFactory(
        gh<_i32.RoutingManager>(),
        gh<_i23.ContactsUseCase>(),
        gh<_i53.ConversationsUseCase>(),
        gh<_i4.AppLifeCycleRepository>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i58.NotificationsRepository>(
      () =>
          baseModules.notificationsRepositoryFactory(gh<_i47.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i59.PresenceRepository>(
      () => baseModules.presenceRepositoryFactory(gh<_i47.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i60.PresenceUseCase>(
      () => baseModules.presenceUseCaseFactory(
        gh<_i9.LocalStorageRepository>(),
        gh<_i59.PresenceRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i61.PushNotificationManager>(
      () => baseModules.pushNotificationManagerFactory(
        gh<_i47.ShimServiceApi>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i62.QuickTextRepository>(
      () => baseModules.quickTextRepositoryFactory(gh<_i47.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i63.SessionRepository>(
      () => baseModules.sessionRepositoryFactory(gh<_i47.ShimServiceApi>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i64.SessionUseCase>(
      () => baseModules.sessionUseCaseFactory(
        gh<_i63.SessionRepository>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i25.EnvironmentInfoRepository>(),
        gh<_i20.AppConfig>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i65.AiSuggestionsBloc>(
        () => baseModules.aiSuggestionsBlocFactory(
              gh<_i38.AiSuggestionsUseCase>(),
              gh<_i53.ConversationsUseCase>(),
            ));
    await gh.singletonAsync<_i66.AuthUseCase>(
      () => baseModules.authUseCaseFactory(
        gh<_i49.AuthRepository>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i20.AppConfig>(),
        gh<_i57.NotificationManager>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i67.ChannelsBloc>(
      () => baseModules.channelsBlocFactory(gh<_i51.ChannelsUseCase>()),
      preResolve: true,
    );
    gh.singleton<_i68.ContactsBloc>(() => baseModules.contactsBlocFactory(
          gh<_i23.ContactsUseCase>(),
          gh<_i53.ConversationsUseCase>(),
        ));
    gh.singleton<_i69.ConversationsBloc>(
        () => baseModules.conversationsBlocFactory(
              gh<_i53.ConversationsUseCase>(),
              gh<_i14.QuickActionsUseCase>(),
              gh<_i66.AuthUseCase>(),
              gh<_i27.LoggingUseCase>(),
            ));
    await gh.singletonAsync<_i70.ConversationsViewmodel>(
      () => baseModules.conversationsViewmodelFactory(
        gh<_i60.PresenceUseCase>(),
        gh<_i53.ConversationsUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i71.MessageQueueManager>(
      () => baseModules.messageQueueManagerFactory(
        gh<_i28.MessageQueueRepository>(),
        gh<_i66.AuthUseCase>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i72.MessagingDefinitionsBloc>(
      () => baseModules.messagingDefinitionsBlocFactory(
          gh<_i56.MessagingDefinitionsUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i73.MessagingUseCase>(
      () => baseModules.messagingUseCaseFactory(
        gh<_i27.LoggingUseCase>(),
        gh<_i52.ConversationRepository>(),
        gh<_i28.MessageQueueRepository>(),
        gh<_i36.ShimWebsocketRepository>(),
        gh<_i58.NotificationsRepository>(),
        gh<_i33.SalesforceDataRepository>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i4.AppLifeCycleRepository>(),
        gh<_i25.EnvironmentInfoRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i74.NotificationsUseCase>(
      () => baseModules.notificationsUseCaseFactory(
        gh<_i58.NotificationsRepository>(),
        gh<_i4.AppLifeCycleRepository>(),
        gh<_i28.MessageQueueRepository>(),
        gh<_i9.LocalStorageRepository>(),
        gh<_i64.SessionUseCase>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i75.PresenceManager>(
      () => baseModules.presenceManagerFactory(
        gh<_i60.PresenceUseCase>(),
        gh<_i64.SessionUseCase>(),
        gh<_i73.MessagingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i76.AuthBloc>(
      () => baseModules.authBlocFactory(
        gh<_i66.AuthUseCase>(),
        gh<_i64.SessionUseCase>(),
        gh<_i53.ConversationsUseCase>(),
        gh<_i73.MessagingUseCase>(),
        gh<_i4.AppLifeCycleRepository>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i77.ChatBloc>(() => baseModules.chatBlocFactory(
          gh<_i53.ConversationsUseCase>(),
          gh<_i37.TypingIndicatorManager>(),
          gh<_i23.ContactsUseCase>(),
          gh<_i73.MessagingUseCase>(),
          gh<_i56.MessagingDefinitionsUseCase>(),
          gh<_i27.LoggingUseCase>(),
          gh<_i14.QuickActionsUseCase>(),
          gh<_i66.AuthUseCase>(),
        ));
    await gh.singletonAsync<_i78.ConversationsService>(
      () => baseModules.conversationsServiceFactory(
        gh<_i53.ConversationsUseCase>(),
        gh<_i74.NotificationsUseCase>(),
        gh<_i64.SessionUseCase>(),
        gh<_i73.MessagingUseCase>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i79.MessagingBloc>(() => baseModules.messagingBlocFactory(
          gh<_i73.MessagingUseCase>(),
          gh<_i71.MessageQueueManager>(),
          gh<_i25.EnvironmentInfoRepository>(),
        ));
    await gh.singletonAsync<_i80.MessagingService>(
      () => baseModules.messagingServiceFactory(
        gh<_i71.MessageQueueManager>(),
        gh<_i73.MessagingUseCase>(),
        gh<_i53.ConversationsUseCase>(),
        gh<_i66.AuthUseCase>(),
        gh<_i27.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    return this;
  }

// initializes the registration of demo-scope dependencies inside of GetIt
  Future<_i1.GetIt> initDemoScope({_i1.ScopeDisposeFunc? dispose}) async {
    return _i2.GetItHelper(this).initScopeAsync(
      'demo',
      dispose: dispose,
      init: (_i2.GetItHelper gh) async {
        final demoModule = _$DemoModule();
        await gh.singletonAsync<_i76.AuthBloc>(
          () => demoModule.authBlocFactory(),
          preResolve: true,
        );
        await gh.singletonAsync<_i67.ChannelsBloc>(
          () => demoModule.channelsBlocFactory(),
          preResolve: true,
        );
        await gh.singletonAsync<_i23.ContactsUseCase>(
          () => demoModule.demoContactsUseCaseFactory(),
          preResolve: true,
        );
        await gh.singletonAsync<_i24.ContactsViewmodelInterface>(
          () => demoModule
              .demoContactsViewmodelFactory(gh<_i23.ContactsUseCase>()),
          preResolve: true,
        );
        gh.singleton<_i69.ConversationsBloc>(
            () => demoModule.conversationsBlocFactory());
        gh.singleton<_i81.DemoAiSuggestionsUseCase>(() => demoModule
            .demoAiSuggestionsUseCaseFactory(gh<_i27.LoggingUseCase>()));
        gh.singleton<_i75.PresenceManager>(
            () => demoModule.demoPresenceManagerFactory());
        gh.singleton<_i31.ProductivityToolsViewmodelInterface>(
            () => demoModule.productivityToolsViewmodelFactory());
        gh.singleton<_i65.AiSuggestionsBloc>(() => demoModule
            .demoAiSuggestionsBlocFactory(gh<_i81.DemoAiSuggestionsUseCase>()));
        gh.singleton<_i82.DemoAiEndUserService>(() => demoModule
            .demoAiEndUserServiceFactory(gh<_i81.DemoAiSuggestionsUseCase>()));
        gh.singleton<_i77.ChatBloc>(() => demoModule.demoChatBlocFactory(
              gh<_i82.DemoAiEndUserService>(),
              gh<_i23.ContactsUseCase>(),
            ));
      },
    );
  }
}

class _$BaseModules extends _i83.BaseModules {}

class _$DemoModule extends _i84.DemoModule {}

class _$ModuleAppConfig extends _i85.ModuleAppConfig {}
