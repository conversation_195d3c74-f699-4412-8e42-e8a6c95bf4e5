import 'dart:ui';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'toast_options.freezed.dart';

@freezed
class ToastOptions with _$ToastOptions {
  const factory ToastOptions({
    /// Android Only
    Toast? toastLength,
    /// position
    ToastGravity? gravity,
    /// iOS & Web Only
    int? timeInSecForIosWeb,
    Color? backgroundColor,
    Color? textColor,
    double? fontSize,
  }) = _ToastOptions;
}