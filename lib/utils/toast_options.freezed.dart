// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'toast_options.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ToastOptions {
  /// Android Only
  Toast? get toastLength => throw _privateConstructorUsedError;

  /// position
  ToastGravity? get gravity => throw _privateConstructorUsedError;

  /// iOS & Web Only
  int? get timeInSecForIosWeb => throw _privateConstructorUsedError;
  Color? get backgroundColor => throw _privateConstructorUsedError;
  Color? get textColor => throw _privateConstructorUsedError;
  double? get fontSize => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ToastOptionsCopyWith<ToastOptions> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ToastOptionsCopyWith<$Res> {
  factory $ToastOptionsCopyWith(
          ToastOptions value, $Res Function(ToastOptions) then) =
      _$ToastOptionsCopyWithImpl<$Res, ToastOptions>;
  @useResult
  $Res call(
      {Toast? toastLength,
      ToastGravity? gravity,
      int? timeInSecForIosWeb,
      Color? backgroundColor,
      Color? textColor,
      double? fontSize});
}

/// @nodoc
class _$ToastOptionsCopyWithImpl<$Res, $Val extends ToastOptions>
    implements $ToastOptionsCopyWith<$Res> {
  _$ToastOptionsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? toastLength = freezed,
    Object? gravity = freezed,
    Object? timeInSecForIosWeb = freezed,
    Object? backgroundColor = freezed,
    Object? textColor = freezed,
    Object? fontSize = freezed,
  }) {
    return _then(_value.copyWith(
      toastLength: freezed == toastLength
          ? _value.toastLength
          : toastLength // ignore: cast_nullable_to_non_nullable
              as Toast?,
      gravity: freezed == gravity
          ? _value.gravity
          : gravity // ignore: cast_nullable_to_non_nullable
              as ToastGravity?,
      timeInSecForIosWeb: freezed == timeInSecForIosWeb
          ? _value.timeInSecForIosWeb
          : timeInSecForIosWeb // ignore: cast_nullable_to_non_nullable
              as int?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      textColor: freezed == textColor
          ? _value.textColor
          : textColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      fontSize: freezed == fontSize
          ? _value.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ToastOptionsImplCopyWith<$Res>
    implements $ToastOptionsCopyWith<$Res> {
  factory _$$ToastOptionsImplCopyWith(
          _$ToastOptionsImpl value, $Res Function(_$ToastOptionsImpl) then) =
      __$$ToastOptionsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Toast? toastLength,
      ToastGravity? gravity,
      int? timeInSecForIosWeb,
      Color? backgroundColor,
      Color? textColor,
      double? fontSize});
}

/// @nodoc
class __$$ToastOptionsImplCopyWithImpl<$Res>
    extends _$ToastOptionsCopyWithImpl<$Res, _$ToastOptionsImpl>
    implements _$$ToastOptionsImplCopyWith<$Res> {
  __$$ToastOptionsImplCopyWithImpl(
      _$ToastOptionsImpl _value, $Res Function(_$ToastOptionsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? toastLength = freezed,
    Object? gravity = freezed,
    Object? timeInSecForIosWeb = freezed,
    Object? backgroundColor = freezed,
    Object? textColor = freezed,
    Object? fontSize = freezed,
  }) {
    return _then(_$ToastOptionsImpl(
      toastLength: freezed == toastLength
          ? _value.toastLength
          : toastLength // ignore: cast_nullable_to_non_nullable
              as Toast?,
      gravity: freezed == gravity
          ? _value.gravity
          : gravity // ignore: cast_nullable_to_non_nullable
              as ToastGravity?,
      timeInSecForIosWeb: freezed == timeInSecForIosWeb
          ? _value.timeInSecForIosWeb
          : timeInSecForIosWeb // ignore: cast_nullable_to_non_nullable
              as int?,
      backgroundColor: freezed == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      textColor: freezed == textColor
          ? _value.textColor
          : textColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      fontSize: freezed == fontSize
          ? _value.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

class _$ToastOptionsImpl implements _ToastOptions {
  const _$ToastOptionsImpl(
      {this.toastLength,
      this.gravity,
      this.timeInSecForIosWeb,
      this.backgroundColor,
      this.textColor,
      this.fontSize});

  /// Android Only
  @override
  final Toast? toastLength;

  /// position
  @override
  final ToastGravity? gravity;

  /// iOS & Web Only
  @override
  final int? timeInSecForIosWeb;
  @override
  final Color? backgroundColor;
  @override
  final Color? textColor;
  @override
  final double? fontSize;

  @override
  String toString() {
    return 'ToastOptions(toastLength: $toastLength, gravity: $gravity, timeInSecForIosWeb: $timeInSecForIosWeb, backgroundColor: $backgroundColor, textColor: $textColor, fontSize: $fontSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ToastOptionsImpl &&
            (identical(other.toastLength, toastLength) ||
                other.toastLength == toastLength) &&
            (identical(other.gravity, gravity) || other.gravity == gravity) &&
            (identical(other.timeInSecForIosWeb, timeInSecForIosWeb) ||
                other.timeInSecForIosWeb == timeInSecForIosWeb) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.textColor, textColor) ||
                other.textColor == textColor) &&
            (identical(other.fontSize, fontSize) ||
                other.fontSize == fontSize));
  }

  @override
  int get hashCode => Object.hash(runtimeType, toastLength, gravity,
      timeInSecForIosWeb, backgroundColor, textColor, fontSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ToastOptionsImplCopyWith<_$ToastOptionsImpl> get copyWith =>
      __$$ToastOptionsImplCopyWithImpl<_$ToastOptionsImpl>(this, _$identity);
}

abstract class _ToastOptions implements ToastOptions {
  const factory _ToastOptions(
      {final Toast? toastLength,
      final ToastGravity? gravity,
      final int? timeInSecForIosWeb,
      final Color? backgroundColor,
      final Color? textColor,
      final double? fontSize}) = _$ToastOptionsImpl;

  @override

  /// Android Only
  Toast? get toastLength;
  @override

  /// position
  ToastGravity? get gravity;
  @override

  /// iOS & Web Only
  int? get timeInSecForIosWeb;
  @override
  Color? get backgroundColor;
  @override
  Color? get textColor;
  @override
  double? get fontSize;
  @override
  @JsonKey(ignore: true)
  _$$ToastOptionsImplCopyWith<_$ToastOptionsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
