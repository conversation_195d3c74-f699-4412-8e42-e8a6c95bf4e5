import 'package:isar/isar.dart';

/// ignore "copyWith" is needed for isar to work with freezed (isar has to ignore the copyWith)
const collectionOnFreezed = Collection(ignore: {'copyWith'});
const embeddedOnFreezed = Embedded(ignore: {'copyWith'});
const nullableEnumerated = Enumerated(EnumType.ordinal32);

/// FNV-1a 64bit hash algorithm optimized for Dart Strings (from Isar docs for using Strings as I ID's)
int fastHash(String string) {
  var hash = 0xcbf29ce484222325;

  var i = 0;
  while (i < string.length) {
    final codeUnit = string.codeUnitAt(i++);
    hash ^= codeUnit >> 8;
    hash *= 0x100000001b3;
    hash ^= codeUnit & 0xFF;
    hash *= 0x100000001b3;
  }

  return hash;
}
