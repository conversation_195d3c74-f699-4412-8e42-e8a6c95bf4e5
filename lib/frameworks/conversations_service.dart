import 'package:get_it/get_it.dart';
import 'package:x1440/api/dtos/outbound_message_body.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/models/conversation_model.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/ui/blocs/chat/chat_bloc.dart';
import 'package:x1440/ui/blocs/chat/chat_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/notifications/notifications_use_case.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';
import 'package:x1440/utils/Utils.dart';

import 'presence/presence_event.dart';

class ConversationsService {
  final ConversationsUseCase _conversationsUseCase;
  final NotificationsUseCase _notificationsUseCase;
  final SessionUseCase _sessionUseCase;
  final MessagingUseCase _messagingUseCase;
  final RemoteLogger _logger;

  /// circular dependency when I included this in BaseModules (CRM, 8/22/24)
  ConversationsBloc get _conversationsBloc => GetIt.I<ConversationsBloc>();

  PresenceManager get _presenceManager => GetIt.I<PresenceManager>();

  ConversationsService(
    this._conversationsUseCase,
    this._notificationsUseCase,
    this._sessionUseCase,
    this._messagingUseCase,
    this._logger,
  );

  Future<void> init() async {
    _conversationsBloc.stream.listen((state) {
      _updateChatBloc();
    });

    _presenceManager.stream.listen((presenceState) {
      if (presenceState.currentStatus.isOnline) {
        _notificationsUseCase.getUnacknowledgedNotifications();
      }
    });
  }

  Future<void> handlePresenceOffline() async {
    GetIt.I<PresenceManager>().add(SetUserOfflineEvent());
    try {
      await _conversationsUseCase
          .handlePresenceOffline()
          .timeout(const Duration(seconds: 20));
      await _sessionUseCase.endSession().timeout(const Duration(seconds: 20));
    } catch (e) {
      _logger.error('Error handling presence offline: $e');
    }
    _messagingUseCase.disconnectWebsocket();

    _updateConversations();
  }

  Future<void> addReceivedMessageToMessagingSession(
      LakMessage message, SfId messagingSessionId) async {
    await _conversationsUseCase.addNewMessage(message, messagingSessionId);
    _updateConversations();
  }

  Future<void> addReceivedMessageToConversationIdentifier(
      LakMessage message, String conversationIdentifier) async {
    await _conversationsUseCase.addNewMessageToConversationIdentifier(
        message, conversationIdentifier);
    _updateConversations();
  }

  void updateMessagingEndUserStatus(
      SfId messagingEndUserId, ConversationStatus status) {
    _conversationsUseCase.updateConversationStatus(messagingEndUserId, status);
    _updateConversations();
  }

  void updateMessagingSessionStatus(
      SfId messagingSessionId, ConversationStatus status) async {
    await _conversationsUseCase.updateMessagingSessionStatus(
        messagingSessionId, status);
    _updateConversations();
  }

  void _updateConversations() {
    _conversationsBloc.add(RefreshConversationsEvent());
    _updateChatBloc();
  }

  void _updateChatBloc() {
    if (GetIt.I.isRegistered<ChatBloc>()) {
      ChatBloc chatBloc = GetIt.I.get<ChatBloc>();
      final conversations = GetIt.I.currentScopeName == 'demo'
          ? GetIt.I<ConversationsBloc>().state.conversations.values
          : _conversationsUseCase.conversations.values;

      if (chatBloc.state.conversation != null && conversations.isNotEmpty) {
        final conversation = conversations.firstWhere((conversation) =>
            GetIt.I.currentScopeName == 'demo'
                ? (conversation.id == chatBloc.state.conversation!.id ||
                    chatBloc.state.savedContact?.key == conversation.id)
                : conversation.id == chatBloc.state.conversation!.id);
        chatBloc.add(SetConversationEvent(conversation: conversation));
      }
    }
  }

  void fetchOpenedAgentWork() async {
    await _conversationsUseCase.fetchOpenedAgentWork();
    _updateConversations();
  }

  void reportFailedOutboundMessages(List<OutboundMessageBody> outboundMessages) {
    for (final message in outboundMessages) {
      _logger.error('Failed to send message: $message');
      _conversationsUseCase.removeMessageFromConversation(
          message.messagingEndUserId, message.messageId);
      updateMessagingEndUserStatus(message.messagingEndUserId, ConversationStatus.closed);
      Utils.showToast(
          'Failed to send message',
          type: ToastType.error);
    }
  }
}
