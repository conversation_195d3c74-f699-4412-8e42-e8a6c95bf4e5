import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:push/push.dart';
import 'package:retrofit/dio.dart';
import 'package:x1440/api/dtos/refresh_device_token_body.dart';
import 'package:x1440/api/shim_service_api.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';

class PushNotificationManager {
  final ShimServiceApi _api;
  final LocalStorageRepository _localStorageRepository;
  final RemoteLogger _logger;

  PushNotificationManager(
      this._api, this._localStorageRepository, this._logger);

  Future<String?> getPushToken() async {
    String? token = await Push.instance.token;

    if (kDebugMode) {
      print("Push Token: $token");
    }
    if (Platform.isIOS && !kReleaseMode) {
      token = "sandbox:$token";
    }

    return token;
  }

  Future<bool> requestPermission() async {
    if (Platform.isAndroid) {
      final settings = await _localStorageRepository.getAppLocalSettings();
      final hasRequestedPushPermission = settings.hasRequestedPushPermission;
      if (hasRequestedPushPermission == false) {
        // await sharedPrefsService.setBool(hasRequestedPushPermissionKey, true);
        await _localStorageRepository.setAppLocalSettings(
            settings.copyWith(hasRequestedPushPermission: true));
        return await Push.instance.requestPermission();
      }
      if (hasRequestedPushPermission == true) {
        return true;
      }
    } else {
      return await Push.instance.requestPermission();
    }

    return false;
  }

  Future<bool> isPermissionGranted() async {
    if (Platform.isAndroid) {
      return Push.instance.areNotificationsEnabled();
    }
    return await Push.instance.requestPermission();
  }

  Future<bool> refreshSessionDeviceToken() async {
    final credentials = await _localStorageRepository.getCredentials();

      final savedToken = credentials.devicePushToken;

    if (credentials.isLoggedIn) {
      final token = await getPushToken();
      final orgId = credentials.orgId;
      final sessionToken = credentials.sessionToken;

      if (token != null && orgId != null && sessionToken != null) {
        if (savedToken == token) {
          return true;
        }
        final body = RefreshDeviceTokenBody(deviceToken: token);
        try {
          final HttpResponse response =
              await _api.refreshSessionDeviceToken(orgId, sessionToken, body);
          if (response.response.statusCode == 200 ||
              response.response.statusCode == 204) {
            await _localStorageRepository.setCredentials(
                credentials.copyWith(devicePushToken: token));
            return true;
          }
        } catch (error) {
          _logger.warn("Error refreshing session device token: $error");
        }
      }
    }

    return false;
  }
}
