// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'typing_indicator_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TypingIndicatorState _$TypingIndicatorStateFromJson(Map<String, dynamic> json) {
  return _TypingIndicatorState.fromJson(json);
}

/// @nodoc
mixin _$TypingIndicatorState {
  Map<String, bool> get activeEndUserTypingIndicatorsByConversationScrtUuId =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TypingIndicatorStateCopyWith<TypingIndicatorState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TypingIndicatorStateCopyWith<$Res> {
  factory $TypingIndicatorStateCopyWith(TypingIndicatorState value,
          $Res Function(TypingIndicatorState) then) =
      _$TypingIndicatorStateCopyWithImpl<$Res, TypingIndicatorState>;
  @useResult
  $Res call(
      {Map<String, bool> activeEndUserTypingIndicatorsByConversationScrtUuId});
}

/// @nodoc
class _$TypingIndicatorStateCopyWithImpl<$Res,
        $Val extends TypingIndicatorState>
    implements $TypingIndicatorStateCopyWith<$Res> {
  _$TypingIndicatorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeEndUserTypingIndicatorsByConversationScrtUuId = null,
  }) {
    return _then(_value.copyWith(
      activeEndUserTypingIndicatorsByConversationScrtUuId: null ==
              activeEndUserTypingIndicatorsByConversationScrtUuId
          ? _value.activeEndUserTypingIndicatorsByConversationScrtUuId
          : activeEndUserTypingIndicatorsByConversationScrtUuId // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TypingIndicatorStateImplCopyWith<$Res>
    implements $TypingIndicatorStateCopyWith<$Res> {
  factory _$$TypingIndicatorStateImplCopyWith(_$TypingIndicatorStateImpl value,
          $Res Function(_$TypingIndicatorStateImpl) then) =
      __$$TypingIndicatorStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Map<String, bool> activeEndUserTypingIndicatorsByConversationScrtUuId});
}

/// @nodoc
class __$$TypingIndicatorStateImplCopyWithImpl<$Res>
    extends _$TypingIndicatorStateCopyWithImpl<$Res, _$TypingIndicatorStateImpl>
    implements _$$TypingIndicatorStateImplCopyWith<$Res> {
  __$$TypingIndicatorStateImplCopyWithImpl(_$TypingIndicatorStateImpl _value,
      $Res Function(_$TypingIndicatorStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeEndUserTypingIndicatorsByConversationScrtUuId = null,
  }) {
    return _then(_$TypingIndicatorStateImpl(
      activeEndUserTypingIndicatorsByConversationScrtUuId: null ==
              activeEndUserTypingIndicatorsByConversationScrtUuId
          ? _value._activeEndUserTypingIndicatorsByConversationScrtUuId
          : activeEndUserTypingIndicatorsByConversationScrtUuId // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TypingIndicatorStateImpl implements _TypingIndicatorState {
  const _$TypingIndicatorStateImpl(
      {final Map<String, bool>
          activeEndUserTypingIndicatorsByConversationScrtUuId = const {}})
      : _activeEndUserTypingIndicatorsByConversationScrtUuId =
            activeEndUserTypingIndicatorsByConversationScrtUuId;

  factory _$TypingIndicatorStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$TypingIndicatorStateImplFromJson(json);

  final Map<String, bool> _activeEndUserTypingIndicatorsByConversationScrtUuId;
  @override
  @JsonKey()
  Map<String, bool> get activeEndUserTypingIndicatorsByConversationScrtUuId {
    if (_activeEndUserTypingIndicatorsByConversationScrtUuId
        is EqualUnmodifiableMapView)
      return _activeEndUserTypingIndicatorsByConversationScrtUuId;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(
        _activeEndUserTypingIndicatorsByConversationScrtUuId);
  }

  @override
  String toString() {
    return 'TypingIndicatorState(activeEndUserTypingIndicatorsByConversationScrtUuId: $activeEndUserTypingIndicatorsByConversationScrtUuId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TypingIndicatorStateImpl &&
            const DeepCollectionEquality().equals(
                other._activeEndUserTypingIndicatorsByConversationScrtUuId,
                _activeEndUserTypingIndicatorsByConversationScrtUuId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality()
          .hash(_activeEndUserTypingIndicatorsByConversationScrtUuId));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TypingIndicatorStateImplCopyWith<_$TypingIndicatorStateImpl>
      get copyWith =>
          __$$TypingIndicatorStateImplCopyWithImpl<_$TypingIndicatorStateImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TypingIndicatorStateImplToJson(
      this,
    );
  }
}

abstract class _TypingIndicatorState implements TypingIndicatorState {
  const factory _TypingIndicatorState(
          {final Map<String, bool>
              activeEndUserTypingIndicatorsByConversationScrtUuId}) =
      _$TypingIndicatorStateImpl;

  factory _TypingIndicatorState.fromJson(Map<String, dynamic> json) =
      _$TypingIndicatorStateImpl.fromJson;

  @override
  Map<String, bool> get activeEndUserTypingIndicatorsByConversationScrtUuId;
  @override
  @JsonKey(ignore: true)
  _$$TypingIndicatorStateImplCopyWith<_$TypingIndicatorStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
