import 'package:freezed_annotation/freezed_annotation.dart';

part 'typing_indicator_state.freezed.dart';
part 'typing_indicator_state.g.dart';

@freezed
class TypingIndicatorState with _$TypingIndicatorState {
  const factory TypingIndicatorState({
    @Default({}) Map<String, bool> activeEndUserTypingIndicatorsByConversationScrtUuId,
  }) = _TypingIndicatorState;

  factory TypingIndicatorState.fromJson(Map<String, dynamic> json) => _$TypingIndicatorStateFromJson(json);
}