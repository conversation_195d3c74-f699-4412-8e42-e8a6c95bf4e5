// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'typing_indicator_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TypingIndicatorStateImpl _$$TypingIndicatorStateImplFromJson(
        Map<String, dynamic> json) =>
    _$TypingIndicatorStateImpl(
      activeEndUserTypingIndicatorsByConversationScrtUuId:
          (json['activeEndUserTypingIndicatorsByConversationScrtUuId']
                      as Map<String, dynamic>?)
                  ?.map(
                (k, e) => MapEntry(k, e as bool),
              ) ??
              const {},
    );

Map<String, dynamic> _$$TypingIndicatorStateImplToJson(
        _$TypingIndicatorStateImpl instance) =>
    <String, dynamic>{
      'activeEndUserTypingIndicatorsByConversationScrtUuId':
          instance.activeEndUserTypingIndicatorsByConversationScrtUuId,
    };
