import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/frameworks/settings/models/app_local_settings.dart';

part 'settings_state.freezed.dart';
part 'settings_state.g.dart';
@freezed
class SettingsState with _$SettingsState {
  const factory SettingsState({
    required AppLocalSettings settings,
}) = _SettingsState;

  factory SettingsState.fromJson(Map<String, dynamic> json) => _$SettingsStateFromJson(json);
}