// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_local_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppLocalSettingsImpl _$$AppLocalSettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$AppLocalSettingsImpl(
      showDevOptions: json['showDevOptions'] as bool? ?? false,
      hasRequestedPushPermission:
          json['hasRequestedPushPermission'] as bool? ?? false,
      userRequestsAiSuggestions:
          json['userRequestsAiSuggestions'] as bool? ?? false,
      aiSuggestionsEnabled: json['aiSuggestionsEnabled'] as bool? ?? true,
      selectedEnvironment: json['selectedEnvironment'] == null
          ? const SalesforceEnvironment(
              type: SalesforceEnvironmentType.production)
          : SalesforceEnvironment.fromJson(
              json['selectedEnvironment'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AppLocalSettingsImplToJson(
        _$AppLocalSettingsImpl instance) =>
    <String, dynamic>{
      'showDevOptions': instance.showDevOptions,
      'hasRequestedPushPermission': instance.hasRequestedPushPermission,
      'userRequestsAiSuggestions': instance.userRequestsAiSuggestions,
      'aiSuggestionsEnabled': instance.aiSuggestionsEnabled,
      'selectedEnvironment': instance.selectedEnvironment.toJson(),
    };
