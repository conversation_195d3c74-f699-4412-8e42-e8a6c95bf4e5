import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/use_cases/models/salesforce_environment.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'app_local_settings.freezed.dart';
part 'app_local_settings.g.dart';

@freezed
@collectionOnFreezed
class AppLocalSettings with _$AppLocalSettings {
  /// Isar ID for indexing — Setting this one to '0' for singleton item in collection; otherwise default use here is (can also use custom indices -- see isar docs):
  final Id id = 0;

  const AppLocalSettings._();

  factory AppLocalSettings({
    @Default(false) bool showDevOptions,
    @Default(false) bool hasRequestedPushPermission,
    @Default(false) bool userRequestsAiSuggestions,
    @Default(true) bool aiSuggestionsEnabled,
    @Default(SalesforceEnvironment(type: SalesforceEnvironmentType.production))
    SalesforceEnvironment selectedEnvironment,
  }) = _AppLocalSettings;

  factory AppLocalSettings.fromJson(Map<String, dynamic> json) =>
      _$AppLocalSettingsFromJson(json);
}
