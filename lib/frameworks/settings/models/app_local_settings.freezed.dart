// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_local_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppLocalSettings _$AppLocalSettingsFromJson(Map<String, dynamic> json) {
  return _AppLocalSettings.fromJson(json);
}

/// @nodoc
mixin _$AppLocalSettings {
  bool get showDevOptions => throw _privateConstructorUsedError;
  bool get hasRequestedPushPermission => throw _privateConstructorUsedError;
  bool get userRequestsAiSuggestions => throw _privateConstructorUsedError;
  bool get aiSuggestionsEnabled => throw _privateConstructorUsedError;
  SalesforceEnvironment get selectedEnvironment =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AppLocalSettingsCopyWith<AppLocalSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppLocalSettingsCopyWith<$Res> {
  factory $AppLocalSettingsCopyWith(
          AppLocalSettings value, $Res Function(AppLocalSettings) then) =
      _$AppLocalSettingsCopyWithImpl<$Res, AppLocalSettings>;
  @useResult
  $Res call(
      {bool showDevOptions,
      bool hasRequestedPushPermission,
      bool userRequestsAiSuggestions,
      bool aiSuggestionsEnabled,
      SalesforceEnvironment selectedEnvironment});

  $SalesforceEnvironmentCopyWith<$Res> get selectedEnvironment;
}

/// @nodoc
class _$AppLocalSettingsCopyWithImpl<$Res, $Val extends AppLocalSettings>
    implements $AppLocalSettingsCopyWith<$Res> {
  _$AppLocalSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDevOptions = null,
    Object? hasRequestedPushPermission = null,
    Object? userRequestsAiSuggestions = null,
    Object? aiSuggestionsEnabled = null,
    Object? selectedEnvironment = null,
  }) {
    return _then(_value.copyWith(
      showDevOptions: null == showDevOptions
          ? _value.showDevOptions
          : showDevOptions // ignore: cast_nullable_to_non_nullable
              as bool,
      hasRequestedPushPermission: null == hasRequestedPushPermission
          ? _value.hasRequestedPushPermission
          : hasRequestedPushPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      userRequestsAiSuggestions: null == userRequestsAiSuggestions
          ? _value.userRequestsAiSuggestions
          : userRequestsAiSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      aiSuggestionsEnabled: null == aiSuggestionsEnabled
          ? _value.aiSuggestionsEnabled
          : aiSuggestionsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedEnvironment: null == selectedEnvironment
          ? _value.selectedEnvironment
          : selectedEnvironment // ignore: cast_nullable_to_non_nullable
              as SalesforceEnvironment,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SalesforceEnvironmentCopyWith<$Res> get selectedEnvironment {
    return $SalesforceEnvironmentCopyWith<$Res>(_value.selectedEnvironment,
        (value) {
      return _then(_value.copyWith(selectedEnvironment: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppLocalSettingsImplCopyWith<$Res>
    implements $AppLocalSettingsCopyWith<$Res> {
  factory _$$AppLocalSettingsImplCopyWith(_$AppLocalSettingsImpl value,
          $Res Function(_$AppLocalSettingsImpl) then) =
      __$$AppLocalSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showDevOptions,
      bool hasRequestedPushPermission,
      bool userRequestsAiSuggestions,
      bool aiSuggestionsEnabled,
      SalesforceEnvironment selectedEnvironment});

  @override
  $SalesforceEnvironmentCopyWith<$Res> get selectedEnvironment;
}

/// @nodoc
class __$$AppLocalSettingsImplCopyWithImpl<$Res>
    extends _$AppLocalSettingsCopyWithImpl<$Res, _$AppLocalSettingsImpl>
    implements _$$AppLocalSettingsImplCopyWith<$Res> {
  __$$AppLocalSettingsImplCopyWithImpl(_$AppLocalSettingsImpl _value,
      $Res Function(_$AppLocalSettingsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDevOptions = null,
    Object? hasRequestedPushPermission = null,
    Object? userRequestsAiSuggestions = null,
    Object? aiSuggestionsEnabled = null,
    Object? selectedEnvironment = null,
  }) {
    return _then(_$AppLocalSettingsImpl(
      showDevOptions: null == showDevOptions
          ? _value.showDevOptions
          : showDevOptions // ignore: cast_nullable_to_non_nullable
              as bool,
      hasRequestedPushPermission: null == hasRequestedPushPermission
          ? _value.hasRequestedPushPermission
          : hasRequestedPushPermission // ignore: cast_nullable_to_non_nullable
              as bool,
      userRequestsAiSuggestions: null == userRequestsAiSuggestions
          ? _value.userRequestsAiSuggestions
          : userRequestsAiSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      aiSuggestionsEnabled: null == aiSuggestionsEnabled
          ? _value.aiSuggestionsEnabled
          : aiSuggestionsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedEnvironment: null == selectedEnvironment
          ? _value.selectedEnvironment
          : selectedEnvironment // ignore: cast_nullable_to_non_nullable
              as SalesforceEnvironment,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppLocalSettingsImpl extends _AppLocalSettings {
  _$AppLocalSettingsImpl(
      {this.showDevOptions = false,
      this.hasRequestedPushPermission = false,
      this.userRequestsAiSuggestions = false,
      this.aiSuggestionsEnabled = true,
      this.selectedEnvironment = const SalesforceEnvironment(
          type: SalesforceEnvironmentType.production)})
      : super._();

  factory _$AppLocalSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppLocalSettingsImplFromJson(json);

  @override
  @JsonKey()
  final bool showDevOptions;
  @override
  @JsonKey()
  final bool hasRequestedPushPermission;
  @override
  @JsonKey()
  final bool userRequestsAiSuggestions;
  @override
  @JsonKey()
  final bool aiSuggestionsEnabled;
  @override
  @JsonKey()
  final SalesforceEnvironment selectedEnvironment;

  @override
  String toString() {
    return 'AppLocalSettings(showDevOptions: $showDevOptions, hasRequestedPushPermission: $hasRequestedPushPermission, userRequestsAiSuggestions: $userRequestsAiSuggestions, aiSuggestionsEnabled: $aiSuggestionsEnabled, selectedEnvironment: $selectedEnvironment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppLocalSettingsImpl &&
            (identical(other.showDevOptions, showDevOptions) ||
                other.showDevOptions == showDevOptions) &&
            (identical(other.hasRequestedPushPermission,
                    hasRequestedPushPermission) ||
                other.hasRequestedPushPermission ==
                    hasRequestedPushPermission) &&
            (identical(other.userRequestsAiSuggestions,
                    userRequestsAiSuggestions) ||
                other.userRequestsAiSuggestions == userRequestsAiSuggestions) &&
            (identical(other.aiSuggestionsEnabled, aiSuggestionsEnabled) ||
                other.aiSuggestionsEnabled == aiSuggestionsEnabled) &&
            (identical(other.selectedEnvironment, selectedEnvironment) ||
                other.selectedEnvironment == selectedEnvironment));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showDevOptions,
      hasRequestedPushPermission,
      userRequestsAiSuggestions,
      aiSuggestionsEnabled,
      selectedEnvironment);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppLocalSettingsImplCopyWith<_$AppLocalSettingsImpl> get copyWith =>
      __$$AppLocalSettingsImplCopyWithImpl<_$AppLocalSettingsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppLocalSettingsImplToJson(
      this,
    );
  }
}

abstract class _AppLocalSettings extends AppLocalSettings {
  factory _AppLocalSettings(
          {final bool showDevOptions,
          final bool hasRequestedPushPermission,
          final bool userRequestsAiSuggestions,
          final bool aiSuggestionsEnabled,
          final SalesforceEnvironment selectedEnvironment}) =
      _$AppLocalSettingsImpl;
  _AppLocalSettings._() : super._();

  factory _AppLocalSettings.fromJson(Map<String, dynamic> json) =
      _$AppLocalSettingsImpl.fromJson;

  @override
  bool get showDevOptions;
  @override
  bool get hasRequestedPushPermission;
  @override
  bool get userRequestsAiSuggestions;
  @override
  bool get aiSuggestionsEnabled;
  @override
  SalesforceEnvironment get selectedEnvironment;
  @override
  @JsonKey(ignore: true)
  _$$AppLocalSettingsImplCopyWith<_$AppLocalSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
