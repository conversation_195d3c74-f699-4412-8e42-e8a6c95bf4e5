import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/services/custom_salesforce_api_service.dart';
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_bloc.dart';
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/use_cases/demo/demo_ai_suggestions_use_case.dart';

import 'demo_ai_end_user_event.dart';
import 'demo_ai_end_user_state.dart';

class DemoAiEndUserService extends Bloc<DemoAiEndUserEvent, DemoAiEndUserState> {
  final DemoAiSuggestionsUseCase _aiSuggestionsUseCase;

  ConversationsBloc get _conversationsBloc => GetIt.I<ConversationsBloc>();

  AiSuggestionsBloc get _aiSuggestionsBloc => GetIt.I<AiSuggestionsBloc>();

  DemoAiEndUserService(this._aiSuggestionsUseCase) : super(DemoAiEndUserState()) {
    on<ReportAgentSentDemoMessage>(_onReportAgentSentDemoMessage);
  }

  void _onReportAgentSentDemoMessage(ReportAgentSentDemoMessage event, Emitter<DemoAiEndUserState> emit) async {
    final conversation = _conversationsBloc.state.conversations[event.meuId];
    if (conversation == null) {
      return;
    }
    _aiSuggestionsBloc.add(ClearSuggestionEvent(event.meuId));

    final suggestion = await _aiSuggestionsUseCase.getDemoAiSuggestions(conversation, persona: GPTPersona.Customer);

    // todo: add delay

    _conversationsBloc.add(SetLakConversationsEvent([conversation.lakConversation!.copyWith(
      messages: [
        LakMessage(
          id: Uuid().v4(),
          messageBody: suggestion.suggestion,
          actorType: MessageActorType.EndUser,
          entryType: MessageEntryType.text,
        ),
        ...conversation.lakConversation!.messages,
      ],
    )]));

    _aiSuggestionsBloc.add(GetAiSuggestionEvent(event.meuId));
  }
}