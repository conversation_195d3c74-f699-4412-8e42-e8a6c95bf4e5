import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_config.freezed.dart';

part 'app_config.g.dart';

@freezed
class AppConfig with _$AppConfig {
  factory AppConfig({
    required List<String> excludedLogs,
    required String shimServiceUrl,
    required SalesforceConfig? productionSalesforceConfig,
    required SalesforceConfig? sandboxSalesforceConfig,
    required GlobalConfig? globalConfig,
  }) = _AppConfig;

  factory AppConfig.fromJson(Map<String, Object?> json) =>
      _$AppConfigFromJson(json);
}

@freezed
class SalesforceConfig with _$SalesforceConfig {
  bool get canLogIn =>
      endPointPath.isNotEmpty &&
      responseType.isNotEmpty &&
      consumerKey.isNotEmpty &&
      authRedirectUri.isNotEmpty;

  SalesforceConfig._();

  factory SalesforceConfig({
    required String name,
    required String endPointBase,
    required String endPointPath,
    required String responseType,
    required String consumerKey,
    required String authRedirectUri,
    required String authCallbackUrlScheme,
  }) = _SalesforceConfig;

  factory SalesforceConfig.fromJson(Map<String, Object?> json) =>
      _$SalesforceConfigFromJson(json);
}

@freezed
class GlobalConfig with _$GlobalConfig {
  factory GlobalConfig({
    required int? shimServiceExpirationInSeconds,
    required int? sessionExpirationInSeconds,
    required int salesforceSearchResultLimit,
    required List<String> contactFields,
  }) = _GlobalConfig;

  factory GlobalConfig.fromJson(Map<String, Object?> json) =>
      _$GlobalConfigFromJson(json);
}
