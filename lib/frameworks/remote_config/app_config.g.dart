// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppConfigImpl _$$AppConfigImplFromJson(Map<String, dynamic> json) =>
    _$AppConfigImpl(
      excludedLogs: (json['excludedLogs'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      shimServiceUrl: json['shimServiceUrl'] as String,
      productionSalesforceConfig: json['productionSalesforceConfig'] == null
          ? null
          : SalesforceConfig.fromJson(
              json['productionSalesforceConfig'] as Map<String, dynamic>),
      sandboxSalesforceConfig: json['sandboxSalesforceConfig'] == null
          ? null
          : SalesforceConfig.fromJson(
              json['sandboxSalesforceConfig'] as Map<String, dynamic>),
      globalConfig: json['globalConfig'] == null
          ? null
          : GlobalConfig.fromJson(json['globalConfig'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AppConfigImplToJson(_$AppConfigImpl instance) =>
    <String, dynamic>{
      'excludedLogs': instance.excludedLogs,
      'shimServiceUrl': instance.shimServiceUrl,
      'productionSalesforceConfig':
          instance.productionSalesforceConfig?.toJson(),
      'sandboxSalesforceConfig': instance.sandboxSalesforceConfig?.toJson(),
      'globalConfig': instance.globalConfig?.toJson(),
    };

_$SalesforceConfigImpl _$$SalesforceConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$SalesforceConfigImpl(
      name: json['name'] as String,
      endPointBase: json['endPointBase'] as String,
      endPointPath: json['endPointPath'] as String,
      responseType: json['responseType'] as String,
      consumerKey: json['consumerKey'] as String,
      authRedirectUri: json['authRedirectUri'] as String,
      authCallbackUrlScheme: json['authCallbackUrlScheme'] as String,
    );

Map<String, dynamic> _$$SalesforceConfigImplToJson(
        _$SalesforceConfigImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'endPointBase': instance.endPointBase,
      'endPointPath': instance.endPointPath,
      'responseType': instance.responseType,
      'consumerKey': instance.consumerKey,
      'authRedirectUri': instance.authRedirectUri,
      'authCallbackUrlScheme': instance.authCallbackUrlScheme,
    };

_$GlobalConfigImpl _$$GlobalConfigImplFromJson(Map<String, dynamic> json) =>
    _$GlobalConfigImpl(
      shimServiceExpirationInSeconds:
          (json['shimServiceExpirationInSeconds'] as num?)?.toInt(),
      sessionExpirationInSeconds:
          (json['sessionExpirationInSeconds'] as num?)?.toInt(),
      salesforceSearchResultLimit:
          (json['salesforceSearchResultLimit'] as num).toInt(),
      contactFields: (json['contactFields'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$GlobalConfigImplToJson(_$GlobalConfigImpl instance) =>
    <String, dynamic>{
      'shimServiceExpirationInSeconds': instance.shimServiceExpirationInSeconds,
      'sessionExpirationInSeconds': instance.sessionExpirationInSeconds,
      'salesforceSearchResultLimit': instance.salesforceSearchResultLimit,
      'contactFields': instance.contactFields,
    };
