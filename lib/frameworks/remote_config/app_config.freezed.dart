// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppConfig _$AppConfigFromJson(Map<String, dynamic> json) {
  return _AppConfig.fromJson(json);
}

/// @nodoc
mixin _$AppConfig {
  List<String> get excludedLogs => throw _privateConstructorUsedError;
  String get shimServiceUrl => throw _privateConstructorUsedError;
  SalesforceConfig? get productionSalesforceConfig =>
      throw _privateConstructorUsedError;
  SalesforceConfig? get sandboxSalesforceConfig =>
      throw _privateConstructorUsedError;
  GlobalConfig? get globalConfig => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AppConfigCopyWith<AppConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppConfigCopyWith<$Res> {
  factory $AppConfigCopyWith(AppConfig value, $Res Function(AppConfig) then) =
      _$AppConfigCopyWithImpl<$Res, AppConfig>;
  @useResult
  $Res call(
      {List<String> excludedLogs,
      String shimServiceUrl,
      SalesforceConfig? productionSalesforceConfig,
      SalesforceConfig? sandboxSalesforceConfig,
      GlobalConfig? globalConfig});

  $SalesforceConfigCopyWith<$Res>? get productionSalesforceConfig;
  $SalesforceConfigCopyWith<$Res>? get sandboxSalesforceConfig;
  $GlobalConfigCopyWith<$Res>? get globalConfig;
}

/// @nodoc
class _$AppConfigCopyWithImpl<$Res, $Val extends AppConfig>
    implements $AppConfigCopyWith<$Res> {
  _$AppConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? excludedLogs = null,
    Object? shimServiceUrl = null,
    Object? productionSalesforceConfig = freezed,
    Object? sandboxSalesforceConfig = freezed,
    Object? globalConfig = freezed,
  }) {
    return _then(_value.copyWith(
      excludedLogs: null == excludedLogs
          ? _value.excludedLogs
          : excludedLogs // ignore: cast_nullable_to_non_nullable
              as List<String>,
      shimServiceUrl: null == shimServiceUrl
          ? _value.shimServiceUrl
          : shimServiceUrl // ignore: cast_nullable_to_non_nullable
              as String,
      productionSalesforceConfig: freezed == productionSalesforceConfig
          ? _value.productionSalesforceConfig
          : productionSalesforceConfig // ignore: cast_nullable_to_non_nullable
              as SalesforceConfig?,
      sandboxSalesforceConfig: freezed == sandboxSalesforceConfig
          ? _value.sandboxSalesforceConfig
          : sandboxSalesforceConfig // ignore: cast_nullable_to_non_nullable
              as SalesforceConfig?,
      globalConfig: freezed == globalConfig
          ? _value.globalConfig
          : globalConfig // ignore: cast_nullable_to_non_nullable
              as GlobalConfig?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SalesforceConfigCopyWith<$Res>? get productionSalesforceConfig {
    if (_value.productionSalesforceConfig == null) {
      return null;
    }

    return $SalesforceConfigCopyWith<$Res>(_value.productionSalesforceConfig!,
        (value) {
      return _then(_value.copyWith(productionSalesforceConfig: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SalesforceConfigCopyWith<$Res>? get sandboxSalesforceConfig {
    if (_value.sandboxSalesforceConfig == null) {
      return null;
    }

    return $SalesforceConfigCopyWith<$Res>(_value.sandboxSalesforceConfig!,
        (value) {
      return _then(_value.copyWith(sandboxSalesforceConfig: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $GlobalConfigCopyWith<$Res>? get globalConfig {
    if (_value.globalConfig == null) {
      return null;
    }

    return $GlobalConfigCopyWith<$Res>(_value.globalConfig!, (value) {
      return _then(_value.copyWith(globalConfig: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppConfigImplCopyWith<$Res>
    implements $AppConfigCopyWith<$Res> {
  factory _$$AppConfigImplCopyWith(
          _$AppConfigImpl value, $Res Function(_$AppConfigImpl) then) =
      __$$AppConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String> excludedLogs,
      String shimServiceUrl,
      SalesforceConfig? productionSalesforceConfig,
      SalesforceConfig? sandboxSalesforceConfig,
      GlobalConfig? globalConfig});

  @override
  $SalesforceConfigCopyWith<$Res>? get productionSalesforceConfig;
  @override
  $SalesforceConfigCopyWith<$Res>? get sandboxSalesforceConfig;
  @override
  $GlobalConfigCopyWith<$Res>? get globalConfig;
}

/// @nodoc
class __$$AppConfigImplCopyWithImpl<$Res>
    extends _$AppConfigCopyWithImpl<$Res, _$AppConfigImpl>
    implements _$$AppConfigImplCopyWith<$Res> {
  __$$AppConfigImplCopyWithImpl(
      _$AppConfigImpl _value, $Res Function(_$AppConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? excludedLogs = null,
    Object? shimServiceUrl = null,
    Object? productionSalesforceConfig = freezed,
    Object? sandboxSalesforceConfig = freezed,
    Object? globalConfig = freezed,
  }) {
    return _then(_$AppConfigImpl(
      excludedLogs: null == excludedLogs
          ? _value._excludedLogs
          : excludedLogs // ignore: cast_nullable_to_non_nullable
              as List<String>,
      shimServiceUrl: null == shimServiceUrl
          ? _value.shimServiceUrl
          : shimServiceUrl // ignore: cast_nullable_to_non_nullable
              as String,
      productionSalesforceConfig: freezed == productionSalesforceConfig
          ? _value.productionSalesforceConfig
          : productionSalesforceConfig // ignore: cast_nullable_to_non_nullable
              as SalesforceConfig?,
      sandboxSalesforceConfig: freezed == sandboxSalesforceConfig
          ? _value.sandboxSalesforceConfig
          : sandboxSalesforceConfig // ignore: cast_nullable_to_non_nullable
              as SalesforceConfig?,
      globalConfig: freezed == globalConfig
          ? _value.globalConfig
          : globalConfig // ignore: cast_nullable_to_non_nullable
              as GlobalConfig?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppConfigImpl implements _AppConfig {
  _$AppConfigImpl(
      {required final List<String> excludedLogs,
      required this.shimServiceUrl,
      required this.productionSalesforceConfig,
      required this.sandboxSalesforceConfig,
      required this.globalConfig})
      : _excludedLogs = excludedLogs;

  factory _$AppConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppConfigImplFromJson(json);

  final List<String> _excludedLogs;
  @override
  List<String> get excludedLogs {
    if (_excludedLogs is EqualUnmodifiableListView) return _excludedLogs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_excludedLogs);
  }

  @override
  final String shimServiceUrl;
  @override
  final SalesforceConfig? productionSalesforceConfig;
  @override
  final SalesforceConfig? sandboxSalesforceConfig;
  @override
  final GlobalConfig? globalConfig;

  @override
  String toString() {
    return 'AppConfig(excludedLogs: $excludedLogs, shimServiceUrl: $shimServiceUrl, productionSalesforceConfig: $productionSalesforceConfig, sandboxSalesforceConfig: $sandboxSalesforceConfig, globalConfig: $globalConfig)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppConfigImpl &&
            const DeepCollectionEquality()
                .equals(other._excludedLogs, _excludedLogs) &&
            (identical(other.shimServiceUrl, shimServiceUrl) ||
                other.shimServiceUrl == shimServiceUrl) &&
            (identical(other.productionSalesforceConfig,
                    productionSalesforceConfig) ||
                other.productionSalesforceConfig ==
                    productionSalesforceConfig) &&
            (identical(
                    other.sandboxSalesforceConfig, sandboxSalesforceConfig) ||
                other.sandboxSalesforceConfig == sandboxSalesforceConfig) &&
            (identical(other.globalConfig, globalConfig) ||
                other.globalConfig == globalConfig));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_excludedLogs),
      shimServiceUrl,
      productionSalesforceConfig,
      sandboxSalesforceConfig,
      globalConfig);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppConfigImplCopyWith<_$AppConfigImpl> get copyWith =>
      __$$AppConfigImplCopyWithImpl<_$AppConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppConfigImplToJson(
      this,
    );
  }
}

abstract class _AppConfig implements AppConfig {
  factory _AppConfig(
      {required final List<String> excludedLogs,
      required final String shimServiceUrl,
      required final SalesforceConfig? productionSalesforceConfig,
      required final SalesforceConfig? sandboxSalesforceConfig,
      required final GlobalConfig? globalConfig}) = _$AppConfigImpl;

  factory _AppConfig.fromJson(Map<String, dynamic> json) =
      _$AppConfigImpl.fromJson;

  @override
  List<String> get excludedLogs;
  @override
  String get shimServiceUrl;
  @override
  SalesforceConfig? get productionSalesforceConfig;
  @override
  SalesforceConfig? get sandboxSalesforceConfig;
  @override
  GlobalConfig? get globalConfig;
  @override
  @JsonKey(ignore: true)
  _$$AppConfigImplCopyWith<_$AppConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SalesforceConfig _$SalesforceConfigFromJson(Map<String, dynamic> json) {
  return _SalesforceConfig.fromJson(json);
}

/// @nodoc
mixin _$SalesforceConfig {
  String get name => throw _privateConstructorUsedError;
  String get endPointBase => throw _privateConstructorUsedError;
  String get endPointPath => throw _privateConstructorUsedError;
  String get responseType => throw _privateConstructorUsedError;
  String get consumerKey => throw _privateConstructorUsedError;
  String get authRedirectUri => throw _privateConstructorUsedError;
  String get authCallbackUrlScheme => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SalesforceConfigCopyWith<SalesforceConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SalesforceConfigCopyWith<$Res> {
  factory $SalesforceConfigCopyWith(
          SalesforceConfig value, $Res Function(SalesforceConfig) then) =
      _$SalesforceConfigCopyWithImpl<$Res, SalesforceConfig>;
  @useResult
  $Res call(
      {String name,
      String endPointBase,
      String endPointPath,
      String responseType,
      String consumerKey,
      String authRedirectUri,
      String authCallbackUrlScheme});
}

/// @nodoc
class _$SalesforceConfigCopyWithImpl<$Res, $Val extends SalesforceConfig>
    implements $SalesforceConfigCopyWith<$Res> {
  _$SalesforceConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? endPointBase = null,
    Object? endPointPath = null,
    Object? responseType = null,
    Object? consumerKey = null,
    Object? authRedirectUri = null,
    Object? authCallbackUrlScheme = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      endPointBase: null == endPointBase
          ? _value.endPointBase
          : endPointBase // ignore: cast_nullable_to_non_nullable
              as String,
      endPointPath: null == endPointPath
          ? _value.endPointPath
          : endPointPath // ignore: cast_nullable_to_non_nullable
              as String,
      responseType: null == responseType
          ? _value.responseType
          : responseType // ignore: cast_nullable_to_non_nullable
              as String,
      consumerKey: null == consumerKey
          ? _value.consumerKey
          : consumerKey // ignore: cast_nullable_to_non_nullable
              as String,
      authRedirectUri: null == authRedirectUri
          ? _value.authRedirectUri
          : authRedirectUri // ignore: cast_nullable_to_non_nullable
              as String,
      authCallbackUrlScheme: null == authCallbackUrlScheme
          ? _value.authCallbackUrlScheme
          : authCallbackUrlScheme // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SalesforceConfigImplCopyWith<$Res>
    implements $SalesforceConfigCopyWith<$Res> {
  factory _$$SalesforceConfigImplCopyWith(_$SalesforceConfigImpl value,
          $Res Function(_$SalesforceConfigImpl) then) =
      __$$SalesforceConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String endPointBase,
      String endPointPath,
      String responseType,
      String consumerKey,
      String authRedirectUri,
      String authCallbackUrlScheme});
}

/// @nodoc
class __$$SalesforceConfigImplCopyWithImpl<$Res>
    extends _$SalesforceConfigCopyWithImpl<$Res, _$SalesforceConfigImpl>
    implements _$$SalesforceConfigImplCopyWith<$Res> {
  __$$SalesforceConfigImplCopyWithImpl(_$SalesforceConfigImpl _value,
      $Res Function(_$SalesforceConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? endPointBase = null,
    Object? endPointPath = null,
    Object? responseType = null,
    Object? consumerKey = null,
    Object? authRedirectUri = null,
    Object? authCallbackUrlScheme = null,
  }) {
    return _then(_$SalesforceConfigImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      endPointBase: null == endPointBase
          ? _value.endPointBase
          : endPointBase // ignore: cast_nullable_to_non_nullable
              as String,
      endPointPath: null == endPointPath
          ? _value.endPointPath
          : endPointPath // ignore: cast_nullable_to_non_nullable
              as String,
      responseType: null == responseType
          ? _value.responseType
          : responseType // ignore: cast_nullable_to_non_nullable
              as String,
      consumerKey: null == consumerKey
          ? _value.consumerKey
          : consumerKey // ignore: cast_nullable_to_non_nullable
              as String,
      authRedirectUri: null == authRedirectUri
          ? _value.authRedirectUri
          : authRedirectUri // ignore: cast_nullable_to_non_nullable
              as String,
      authCallbackUrlScheme: null == authCallbackUrlScheme
          ? _value.authCallbackUrlScheme
          : authCallbackUrlScheme // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SalesforceConfigImpl extends _SalesforceConfig {
  _$SalesforceConfigImpl(
      {required this.name,
      required this.endPointBase,
      required this.endPointPath,
      required this.responseType,
      required this.consumerKey,
      required this.authRedirectUri,
      required this.authCallbackUrlScheme})
      : super._();

  factory _$SalesforceConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$SalesforceConfigImplFromJson(json);

  @override
  final String name;
  @override
  final String endPointBase;
  @override
  final String endPointPath;
  @override
  final String responseType;
  @override
  final String consumerKey;
  @override
  final String authRedirectUri;
  @override
  final String authCallbackUrlScheme;

  @override
  String toString() {
    return 'SalesforceConfig(name: $name, endPointBase: $endPointBase, endPointPath: $endPointPath, responseType: $responseType, consumerKey: $consumerKey, authRedirectUri: $authRedirectUri, authCallbackUrlScheme: $authCallbackUrlScheme)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SalesforceConfigImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.endPointBase, endPointBase) ||
                other.endPointBase == endPointBase) &&
            (identical(other.endPointPath, endPointPath) ||
                other.endPointPath == endPointPath) &&
            (identical(other.responseType, responseType) ||
                other.responseType == responseType) &&
            (identical(other.consumerKey, consumerKey) ||
                other.consumerKey == consumerKey) &&
            (identical(other.authRedirectUri, authRedirectUri) ||
                other.authRedirectUri == authRedirectUri) &&
            (identical(other.authCallbackUrlScheme, authCallbackUrlScheme) ||
                other.authCallbackUrlScheme == authCallbackUrlScheme));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, endPointBase, endPointPath,
      responseType, consumerKey, authRedirectUri, authCallbackUrlScheme);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SalesforceConfigImplCopyWith<_$SalesforceConfigImpl> get copyWith =>
      __$$SalesforceConfigImplCopyWithImpl<_$SalesforceConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SalesforceConfigImplToJson(
      this,
    );
  }
}

abstract class _SalesforceConfig extends SalesforceConfig {
  factory _SalesforceConfig(
      {required final String name,
      required final String endPointBase,
      required final String endPointPath,
      required final String responseType,
      required final String consumerKey,
      required final String authRedirectUri,
      required final String authCallbackUrlScheme}) = _$SalesforceConfigImpl;
  _SalesforceConfig._() : super._();

  factory _SalesforceConfig.fromJson(Map<String, dynamic> json) =
      _$SalesforceConfigImpl.fromJson;

  @override
  String get name;
  @override
  String get endPointBase;
  @override
  String get endPointPath;
  @override
  String get responseType;
  @override
  String get consumerKey;
  @override
  String get authRedirectUri;
  @override
  String get authCallbackUrlScheme;
  @override
  @JsonKey(ignore: true)
  _$$SalesforceConfigImplCopyWith<_$SalesforceConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GlobalConfig _$GlobalConfigFromJson(Map<String, dynamic> json) {
  return _GlobalConfig.fromJson(json);
}

/// @nodoc
mixin _$GlobalConfig {
  int? get shimServiceExpirationInSeconds => throw _privateConstructorUsedError;
  int? get sessionExpirationInSeconds => throw _privateConstructorUsedError;
  int get salesforceSearchResultLimit => throw _privateConstructorUsedError;
  List<String> get contactFields => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GlobalConfigCopyWith<GlobalConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GlobalConfigCopyWith<$Res> {
  factory $GlobalConfigCopyWith(
          GlobalConfig value, $Res Function(GlobalConfig) then) =
      _$GlobalConfigCopyWithImpl<$Res, GlobalConfig>;
  @useResult
  $Res call(
      {int? shimServiceExpirationInSeconds,
      int? sessionExpirationInSeconds,
      int salesforceSearchResultLimit,
      List<String> contactFields});
}

/// @nodoc
class _$GlobalConfigCopyWithImpl<$Res, $Val extends GlobalConfig>
    implements $GlobalConfigCopyWith<$Res> {
  _$GlobalConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shimServiceExpirationInSeconds = freezed,
    Object? sessionExpirationInSeconds = freezed,
    Object? salesforceSearchResultLimit = null,
    Object? contactFields = null,
  }) {
    return _then(_value.copyWith(
      shimServiceExpirationInSeconds: freezed == shimServiceExpirationInSeconds
          ? _value.shimServiceExpirationInSeconds
          : shimServiceExpirationInSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionExpirationInSeconds: freezed == sessionExpirationInSeconds
          ? _value.sessionExpirationInSeconds
          : sessionExpirationInSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      salesforceSearchResultLimit: null == salesforceSearchResultLimit
          ? _value.salesforceSearchResultLimit
          : salesforceSearchResultLimit // ignore: cast_nullable_to_non_nullable
              as int,
      contactFields: null == contactFields
          ? _value.contactFields
          : contactFields // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GlobalConfigImplCopyWith<$Res>
    implements $GlobalConfigCopyWith<$Res> {
  factory _$$GlobalConfigImplCopyWith(
          _$GlobalConfigImpl value, $Res Function(_$GlobalConfigImpl) then) =
      __$$GlobalConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? shimServiceExpirationInSeconds,
      int? sessionExpirationInSeconds,
      int salesforceSearchResultLimit,
      List<String> contactFields});
}

/// @nodoc
class __$$GlobalConfigImplCopyWithImpl<$Res>
    extends _$GlobalConfigCopyWithImpl<$Res, _$GlobalConfigImpl>
    implements _$$GlobalConfigImplCopyWith<$Res> {
  __$$GlobalConfigImplCopyWithImpl(
      _$GlobalConfigImpl _value, $Res Function(_$GlobalConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shimServiceExpirationInSeconds = freezed,
    Object? sessionExpirationInSeconds = freezed,
    Object? salesforceSearchResultLimit = null,
    Object? contactFields = null,
  }) {
    return _then(_$GlobalConfigImpl(
      shimServiceExpirationInSeconds: freezed == shimServiceExpirationInSeconds
          ? _value.shimServiceExpirationInSeconds
          : shimServiceExpirationInSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionExpirationInSeconds: freezed == sessionExpirationInSeconds
          ? _value.sessionExpirationInSeconds
          : sessionExpirationInSeconds // ignore: cast_nullable_to_non_nullable
              as int?,
      salesforceSearchResultLimit: null == salesforceSearchResultLimit
          ? _value.salesforceSearchResultLimit
          : salesforceSearchResultLimit // ignore: cast_nullable_to_non_nullable
              as int,
      contactFields: null == contactFields
          ? _value._contactFields
          : contactFields // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GlobalConfigImpl implements _GlobalConfig {
  _$GlobalConfigImpl(
      {required this.shimServiceExpirationInSeconds,
      required this.sessionExpirationInSeconds,
      required this.salesforceSearchResultLimit,
      required final List<String> contactFields})
      : _contactFields = contactFields;

  factory _$GlobalConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$GlobalConfigImplFromJson(json);

  @override
  final int? shimServiceExpirationInSeconds;
  @override
  final int? sessionExpirationInSeconds;
  @override
  final int salesforceSearchResultLimit;
  final List<String> _contactFields;
  @override
  List<String> get contactFields {
    if (_contactFields is EqualUnmodifiableListView) return _contactFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contactFields);
  }

  @override
  String toString() {
    return 'GlobalConfig(shimServiceExpirationInSeconds: $shimServiceExpirationInSeconds, sessionExpirationInSeconds: $sessionExpirationInSeconds, salesforceSearchResultLimit: $salesforceSearchResultLimit, contactFields: $contactFields)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GlobalConfigImpl &&
            (identical(other.shimServiceExpirationInSeconds,
                    shimServiceExpirationInSeconds) ||
                other.shimServiceExpirationInSeconds ==
                    shimServiceExpirationInSeconds) &&
            (identical(other.sessionExpirationInSeconds,
                    sessionExpirationInSeconds) ||
                other.sessionExpirationInSeconds ==
                    sessionExpirationInSeconds) &&
            (identical(other.salesforceSearchResultLimit,
                    salesforceSearchResultLimit) ||
                other.salesforceSearchResultLimit ==
                    salesforceSearchResultLimit) &&
            const DeepCollectionEquality()
                .equals(other._contactFields, _contactFields));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      shimServiceExpirationInSeconds,
      sessionExpirationInSeconds,
      salesforceSearchResultLimit,
      const DeepCollectionEquality().hash(_contactFields));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GlobalConfigImplCopyWith<_$GlobalConfigImpl> get copyWith =>
      __$$GlobalConfigImplCopyWithImpl<_$GlobalConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GlobalConfigImplToJson(
      this,
    );
  }
}

abstract class _GlobalConfig implements GlobalConfig {
  factory _GlobalConfig(
      {required final int? shimServiceExpirationInSeconds,
      required final int? sessionExpirationInSeconds,
      required final int salesforceSearchResultLimit,
      required final List<String> contactFields}) = _$GlobalConfigImpl;

  factory _GlobalConfig.fromJson(Map<String, dynamic> json) =
      _$GlobalConfigImpl.fromJson;

  @override
  int? get shimServiceExpirationInSeconds;
  @override
  int? get sessionExpirationInSeconds;
  @override
  int get salesforceSearchResultLimit;
  @override
  List<String> get contactFields;
  @override
  @JsonKey(ignore: true)
  _$$GlobalConfigImplCopyWith<_$GlobalConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
