import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:x1440/repositories/message_queue/message_queue_repository.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';

class MessageQueueManager {
  final MessageQueueRepository _messageQueueRepository;
  final AuthUseCase _authUseCase;
  final RemoteLogger _logger;

  MessageQueueManager(this._messageQueueRepository, this._authUseCase, this._logger);

  Stream<List<QueueReceiveMessage>> get messageReceiveQueue => _messageReceiveQueueController.stream;
  final StreamController<List<QueueReceiveMessage>>  _messageReceiveQueueController = StreamController<List<QueueReceiveMessage>>.broadcast();

  Stream<List<QueueSendMessage>> get messageSendQueue => _messageSendQueueController.stream;
  final StreamController<List<QueueSendMessage>>  _messageSendQueueController = StreamController<List<QueueSendMessage>>.broadcast();

  Future<void> init() async {
    await _messageQueueRepository.clearReceiveQueue();
    await _messageQueueRepository.clearSendQueue();
    _messageQueueRepository.messageReceiveQueueStream.listen(_onWatchMessageReceiveQueue);
    _messageQueueRepository.messageSendQueueStream.listen(_onWatchMessageSendQueue);
  }

  Future<void> _onWatchMessageReceiveQueue(List<QueueReceiveMessage> workMessageBodies) async {
    final credentials = await _authUseCase.getCredentials();
    for (QueueReceiveMessage message in workMessageBodies) {
      if (message.sessionId != credentials.sessionId) {
        _logger.info(
            'Received message from different session; removing. Message SessionId: ${message
                .sessionId}; Credentials SessionId: ${credentials.sessionId}');
        await _messageQueueRepository
            .removeMessageIdsFromReceiveQueue([message.notificationId]);
      } else {
        _messageReceiveQueueController.add([message]);
      }
    }
  }

  Future<void> _onWatchMessageSendQueue(List<QueueSendMessage> queueSendMessages) {
    _messageSendQueueController.add(queueSendMessages);
    return Future.value(null);
  }

  Future<void> addMessageToSendQueue(QueueSendMessage queueSendMessage) async {
    return _messageQueueRepository.addMessageToSendQueue(queueSendMessage);
  }

  @visibleForTesting // TODO: change test?
  Future<void> removeMessageIdsFromSendQueue(List<String> messageIds) async {
    return _messageQueueRepository.removeMessageIdsFromSendQueue(messageIds);
  }

  @visibleForTesting // TODO: change test?
  Future<void> addMessageToReceiveQueue(QueueReceiveMessage queueReceiveMessage) async {
    return _messageQueueRepository.addMessageToReceiveQueue(queueReceiveMessage);
  }

  @visibleForTesting // TODO: change test?
  Future<void> removeMessageIdsFromReceiveQueue(List<String> messageIds) async {
    return _messageQueueRepository.removeMessageIdsFromReceiveQueue(messageIds);
  }
}