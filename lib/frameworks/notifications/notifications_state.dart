import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';

import 'models/in_app_message_notification.dart';

part 'notifications_state.freezed.dart';

@freezed
class NotificationsState with _$NotificationsState {
  const factory NotificationsState({
    @Default([]) List<InAppMessageNotification> unacknowledgedNotifications,
    @Default([]) List<QueueReceiveMessage> messagesWithUnprocessedActions,
  }) = _NotificationsState;
}
