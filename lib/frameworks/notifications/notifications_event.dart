import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/services/shim_service/models/shim_service_payload.dart';

import 'models/in_app_message_notification.dart';

abstract class NotificationsEvent {}
class ClearNotificationsEvent extends NotificationsEvent {}
/// this should be called on successful shim service session start
class HandleMessagesWithUnprocessedActionsEvent extends NotificationsEvent {}
class ReportMessageReceivedEvent extends NotificationsEvent {
  final QueueReceiveMessage? queueReceivedMessage;
  final ShimServicePayload? shimServicePayload;

  ReportMessageReceivedEvent({this.queueReceivedMessage, this.shimServicePayload});
}
class AcknowledgeNotificationsEvent extends NotificationsEvent {
  final List<String> notificationIds;
  AcknowledgeNotificationsEvent(this.notificationIds);
}
///  when the NotificationManager is refactored, this can probably be made to also handle the DemoMode unread indicators
class AcknowledgeNotificationsForConversationEvent extends NotificationsEvent {
  final SfId conversationId;
  AcknowledgeNotificationsForConversationEvent(this.conversationId);
}
class ReportMessageNotificationNeedsDisplayEvent extends NotificationsEvent {
  final InAppMessageNotification inAppMessageNotification;
  ReportMessageNotificationNeedsDisplayEvent(this.inAppMessageNotification);
}
class RemoveNotificationByConversationIdEvent extends NotificationsEvent {
  final SfId conversationId;
  RemoveNotificationByConversationIdEvent(this.conversationId);
}
class RemoveNotificationByWorkTargetIdEvent extends NotificationsEvent {
  final SfId workTargetId;
  RemoveNotificationByWorkTargetIdEvent(this.workTargetId);
}