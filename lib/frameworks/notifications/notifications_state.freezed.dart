// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notifications_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NotificationsState {
  List<InAppMessageNotification> get unacknowledgedNotifications =>
      throw _privateConstructorUsedError;
  List<QueueReceiveMessage> get messagesWithUnprocessedActions =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $NotificationsStateCopyWith<NotificationsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationsStateCopyWith<$Res> {
  factory $NotificationsStateCopyWith(
          NotificationsState value, $Res Function(NotificationsState) then) =
      _$NotificationsStateCopyWithImpl<$Res, NotificationsState>;
  @useResult
  $Res call(
      {List<InAppMessageNotification> unacknowledgedNotifications,
      List<QueueReceiveMessage> messagesWithUnprocessedActions});
}

/// @nodoc
class _$NotificationsStateCopyWithImpl<$Res, $Val extends NotificationsState>
    implements $NotificationsStateCopyWith<$Res> {
  _$NotificationsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unacknowledgedNotifications = null,
    Object? messagesWithUnprocessedActions = null,
  }) {
    return _then(_value.copyWith(
      unacknowledgedNotifications: null == unacknowledgedNotifications
          ? _value.unacknowledgedNotifications
          : unacknowledgedNotifications // ignore: cast_nullable_to_non_nullable
              as List<InAppMessageNotification>,
      messagesWithUnprocessedActions: null == messagesWithUnprocessedActions
          ? _value.messagesWithUnprocessedActions
          : messagesWithUnprocessedActions // ignore: cast_nullable_to_non_nullable
              as List<QueueReceiveMessage>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationsStateImplCopyWith<$Res>
    implements $NotificationsStateCopyWith<$Res> {
  factory _$$NotificationsStateImplCopyWith(_$NotificationsStateImpl value,
          $Res Function(_$NotificationsStateImpl) then) =
      __$$NotificationsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<InAppMessageNotification> unacknowledgedNotifications,
      List<QueueReceiveMessage> messagesWithUnprocessedActions});
}

/// @nodoc
class __$$NotificationsStateImplCopyWithImpl<$Res>
    extends _$NotificationsStateCopyWithImpl<$Res, _$NotificationsStateImpl>
    implements _$$NotificationsStateImplCopyWith<$Res> {
  __$$NotificationsStateImplCopyWithImpl(_$NotificationsStateImpl _value,
      $Res Function(_$NotificationsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unacknowledgedNotifications = null,
    Object? messagesWithUnprocessedActions = null,
  }) {
    return _then(_$NotificationsStateImpl(
      unacknowledgedNotifications: null == unacknowledgedNotifications
          ? _value._unacknowledgedNotifications
          : unacknowledgedNotifications // ignore: cast_nullable_to_non_nullable
              as List<InAppMessageNotification>,
      messagesWithUnprocessedActions: null == messagesWithUnprocessedActions
          ? _value._messagesWithUnprocessedActions
          : messagesWithUnprocessedActions // ignore: cast_nullable_to_non_nullable
              as List<QueueReceiveMessage>,
    ));
  }
}

/// @nodoc

class _$NotificationsStateImpl implements _NotificationsState {
  const _$NotificationsStateImpl(
      {final List<InAppMessageNotification> unacknowledgedNotifications =
          const [],
      final List<QueueReceiveMessage> messagesWithUnprocessedActions =
          const []})
      : _unacknowledgedNotifications = unacknowledgedNotifications,
        _messagesWithUnprocessedActions = messagesWithUnprocessedActions;

  final List<InAppMessageNotification> _unacknowledgedNotifications;
  @override
  @JsonKey()
  List<InAppMessageNotification> get unacknowledgedNotifications {
    if (_unacknowledgedNotifications is EqualUnmodifiableListView)
      return _unacknowledgedNotifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_unacknowledgedNotifications);
  }

  final List<QueueReceiveMessage> _messagesWithUnprocessedActions;
  @override
  @JsonKey()
  List<QueueReceiveMessage> get messagesWithUnprocessedActions {
    if (_messagesWithUnprocessedActions is EqualUnmodifiableListView)
      return _messagesWithUnprocessedActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_messagesWithUnprocessedActions);
  }

  @override
  String toString() {
    return 'NotificationsState(unacknowledgedNotifications: $unacknowledgedNotifications, messagesWithUnprocessedActions: $messagesWithUnprocessedActions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationsStateImpl &&
            const DeepCollectionEquality().equals(
                other._unacknowledgedNotifications,
                _unacknowledgedNotifications) &&
            const DeepCollectionEquality().equals(
                other._messagesWithUnprocessedActions,
                _messagesWithUnprocessedActions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_unacknowledgedNotifications),
      const DeepCollectionEquality().hash(_messagesWithUnprocessedActions));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationsStateImplCopyWith<_$NotificationsStateImpl> get copyWith =>
      __$$NotificationsStateImplCopyWithImpl<_$NotificationsStateImpl>(
          this, _$identity);
}

abstract class _NotificationsState implements NotificationsState {
  const factory _NotificationsState(
          {final List<InAppMessageNotification> unacknowledgedNotifications,
          final List<QueueReceiveMessage> messagesWithUnprocessedActions}) =
      _$NotificationsStateImpl;

  @override
  List<InAppMessageNotification> get unacknowledgedNotifications;
  @override
  List<QueueReceiveMessage> get messagesWithUnprocessedActions;
  @override
  @JsonKey(ignore: true)
  _$$NotificationsStateImplCopyWith<_$NotificationsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
