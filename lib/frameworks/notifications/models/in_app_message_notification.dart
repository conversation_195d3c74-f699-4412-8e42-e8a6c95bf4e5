import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/services/shim_service/models/shim_service_payload.dart';
import 'package:x1440/utils/constants.dart';
import 'package:x1440/utils/json_utils.dart';

part 'in_app_message_notification.freezed.dart';

enum MessageType {
  message,
  work;

  static MessageType? fromString(String? type) {
    type = type?.toLowerCase();
    switch (type) {
      case 'work':
      case 'work_action':
      case 'workassigned':
      case 'presence/workassigned':
        return MessageType.work;
      case 'message':
        return MessageType.message;
      default:
        return null;
    }
  }
}

@freezed
class InAppMessageNotification with _$InAppMessageNotification {
  const factory InAppMessageNotification(
      {required String messageId,
      String? pushNotificationId,
        @ParseSfIdConverter() SfId? conversationId,
      String? conversationScrtUuid,
        @ParseSfIdConverter() SfId? messagingSessionId,
      required String title,
      required String body,
        @Deprecated('do we still need this?')
      String? username,
      String? userPhotoUrl,
        @ParseSfIdConverter() SfId? workId,
        @ParseSfIdConverter() SfId? workTargetId,
      String? notificationAction,
      required ChannelType channelType,
      @Default(MessageType.message) MessageType messageType,
      String? lastMessageTime}) = _InAppMessageNotification;

  @Deprecated('to support legacy code')
  static InAppMessageNotification? fromQueueAndShim(
      QueueReceiveMessage? queueReceivedMessage,
      ShimServicePayload? shimServicePayload) {
    Map<String, dynamic> entryPayload = safeJsonDecode(
        shimServicePayload?.message["conversationEntry"]?["entryPayload"] ??
            {});
    return InAppMessageNotification(
      pushNotificationId: queueReceivedMessage?.notificationId,
      notificationAction: queueReceivedMessage?.notificationAction,
      messageId:
          shimServicePayload?.message["conversationEntry"]?["identifier"] ?? '',
      /// if we have a 'ConversationId' here, it is an enhanced SCRT UUID, not a in-app "conversationId"=MessagingEndUserId
      // conversationId: shimServicePayload?.message["conversationId"] ?? shimServicePayload?.message["conversationId"],
      messagingSessionId: queueReceivedMessage?.relatedRecords.firstOrNull?.toSfId(),
      workId: (shimServicePayload?.message["workId"] as String?)?.toSfId(),
      workTargetId: (shimServicePayload?.message["workTargetId"] as String?)?.toSfId(),
      channelType: Constants.getChannelType(
          ChannelProvider.salesforce,
          shimServicePayload?.message["channelType"] ??
              shimServicePayload?.message["channelName"] ??
              "text"),
      username: shimServicePayload?.message["conversationEntry"]
          ?["senderDisplayName"] ?? queueReceivedMessage?.notificationTitle,
      title: queueReceivedMessage?.notificationTitle ?? "",
      messageType: MessageType.fromString(
              shimServicePayload?.message["conversationEntry"]?["entryType"] ??
                  queueReceivedMessage?.messageCategory) ??
          MessageType.message,
      userPhotoUrl: queueReceivedMessage?.notificationAvatarUrl,
      body: queueReceivedMessage?.notificationBody ??
          entryPayload["abstractMessage"]?["staticContent"]?["text"] ?? queueReceivedMessage?.notificationBody ??
          "",
    );
  }

  @override
  String toString() {
    return 'InAppMessageNotification{messageId: $messageId, pushNotificationId: $pushNotificationId, messageType: $messageType, conversationId: $conversationId, conversationScrtUuid: $conversationScrtUuid, title: $title, body: $body, username: $username, userPhotoUrl: $userPhotoUrl, workId: $workId, workTargetId: $workTargetId, notificationAction: $notificationAction, channelType: $channelType,  lastMessageTime: $lastMessageTime}';
  }
}
