// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'in_app_message_notification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InAppMessageNotification {
  String get messageId => throw _privateConstructorUsedError;
  String? get pushNotificationId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get conversationId => throw _privateConstructorUsedError;
  String? get conversationScrtUuid => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get messagingSessionId => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get body => throw _privateConstructorUsedError;
  @Deprecated('do we still need this?')
  String? get username => throw _privateConstructorUsedError;
  String? get userPhotoUrl => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get workId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get workTargetId => throw _privateConstructorUsedError;
  String? get notificationAction => throw _privateConstructorUsedError;
  ChannelType get channelType => throw _privateConstructorUsedError;
  MessageType get messageType => throw _privateConstructorUsedError;
  String? get lastMessageTime => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InAppMessageNotificationCopyWith<InAppMessageNotification> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InAppMessageNotificationCopyWith<$Res> {
  factory $InAppMessageNotificationCopyWith(InAppMessageNotification value,
          $Res Function(InAppMessageNotification) then) =
      _$InAppMessageNotificationCopyWithImpl<$Res, InAppMessageNotification>;
  @useResult
  $Res call(
      {String messageId,
      String? pushNotificationId,
      @ParseSfIdConverter() SfId? conversationId,
      String? conversationScrtUuid,
      @ParseSfIdConverter() SfId? messagingSessionId,
      String title,
      String body,
      @Deprecated('do we still need this?') String? username,
      String? userPhotoUrl,
      @ParseSfIdConverter() SfId? workId,
      @ParseSfIdConverter() SfId? workTargetId,
      String? notificationAction,
      ChannelType channelType,
      MessageType messageType,
      String? lastMessageTime});

  $SfIdCopyWith<$Res>? get conversationId;
  $SfIdCopyWith<$Res>? get messagingSessionId;
  $SfIdCopyWith<$Res>? get workId;
  $SfIdCopyWith<$Res>? get workTargetId;
}

/// @nodoc
class _$InAppMessageNotificationCopyWithImpl<$Res,
        $Val extends InAppMessageNotification>
    implements $InAppMessageNotificationCopyWith<$Res> {
  _$InAppMessageNotificationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? pushNotificationId = freezed,
    Object? conversationId = freezed,
    Object? conversationScrtUuid = freezed,
    Object? messagingSessionId = freezed,
    Object? title = null,
    Object? body = null,
    Object? username = freezed,
    Object? userPhotoUrl = freezed,
    Object? workId = freezed,
    Object? workTargetId = freezed,
    Object? notificationAction = freezed,
    Object? channelType = null,
    Object? messageType = null,
    Object? lastMessageTime = freezed,
  }) {
    return _then(_value.copyWith(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      pushNotificationId: freezed == pushNotificationId
          ? _value.pushNotificationId
          : pushNotificationId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      conversationScrtUuid: freezed == conversationScrtUuid
          ? _value.conversationScrtUuid
          : conversationScrtUuid // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingSessionId: freezed == messagingSessionId
          ? _value.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      userPhotoUrl: freezed == userPhotoUrl
          ? _value.userPhotoUrl
          : userPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      workId: freezed == workId
          ? _value.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      workTargetId: freezed == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      notificationAction: freezed == notificationAction
          ? _value.notificationAction
          : notificationAction // ignore: cast_nullable_to_non_nullable
              as String?,
      channelType: null == channelType
          ? _value.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as ChannelType,
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as MessageType,
      lastMessageTime: freezed == lastMessageTime
          ? _value.lastMessageTime
          : lastMessageTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get conversationId {
    if (_value.conversationId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.conversationId!, (value) {
      return _then(_value.copyWith(conversationId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingSessionId {
    if (_value.messagingSessionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.messagingSessionId!, (value) {
      return _then(_value.copyWith(messagingSessionId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workId {
    if (_value.workId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.workId!, (value) {
      return _then(_value.copyWith(workId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_value.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.workTargetId!, (value) {
      return _then(_value.copyWith(workTargetId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$InAppMessageNotificationImplCopyWith<$Res>
    implements $InAppMessageNotificationCopyWith<$Res> {
  factory _$$InAppMessageNotificationImplCopyWith(
          _$InAppMessageNotificationImpl value,
          $Res Function(_$InAppMessageNotificationImpl) then) =
      __$$InAppMessageNotificationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String messageId,
      String? pushNotificationId,
      @ParseSfIdConverter() SfId? conversationId,
      String? conversationScrtUuid,
      @ParseSfIdConverter() SfId? messagingSessionId,
      String title,
      String body,
      @Deprecated('do we still need this?') String? username,
      String? userPhotoUrl,
      @ParseSfIdConverter() SfId? workId,
      @ParseSfIdConverter() SfId? workTargetId,
      String? notificationAction,
      ChannelType channelType,
      MessageType messageType,
      String? lastMessageTime});

  @override
  $SfIdCopyWith<$Res>? get conversationId;
  @override
  $SfIdCopyWith<$Res>? get messagingSessionId;
  @override
  $SfIdCopyWith<$Res>? get workId;
  @override
  $SfIdCopyWith<$Res>? get workTargetId;
}

/// @nodoc
class __$$InAppMessageNotificationImplCopyWithImpl<$Res>
    extends _$InAppMessageNotificationCopyWithImpl<$Res,
        _$InAppMessageNotificationImpl>
    implements _$$InAppMessageNotificationImplCopyWith<$Res> {
  __$$InAppMessageNotificationImplCopyWithImpl(
      _$InAppMessageNotificationImpl _value,
      $Res Function(_$InAppMessageNotificationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? pushNotificationId = freezed,
    Object? conversationId = freezed,
    Object? conversationScrtUuid = freezed,
    Object? messagingSessionId = freezed,
    Object? title = null,
    Object? body = null,
    Object? username = freezed,
    Object? userPhotoUrl = freezed,
    Object? workId = freezed,
    Object? workTargetId = freezed,
    Object? notificationAction = freezed,
    Object? channelType = null,
    Object? messageType = null,
    Object? lastMessageTime = freezed,
  }) {
    return _then(_$InAppMessageNotificationImpl(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      pushNotificationId: freezed == pushNotificationId
          ? _value.pushNotificationId
          : pushNotificationId // ignore: cast_nullable_to_non_nullable
              as String?,
      conversationId: freezed == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      conversationScrtUuid: freezed == conversationScrtUuid
          ? _value.conversationScrtUuid
          : conversationScrtUuid // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingSessionId: freezed == messagingSessionId
          ? _value.messagingSessionId
          : messagingSessionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      userPhotoUrl: freezed == userPhotoUrl
          ? _value.userPhotoUrl
          : userPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      workId: freezed == workId
          ? _value.workId
          : workId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      workTargetId: freezed == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      notificationAction: freezed == notificationAction
          ? _value.notificationAction
          : notificationAction // ignore: cast_nullable_to_non_nullable
              as String?,
      channelType: null == channelType
          ? _value.channelType
          : channelType // ignore: cast_nullable_to_non_nullable
              as ChannelType,
      messageType: null == messageType
          ? _value.messageType
          : messageType // ignore: cast_nullable_to_non_nullable
              as MessageType,
      lastMessageTime: freezed == lastMessageTime
          ? _value.lastMessageTime
          : lastMessageTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$InAppMessageNotificationImpl implements _InAppMessageNotification {
  const _$InAppMessageNotificationImpl(
      {required this.messageId,
      this.pushNotificationId,
      @ParseSfIdConverter() this.conversationId,
      this.conversationScrtUuid,
      @ParseSfIdConverter() this.messagingSessionId,
      required this.title,
      required this.body,
      @Deprecated('do we still need this?') this.username,
      this.userPhotoUrl,
      @ParseSfIdConverter() this.workId,
      @ParseSfIdConverter() this.workTargetId,
      this.notificationAction,
      required this.channelType,
      this.messageType = MessageType.message,
      this.lastMessageTime});

  @override
  final String messageId;
  @override
  final String? pushNotificationId;
  @override
  @ParseSfIdConverter()
  final SfId? conversationId;
  @override
  final String? conversationScrtUuid;
  @override
  @ParseSfIdConverter()
  final SfId? messagingSessionId;
  @override
  final String title;
  @override
  final String body;
  @override
  @Deprecated('do we still need this?')
  final String? username;
  @override
  final String? userPhotoUrl;
  @override
  @ParseSfIdConverter()
  final SfId? workId;
  @override
  @ParseSfIdConverter()
  final SfId? workTargetId;
  @override
  final String? notificationAction;
  @override
  final ChannelType channelType;
  @override
  @JsonKey()
  final MessageType messageType;
  @override
  final String? lastMessageTime;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InAppMessageNotificationImpl &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.pushNotificationId, pushNotificationId) ||
                other.pushNotificationId == pushNotificationId) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.conversationScrtUuid, conversationScrtUuid) ||
                other.conversationScrtUuid == conversationScrtUuid) &&
            (identical(other.messagingSessionId, messagingSessionId) ||
                other.messagingSessionId == messagingSessionId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userPhotoUrl, userPhotoUrl) ||
                other.userPhotoUrl == userPhotoUrl) &&
            (identical(other.workId, workId) || other.workId == workId) &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.notificationAction, notificationAction) ||
                other.notificationAction == notificationAction) &&
            (identical(other.channelType, channelType) ||
                other.channelType == channelType) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType) &&
            (identical(other.lastMessageTime, lastMessageTime) ||
                other.lastMessageTime == lastMessageTime));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      messageId,
      pushNotificationId,
      conversationId,
      conversationScrtUuid,
      messagingSessionId,
      title,
      body,
      username,
      userPhotoUrl,
      workId,
      workTargetId,
      notificationAction,
      channelType,
      messageType,
      lastMessageTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InAppMessageNotificationImplCopyWith<_$InAppMessageNotificationImpl>
      get copyWith => __$$InAppMessageNotificationImplCopyWithImpl<
          _$InAppMessageNotificationImpl>(this, _$identity);
}

abstract class _InAppMessageNotification implements InAppMessageNotification {
  const factory _InAppMessageNotification(
      {required final String messageId,
      final String? pushNotificationId,
      @ParseSfIdConverter() final SfId? conversationId,
      final String? conversationScrtUuid,
      @ParseSfIdConverter() final SfId? messagingSessionId,
      required final String title,
      required final String body,
      @Deprecated('do we still need this?') final String? username,
      final String? userPhotoUrl,
      @ParseSfIdConverter() final SfId? workId,
      @ParseSfIdConverter() final SfId? workTargetId,
      final String? notificationAction,
      required final ChannelType channelType,
      final MessageType messageType,
      final String? lastMessageTime}) = _$InAppMessageNotificationImpl;

  @override
  String get messageId;
  @override
  String? get pushNotificationId;
  @override
  @ParseSfIdConverter()
  SfId? get conversationId;
  @override
  String? get conversationScrtUuid;
  @override
  @ParseSfIdConverter()
  SfId? get messagingSessionId;
  @override
  String get title;
  @override
  String get body;
  @override
  @Deprecated('do we still need this?')
  String? get username;
  @override
  String? get userPhotoUrl;
  @override
  @ParseSfIdConverter()
  SfId? get workId;
  @override
  @ParseSfIdConverter()
  SfId? get workTargetId;
  @override
  String? get notificationAction;
  @override
  ChannelType get channelType;
  @override
  MessageType get messageType;
  @override
  String? get lastMessageTime;
  @override
  @JsonKey(ignore: true)
  _$$InAppMessageNotificationImplCopyWith<_$InAppMessageNotificationImpl>
      get copyWith => throw _privateConstructorUsedError;
}
