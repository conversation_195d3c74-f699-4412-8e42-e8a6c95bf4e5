// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'presence_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PresenceState {
  bool get statusIsUpdating => throw _privateConstructorUsedError;
  PresenceStatus get currentStatus => throw _privateConstructorUsedError;
  List<PresenceStatus> get availableStatuses =>
      throw _privateConstructorUsedError;
  UiEvent<Nothing>? get showPresenceModal => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PresenceStateCopyWith<PresenceState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PresenceStateCopyWith<$Res> {
  factory $PresenceStateCopyWith(
          PresenceState value, $Res Function(PresenceState) then) =
      _$PresenceStateCopyWithImpl<$Res, PresenceState>;
  @useResult
  $Res call(
      {bool statusIsUpdating,
      PresenceStatus currentStatus,
      List<PresenceStatus> availableStatuses,
      UiEvent<Nothing>? showPresenceModal});

  $PresenceStatusCopyWith<$Res> get currentStatus;
}

/// @nodoc
class _$PresenceStateCopyWithImpl<$Res, $Val extends PresenceState>
    implements $PresenceStateCopyWith<$Res> {
  _$PresenceStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusIsUpdating = null,
    Object? currentStatus = null,
    Object? availableStatuses = null,
    Object? showPresenceModal = freezed,
  }) {
    return _then(_value.copyWith(
      statusIsUpdating: null == statusIsUpdating
          ? _value.statusIsUpdating
          : statusIsUpdating // ignore: cast_nullable_to_non_nullable
              as bool,
      currentStatus: null == currentStatus
          ? _value.currentStatus
          : currentStatus // ignore: cast_nullable_to_non_nullable
              as PresenceStatus,
      availableStatuses: null == availableStatuses
          ? _value.availableStatuses
          : availableStatuses // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>,
      showPresenceModal: freezed == showPresenceModal
          ? _value.showPresenceModal
          : showPresenceModal // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PresenceStatusCopyWith<$Res> get currentStatus {
    return $PresenceStatusCopyWith<$Res>(_value.currentStatus, (value) {
      return _then(_value.copyWith(currentStatus: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PresenceStateImplCopyWith<$Res>
    implements $PresenceStateCopyWith<$Res> {
  factory _$$PresenceStateImplCopyWith(
          _$PresenceStateImpl value, $Res Function(_$PresenceStateImpl) then) =
      __$$PresenceStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool statusIsUpdating,
      PresenceStatus currentStatus,
      List<PresenceStatus> availableStatuses,
      UiEvent<Nothing>? showPresenceModal});

  @override
  $PresenceStatusCopyWith<$Res> get currentStatus;
}

/// @nodoc
class __$$PresenceStateImplCopyWithImpl<$Res>
    extends _$PresenceStateCopyWithImpl<$Res, _$PresenceStateImpl>
    implements _$$PresenceStateImplCopyWith<$Res> {
  __$$PresenceStateImplCopyWithImpl(
      _$PresenceStateImpl _value, $Res Function(_$PresenceStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? statusIsUpdating = null,
    Object? currentStatus = null,
    Object? availableStatuses = null,
    Object? showPresenceModal = freezed,
  }) {
    return _then(_$PresenceStateImpl(
      statusIsUpdating: null == statusIsUpdating
          ? _value.statusIsUpdating
          : statusIsUpdating // ignore: cast_nullable_to_non_nullable
              as bool,
      currentStatus: null == currentStatus
          ? _value.currentStatus
          : currentStatus // ignore: cast_nullable_to_non_nullable
              as PresenceStatus,
      availableStatuses: null == availableStatuses
          ? _value._availableStatuses
          : availableStatuses // ignore: cast_nullable_to_non_nullable
              as List<PresenceStatus>,
      showPresenceModal: freezed == showPresenceModal
          ? _value.showPresenceModal
          : showPresenceModal // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }
}

/// @nodoc

class _$PresenceStateImpl extends _PresenceState {
  const _$PresenceStateImpl(
      {this.statusIsUpdating = false,
      this.currentStatus = const PresenceStatus(
          id: '', label: 'Offline', statusOption: PresenceStatusOption.offline),
      final List<PresenceStatus> availableStatuses = const [],
      this.showPresenceModal})
      : _availableStatuses = availableStatuses,
        super._();

  @override
  @JsonKey()
  final bool statusIsUpdating;
  @override
  @JsonKey()
  final PresenceStatus currentStatus;
  final List<PresenceStatus> _availableStatuses;
  @override
  @JsonKey()
  List<PresenceStatus> get availableStatuses {
    if (_availableStatuses is EqualUnmodifiableListView)
      return _availableStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableStatuses);
  }

  @override
  final UiEvent<Nothing>? showPresenceModal;

  @override
  String toString() {
    return 'PresenceState(statusIsUpdating: $statusIsUpdating, currentStatus: $currentStatus, availableStatuses: $availableStatuses, showPresenceModal: $showPresenceModal)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PresenceStateImpl &&
            (identical(other.statusIsUpdating, statusIsUpdating) ||
                other.statusIsUpdating == statusIsUpdating) &&
            (identical(other.currentStatus, currentStatus) ||
                other.currentStatus == currentStatus) &&
            const DeepCollectionEquality()
                .equals(other._availableStatuses, _availableStatuses) &&
            (identical(other.showPresenceModal, showPresenceModal) ||
                other.showPresenceModal == showPresenceModal));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      statusIsUpdating,
      currentStatus,
      const DeepCollectionEquality().hash(_availableStatuses),
      showPresenceModal);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PresenceStateImplCopyWith<_$PresenceStateImpl> get copyWith =>
      __$$PresenceStateImplCopyWithImpl<_$PresenceStateImpl>(this, _$identity);
}

abstract class _PresenceState extends PresenceState {
  const factory _PresenceState(
      {final bool statusIsUpdating,
      final PresenceStatus currentStatus,
      final List<PresenceStatus> availableStatuses,
      final UiEvent<Nothing>? showPresenceModal}) = _$PresenceStateImpl;
  const _PresenceState._() : super._();

  @override
  bool get statusIsUpdating;
  @override
  PresenceStatus get currentStatus;
  @override
  List<PresenceStatus> get availableStatuses;
  @override
  UiEvent<Nothing>? get showPresenceModal;
  @override
  @JsonKey(ignore: true)
  _$$PresenceStateImplCopyWith<_$PresenceStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
