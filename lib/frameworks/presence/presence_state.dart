import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/presence_status.dart';
import 'package:x1440/ui/blocs/ui_event.dart';

part 'presence_state.freezed.dart';

@freezed
class PresenceState with _$PresenceState {
  const PresenceState._();

  PresenceStatus? getPresenceStatus(String id) {
    int index = availableStatuses.indexWhere((s) => s.id == id);
    if (index != -1) {
      return availableStatuses[index];
    }
    return null;
  }

  const factory PresenceState({
    @Default(false) bool statusIsUpdating,
    @Default(PresenceStatus(
        id: '',
        label: 'Offline',
        // This is part of a check that runs before the app initializes, so it throws b/c S. is not initializaed yet // TODO: refactor this ... the label should be in the UI
        // label: S.current.presence_status_offline_label,
        statusOption: PresenceStatusOption.offline)) PresenceStatus currentStatus,
    @Default([]) List<PresenceStatus> availableStatuses,
    UiEvent<Nothing>? showPresenceModal,
  }) = _PresenceState;
}