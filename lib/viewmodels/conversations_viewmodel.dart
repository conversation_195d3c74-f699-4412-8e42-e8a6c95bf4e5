import 'dart:async';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/frameworks/typing_indicator/typing_indicator_event.dart';
import 'package:x1440/frameworks/typing_indicator/typing_indicator_manager.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/app_error_model.dart';
import 'package:x1440/models/conversation_model.dart';
import 'package:x1440/models/legacy/agent_chatrequest.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/frameworks/conversations_service.dart';
import 'package:x1440/models/polling_response.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/services/de_conversations_service.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/utils/extensions/iterable_extension.dart';
import 'base_viewmodels/disposable_viewmodel.dart';

class ConversationsViewmodel extends DisposableViewModel {
  final ConversationsUseCase _conversationsUseCase;

  DeConversationsService get _deConversationService =>
      GetIt.I<DeConversationsService>();

  RemoteLogger get _logger =>
      GetIt.I<LoggingUseCase>().getRemoteLogger('ConversationsViewmodel');

  Future<bool> _reportError(AppError newError) async {
    GetIt.I<AppErrorBloc>().add(ReportAppErrorEvent(newError));
    return false;
  }

  DeConversationsService? get conversationsRoutingService =>
      _deConversationService;

  ConversationsViewmodel(this._conversationsUseCase);

  void clear() {
    _logger.info('clearing ConversationsViewmodel');
    _conversationDetailsFetched = {};
    _sessionsToActivate = [];
    _isAutoAcceptEnabled = false;
    notifyListeners();
  }

  Map<String, LakConversation> get _conversations =>
      _conversationsUseCase.lakConversations; // {};

  Set<String> _conversationDetailsFetched = {};
  List<String> _sessionsToActivate = [];

  bool _isAutoAcceptEnabled = false;

  bool get isAutoAcceptEnabled => _isAutoAcceptEnabled;

  bool isSessionToActivate(String? id) {
    return id == null
        ? false
        : _sessionsToActivate.any((session) =>
            session == id || session.startsWith(id.substring(0, 15)));
  }

  String? shortenId(String? id) {
    if (id == null) return null;
    return id.length > 15 ? id.substring(0, 15) : id;
  }

  Future<void> fetchConvoFunc(String workTargetId) async {
    await conversationsRoutingService
        ?.fetchConversationForMessagingSession(workTargetId.substring(0, 15))
        .then((conversation) async {
      if (conversation != null) {
        NewConversationResponse newConversationPollingResponse =
            NewConversationResponse(
          conversation: conversation,
          type: PollingResponseType.newConversation,
          isFetchedFromLogin: true,
        );
        await handlePollingCallback(newConversationPollingResponse);
        notifyListeners();
      }
    });
  }

  // ************************************************************************
  //
  // Conversation Service Callback Response Methods
  //
  // ************************************************************************

  /// This callback is called when new work is received
  @Deprecated(
      'ALERT! Not to be used -- only to support legacy workAssigned on rejoining existing session; to be removed ASAP on shim service handling update')
  void workAssignedCallback(WorkAssignedResponse workAssignedResponse) async {
    if (_conversationsUseCase
        .isWorkAssignedEventHandled(workAssignedResponse.workId)) {
      loggy.info(
          'WorkAssigned event already handled, ignoring: ${workAssignedResponse.workId}');
      return;
    }
    _conversationsUseCase
        .addWorkAssignedEventHandled(workAssignedResponse.workId);

    MessagingUseCase messagingUseCase = GetIt.I<MessagingUseCase>();

    bool isOutboundResponse =
        messagingUseCase.removeOutboundMessagingSessionNotRespondedTo(
            workAssignedResponse.workTargetId);
    bool shouldAutoAccept = isOutboundResponse ||
        workAssignedResponse.autoAccept ||
        _isAutoAcceptEnabled;

    _logger.info(
        'workAssignedCallback: workId: ${workAssignedResponse.workId}; workTargetId: ${workAssignedResponse.workTargetId}; autoAccept: ${workAssignedResponse.autoAccept}; autoDecline: ${workAssignedResponse.autoDecline}; shouldAutoAccept: $shouldAutoAccept; isOutboundResponse: $isOutboundResponse');

    if (workAssignedResponse.autoDecline) {
      await _conversationsUseCase.declineWork(
          workAssignedResponse.workId, workAssignedResponse.workTargetId);
      return;
    }

    LakConversation? fetchedConversation = await fetchConversationInfoIfNeeded(
        workAssignedResponse.workTargetId.toString(),
        force: true);

    if (fetchedConversation == null) {
      loggy.error('Error: fetchedConversation is null [704]');
      return;
    }

    if (fetchedConversation.messages.isEmpty) {
      _logger.warn(
          'No messages found for conversation: ${fetchedConversation.id}');
      fetchedConversation.messages = [
        LakMessage(
          // conversationId: fetchedConversation.id,
          messageBody: 'Failed to load message for ${fetchedConversation.id}',
          actorType: MessageActorType.EndUser,
          // workTargetId: workAssignedResponse.workTargetId,
          id: '',
          entryType: MessageEntryType.text,
        )
      ];
    }

    final Map<String, LakConversation> convos = Map.from(_conversations);
    convos[fetchedConversation.id] = fetchedConversation;
    _conversationsUseCase.setLakConversations(convos.values.toList());

    LakMessage? messageForNotification =
        fetchedConversation.firstEndUserMessage ??
            LakMessage(
                id: const Uuid().v4(),
                actorType: MessageActorType.EndUser,
                entryType: MessageEntryType.text,
                messageBody: S.current.chat_load_error_message_label);
    if (!shouldAutoAccept) {
      await _conversationsUseCase.sendNewMessageNotification(
        messageForNotification,
        fetchedConversation.sfId,
        isWorkOffered: _isAutoAcceptEnabled ? false : true,
      );
    } else {
      _logger.info(
          'Auto-accepting work: ${workAssignedResponse.workId}; workTargetId: ${workAssignedResponse.workTargetId}; autoAccept: ${workAssignedResponse.autoAccept}; autoDecline: ${workAssignedResponse.autoDecline}; id: ${fetchedConversation.id}');

      final success = await _conversationsUseCase.acceptConversation(
        fetchedConversation.sfId,
      );
      if (success) {
        if (workAssignedResponse.autoAccept) {
          _logger.info(
              'goToChatScreen from workAssignedCallback to: ${fetchedConversation.id}');
          GetIt.I<RoutingManager>()
              .goToChatScreen(fetchedConversation.sfId, null);
        } else {
          await _conversationsUseCase.sendNewMessageNotification(
            messageForNotification,
            fetchedConversation.sfId,
            isWorkOffered: false,
          );
        }
      }
    }
    _conversationsUseCase
        .removeWorkAssignedEventHandled(workAssignedResponse.workId);
    GetIt.I<ConversationsBloc>().add(RefreshConversationsEvent());
  }

  void chatEstablishedCallback(ChatEstablishedResponse parsedResponse) async {
    /// update conversation status
    /// get conversation from workTargetId
    LakConversation? matchedConversation =
        (await _conversationsUseCase.getConversationByMessagingSessionId(
                (parsedResponse.workTargetId ?? '').toSfId()))
            ?.lakConversation;

    if (matchedConversation == null) return;

    // ignore the first message from this payload to avoid duplicate messages
    if (parsedResponse.messages.length > 1) {
      for (LakMessage msg in parsedResponse.messages.skip(1)) {
        msg.readStatus = false;
        _conversations[matchedConversation.id]?.addMessage(
            msg.copyWith(workTargetId: parsedResponse.workTargetId));
      }
    }

    if (isSessionToActivate(parsedResponse.workTargetId!)) {
      if (parsedResponse.messages.isNotEmpty) {
        LakMessage earliestMessage =
            parsedResponse.messages.reduce((current, next) {
          return (current.timeStamp < next.timeStamp) ? current : next;
        });

        _conversationsUseCase.sendNewMessageNotification(
            earliestMessage, matchedConversation.sfId);
      }
      return;
    }

    if (!_isAutoAcceptEnabled) {
      _logger.info(
          'goToChatScreen from chatEstablishedCallback to: ${matchedConversation.id}');
      GetIt.I<RoutingManager>().goToChatScreen(matchedConversation.sfId, null);
    }
    _conversationsUseCase
        .removePendingAcceptedConversation(matchedConversation.id);
    notifyListeners();
    return;
  }

  /// *************************************************
  Future<void> handlePollingCallback(PollingResponse response) async {
    PollingResponse parsedResponse =
        (response is RawPollingResponse) ? response.parse() : response;

    if (response is RawPollingResponse) {
      if (response.json == {}) return;
    }

    switch (parsedResponse.runtimeType) {
      case LiveAgentKitShutdown:
        // attempt to reconnect to liveAgent through Shim Service
        GetIt.I<AuthUseCase>().attemptAutoLogin();

        notifyListeners();
      case WorkAssignedResponse:
        throw Exception('WorkAssignedResponse received by CVM');

      case AgentChatRequest: // I don't think we'll ever hit this? or should? AgentChatRequestResponse is what you're looking for; some way to add a sensible enum so we don't have unnecessary cases in this switch?
        break;

      case NewConversationResponse:
        _conversationsUseCase
            .newConversationCallback(parsedResponse as NewConversationResponse);
        break;

      case PresenceConfigurationResponse:
        _isAutoAcceptEnabled = (parsedResponse as PresenceConfigurationResponse)
            .isAutoAcceptEnabled;
        notifyListeners();
        break;

      case PresenceStatusOffline:
        notifyListeners();
        break;

      case PresenceStatusPollingResponse:
        break;

      case PresenceStatusLogoutResponse:
        // TODO: cleanup w/ logout/dispose improvements
        String reasonMessage =
            (parsedResponse as PresenceStatusLogoutResponse).reason ??
                S.current.app_error_salesforce_lost_connection_usfce_label;
        reasonMessage = (reasonMessage == 'DuplicateLogin'
            ? S.current.app_error_salesforce_existing_login_label
            : reasonMessage); // TODO: Refactor
        reasonMessage = (reasonMessage == 'LiveAgentKitTimeout'
            ? S.current.app_error_salesforce_lost_connection_lakt_label
            : reasonMessage); // TODO: Refactor
        if (!isDisposed && reasonMessage != 'Logout') {
          _reportError(AppError(
              message: reasonMessage,
              showErrorOverlay: true,
              shouldShowSystemNotification: true,
              shouldForceLogout: true));
        }

        break;

      case LoginResultResponse:
        break;

      case ConversationAcceptedResponse:
        final workId = (parsedResponse as ConversationAcceptedResponse).workId;
        final workSfId = workId.toSfId();
        LakConversation? conversation = (await _conversationsUseCase
                .getConversationByMessagingSessionId(workSfId))
            ?.lakConversation;

        notifyListeners();

        break;

      case ChatEstablishedResponse:
        chatEstablishedCallback(parsedResponse as ChatEstablishedResponse);
        break;

      // case AgentChatRequestResponse:
      //   // agentChatRequestCallback(
      //   //     parsedResponse as AgentChatRequestResponse, conversationsService);
      //   break;

      case MessageResponse:
        // GetIt.I<ConversationsService>().addReceivedMessage(
        //   (parsedResponse as MessageResponse).message,
        //   shimServicePayload.message['workTargetId'] ??
        //       shimServicePayload.message['conversationId'],
        // );

        throw Exception('MessageResponse received by CVM');
        // _conversationsUseCase.addNewMessage((parsedResponse as MessageResponse).message,
        //     'fakeId',
        //     shouldSendNotification: parsedResponse.showNotification);
        break;

      case WorkEndedResponse:
        GetIt.I<ConversationsService>().updateMessagingSessionStatus(
            ((parsedResponse as WorkEndedResponse).workTargetId ??
                    parsedResponse.workId ??
                    parsedResponse.conversationId)
                .toSfId(),
            ConversationStatus.closed);
        break;

      case AfterConversationWorkEndedResponse:
        // LakConversation? conversation = _conversationsUseCase.getConversationByMessagingSessionId(
        //     (parsedResponse as AfterConversationWorkEndedResponse).workTargetId)?.lakConversation;
        break;

      case TypingStartedPollingResponse: // conversationId is SCRT UUID
        if ((parsedResponse as TypingStartedPollingResponse).conversationId !=
            null) {
          GetIt.I<TypingIndicatorManager>()
              .add(EndUserTypingStartedEvent((parsedResponse).conversationId!));
        }
        // handleTypingStarted(parsedResponse as TypingStartedPollingResponse);
        break;

      case TypingEndedPollingResponse: // conversationId is SCRT UUID
        if ((parsedResponse as TypingEndedPollingResponse).conversationId !=
            null) {
          GetIt.I<TypingIndicatorManager>()
              .add(EndUserTypingEndedEvent((parsedResponse).conversationId!));
        }

        // handleTypingEnded(parsedResponse as TypingEndedPollingResponse);
        break;

      case WorkAutoDeclined:
        handleWorkAutoDeclined(parsedResponse as WorkAutoDeclined);
    }
    notifyListeners();
  }

  void mergeOrAddConversation(LakConversation newConversation) {
    // if conversation at a short id and we have a long id, remove short id, merge, & replace // TODO: not at all sure this is the best way to handle this!!!!
    LakConversation? existingConversationAtId =
        _conversations[newConversation.id] ??
            _conversations[newConversation.shortId] ??
            () {
              LakConversation? potentialExistingConvo = _conversations.values
                  .firstWhereOrNull(
                      (convo) => convo.shortId == newConversation.shortId);
              return potentialExistingConvo;
            }();

    LakConversation mergedConversation =
        newConversation.merge(existingConversationAtId);

    if (existingConversationAtId != null) {
      _conversations.remove(existingConversationAtId.id);
      _conversations.remove(existingConversationAtId.shortId);
    }

    _conversations[mergedConversation.id] = mergedConversation;
    notifyListeners();
  }

  void handleWorkAutoDeclined(WorkAutoDeclined parsedResponse) async {
    /// this previously was called when the conversationUseCase handleWorkAutoDeclined failed
    _logger.warn('cvm received WorkAutoDeclined: ${parsedResponse.workId}');
  }

  // = = = = = HELPER METHODS:
  LakConversation? getConversation(String id) =>
      _conversationsUseCase.getConversation(id.toSfId())?.lakConversation;

  Future<LakConversation?> fetchConversationInfoIfNeeded(String conversationId,
      {bool force = false}) {
    // Normalize the provided conversationId for comparison
    String? shortenedConversationId = shortenId(conversationId);

    // Check if the conversation details have already been fetched
    String? fetchedId = _conversationDetailsFetched
        .firstWhereOrNull((id) => shortenId(id) == shortenedConversationId);

    if (!force && fetchedId != null) {
      return Future.value(_conversations[fetchedId]);
    }

    fetchedId ??= conversationId;

    if (!_conversationDetailsFetched.contains(fetchedId) &&
        fetchedId.length == 18) {
      _conversationDetailsFetched.add(fetchedId);
    }

    return _deConversationService
        .fetchConversationForMessagingSession(fetchedId)
        .then(((value) {
      if (value != null) {
        mergeOrAddConversation(value);
      }
      return value;
    }));
  }
}
