// import 'dart:async';
//
// import 'package:flutter/material.dart';
// import 'package:get_it/get_it.dart';
// import 'package:go_router/go_router.dart';
// import 'package:live_activities/models/live_activity_state.dart';
//
// import 'package:shared_preferences_ios_sn/SPUtil.dart';
// import 'package:uuid/uuid.dart';
// import 'package:x1440/frameworks/routing/routing_manager.dart';
//
// import '../models/app_error_model.dart';
// import '../models/live_activity_model.dart';
// import '../services/live_activity_service.dart';
// import '../ui/modals/new_message_modal/new_message_modal.dart';
// import 'app_lifecycle_viewmodel.dart';
// import 'base_viewmodels/disposable_viewmodel.dart';
//
// class LiveActivityViewModel extends DisposableViewModel {
//   late final LiveActivityService _liveActivityService;
//   final void Function(AppError) _reportError;
//   final GoRouter _router;
//   final void Function() _sendConfirmLogoutNotification;
//
//   List<String> _activeLiveMessageIds = [];
//
//   final Map<String, LiveMessage> _liveMessages = {};
//
//   String get deepLinkBasePath => 'liveactivity';
//
//   LiveMessage? _baseLiveMessage;
//
//   String? _tappedLiveMessageAssocConvoId;
//
//   /// gets last tapped live message; sets to null
//   String? get tappedLiveMessageAssocConvoId {
//     loggy.info('getting tappedLiveMessage: $_tappedLiveMessageAssocConvoId');
//
//     String? tappedLiveMessageAssocConvoId = _tappedLiveMessageAssocConvoId;
//     _tappedLiveMessageAssocConvoId = null;
//     return tappedLiveMessageAssocConvoId;
//   }
//
//   final AppLifeCycleViewModel _appLifeCycleViewModel;
//
//   LiveActivityViewModel({
//     required void Function(AppError) reportError,
//     required GoRouter router,
//     LiveActivityService? liveActivityService,
//     required AppLifeCycleViewModel appLifeCycleViewModel,
//     required void Function() sendConfirmLogoutNotification,
//     required void Function({
//       required String path,
//       required void Function(Uri uri)
//           callback, // TODO: can we better determine this type?
//     }) registerDeepLinkPathCallback,
//   })  : _sendConfirmLogoutNotification = sendConfirmLogoutNotification,
//         _appLifeCycleViewModel = appLifeCycleViewModel,
//         _reportError = reportError,
//         _router = router {
//     _liveActivityService = liveActivityService ??
//         LiveActivityService(
//             appGroupId: 'group.io.1440',
//             urlScheme: 'x1440',
//             onLiveActivityEnded: onLiveActivityEnded,
//             deepLinkBasePath: deepLinkBasePath);
//     _listener = _appLifeCycleViewModel.addAppLifeCycleListener(
//         AppLifeCycleListener(
//             currentState: AppLifecycleState.resumed,
//             onUpdate: () => _setActivities(isForegrounded: true)));
//
//     // TODO: do we want to handle these paths in a more centralized way? Should they be an enum?
//     registerDeepLinkPathCallback(
//         path: _liveActivityService.deepLinkBasePath,
//         callback: _handleLiveActivityCallback);
//     initialize();
//   }
//
//   late final AppLifeCycleListener _listener;
//
//   @override
//   void dispose() async {
//     loggy.info('disposing; isDisposed = $isDisposed');
//     _liveActivityService.dispose();
//     if (isDisposed) return;
//
//     _appLifeCycleViewModel.removeAppLifeCycleListener(_listener);
//     _liveMessageListenerTimer?.cancel();
//     _liveMessageListenerTimer = null;
//     super.dispose();
//   }
//
//   Future<void> initialize() async {
//     loggy.info('initializing');
//
//     _liveActivityService.init(
//       appGroupId: 'group.io.1440',
//       // urlScheme: 'x1440',
//       // urlSchemeStream: _liveActivityStreamCallback,
//     );
//
//     _liveActivityService.endAllActivities();
//   }
//
//   void checkin() => checkLiveActivityStatus()
//       .then((value) => value == false ? onLiveActivityEnded() : null);
//
//   Future<bool> checkLiveActivityStatus() async {
//     loggy.info('checkLiveActivityStatus');
//
//     try {
//       return getAllActivityStates()
//           .then((Map<String, LiveActivityState?> states) {
//         loggy.info('getAllActivityStates: $states');
//         if (states.values.any(
//             (LiveActivityState? state) => state == LiveActivityState.active)) {
//           loggy.info('found running activity');
//
//           return true;
//         }
//
//         return false;
//       });
//     } catch (e) {
//       String message = 'checkLiveActivityStatus error: $e';
//       loggy.warning(message);
//
//       return false;
//     }
//   }
//
//   Future<Map<String, LiveActivityState?>> getAllActivityStates() =>
//       _liveActivityService.getAllActivityStates();
//
//   void onLiveActivityEnded() {
//     loggy.info('onLiveActivityEnded; isDisposed = $isDisposed');
//
//     if (isDisposed || _appLifeCycleViewModel.isForegrounded) return;
//     // TODO: this is using the "livemessage" handler for a non-live message situation
//     _startNotificationConfirmLogoutListener();
//     _sendConfirmLogoutNotification();
//   }
//
//   void _handleLiveActivityCallback(Uri uri) {
//     loggy.info('handleLiveActivityCallback: $uri');
//
//     List<String> pathSegments = uri.pathSegments;
//
//     if (pathSegments[1] == 'newConversation') {
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         _router.push('/newConversation/${null}');
//       });
//       return;
//     } else if (pathSegments[1] == 'newconversationcontacts') {
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         BuildContext? context =
//             GetIt.I<RoutingManager>().navigatorKey.currentContext;
//         if (context != null) {
//           showNewMessageModalBottomSheet(context: context).then((value) {
//             if (value is NewConversation) {
//               context.pushReplacement('/chat/${value.conversation.id}');
//             }
//           });
//         }
//       });
//       return;
//     }
//
//     if (pathSegments.length < 2) {
//       loggy.warning(
//           'live activity deep link callback hit with insufficient path to handle: $uri');
//       return;
//     }
//
//     // get associated live message
//     LiveMessage? assocLiveMessage = _liveMessages[pathSegments[1]];
//
//     if (assocLiveMessage == null) {
//       loggy.warning(
//           'handleLiveActivityCallback found no live message for id: ${pathSegments[1]}');
//     } else if (pathSegments.length < 3) {
//       loggy.warning(
//           'no callback identifier provided for live message: ${pathSegments[1]}');
//     } else if (pathSegments[2] == 'openConversation' &&
//         assocLiveMessage is NewMessageLiveMessage) {
//       _tappedLiveMessageAssocConvoId =
//           assocLiveMessage.openConversationCallback?.routeToIdOnAppOpen;
//     } else {
//       /// the real magic -- above code is handling one-off cases less elegantly;
//       /// if we don't have one of those, use the correct callback attached to the LiveMessage
//       loggy.info(
//           'handleLiveActivityCallback found assocLiveMessage: $assocLiveMessage');
//       LiveActivityCallback? assocCallback =
//           assocLiveMessage.callbacks[pathSegments[2]];
//       if (assocCallback?.callback == null) {
//         loggy.warning(
//             'live message had no registered callback for: ${pathSegments[2]}');
//
//         String? assocConvoId = (assocLiveMessage is NewMessageLiveMessage)
//             ? assocLiveMessage.conversationId
//             : null;
//
//         _tappedLiveMessageAssocConvoId = assocConvoId;
//       } else {
//         /// use the callback associated with the liveMessage
//         loggy.info(
//             'setting tapped live message id to : ${assocCallback?.routeToIdOnAppOpen}');
//         _tappedLiveMessageAssocConvoId = assocCallback?.routeToIdOnAppOpen;
//         assocCallback?.callback?.call();
//       }
//     }
//   }
//
//   Future<bool> _setActivitiesQueable(
//       {bool? isForegrounded, bool allowAlertConfig = true}) async {
//     loggy.info(
//         'setting ${_activeLiveMessageIds.length} live activities with isForegrounded: $isForegrounded');
//
//     if (_appLifeCycleViewModel.isForegrounded) {
//       await _liveActivityService
//           .endAllActivities(); // TODO: we could add this to a Future.wait w/ the work going on below (not including any other liveActivityService calls
//     }
//
//     /// remove any listeners that should be removed on app open
//     List<LiveMessageListenerHandler> handlersToRemove = [];
//     for (LiveMessageListenerHandler listenerHandler
//         in _liveMessageListenerHandlers) {
//       if (listenerHandler.removeOnAppOpen) {
//         handlersToRemove.add(listenerHandler);
//       }
//     }
//     _liveMessageListenerHandlers
//         .removeWhere((handler) => handlersToRemove.contains(handler));
//
//     /// if our live message was closed & we're waiting for confirmation on logout/re-open, cancel that timer on open
//     _liveMessageClosedDismissTimer?.cancel();
//     _liveMessageClosedDismissTimer = null;
//
//     /// remove any messages that are no longer active
//     List<String> filteredActiveLiveMessageIds =
//         List.from(_activeLiveMessageIds);
//     for (String id in _activeLiveMessageIds) {
//       if (_liveMessages[id]?.shouldPersist != true) {
//         filteredActiveLiveMessageIds.remove(id);
//       }
//     }
//     _activeLiveMessageIds = filteredActiveLiveMessageIds;
//     List<Map<String, dynamic>> messagesPayload = [];
//     Map<String, dynamic> imageData = {};
//
//     LiveMessage? latestMessage;
//
//     for (String id in _activeLiveMessageIds) {
//       if (_liveMessages[id]?.data.isNotEmpty == true) {
//         /// get latest message for alertConfig
//         latestMessage = latestMessage ?? _liveMessages[id];
//         messagesPayload.add({'id': id, ..._liveMessages[id]!.data});
//
//         imageData = {
//           ...imageData,
//           ..._liveMessages[id]!
//               .imageData
//               .map((key, value) => MapEntry("${id}_$key", value))
//         };
//       }
//     }
//
//     messagesPayload = [
//       ...messagesPayload,
//       if (_baseLiveMessage != null) {'id': 'base', ..._baseLiveMessage!.data},
//     ];
//
//     /// we have to send imageData at a top-level with current flutter live activity package (unknown if this is package-level or functionality-level limitation)
//     imageData = {
//       ...imageData,
//       if (_baseLiveMessage != null)
//         ..._baseLiveMessage!.imageData
//             .map((key, value) => MapEntry("base_$key", value))
//     };
//
//     try {
//       // TODO: each 'message' must have a non-null 'body' -- figure out an elegant way to handle that
//       String? id = await _liveActivityService.setActivity(
//           isForegrounded: isForegrounded,
//           data: {...imageData, 'messages': messagesPayload},
//           alertConfig: !allowAlertConfig ? null : latestMessage?.alertConfig);
//
//       loggy.info('setActivities returned id: $id');
//       return id != null;
//     } catch (e) {
//       loggy.warning('setActivities error: $e');
//
//       dispose();
//       return false;
//     }
//   }
//
//   // TODO: better handle 'isForegrounded' -- this func takes it as a parameter but uses the appLifeCycleViewModel.isForegrounded in some places ... ???
//   bool _isCurrentlySettingActivities = false;
//   List<Future<bool> Function()> queuedSetActivities = [];
//
//   // TODO: (maybe) track success/failure of these setting's and handle
//   void _setActivities({bool? isForegrounded, bool allowAlertConfig = true}) {
//     thisFunc() async => _setActivitiesQueable(
//         isForegrounded: isForegrounded, allowAlertConfig: allowAlertConfig);
//
//     queuedSetActivities.add(thisFunc);
//
//     if (_isCurrentlySettingActivities) {
//       loggy.info('currently setting activities; queued next');
//     } else {
//       () async {
//         _isCurrentlySettingActivities = true;
//         while (queuedSetActivities.isNotEmpty) {
//           loggy.info('setting activities from queue');
//           await queuedSetActivities.removeAt(0)();
//         }
//         _isCurrentlySettingActivities = false;
//       }();
//     }
//   }
//
//   /// base live message is what we show when there are no other live messages to show
//   /// (as of 11/2023 implementation, this is the Status Live View)
//   Future<String?> setBaseLiveMessage(LiveMessage liveMessage,
//       [String? id]) async {
//     loggy.info('setBaseLiveMessage liveMessage: $liveMessage');
//
//     id ??= const Uuid().v4();
//     _baseLiveMessage = liveMessage;
//     _setActivities(isForegrounded: false, allowAlertConfig: false);
//     return id;
//   }
//
//   Future<String?> addLiveMessage(final LiveMessage liveMessage,
//       {final String? id, final bool? isForegrounded}) async {
//     loggy.info('addLiveMessage liveMessage: $liveMessage');
//     loggy.info(liveMessage.data);
//     if (liveMessage is NewMessageLiveMessage)
//       loggy.info(
//           'adding NewMessageLiveMessage live message with channelImg: ${liveMessage.channelImg}');
//     String uuid = id ?? const Uuid().v4();
//     _liveMessages[uuid] = liveMessage;
//     _activeLiveMessageIds.insert(0, uuid);
//     // bool success =
//     _setActivities(isForegrounded: isForegrounded);
//     startLiveMessageListener(uuid, liveMessage);
//
//     // if (success) return uuid;
//     return null;
//   }
//
//   Map<String, LiveMessage?> removeLiveMessagesWhere(
//       bool Function(LiveMessage) predicate) {
//     loggy.info('removeLiveMessagesWhere start');
//     List<String> updatedLiveMessageIds = List.from(_activeLiveMessageIds);
//     Map<String, LiveMessage?> removedLiveMesages = {};
//     for (String id in _activeLiveMessageIds) {
//       if (_liveMessages[id] != null && predicate(_liveMessages[id]!)) {
//         updatedLiveMessageIds.remove(id);
//         _liveMessageListenerHandlers
//             .removeWhere((handler) => handler.liveMessageId == id);
//         removedLiveMesages[id] = _liveMessages.remove(id);
//       }
//     }
//     loggy.info(
//         'removeLiveMessagesWhere removed: ${_activeLiveMessageIds.length - updatedLiveMessageIds.length} messages');
//     _activeLiveMessageIds = updatedLiveMessageIds;
//
//     _setActivities(allowAlertConfig: true);
//     return removedLiveMesages;
//   }
//
//   /// ************************************************************************
//   ///
//   ///  LISTEN TO LIVE ACTIVITY INTERACTIONS
//   ///  NOTE: this is implemented to catch user's declining a conversation
//   ///  from the live activity using SharedPrefs (before the Shim Service
//   ///  is available) ... should probably be removed once we have the Shim
//   ///  Service implementation [CRM; 11/2023]
//   ///
//   /// SPUtil dependency can/should also be removed at that point
//   ///
//   /// ************************************************************************
//   /// startLiveMessageListener is used inside a NewConversationLiveMessage listener call (refactor this if we're keeping it; CRM found unintuitive)
//   void startLiveMessageListener(String id, LiveMessage liveMessage) {
//     loggy.info('startLiveMessageListener id: $id; liveMessage: $liveMessage');
//
//     if (liveMessage is! LiveMessageListener) return;
//     (liveMessage as NewConversationLiveMessage).listener(id);
//   }
//
//   void _handleLiveActivityCloseLogoutConfirm() {
//     loggy.info('_handleLiveActivityCloseLogoutConfirm');
//     _reportError(AppError(message: 'You logged out', shouldForceLogout: true));
//     dispose();
//   }
//
//   void _startNotificationConfirmLogoutListener() {
//     loggy.info('startNotificationConfirmLogoutListener');
//
//     _liveMessageClosedDismissTimer = Timer(const Duration(seconds: 30), () {
//       loggy.info('_liveMessageClosedDismissTimer hit');
//       _handleLiveActivityCloseLogoutConfirm();
//     });
//
//     handleLiveActivityListener([
//       LiveMessageListenerHandler(
//           liveMessageId: 'confirmLogoutNotification',
//           method: 'dismissed',
//           removeOnAppOpen: true,
//           callback: () async {
//             loggy.info('confirmLogoutNotification dismissed');
//             _handleLiveActivityCloseLogoutConfirm();
//           })
//     ]);
//   }
//
//   Timer? _liveMessageListenerTimer;
//   Timer? _liveMessageClosedDismissTimer;
//   final List<LiveMessageListenerHandler> _liveMessageListenerHandlers = [];
//
//   Set<String> get _liveMessagesListenedTo =>
//       _liveMessageListenerHandlers.map((e) => e.liveMessageId).toSet();
//
//   LiveMessageListenerHandler? getHandlerFor(
//       String liveMessageId, String method) {
//     for (LiveMessageListenerHandler listenerHandler
//         in _liveMessageListenerHandlers) {
//       if (listenerHandler.liveMessageId == liveMessageId &&
//           method == listenerHandler.method) {
//         _liveMessageListenerHandlers.remove(listenerHandler);
//         return listenerHandler;
//       }
//     }
//     return null;
//   }
//
//   void handleLiveActivityListener(
//       List<LiveMessageListenerHandler> listenerHandler) {
//     final bool shouldLog = false;
//     _liveMessageListenerHandlers.addAll(listenerHandler);
//
//     /// start timer if not already active
//     _liveMessageListenerTimer ??=
//         Timer.periodic(const Duration(milliseconds: 500), (timer) async {
//       if (shouldLog)
//         loggy.info(
//             'fetching any live message activity for ${_liveMessageListenerHandlers.length} handlers');
//       final _prefs = await SPUtil.withSuitName("group.io.1440");
//
//       /// NOTE: had to use SPUtil in order to use this SuiteName call, which was necessary for live activity package
//       await _prefs.reload();
//
//       for (String liveMessageId in _liveMessagesListenedTo) {
//         String? liveMessageListenerResult = _prefs.getString(liveMessageId);
//         if (liveMessageListenerResult != null) {
//           LiveMessageListenerHandler? handler =
//               getHandlerFor(liveMessageId, liveMessageListenerResult);
//           handler?.callback();
//           _prefs.remove(liveMessageId);
//         }
//       }
//
//       /// cancel timer if no more listeners
//       if (_liveMessageListenerHandlers.isEmpty) {
//         timer.cancel();
//         _liveMessageListenerTimer = null;
//       }
//     });
//   }
// }
