import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:x1440/debug/debug_viewmodel.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/app_error_model.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/services/interfaces/contact_viewmodel_interface.dart';
import 'package:x1440/services/interfaces/productivity_tools_viewmodel_interface.dart';
import 'package:x1440/ui/blocs/app_error/app_error_bloc.dart';
import 'package:x1440/ui/blocs/app_error/app_error_event.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_state.dart';
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_manager.dart';
import 'package:x1440/ui/blocs/demo/demo_mode/demo_mode_state.dart';
import 'package:x1440/ui/error/app_blocking_errors_view.dart';
import 'package:x1440/ui/themes/themeConstants.dart';
import 'package:x1440/ui/themes/themes.dart';
import 'package:x1440/use_cases/models/credentials.dart';

import 'conversations_viewmodel.dart';
import 'deeplink_viewmodel.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

/// rename to "getApp" or something?
Widget getAppWideViewModels({bool liveActivitiesAreEnabled = false}) =>
    BlocBuilder<DemoModeManager, DemoModeState>(
        bloc: GetIt.I<DemoModeManager>(),
        buildWhen: (previous, current) => previous.toggle != current.toggle,
        builder: (context, demoModeState) {
          {
            /// re-load viewmodels, providers on switching to or  from demo mode
            if (!demoModeState.toggle) {
              return Container();
            }

            GoRouter router = GetIt.I<RoutingManager>().router;
            void reportError(AppError error) {
              try {
                if (GetIt.I.isRegistered<AppErrorBloc>()) {
                  GetIt.I<AppErrorBloc>().add(ReportAppErrorEvent(error));
                }
              } catch (e) {
                if (kDebugMode) {
                  print('Error reporting error: $e');
                }
              }
            }

            return MultiProvider(
                providers: [
                  ChangeNotifierProvider(
                      create: (context) =>
                          DebugViewModel(reportError: reportError)),
                  ChangeNotifierProvider(
                    create: (context) => DeepLinkViewModel(router),
                    lazy: false,
                  ),
                  BlocProvider.value(value: GetIt.I<NotificationManager>()),
                  BlocProvider.value(value: GetIt.I<PresenceManager>()),
                  ChangeNotifierProvider<
                          ProductivityToolsViewmodelInterface>.value(
                      value: GetIt.I<ProductivityToolsViewmodelInterface>()),
                  ChangeNotifierProvider<ContactsViewmodelInterface>.value(
                    value: GetIt.I<ContactsViewmodelInterface>(),
                  ),
                ],
                builder: (context, _) {
                  Widget child = Directionality(
                    textDirection: TextDirection.ltr,
                    child: Stack(
                      children: [
                       MaterialApp.router(
                                  localizationsDelegates: const [
                                    S.delegate,
                                    GlobalMaterialLocalizations.delegate,
                                    GlobalWidgetsLocalizations.delegate,
                                    GlobalCupertinoLocalizations.delegate,
                                  ],
                                  supportedLocales: S.delegate.supportedLocales,
                                  debugShowCheckedModeBanner: false,
                                  theme: lightThemeData(context),
                                  darkTheme: darkThemeData(context),
                                  themeMode: ThemeMode.system,
                                  routerConfig: router,
                                ),
                        const Positioned(
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: AppOverlaysLayer())
                      ],
                    ),
                  );

                  return BlocProvider<AuthBloc>.value(
                      value: GetIt.I<AuthBloc>(),
                      child: BlocConsumer<AuthBloc, AuthState>(
                        listener: (context, authState) {
                          if (authState.logIn?.consume() != null) {
                            // TODO: fix this -- doing this here caused a race condition with the future builder below ... the /conversations screen needs the providers that are created after login (and need the credentials); For now this post-login routing is handled in the Router redirect
                            // GetIt.I<RoutingManager>().router.go('/conversations');
                          } else if (authState.logOut?.consume() != null) {
                            GetIt.I<RoutingManager>()
                                .router
                                .go('/', extra: authState.appError);
                          }
                        },
                        builder: (context, authState) {
                          if (authState.isLoggingIn || authState.isLoggingOut) {
                            return Directionality(
                                textDirection: TextDirection.ltr,
                                child: Scaffold(
                                  backgroundColor:
                                      ThemeConstants.colors.appBlack,
                                  body: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                ));
                          }
                          if (authState.isLoggedIn != true) {
                            return child; // If we're not logged in, we have everything we need
                          } else {
                            return FutureBuilder(
                                future: GetIt.I<LocalStorageRepository>()
                                    .getCredentials(),
                                builder: (context, creds) {
                                  Credentials? credentials =
                                      creds.data; // cvmModule.credentials;
                                  if (credentials == null ||
                                      credentials.sfLoggedIn == false) {
                                    return child;
                                  }
                                  return MultiProvider(
                                    providers: [
                                      ChangeNotifierProvider<
                                              ConversationsViewmodel>.value(
                                          value: GetIt.I<
                                              ConversationsViewmodel>()),
                                    ],
                                    child: child,
                                  );
                                  // ),
                                });
                          }
                        },
                      ));
                });
          }
        });
