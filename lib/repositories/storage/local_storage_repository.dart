import 'package:x1440/api/dtos/log_event.dart';
import 'package:x1440/api/dtos/messaging_channel_entry.dart';
import 'package:x1440/frameworks/settings/models/app_local_settings.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';
import 'package:x1440/repositories/models/active_presence_status.dart';
import 'package:x1440/ui/blocs/demo/demo_user.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/models/previously_logged_in_user.dart';
import 'package:x1440/use_cases/models/scrt_credentials.dart';

enum SalesforceEnvironmentType {
  production("production"),
  sandbox("sandbox"),
  custom("custom"),
  demo("demo");

  // Define a field to hold the string value
  final String name;

  // Constructor for the enum
  const SalesforceEnvironmentType(this.name);

  @override
  String toString() => name;

  // Static method to get AIModel from string
  static SalesforceEnvironmentType? fromString(String sfType) {
    // Find the enum that matches the given name
    for (var type in SalesforceEnvironmentType.values) {
      if (type.name == sfType) {
        return type;
      }
    }
    return null; // Return null if no matching model found
  }
}

abstract class LocalStorageRepository {
  /// **************************************
  /// Previously logged in user
  Future<void> clearPreviouslyLoggedInUser();
  Future<PreviouslyLoggedInUser?> getPreviouslyLoggedInUser();
  Future<void> setPreviouslyLoggedInUser(
      PreviouslyLoggedInUser previouslyLoggedInUser);

  /// **************************************
  /// Credentials
  Stream<Credentials> get credentialsStream;
  Future<void> clearCredentials();
  Future<Credentials> getCredentials();
  Future<void> setCredentials(Credentials credentials);

  /// **************************************
  /// SCRT Credentials
  Future<ScrtCredentials> getScrtCredentials();
  Future<void> setScrtCredentials(ScrtCredentials credentials);
  Future<void> clearScrtCredentials();

  /// **************************************
  /// AppLocalSettings
  Stream<AppLocalSettings> get appLocalSettingsStream;
  Future<void> clearAppLocalSettings();
  Future<AppLocalSettings> getAppLocalSettings();
  Future<void> setAppLocalSettings(AppLocalSettings credentials);

  /// **************************************
  /// Presence Status
  Future<void> clearActivePresenceStatus();
  Future<void> setActivePresenceStatus(ActivePresenceStatus activeStatus);
  Future<ActivePresenceStatus> getActivePresenceStatus();

  /// **************************************
  /// Message Receive Queue
  Future<List<QueueReceiveMessage>> getMessageReceiveQueueState();
  Stream<List<QueueReceiveMessage>> get messageReceiveQueueStream;
  Future<void> addMessageToReceiveQueue(
      QueueReceiveMessage queueReceiveMessage);
  Future<void> removeMessageFromReceiveQueueByNotificationId(
      String notificationId);
  clearReceiveQueue() {}

  /// **************************************
  /// Message Send Queue
  Future<List<QueueSendMessage>> getMessageSendQueueState();
  Stream<List<QueueSendMessage>> get messageSendQueueStream;

  Future<void> addMessageToSendQueue(QueueSendMessage queueSendMessage);
  Future<void> removeMessageFromSendQueueByMessageId(String messageId);
  clearSendQueue() {}

  ///**************************************
  /// Last selected/default Messaging Channel
  Future<void> setMessagingChannel(MessagingChannelEntry channel);
  Future<MessagingChannelEntry?> getMessagingChannel();
  Future<void> clearMessagingChannel();

  /// **************************************
  /// Logging Queue
  /// WARNING: if the logEvent.message is an empty string, the repository will not store it
  Future<void> addLogEvent(LogEvent log);
  Future<List<LogEvent>> getLogs();
  Future<void> clearLogs();

  /// **************************************
  /// Demo
  Future<void> setDemoUser(DemoUser user);
  Future<DemoUser?> getDemoUser();
}
