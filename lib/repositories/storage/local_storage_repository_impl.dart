/// This file contains the implementation of the [LocalStorageRepository] interface.
/// Previously the implementation was using Hive for storage, but now it uses Isar.
library;

import 'dart:async';

import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:x1440/api/dtos/log_event.dart';
import 'package:x1440/api/dtos/messaging_channel_entry.dart';
import 'package:x1440/api/dtos/presence_status.dart';
import 'package:x1440/frameworks/settings/models/app_local_settings.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';
import 'package:x1440/repositories/models/active_presence_status.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/demo/demo_user.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/models/previously_logged_in_user.dart';
import 'package:x1440/use_cases/models/scrt_credentials.dart';

class LocalStorageRepositoryImpl implements LocalStorageRepository {
  late Isar isar;

  Future<void> init() async {
    final dir = await getApplicationDocumentsDirectory();
    isar = await Isar.open(
      [
        PreviouslyLoggedInUserSchema,
        CredentialsSchema,
        QueueReceiveMessageSchema,
        QueueSendMessageSchema,
        LogEventSchema,
        AppLocalSettingsSchema,
        PresenceStatusSchema,
        ActivePresenceStatusSchema,
        ScrtCredentialsSchema,
        MessagingChannelEntrySchema,
        DemoUserSchema,
      ],
      directory: dir.path,
    );

    isar.queueReceiveMessages.watchLazy().listen((_) async {
      _messageReceiveQueueStreamController
          .add(await getMessageReceiveQueueState());
    });
    isar.queueSendMessages.watchLazy().listen((_) async {
      _messageSendQueueStreamController.add(await getMessageSendQueueState());
    });
    isar.credentials.watchLazy().listen((_) async {
      final newCreds = await getCredentials();
      if (newCreds != _lastCreds) {
        _lastCreds = newCreds;
        _credentialsStreamStreamController.add(newCreds);
      }
    });
    isar.scrtCredentials.watchLazy().listen((_) async {
      final newScrtCreds = await getScrtCredentials();
      if (newScrtCreds != _lastScrtCreds) {
        _lastScrtCreds = newScrtCreds;
        _scrtCredentialsStreamController.add(newScrtCreds);
      }
    });
    isar.appLocalSettings.watchLazy().listen((_) async {
      _appLocalSettingsStreamController.add(await getAppLocalSettings());
    });
    isar.activePresenceStatus.watchLazy().listen((_) async {
      _activePresenceStatusStream.add(await getActivePresenceStatus());
    });
  }

  Credentials? _lastCreds;
  ScrtCredentials? _lastScrtCreds;

  /// **************************************
  /// Credentials
  final StreamController<Credentials> _credentialsStreamStreamController =
      StreamController<Credentials>.broadcast();

  @override
  Stream<Credentials> get credentialsStream =>
      _credentialsStreamStreamController.stream;

  @override
  Future<void> clearCredentials() => isar.writeTxn(() async {
        final count = await isar.credentials.where().deleteAll();
        print('Deleted $count credentials');
      });

  @override
  Future<Credentials> getCredentials() async {
    Credentials? credentials = await isar.credentials.where().findFirst();
    Credentials creds = credentials ?? const Credentials(accessToken: null);
    return creds;
  }

  @override
  Future<void> setCredentials(Credentials credentials) =>
      isar.writeTxn(() => isar.credentials.put(credentials));

  /// **************************************
  /// SCRT Credentials
  final StreamController<ScrtCredentials> _scrtCredentialsStreamController =
  StreamController<ScrtCredentials>.broadcast();

  @override
  Future<ScrtCredentials> getScrtCredentials() async {
    ScrtCredentials? fetchedScrtCredentials =
        await isar.scrtCredentials.where().findFirst();
    return fetchedScrtCredentials ?? ScrtCredentials();
  }

  @override
  Future<void> setScrtCredentials(ScrtCredentials credentials) =>
      isar.writeTxn(() => isar.scrtCredentials.put(credentials));

  @override
  Future<void> clearScrtCredentials() => isar.writeTxn(() async {
    final count = await isar.scrtCredentials.where().deleteAll();
    print('Deleted $count scrtCredentials');
  });

  /// **************************************
  /// AppLocalSettings
  final StreamController<AppLocalSettings> _appLocalSettingsStreamController =
      StreamController<AppLocalSettings>.broadcast();

  @override
  Stream<AppLocalSettings> get appLocalSettingsStream =>
      _appLocalSettingsStreamController.stream;

  @override
  Future<void> clearAppLocalSettings() => isar.writeTxn(() async {
        final count = await isar.appLocalSettings.where().deleteAll();
        print('Deleted $count appLocalSettings');
      });

  @override
  Future<AppLocalSettings> getAppLocalSettings() async {
    AppLocalSettings? fetchedAppLocalSettings =
        await isar.appLocalSettings.where().findFirst();
    return fetchedAppLocalSettings ?? AppLocalSettings();
  }

  @override
  Future<void> setAppLocalSettings(AppLocalSettings appLocalSettings) =>
      isar.writeTxn(() => isar.appLocalSettings.put(appLocalSettings));

  /// **************************************
  /// Presence Status
  final StreamController<ActivePresenceStatus> _activePresenceStatusStream =
      StreamController<ActivePresenceStatus>.broadcast();

  @override
  Stream<ActivePresenceStatus> get activePresenceStatusStream =>
      _activePresenceStatusStream.stream;

  @override
  Future<void> clearActivePresenceStatus() => isar.writeTxn(() async {
        final count = await isar.activePresenceStatus.where().deleteAll();
      });

  @override
  Future<void> setActivePresenceStatus(ActivePresenceStatus activeStatus) =>
      isar.writeTxn(() => isar.activePresenceStatus.put(activeStatus));

  @override
  Future<ActivePresenceStatus> getActivePresenceStatus() =>
      isar.writeTxn(() async => (await isar.activePresenceStatus.where().findFirst()) ??
          ActivePresenceStatus(id: null));

  /// **************************************
  /// Message Receive Queue
  final StreamController<List<QueueReceiveMessage>>
      _messageReceiveQueueStreamController =
      StreamController<List<QueueReceiveMessage>>.broadcast();

  @override
  Stream<List<QueueReceiveMessage>> get messageReceiveQueueStream =>
      _messageReceiveQueueStreamController.stream;

  @override
  Future<List<QueueReceiveMessage>> getMessageReceiveQueueState() =>
      isar.queueReceiveMessages.where().findAll();

  Future<void> clearMessageReceiveQueue() async {
    await isar.writeTxn(() async {
      final count = await isar.queueReceiveMessages.where().deleteAll();
      print('Deleted $count queueReceiveMessages');
    });
  }

  @override
  Future<void> addMessageToReceiveQueue(
          QueueReceiveMessage queueReceiveMessage) =>
      isar.writeTxn(() => isar.queueReceiveMessages.put(queueReceiveMessage));

  @override
  Future<void> removeMessageFromReceiveQueueByNotificationId(
          String notificationId) =>
      isar.writeTxn(() async {
        final count = await isar.queueReceiveMessages
            .filter()
            .notificationIdEqualTo(notificationId)
            .deleteAll();
        print('Deleted $count queueReceiveMessages');
      });

  @override
  clearReceiveQueue() => isar.writeTxn(() async {
        final countEmpty = await isar.queueReceiveMessages
            .filter()
            .notificationIdIsEmpty()
            .deleteAll();
        final count = await isar.queueReceiveMessages
            .filter()
            .notificationIdIsNotEmpty()
            .deleteAll();
        print('Deleted $countEmpty empty and $count queueReceiveMessages');
      });

  /// **************************************
  /// Message Send Queue
  final StreamController<List<QueueSendMessage>>
      _messageSendQueueStreamController =
      StreamController<List<QueueSendMessage>>.broadcast();

  @override
  Stream<List<QueueSendMessage>> get messageSendQueueStream =>
      _messageSendQueueStreamController.stream;

  @override
  Future<List<QueueSendMessage>> getMessageSendQueueState() =>
      isar.queueSendMessages.where().findAll();

  Future<void> clearMessageSendQueue() => isar.writeTxn(() async {
        final count = await isar.queueSendMessages.where().deleteAll();
        print('Deleted $count queueSendMessages');
      });

  @override
  Future<void> addMessageToSendQueue(QueueSendMessage queueSendMessage) =>
      isar.writeTxn(() => isar.queueSendMessages.put(queueSendMessage));

  @override
  Future<void> removeMessageFromSendQueueByMessageId(String messageId) =>
      isar.writeTxn(() async {
        final count = await isar.queueSendMessages
            .filter()
            .messageIdEqualTo(messageId)
            .deleteAll();
        print('Deleted $count queueReceiveMessages');
      });

  @override
  clearSendQueue() => isar.writeTxn(() async {
        final countEmpty = await isar.queueSendMessages
            .filter()
            .messageContentIsEmpty()
            .deleteAll();
        final count = await isar.queueSendMessages
            .filter()
            .messageContentIsNotEmpty()
            .deleteAll();
        print(
            'Deleted $countEmpty empty content and $count other queueReceiveMessages');
      });

  /// **************************************
  /// Logging Queue
  @override
  Future<void> clearLogs() => isar.writeTxn(() async {
        final count = await isar.logEvents.where().deleteAll();
        print('Deleted $count logEvents');
      });

  @override
  Future<List<LogEvent>> getLogs() => isar.logEvents.where().findAll();

  @override
  Future<void> addLogEvent(LogEvent log) => log.message.isNotEmpty
      ? isar.writeTxn(() => isar.logEvents.put(log))
      : Future.value();

  /// **************************************
  /// Previously logged in user
  @override
  Future<void> clearPreviouslyLoggedInUser() => isar.writeTxn(() async {
        final count = await isar.previouslyLoggedInUsers.where().deleteAll();
        print('Deleted $count credentials');
      });

  @override
  Future<PreviouslyLoggedInUser> getPreviouslyLoggedInUser() async {
    PreviouslyLoggedInUser? previouslyLoggedInUser =
        await isar.previouslyLoggedInUsers.where().findFirst();
    return previouslyLoggedInUser ??
        const PreviouslyLoggedInUser(userId: null, orgId: null);
  }

  @override
  Future<void> setPreviouslyLoggedInUser(
          PreviouslyLoggedInUser previouslyLoggedInUser) =>
      isar.writeTxn(
          () => isar.previouslyLoggedInUsers.put(previouslyLoggedInUser));

  @override
  Future<void> clearMessagingChannel() async {
    await isar.writeTxn(() async {
      // Clear any existing channels
      await isar.messagingChannelEntrys.where().deleteAll();
    });
  }

  @override
  Future<MessagingChannelEntry?> getMessagingChannel() async {
    // Retirve the first channelId
    MessagingChannelEntry? channel =
        await isar.messagingChannelEntrys.where().findFirst();
    return channel;
  }

  @override
  Future<void> setMessagingChannel(MessagingChannelEntry channel) async {
    // Only allow to store a unique channel given the channelId
    await isar.writeTxn(() async {
      // Add the new channel
      await isar.messagingChannelEntrys.put(channel);
    });
  }

  @override
  Future<DemoUser?> getDemoUser() async {
    DemoUser? demoUser = await isar.demoUsers.where().findFirst();
    return demoUser;
  }

  @override
  Future<void> setDemoUser(DemoUser user) async {
    await isar.writeTxn(() async {
      await isar.demoUsers.put(user);
    });
  }
}
