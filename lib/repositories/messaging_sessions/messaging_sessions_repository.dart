import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/api/salesforce/dtos/messaging_session_response.dart';
import 'package:x1440/api/salesforce/salesforce_api.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';

class MessagingSessionsRepository {
  final LocalStorageRepository _localStorageRepository;

  MessagingSessionsRepository(this._localStorageRepository);

  Future<SalesforceApi?> get _salesforceApi async {
    final credentials = await _localStorageRepository.getCredentials();
    if (credentials.instanceUrl != null) {
      final sfApi = SalesforceApi(GetIt.instance<Dio>(),
          baseUrl: credentials.instanceUrl!);
      return sfApi;
    }
    return null;
  }

  Future<Result<MessagingSessionResponse, ApiError>> fetchMessagingSession(String messagingSessionId) async {
    try {
      final sfApi = await _salesforceApi;
      final response = await sfApi?.getMessagingSession(messagingSessionId);
      if (response == null) {
        return Error(ApiError.createError(Exception('Failed to fetch messaging session sfApi:  ${sfApi == null ? 'is null' : 'is not null'}')));
      }
      return Success(response);
    } catch (error) {
      return Error(ApiError.createError(error as Exception));
    }
  }
}