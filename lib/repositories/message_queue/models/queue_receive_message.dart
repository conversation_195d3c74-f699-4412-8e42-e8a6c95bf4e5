import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/api/converters/timestamp_int_converter.dart';
import 'package:x1440/api/dtos/notification_message.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/models/shim_websocket_message.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';
import 'package:x1440/utils/json_utils.dart';

part 'queue_receive_message.freezed.dart';
part 'queue_receive_message.g.dart';

@freezed
@collectionOnFreezed
class QueueReceiveMessage with _$QueueReceiveMessage {
  Id get localDbId => Isar.autoIncrement;

  const QueueReceiveMessage._();

  const factory QueueReceiveMessage({
    @TimestampIntConverter() required int timestamp, /// if we want to sort by these, this should be an Isar index
    required String notificationId, /// if we want to query by this, it should be an Isar index
    String? notificationAction,
    required String messageCategory,
    required String stringifiedPayload,
    required String sessionId,
    // TODO: handle this better/differently ... being used to get the message info from a WorkOffered notification
    // NOTE: as of 2024-06, ISAR does not support Maps here, but will in the future
    // ExtraNotificationInfo? extraNotificationInfo
    String? notificationAvatarUrl,
    String? notificationAvatarName,
    String? notificationTitle,
    String? notificationBody
  }) = _QueueReceiveMessage;

  factory QueueReceiveMessage.fromJson(
      Map<String, dynamic> json) =>
      _$QueueReceiveMessageFromJson(json);

  @ignore
  Map<String, dynamic> get payload => safeJsonDecode(stringifiedPayload);

  // @ignore
  // String? get conversationIdentifier {
  //   final x1440Payload = safeJsonDecode(payload['x1440Payload']);
  //   final message = safeJsonDecode(x1440Payload['message']);
  //   return message['conversationId'] as String?;
  // }

  @ignore
  List<String> get relatedRecords {
    final x1440Payload = safeJsonDecode(payload['x1440Payload']);
    final message = safeJsonDecode(x1440Payload['message']);
    final conversationEntryJson = safeJsonDecode(message['conversationEntry']);
    final relatedRecords = conversationEntryJson['relatedRecords'] ?? [];

    return relatedRecords.map((record) => record is String ? record : null).whereType<String>().toList();
  }

  @ignore
  Map<String, dynamic> get shimServicePayload {
    // Decode the x1440Payload string to a Map
    final json = payload;
    if (json['x1440Payload'] is! String && json['x1440Payload'] is! Map) {
      print(
          'ShimServicePayload fromJson - failed to parse x1440Payload for message: $json');
      print(json.keys);
      return {};
    }
    final Map<String, dynamic> x1440Payload =
    json['x1440Payload'] is Map<String, dynamic>
        ? json['x1440Payload']
        : safeJsonDecode(json['x1440Payload'] as String);

    // Decode the nested message string to a Map
    final String messageString = x1440Payload['message'] as String;
    final Map<String, dynamic> message = safeJsonDecode(messageString);

    return {
      'platformType': x1440Payload['platformType'] as String?,
      'messageType': x1440Payload['messageType'] as String,
      'message': message,
    };
  }

  @ignore
  Map<String, dynamic> get shimServiceMessage => safeJsonDecode(shimServicePayload['message']);

  @ignore
  SfId? get workId => (shimServiceMessage['workId'] as String?)?.toSfId();

  @ignore
  SfId? get workTargetId => (shimServiceMessage['workTargetId'] as String?)?.toSfId();

  static QueueReceiveMessage? fromPushNotification(
      String? categoryId, String? action, Map<String?, Object?>? payload) {

    if (categoryId == null || payload?['notificationId'] is! String) return null;

    return QueueReceiveMessage(
        timestamp: const TimestampIntConverter().fromJson(payload?['timestamp']),
        notificationId: payload?['notificationId'] as String,
        sessionId: payload?['sessionId'] as String,
        messageCategory: categoryId,
        stringifiedPayload: payload?['payload'] as String? ?? jsonEncode(payload),
        notificationAction: action,

        notificationAvatarUrl: payload?['avatarUrl'] as String?,
        notificationAvatarName: payload?['avatarName'] as String?,
        notificationTitle: payload?['title'] as String?,
        notificationBody: payload?['body'] as String? ?? (payload?['aps'] as Map?)?['alert']?['body'] as String?
            // ? null : (payload!['aps'] as Map)['alert']?['body'] as String?)
    );
  }
  // notificationBody: (payload as Map<String, dynamic>)?['body'] as String? ?? (payload?['aps'] as Map<String, dynamic>)?['alert']?['body'] as String?
  factory QueueReceiveMessage.fromShimWebsocketMessage(ShimWebsocketMessage shimWebsocketMessage) {
    // ExtraNotificationInfo extraNotificationInfo = ExtraNotificationInfo(
    //   avatarUrl: shimWebsocketMessage.notificationAvatarUrl,
    //   avatarName: shimWebsocketMessage.notificationAvatarName,
    //   title: shimWebsocketMessage.notificationTitle,
    //   body: shimWebsocketMessage.notificationBody
    // );

    return QueueReceiveMessage(
      timestamp: shimWebsocketMessage.timestamp,
      notificationId: shimWebsocketMessage.notificationId,
    sessionId: shimWebsocketMessage.sessionId,
      messageCategory: shimWebsocketMessage.messageCategory,
    stringifiedPayload: shimWebsocketMessage.payload,
        notificationAvatarUrl: shimWebsocketMessage.notificationAvatarUrl,
        notificationAvatarName: shimWebsocketMessage.notificationAvatarName,
        notificationTitle: shimWebsocketMessage.notificationTitle,
        notificationBody: shimWebsocketMessage.notificationBody
      // extraNotificationInfo: extraNotificationInfo
    );
  }

  factory QueueReceiveMessage.fromNotificationMessage(NotificationMessage notification, {String? notificationAction}) => QueueReceiveMessage(
      timestamp: notification.timestamp,
      notificationId: notification.notificationId,
    sessionId: notification.sessionId,
      messageCategory: notification.messageCategory,
    stringifiedPayload: jsonEncode(notification.payload),
    );
}
