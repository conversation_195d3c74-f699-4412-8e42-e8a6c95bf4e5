import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/api/converters/file_to_path_converter.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'queue_send_message.freezed.dart';

part 'queue_send_message.g.dart';

enum QueueSendMessageType {
  workMessage,
  outboundMessage,
}

@freezed
@collectionOnFreezed
class QueueSendMessage with _$QueueSendMessage {
  Id get localDbId => Isar.autoIncrement;

  const QueueSendMessage._();

  // TODO: we either need a workTargetId OR a messagingEndUserId ... handle verification (NOTE: freezed union classes don't play too nicely with Isar)
  const factory QueueSendMessage({
    @ParseSfIdConverter() SfId? workTargetId,
    required String messageId,
    @enumerated required QueueSendMessageType type,
    String? messageContent,
    @ParseSfIdConverter() SfId? messagingDefinitionId,
    @ParseSfIdConverter() SfId? messagingEndUserId,
    @ParseSfIdConverter() SfId? definitionId,
    String? definitionName,
    @Default(<String>[]) @FileToPathConverter() List<String> filePathsToAttach,
  }) = _QueueSendMessage;

  @ignore
  List<File> get filesToAttach =>
      const FileToPathConverter().fromJson(filePathsToAttach);

  @ignore
  MessageEntryType get messageEntryType => filePathsToAttach.isNotEmpty
      ? MessageEntryType.media
      : MessageEntryType.text;

  factory QueueSendMessage.fromJson(Map<String, dynamic> json) =>
      _$QueueSendMessageFromJson(json);
}
