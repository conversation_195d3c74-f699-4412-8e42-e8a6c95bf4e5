// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'queue_send_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

QueueSendMessage _$QueueSendMessageFromJson(Map<String, dynamic> json) {
  return _QueueSendMessage.fromJson(json);
}

/// @nodoc
mixin _$QueueSendMessage {
  @ParseSfIdConverter()
  SfId? get workTargetId => throw _privateConstructorUsedError;
  String get messageId => throw _privateConstructorUsedError;
  @enumerated
  QueueSendMessageType get type => throw _privateConstructorUsedError;
  String? get messageContent => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get messagingDefinitionId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get messagingEndUserId => throw _privateConstructorUsedError;
  @ParseSfIdConverter()
  SfId? get definitionId => throw _privateConstructorUsedError;
  String? get definitionName => throw _privateConstructorUsedError;
  @FileToPathConverter()
  List<String> get filePathsToAttach => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QueueSendMessageCopyWith<QueueSendMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QueueSendMessageCopyWith<$Res> {
  factory $QueueSendMessageCopyWith(
          QueueSendMessage value, $Res Function(QueueSendMessage) then) =
      _$QueueSendMessageCopyWithImpl<$Res, QueueSendMessage>;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId? workTargetId,
      String messageId,
      @enumerated QueueSendMessageType type,
      String? messageContent,
      @ParseSfIdConverter() SfId? messagingDefinitionId,
      @ParseSfIdConverter() SfId? messagingEndUserId,
      @ParseSfIdConverter() SfId? definitionId,
      String? definitionName,
      @FileToPathConverter() List<String> filePathsToAttach});

  $SfIdCopyWith<$Res>? get workTargetId;
  $SfIdCopyWith<$Res>? get messagingDefinitionId;
  $SfIdCopyWith<$Res>? get messagingEndUserId;
  $SfIdCopyWith<$Res>? get definitionId;
}

/// @nodoc
class _$QueueSendMessageCopyWithImpl<$Res, $Val extends QueueSendMessage>
    implements $QueueSendMessageCopyWith<$Res> {
  _$QueueSendMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workTargetId = freezed,
    Object? messageId = null,
    Object? type = null,
    Object? messageContent = freezed,
    Object? messagingDefinitionId = freezed,
    Object? messagingEndUserId = freezed,
    Object? definitionId = freezed,
    Object? definitionName = freezed,
    Object? filePathsToAttach = null,
  }) {
    return _then(_value.copyWith(
      workTargetId: freezed == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as QueueSendMessageType,
      messageContent: freezed == messageContent
          ? _value.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingDefinitionId: freezed == messagingDefinitionId
          ? _value.messagingDefinitionId
          : messagingDefinitionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingEndUserId: freezed == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      definitionId: freezed == definitionId
          ? _value.definitionId
          : definitionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      definitionName: freezed == definitionName
          ? _value.definitionName
          : definitionName // ignore: cast_nullable_to_non_nullable
              as String?,
      filePathsToAttach: null == filePathsToAttach
          ? _value.filePathsToAttach
          : filePathsToAttach // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get workTargetId {
    if (_value.workTargetId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.workTargetId!, (value) {
      return _then(_value.copyWith(workTargetId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingDefinitionId {
    if (_value.messagingDefinitionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.messagingDefinitionId!, (value) {
      return _then(_value.copyWith(messagingDefinitionId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_value.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.messagingEndUserId!, (value) {
      return _then(_value.copyWith(messagingEndUserId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get definitionId {
    if (_value.definitionId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.definitionId!, (value) {
      return _then(_value.copyWith(definitionId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$QueueSendMessageImplCopyWith<$Res>
    implements $QueueSendMessageCopyWith<$Res> {
  factory _$$QueueSendMessageImplCopyWith(_$QueueSendMessageImpl value,
          $Res Function(_$QueueSendMessageImpl) then) =
      __$$QueueSendMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId? workTargetId,
      String messageId,
      @enumerated QueueSendMessageType type,
      String? messageContent,
      @ParseSfIdConverter() SfId? messagingDefinitionId,
      @ParseSfIdConverter() SfId? messagingEndUserId,
      @ParseSfIdConverter() SfId? definitionId,
      String? definitionName,
      @FileToPathConverter() List<String> filePathsToAttach});

  @override
  $SfIdCopyWith<$Res>? get workTargetId;
  @override
  $SfIdCopyWith<$Res>? get messagingDefinitionId;
  @override
  $SfIdCopyWith<$Res>? get messagingEndUserId;
  @override
  $SfIdCopyWith<$Res>? get definitionId;
}

/// @nodoc
class __$$QueueSendMessageImplCopyWithImpl<$Res>
    extends _$QueueSendMessageCopyWithImpl<$Res, _$QueueSendMessageImpl>
    implements _$$QueueSendMessageImplCopyWith<$Res> {
  __$$QueueSendMessageImplCopyWithImpl(_$QueueSendMessageImpl _value,
      $Res Function(_$QueueSendMessageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workTargetId = freezed,
    Object? messageId = null,
    Object? type = null,
    Object? messageContent = freezed,
    Object? messagingDefinitionId = freezed,
    Object? messagingEndUserId = freezed,
    Object? definitionId = freezed,
    Object? definitionName = freezed,
    Object? filePathsToAttach = null,
  }) {
    return _then(_$QueueSendMessageImpl(
      workTargetId: freezed == workTargetId
          ? _value.workTargetId
          : workTargetId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as QueueSendMessageType,
      messageContent: freezed == messageContent
          ? _value.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingDefinitionId: freezed == messagingDefinitionId
          ? _value.messagingDefinitionId
          : messagingDefinitionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingEndUserId: freezed == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      definitionId: freezed == definitionId
          ? _value.definitionId
          : definitionId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      definitionName: freezed == definitionName
          ? _value.definitionName
          : definitionName // ignore: cast_nullable_to_non_nullable
              as String?,
      filePathsToAttach: null == filePathsToAttach
          ? _value._filePathsToAttach
          : filePathsToAttach // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QueueSendMessageImpl extends _QueueSendMessage {
  const _$QueueSendMessageImpl(
      {@ParseSfIdConverter() this.workTargetId,
      required this.messageId,
      @enumerated required this.type,
      this.messageContent,
      @ParseSfIdConverter() this.messagingDefinitionId,
      @ParseSfIdConverter() this.messagingEndUserId,
      @ParseSfIdConverter() this.definitionId,
      this.definitionName,
      @FileToPathConverter()
      final List<String> filePathsToAttach = const <String>[]})
      : _filePathsToAttach = filePathsToAttach,
        super._();

  factory _$QueueSendMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$QueueSendMessageImplFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId? workTargetId;
  @override
  final String messageId;
  @override
  @enumerated
  final QueueSendMessageType type;
  @override
  final String? messageContent;
  @override
  @ParseSfIdConverter()
  final SfId? messagingDefinitionId;
  @override
  @ParseSfIdConverter()
  final SfId? messagingEndUserId;
  @override
  @ParseSfIdConverter()
  final SfId? definitionId;
  @override
  final String? definitionName;
  final List<String> _filePathsToAttach;
  @override
  @JsonKey()
  @FileToPathConverter()
  List<String> get filePathsToAttach {
    if (_filePathsToAttach is EqualUnmodifiableListView)
      return _filePathsToAttach;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filePathsToAttach);
  }

  @override
  String toString() {
    return 'QueueSendMessage(workTargetId: $workTargetId, messageId: $messageId, type: $type, messageContent: $messageContent, messagingDefinitionId: $messagingDefinitionId, messagingEndUserId: $messagingEndUserId, definitionId: $definitionId, definitionName: $definitionName, filePathsToAttach: $filePathsToAttach)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QueueSendMessageImpl &&
            (identical(other.workTargetId, workTargetId) ||
                other.workTargetId == workTargetId) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            (identical(other.messagingDefinitionId, messagingDefinitionId) ||
                other.messagingDefinitionId == messagingDefinitionId) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.definitionId, definitionId) ||
                other.definitionId == definitionId) &&
            (identical(other.definitionName, definitionName) ||
                other.definitionName == definitionName) &&
            const DeepCollectionEquality()
                .equals(other._filePathsToAttach, _filePathsToAttach));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      workTargetId,
      messageId,
      type,
      messageContent,
      messagingDefinitionId,
      messagingEndUserId,
      definitionId,
      definitionName,
      const DeepCollectionEquality().hash(_filePathsToAttach));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QueueSendMessageImplCopyWith<_$QueueSendMessageImpl> get copyWith =>
      __$$QueueSendMessageImplCopyWithImpl<_$QueueSendMessageImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QueueSendMessageImplToJson(
      this,
    );
  }
}

abstract class _QueueSendMessage extends QueueSendMessage {
  const factory _QueueSendMessage(
          {@ParseSfIdConverter() final SfId? workTargetId,
          required final String messageId,
          @enumerated required final QueueSendMessageType type,
          final String? messageContent,
          @ParseSfIdConverter() final SfId? messagingDefinitionId,
          @ParseSfIdConverter() final SfId? messagingEndUserId,
          @ParseSfIdConverter() final SfId? definitionId,
          final String? definitionName,
          @FileToPathConverter() final List<String> filePathsToAttach}) =
      _$QueueSendMessageImpl;
  const _QueueSendMessage._() : super._();

  factory _QueueSendMessage.fromJson(Map<String, dynamic> json) =
      _$QueueSendMessageImpl.fromJson;

  @override
  @ParseSfIdConverter()
  SfId? get workTargetId;
  @override
  String get messageId;
  @override
  @enumerated
  QueueSendMessageType get type;
  @override
  String? get messageContent;
  @override
  @ParseSfIdConverter()
  SfId? get messagingDefinitionId;
  @override
  @ParseSfIdConverter()
  SfId? get messagingEndUserId;
  @override
  @ParseSfIdConverter()
  SfId? get definitionId;
  @override
  String? get definitionName;
  @override
  @FileToPathConverter()
  List<String> get filePathsToAttach;
  @override
  @JsonKey(ignore: true)
  _$$QueueSendMessageImplCopyWith<_$QueueSendMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
