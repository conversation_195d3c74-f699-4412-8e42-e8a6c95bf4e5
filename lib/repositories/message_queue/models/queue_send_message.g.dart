// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'queue_send_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QueueSendMessageImpl _$$QueueSendMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$QueueSendMessageImpl(
      workTargetId: const ParseSfIdConverter().fromJson(json['workTargetId']),
      messageId: json['messageId'] as String,
      type: $enumDecode(_$QueueSendMessageTypeEnumMap, json['type']),
      messageContent: json['messageContent'] as String?,
      messagingDefinitionId:
          const ParseSfIdConverter().fromJson(json['messagingDefinitionId']),
      messagingEndUserId:
          const ParseSfIdConverter().fromJson(json['messagingEndUserId']),
      definitionId: const ParseSfIdConverter().fromJson(json['definitionId']),
      definitionName: json['definitionName'] as String?,
      filePathsToAttach: (json['filePathsToAttach'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const <String>[],
    );

Map<String, dynamic> _$$QueueSendMessageImplToJson(
        _$QueueSendMessageImpl instance) =>
    <String, dynamic>{
      'workTargetId': _$JsonConverterToJson<Object?, SfId>(
          instance.workTargetId, const ParseSfIdConverter().toJson),
      'messageId': instance.messageId,
      'type': _$QueueSendMessageTypeEnumMap[instance.type]!,
      'messageContent': instance.messageContent,
      'messagingDefinitionId': _$JsonConverterToJson<Object?, SfId>(
          instance.messagingDefinitionId, const ParseSfIdConverter().toJson),
      'messagingEndUserId': _$JsonConverterToJson<Object?, SfId>(
          instance.messagingEndUserId, const ParseSfIdConverter().toJson),
      'definitionId': _$JsonConverterToJson<Object?, SfId>(
          instance.definitionId, const ParseSfIdConverter().toJson),
      'definitionName': instance.definitionName,
      'filePathsToAttach': instance.filePathsToAttach,
    };

const _$QueueSendMessageTypeEnumMap = {
  QueueSendMessageType.workMessage: 'workMessage',
  QueueSendMessageType.outboundMessage: 'outboundMessage',
};

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);
