// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'queue_receive_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

QueueReceiveMessage _$QueueReceiveMessageFromJson(Map<String, dynamic> json) {
  return _QueueReceiveMessage.fromJson(json);
}

/// @nodoc
mixin _$QueueReceiveMessage {
  @TimestampIntConverter()
  int get timestamp => throw _privateConstructorUsedError;

  /// if we want to sort by these, this should be an Isar index
  String get notificationId => throw _privateConstructorUsedError;

  /// if we want to query by this, it should be an Isar index
  String? get notificationAction => throw _privateConstructorUsedError;
  String get messageCategory => throw _privateConstructorUsedError;
  String get stringifiedPayload => throw _privateConstructorUsedError;
  String get sessionId =>
      throw _privateConstructorUsedError; // TODO: handle this better/differently ... being used to get the message info from a WorkOffered notification
// NOTE: as of 2024-06, ISAR does not support Maps here, but will in the future
// ExtraNotificationInfo? extraNotificationInfo
  String? get notificationAvatarUrl => throw _privateConstructorUsedError;
  String? get notificationAvatarName => throw _privateConstructorUsedError;
  String? get notificationTitle => throw _privateConstructorUsedError;
  String? get notificationBody => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QueueReceiveMessageCopyWith<QueueReceiveMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QueueReceiveMessageCopyWith<$Res> {
  factory $QueueReceiveMessageCopyWith(
          QueueReceiveMessage value, $Res Function(QueueReceiveMessage) then) =
      _$QueueReceiveMessageCopyWithImpl<$Res, QueueReceiveMessage>;
  @useResult
  $Res call(
      {@TimestampIntConverter() int timestamp,
      String notificationId,
      String? notificationAction,
      String messageCategory,
      String stringifiedPayload,
      String sessionId,
      String? notificationAvatarUrl,
      String? notificationAvatarName,
      String? notificationTitle,
      String? notificationBody});
}

/// @nodoc
class _$QueueReceiveMessageCopyWithImpl<$Res, $Val extends QueueReceiveMessage>
    implements $QueueReceiveMessageCopyWith<$Res> {
  _$QueueReceiveMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? notificationAction = freezed,
    Object? messageCategory = null,
    Object? stringifiedPayload = null,
    Object? sessionId = null,
    Object? notificationAvatarUrl = freezed,
    Object? notificationAvatarName = freezed,
    Object? notificationTitle = freezed,
    Object? notificationBody = freezed,
  }) {
    return _then(_value.copyWith(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationAction: freezed == notificationAction
          ? _value.notificationAction
          : notificationAction // ignore: cast_nullable_to_non_nullable
              as String?,
      messageCategory: null == messageCategory
          ? _value.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      stringifiedPayload: null == stringifiedPayload
          ? _value.stringifiedPayload
          : stringifiedPayload // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationAvatarUrl: freezed == notificationAvatarUrl
          ? _value.notificationAvatarUrl
          : notificationAvatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationAvatarName: freezed == notificationAvatarName
          ? _value.notificationAvatarName
          : notificationAvatarName // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationTitle: freezed == notificationTitle
          ? _value.notificationTitle
          : notificationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationBody: freezed == notificationBody
          ? _value.notificationBody
          : notificationBody // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QueueReceiveMessageImplCopyWith<$Res>
    implements $QueueReceiveMessageCopyWith<$Res> {
  factory _$$QueueReceiveMessageImplCopyWith(_$QueueReceiveMessageImpl value,
          $Res Function(_$QueueReceiveMessageImpl) then) =
      __$$QueueReceiveMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@TimestampIntConverter() int timestamp,
      String notificationId,
      String? notificationAction,
      String messageCategory,
      String stringifiedPayload,
      String sessionId,
      String? notificationAvatarUrl,
      String? notificationAvatarName,
      String? notificationTitle,
      String? notificationBody});
}

/// @nodoc
class __$$QueueReceiveMessageImplCopyWithImpl<$Res>
    extends _$QueueReceiveMessageCopyWithImpl<$Res, _$QueueReceiveMessageImpl>
    implements _$$QueueReceiveMessageImplCopyWith<$Res> {
  __$$QueueReceiveMessageImplCopyWithImpl(_$QueueReceiveMessageImpl _value,
      $Res Function(_$QueueReceiveMessageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? notificationAction = freezed,
    Object? messageCategory = null,
    Object? stringifiedPayload = null,
    Object? sessionId = null,
    Object? notificationAvatarUrl = freezed,
    Object? notificationAvatarName = freezed,
    Object? notificationTitle = freezed,
    Object? notificationBody = freezed,
  }) {
    return _then(_$QueueReceiveMessageImpl(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationAction: freezed == notificationAction
          ? _value.notificationAction
          : notificationAction // ignore: cast_nullable_to_non_nullable
              as String?,
      messageCategory: null == messageCategory
          ? _value.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      stringifiedPayload: null == stringifiedPayload
          ? _value.stringifiedPayload
          : stringifiedPayload // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationAvatarUrl: freezed == notificationAvatarUrl
          ? _value.notificationAvatarUrl
          : notificationAvatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationAvatarName: freezed == notificationAvatarName
          ? _value.notificationAvatarName
          : notificationAvatarName // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationTitle: freezed == notificationTitle
          ? _value.notificationTitle
          : notificationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationBody: freezed == notificationBody
          ? _value.notificationBody
          : notificationBody // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QueueReceiveMessageImpl extends _QueueReceiveMessage {
  const _$QueueReceiveMessageImpl(
      {@TimestampIntConverter() required this.timestamp,
      required this.notificationId,
      this.notificationAction,
      required this.messageCategory,
      required this.stringifiedPayload,
      required this.sessionId,
      this.notificationAvatarUrl,
      this.notificationAvatarName,
      this.notificationTitle,
      this.notificationBody})
      : super._();

  factory _$QueueReceiveMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$QueueReceiveMessageImplFromJson(json);

  @override
  @TimestampIntConverter()
  final int timestamp;

  /// if we want to sort by these, this should be an Isar index
  @override
  final String notificationId;

  /// if we want to query by this, it should be an Isar index
  @override
  final String? notificationAction;
  @override
  final String messageCategory;
  @override
  final String stringifiedPayload;
  @override
  final String sessionId;
// TODO: handle this better/differently ... being used to get the message info from a WorkOffered notification
// NOTE: as of 2024-06, ISAR does not support Maps here, but will in the future
// ExtraNotificationInfo? extraNotificationInfo
  @override
  final String? notificationAvatarUrl;
  @override
  final String? notificationAvatarName;
  @override
  final String? notificationTitle;
  @override
  final String? notificationBody;

  @override
  String toString() {
    return 'QueueReceiveMessage(timestamp: $timestamp, notificationId: $notificationId, notificationAction: $notificationAction, messageCategory: $messageCategory, stringifiedPayload: $stringifiedPayload, sessionId: $sessionId, notificationAvatarUrl: $notificationAvatarUrl, notificationAvatarName: $notificationAvatarName, notificationTitle: $notificationTitle, notificationBody: $notificationBody)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QueueReceiveMessageImpl &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.notificationAction, notificationAction) ||
                other.notificationAction == notificationAction) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            (identical(other.stringifiedPayload, stringifiedPayload) ||
                other.stringifiedPayload == stringifiedPayload) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.notificationAvatarUrl, notificationAvatarUrl) ||
                other.notificationAvatarUrl == notificationAvatarUrl) &&
            (identical(other.notificationAvatarName, notificationAvatarName) ||
                other.notificationAvatarName == notificationAvatarName) &&
            (identical(other.notificationTitle, notificationTitle) ||
                other.notificationTitle == notificationTitle) &&
            (identical(other.notificationBody, notificationBody) ||
                other.notificationBody == notificationBody));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      notificationAction,
      messageCategory,
      stringifiedPayload,
      sessionId,
      notificationAvatarUrl,
      notificationAvatarName,
      notificationTitle,
      notificationBody);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QueueReceiveMessageImplCopyWith<_$QueueReceiveMessageImpl> get copyWith =>
      __$$QueueReceiveMessageImplCopyWithImpl<_$QueueReceiveMessageImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QueueReceiveMessageImplToJson(
      this,
    );
  }
}

abstract class _QueueReceiveMessage extends QueueReceiveMessage {
  const factory _QueueReceiveMessage(
      {@TimestampIntConverter() required final int timestamp,
      required final String notificationId,
      final String? notificationAction,
      required final String messageCategory,
      required final String stringifiedPayload,
      required final String sessionId,
      final String? notificationAvatarUrl,
      final String? notificationAvatarName,
      final String? notificationTitle,
      final String? notificationBody}) = _$QueueReceiveMessageImpl;
  const _QueueReceiveMessage._() : super._();

  factory _QueueReceiveMessage.fromJson(Map<String, dynamic> json) =
      _$QueueReceiveMessageImpl.fromJson;

  @override
  @TimestampIntConverter()
  int get timestamp;
  @override

  /// if we want to sort by these, this should be an Isar index
  String get notificationId;
  @override

  /// if we want to query by this, it should be an Isar index
  String? get notificationAction;
  @override
  String get messageCategory;
  @override
  String get stringifiedPayload;
  @override
  String get sessionId;
  @override // TODO: handle this better/differently ... being used to get the message info from a WorkOffered notification
// NOTE: as of 2024-06, ISAR does not support Maps here, but will in the future
// ExtraNotificationInfo? extraNotificationInfo
  String? get notificationAvatarUrl;
  @override
  String? get notificationAvatarName;
  @override
  String? get notificationTitle;
  @override
  String? get notificationBody;
  @override
  @JsonKey(ignore: true)
  _$$QueueReceiveMessageImplCopyWith<_$QueueReceiveMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
