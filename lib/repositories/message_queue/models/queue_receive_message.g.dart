// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'queue_receive_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QueueReceiveMessageImpl _$$QueueReceiveMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$QueueReceiveMessageImpl(
      timestamp: const TimestampIntConverter().fromJson(json['timestamp']),
      notificationId: json['notificationId'] as String,
      notificationAction: json['notificationAction'] as String?,
      messageCategory: json['messageCategory'] as String,
      stringifiedPayload: json['stringifiedPayload'] as String,
      sessionId: json['sessionId'] as String,
      notificationAvatarUrl: json['notificationAvatarUrl'] as String?,
      notificationAvatarName: json['notificationAvatarName'] as String?,
      notificationTitle: json['notificationTitle'] as String?,
      notificationBody: json['notificationBody'] as String?,
    );

Map<String, dynamic> _$$QueueReceiveMessageImplToJson(
        _$QueueReceiveMessageImpl instance) =>
    <String, dynamic>{
      'timestamp': const TimestampIntConverter().toJson(instance.timestamp),
      'notificationId': instance.notificationId,
      'notificationAction': instance.notificationAction,
      'messageCategory': instance.messageCategory,
      'stringifiedPayload': instance.stringifiedPayload,
      'sessionId': instance.sessionId,
      'notificationAvatarUrl': instance.notificationAvatarUrl,
      'notificationAvatarName': instance.notificationAvatarName,
      'notificationTitle': instance.notificationTitle,
      'notificationBody': instance.notificationBody,
    };
