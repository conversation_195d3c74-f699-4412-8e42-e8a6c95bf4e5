// import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:x1440/utils/extensions/isar_on_freezed.dart';
//
// part 'extra_notification_info.freezed.dart';
// part 'extra_notification_info.g.dart';
//
// @freezed
// @embeddedOnFreezed
// class ExtraNotificationInfo with _$ExtraNotificationInfo {
//   const ExtraNotificationInfo._();
//   const factory ExtraNotificationInfo({
//     String? avatarUrl,
//     String? avatarName,
//     String? title,
//     String? body
//   }) = _ExtraNotificationInfo;
//
//   factory ExtraNotificationInfo.fromJson(Map<String, dynamic> json) =>
//       _$ExtraNotificationInfoFromJson(json);
// }