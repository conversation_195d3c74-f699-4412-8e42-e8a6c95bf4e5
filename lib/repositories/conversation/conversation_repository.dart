import 'package:retrofit/dio.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/api/dtos/close_work_body.dart';
import 'package:x1440/api/dtos/decline_work_body.dart';

import 'package:x1440/api/dtos/outbound_message_body.dart';
import 'package:x1440/api/dtos/outbound_message_response.dart';
import 'package:x1440/api/dtos/outbound_messages_body.dart';
import 'package:x1440/api/dtos/outbound_messages_response.dart';
import 'package:x1440/api/dtos/work_body.dart';
import 'package:x1440/api/dtos/work_message_body.dart';
import 'package:x1440/api/dtos/work_messages_body.dart';
import 'package:x1440/api/dtos/work_messages_response.dart';
import 'package:x1440/api/dtos/work_response.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/create_messaging_end_user_body.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/create_object_response.dart';
import 'package:x1440/api/dtos/messaging_end_user_status_response.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/update_messaging_end_user_body.dart';
import 'package:x1440/models/sf_id.dart';

abstract class ConversationRepository {
  Future<Result<WorkResponse, ApiError>> acceptWork(
      WorkBody acceptWorkBody);

  Future<Result<HttpResponse, ApiError>> declineWork(DeclineWorkBody body);

  Future<Result<HttpResponse, ApiError>> closeWork(CloseWorkBody body);

  Future<Result<OutboundMessageResponse, ApiError>> sendNewOutboundMessage(
      OutboundMessageBody body);

  Future<Result<HttpResponse, ApiError>> sendWorkMessage(
      String workTargetId, WorkMessageBody body);

  Future<Result<OutboundMessagesResponse, ApiError>> sendNewOutboundMessages(
      OutboundMessagesBody body);

  Future<Result<WorkMessagesResponse, ApiError>> sendWorkMessages(
      WorkMessagesBody body);

  Future<Result<CreateSfObjectReponse, ApiError>> createMessagingEndUser(
      CreateMessagingEndUserBody createMessagingEndUserBody);

  Future<Result<MessagingEndUserStatusResponse, ApiError>> fetchMessagingEndUserStatus(
      SfId messagingEndUserId);

  Future<Result<HttpResponse, ApiError>> updateMessagingEndUser(
      String messagingEndUserId,
      UpdateMessagingEndUserBody updateMessagingEndUserBody);
  }
