import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'active_presence_status.freezed.dart';
part 'active_presence_status.g.dart';
//   Id get isarId => fastHash(id);
@freezed
@collectionOnFreezed
class ActivePresenceStatus with _$ActivePresenceStatus {
  /// Isar ID for indexing — Setting this one to '0' for singleton item in collection; otherwise default use here is (can also use custom indices -- see isar docs):
  final Id isarId = 0;
  const ActivePresenceStatus._();

  const factory ActivePresenceStatus({
    String? id,
    // required PresenceStatus presenceStatus
  }) = _ActivePresenceStatus;

  factory ActivePresenceStatus.fromJson(Map<String, dynamic> json) =>
      _$ActivePresenceStatusFromJson(json);

  factory ActivePresenceStatus.offline() => const ActivePresenceStatus();

  // @ignore
  // get isOnline => presenceStatus.isOnline;
}