// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shim_websocket_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ShimWebsocketMessageImpl _$$ShimWebsocketMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$ShimWebsocketMessageImpl(
      timestamp: (json['timestamp'] as num).toInt(),
      notificationId: json['notificationId'] as String,
      messageCategory: json['messageCategory'] as String,
      payload: json['payload'] as String,
      sessionId: json['sessionId'] as String,
      ack: json['ack'] == null
          ? false
          : const StringToBoolConverter().fromJson(json['ack']),
      notificationAvatarUrl: json['avatarUrl'] as String?,
      notificationAvatarName: json['avatarName'] as String?,
      notificationTitle:
          const StringToHtmlUnescapedStringConverter().fromJson(json['title']),
      notificationBody:
          const StringToHtmlUnescapedStringConverter().fromJson(json['body']),
    );

Map<String, dynamic> _$$ShimWebsocketMessageImplToJson(
        _$ShimWebsocketMessageImpl instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp,
      'notificationId': instance.notificationId,
      'messageCategory': instance.messageCategory,
      'payload': instance.payload,
      'sessionId': instance.sessionId,
      'ack': const StringToBoolConverter().toJson(instance.ack),
      'avatarUrl': instance.notificationAvatarUrl,
      'avatarName': instance.notificationAvatarName,
      'title': const StringToHtmlUnescapedStringConverter()
          .toJson(instance.notificationTitle),
      'body': const StringToHtmlUnescapedStringConverter()
          .toJson(instance.notificationBody),
    };
