import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/converters/string_to_bool_converter.dart';
import 'package:x1440/api/converters/string_to_html_unescaped_string.dart';
part 'shim_websocket_message.freezed.dart';
part 'shim_websocket_message.g.dart';

@freezed
class ShimWebsocketMessage with _$ShimWebsocketMessage {
  const factory ShimWebsocketMessage({
    required int timestamp,
    required String notificationId,
    required String messageCategory,
    required String payload,
    required String sessionId,
    @Default(false) @StringToBoolConverter() bool ack,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'avatarUrl') String? notificationAvatarUrl,
    @J<PERSON><PERSON>ey(name: 'avatarName') String? notificationAvatarName,
    @JsonKey(name: 'title') @StringToHtmlUnescapedStringConverter() String? notificationTitle,
    // @Default(false) @StringToBoolConverter() bool ack,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'body') @StringToHtmlUnescapedStringConverter() String? notificationBody
      }) = _ShimWebsocketMessage;

  factory ShimWebsocketMessage.fromJson(Map<String, dynamic> json) =>
      _$ShimWebsocketMessageFromJson(json);
}
