// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'active_presence_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ActivePresenceStatus _$ActivePresenceStatusFromJson(Map<String, dynamic> json) {
  return _ActivePresenceStatus.fromJson(json);
}

/// @nodoc
mixin _$ActivePresenceStatus {
  String? get id => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ActivePresenceStatusCopyWith<ActivePresenceStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ActivePresenceStatusCopyWith<$Res> {
  factory $ActivePresenceStatusCopyWith(ActivePresenceStatus value,
          $Res Function(ActivePresenceStatus) then) =
      _$ActivePresenceStatusCopyWithImpl<$Res, ActivePresenceStatus>;
  @useResult
  $Res call({String? id});
}

/// @nodoc
class _$ActivePresenceStatusCopyWithImpl<$Res,
        $Val extends ActivePresenceStatus>
    implements $ActivePresenceStatusCopyWith<$Res> {
  _$ActivePresenceStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ActivePresenceStatusImplCopyWith<$Res>
    implements $ActivePresenceStatusCopyWith<$Res> {
  factory _$$ActivePresenceStatusImplCopyWith(_$ActivePresenceStatusImpl value,
          $Res Function(_$ActivePresenceStatusImpl) then) =
      __$$ActivePresenceStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? id});
}

/// @nodoc
class __$$ActivePresenceStatusImplCopyWithImpl<$Res>
    extends _$ActivePresenceStatusCopyWithImpl<$Res, _$ActivePresenceStatusImpl>
    implements _$$ActivePresenceStatusImplCopyWith<$Res> {
  __$$ActivePresenceStatusImplCopyWithImpl(_$ActivePresenceStatusImpl _value,
      $Res Function(_$ActivePresenceStatusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
  }) {
    return _then(_$ActivePresenceStatusImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ActivePresenceStatusImpl extends _ActivePresenceStatus {
  const _$ActivePresenceStatusImpl({this.id}) : super._();

  factory _$ActivePresenceStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$ActivePresenceStatusImplFromJson(json);

  @override
  final String? id;

  @override
  String toString() {
    return 'ActivePresenceStatus(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ActivePresenceStatusImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ActivePresenceStatusImplCopyWith<_$ActivePresenceStatusImpl>
      get copyWith =>
          __$$ActivePresenceStatusImplCopyWithImpl<_$ActivePresenceStatusImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ActivePresenceStatusImplToJson(
      this,
    );
  }
}

abstract class _ActivePresenceStatus extends ActivePresenceStatus {
  const factory _ActivePresenceStatus({final String? id}) =
      _$ActivePresenceStatusImpl;
  const _ActivePresenceStatus._() : super._();

  factory _ActivePresenceStatus.fromJson(Map<String, dynamic> json) =
      _$ActivePresenceStatusImpl.fromJson;

  @override
  String? get id;
  @override
  @JsonKey(ignore: true)
  _$$ActivePresenceStatusImplCopyWith<_$ActivePresenceStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}
