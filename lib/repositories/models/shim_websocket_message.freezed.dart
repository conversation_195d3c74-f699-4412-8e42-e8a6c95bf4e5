// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shim_websocket_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ShimWebsocketMessage _$ShimWebsocketMessageFromJson(Map<String, dynamic> json) {
  return _ShimWebsocketMessage.fromJson(json);
}

/// @nodoc
mixin _$ShimWebsocketMessage {
  int get timestamp => throw _privateConstructorUsedError;
  String get notificationId => throw _privateConstructorUsedError;
  String get messageCategory => throw _privateConstructorUsedError;
  String get payload => throw _privateConstructorUsedError;
  String get sessionId => throw _privateConstructorUsedError;
  @StringToBoolConverter()
  bool get ack => throw _privateConstructorUsedError;
  @JsonKey(name: 'avatarUrl')
  String? get notificationAvatarUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'avatarName')
  String? get notificationAvatarName => throw _privateConstructorUsedError;
  @JsonKey(name: 'title')
  @StringToHtmlUnescapedStringConverter()
  String? get notificationTitle =>
      throw _privateConstructorUsedError; // @Default(false) @StringToBoolConverter() bool ack,
  @JsonKey(name: 'body')
  @StringToHtmlUnescapedStringConverter()
  String? get notificationBody => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShimWebsocketMessageCopyWith<ShimWebsocketMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShimWebsocketMessageCopyWith<$Res> {
  factory $ShimWebsocketMessageCopyWith(ShimWebsocketMessage value,
          $Res Function(ShimWebsocketMessage) then) =
      _$ShimWebsocketMessageCopyWithImpl<$Res, ShimWebsocketMessage>;
  @useResult
  $Res call(
      {int timestamp,
      String notificationId,
      String messageCategory,
      String payload,
      String sessionId,
      @StringToBoolConverter() bool ack,
      @JsonKey(name: 'avatarUrl') String? notificationAvatarUrl,
      @JsonKey(name: 'avatarName') String? notificationAvatarName,
      @JsonKey(name: 'title')
      @StringToHtmlUnescapedStringConverter()
      String? notificationTitle,
      @JsonKey(name: 'body')
      @StringToHtmlUnescapedStringConverter()
      String? notificationBody});
}

/// @nodoc
class _$ShimWebsocketMessageCopyWithImpl<$Res,
        $Val extends ShimWebsocketMessage>
    implements $ShimWebsocketMessageCopyWith<$Res> {
  _$ShimWebsocketMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? messageCategory = null,
    Object? payload = null,
    Object? sessionId = null,
    Object? ack = null,
    Object? notificationAvatarUrl = freezed,
    Object? notificationAvatarName = freezed,
    Object? notificationTitle = freezed,
    Object? notificationBody = freezed,
  }) {
    return _then(_value.copyWith(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      messageCategory: null == messageCategory
          ? _value.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      ack: null == ack
          ? _value.ack
          : ack // ignore: cast_nullable_to_non_nullable
              as bool,
      notificationAvatarUrl: freezed == notificationAvatarUrl
          ? _value.notificationAvatarUrl
          : notificationAvatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationAvatarName: freezed == notificationAvatarName
          ? _value.notificationAvatarName
          : notificationAvatarName // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationTitle: freezed == notificationTitle
          ? _value.notificationTitle
          : notificationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationBody: freezed == notificationBody
          ? _value.notificationBody
          : notificationBody // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShimWebsocketMessageImplCopyWith<$Res>
    implements $ShimWebsocketMessageCopyWith<$Res> {
  factory _$$ShimWebsocketMessageImplCopyWith(_$ShimWebsocketMessageImpl value,
          $Res Function(_$ShimWebsocketMessageImpl) then) =
      __$$ShimWebsocketMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int timestamp,
      String notificationId,
      String messageCategory,
      String payload,
      String sessionId,
      @StringToBoolConverter() bool ack,
      @JsonKey(name: 'avatarUrl') String? notificationAvatarUrl,
      @JsonKey(name: 'avatarName') String? notificationAvatarName,
      @JsonKey(name: 'title')
      @StringToHtmlUnescapedStringConverter()
      String? notificationTitle,
      @JsonKey(name: 'body')
      @StringToHtmlUnescapedStringConverter()
      String? notificationBody});
}

/// @nodoc
class __$$ShimWebsocketMessageImplCopyWithImpl<$Res>
    extends _$ShimWebsocketMessageCopyWithImpl<$Res, _$ShimWebsocketMessageImpl>
    implements _$$ShimWebsocketMessageImplCopyWith<$Res> {
  __$$ShimWebsocketMessageImplCopyWithImpl(_$ShimWebsocketMessageImpl _value,
      $Res Function(_$ShimWebsocketMessageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timestamp = null,
    Object? notificationId = null,
    Object? messageCategory = null,
    Object? payload = null,
    Object? sessionId = null,
    Object? ack = null,
    Object? notificationAvatarUrl = freezed,
    Object? notificationAvatarName = freezed,
    Object? notificationTitle = freezed,
    Object? notificationBody = freezed,
  }) {
    return _then(_$ShimWebsocketMessageImpl(
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      notificationId: null == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      messageCategory: null == messageCategory
          ? _value.messageCategory
          : messageCategory // ignore: cast_nullable_to_non_nullable
              as String,
      payload: null == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      ack: null == ack
          ? _value.ack
          : ack // ignore: cast_nullable_to_non_nullable
              as bool,
      notificationAvatarUrl: freezed == notificationAvatarUrl
          ? _value.notificationAvatarUrl
          : notificationAvatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationAvatarName: freezed == notificationAvatarName
          ? _value.notificationAvatarName
          : notificationAvatarName // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationTitle: freezed == notificationTitle
          ? _value.notificationTitle
          : notificationTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      notificationBody: freezed == notificationBody
          ? _value.notificationBody
          : notificationBody // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShimWebsocketMessageImpl implements _ShimWebsocketMessage {
  const _$ShimWebsocketMessageImpl(
      {required this.timestamp,
      required this.notificationId,
      required this.messageCategory,
      required this.payload,
      required this.sessionId,
      @StringToBoolConverter() this.ack = false,
      @JsonKey(name: 'avatarUrl') this.notificationAvatarUrl,
      @JsonKey(name: 'avatarName') this.notificationAvatarName,
      @JsonKey(name: 'title')
      @StringToHtmlUnescapedStringConverter()
      this.notificationTitle,
      @JsonKey(name: 'body')
      @StringToHtmlUnescapedStringConverter()
      this.notificationBody});

  factory _$ShimWebsocketMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShimWebsocketMessageImplFromJson(json);

  @override
  final int timestamp;
  @override
  final String notificationId;
  @override
  final String messageCategory;
  @override
  final String payload;
  @override
  final String sessionId;
  @override
  @JsonKey()
  @StringToBoolConverter()
  final bool ack;
  @override
  @JsonKey(name: 'avatarUrl')
  final String? notificationAvatarUrl;
  @override
  @JsonKey(name: 'avatarName')
  final String? notificationAvatarName;
  @override
  @JsonKey(name: 'title')
  @StringToHtmlUnescapedStringConverter()
  final String? notificationTitle;
// @Default(false) @StringToBoolConverter() bool ack,
  @override
  @JsonKey(name: 'body')
  @StringToHtmlUnescapedStringConverter()
  final String? notificationBody;

  @override
  String toString() {
    return 'ShimWebsocketMessage(timestamp: $timestamp, notificationId: $notificationId, messageCategory: $messageCategory, payload: $payload, sessionId: $sessionId, ack: $ack, notificationAvatarUrl: $notificationAvatarUrl, notificationAvatarName: $notificationAvatarName, notificationTitle: $notificationTitle, notificationBody: $notificationBody)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShimWebsocketMessageImpl &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.messageCategory, messageCategory) ||
                other.messageCategory == messageCategory) &&
            (identical(other.payload, payload) || other.payload == payload) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.ack, ack) || other.ack == ack) &&
            (identical(other.notificationAvatarUrl, notificationAvatarUrl) ||
                other.notificationAvatarUrl == notificationAvatarUrl) &&
            (identical(other.notificationAvatarName, notificationAvatarName) ||
                other.notificationAvatarName == notificationAvatarName) &&
            (identical(other.notificationTitle, notificationTitle) ||
                other.notificationTitle == notificationTitle) &&
            (identical(other.notificationBody, notificationBody) ||
                other.notificationBody == notificationBody));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      timestamp,
      notificationId,
      messageCategory,
      payload,
      sessionId,
      ack,
      notificationAvatarUrl,
      notificationAvatarName,
      notificationTitle,
      notificationBody);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShimWebsocketMessageImplCopyWith<_$ShimWebsocketMessageImpl>
      get copyWith =>
          __$$ShimWebsocketMessageImplCopyWithImpl<_$ShimWebsocketMessageImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShimWebsocketMessageImplToJson(
      this,
    );
  }
}

abstract class _ShimWebsocketMessage implements ShimWebsocketMessage {
  const factory _ShimWebsocketMessage(
      {required final int timestamp,
      required final String notificationId,
      required final String messageCategory,
      required final String payload,
      required final String sessionId,
      @StringToBoolConverter() final bool ack,
      @JsonKey(name: 'avatarUrl') final String? notificationAvatarUrl,
      @JsonKey(name: 'avatarName') final String? notificationAvatarName,
      @JsonKey(name: 'title')
      @StringToHtmlUnescapedStringConverter()
      final String? notificationTitle,
      @JsonKey(name: 'body')
      @StringToHtmlUnescapedStringConverter()
      final String? notificationBody}) = _$ShimWebsocketMessageImpl;

  factory _ShimWebsocketMessage.fromJson(Map<String, dynamic> json) =
      _$ShimWebsocketMessageImpl.fromJson;

  @override
  int get timestamp;
  @override
  String get notificationId;
  @override
  String get messageCategory;
  @override
  String get payload;
  @override
  String get sessionId;
  @override
  @StringToBoolConverter()
  bool get ack;
  @override
  @JsonKey(name: 'avatarUrl')
  String? get notificationAvatarUrl;
  @override
  @JsonKey(name: 'avatarName')
  String? get notificationAvatarName;
  @override
  @JsonKey(name: 'title')
  @StringToHtmlUnescapedStringConverter()
  String? get notificationTitle;
  @override // @Default(false) @StringToBoolConverter() bool ack,
  @JsonKey(name: 'body')
  @StringToHtmlUnescapedStringConverter()
  String? get notificationBody;
  @override
  @JsonKey(ignore: true)
  _$$ShimWebsocketMessageImplCopyWith<_$ShimWebsocketMessageImpl>
      get copyWith => throw _privateConstructorUsedError;
}
