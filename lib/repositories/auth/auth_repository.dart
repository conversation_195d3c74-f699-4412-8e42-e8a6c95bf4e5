import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/api_error.dart';

import 'package:x1440/api/dtos/refresh_salesforce_token_response.dart';

import 'package:x1440/api/dtos/salesforce_token_body.dart';
import 'package:x1440/api/dtos/salesforce_token_response.dart';
import 'package:x1440/api/salesforce/dtos/oauth_response.dart';

import 'package:x1440/api/salesforce/dtos/revoke_token_body.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';

abstract class AuthRepository {
  Future<Result<SalesforceTokenResponse, ApiError>>
      authShimServiceFromSalesforce(SalesforceTokenBody body);

  Future<Result<RefreshSalesforceTokenResponse, ApiError>>
      refreshSalesforceToken(String refreshToken);

  Future<Result<bool, ApiError>> logoutFromSalesForce(
      RevokeTokenBody body, SalesforceConfig sfConfig);

  Future<Result<OAuthResponse, ApiError>> loginToSalesforce(
      SalesforceConfig sfConfig, String language);
}
