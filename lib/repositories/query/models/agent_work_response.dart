import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/salesforce/dtos/search_records_response.dart';

part 'agent_work_response.freezed.dart';
part 'agent_work_response.g.dart';
@freezed
class AgentWorkResponse with _$AgentWorkResponse {
  const factory AgentWorkResponse({
    Attributes? attributes,
    @J<PERSON><PERSON><PERSON>(name: 'Id') String? id,
    @JsonKey(name: 'Status') String? status,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'LastModifiedDate') String? lastModifiedDate,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'WorkItemId') String? workItemId,
}) = _AgentWorkResponse;

  factory AgentWorkResponse.fromJson(Map<String, dynamic> json) =>
      _$AgentWorkResponseFromJson(json);
}