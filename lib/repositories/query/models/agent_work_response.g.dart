// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_work_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AgentWorkResponseImpl _$$AgentWorkResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AgentWorkResponseImpl(
      attributes: json['attributes'] == null
          ? null
          : Attributes.fromJson(json['attributes'] as Map<String, dynamic>),
      id: json['Id'] as String?,
      status: json['Status'] as String?,
      lastModifiedDate: json['LastModifiedDate'] as String?,
      workItemId: json['WorkItemId'] as String?,
    );

Map<String, dynamic> _$$AgentWorkResponseImplToJson(
        _$AgentWorkResponseImpl instance) =>
    <String, dynamic>{
      'attributes': instance.attributes?.toJson(),
      'Id': instance.id,
      'Status': instance.status,
      'LastModifiedDate': instance.lastModifiedDate,
      'WorkItemId': instance.workItemId,
    };
