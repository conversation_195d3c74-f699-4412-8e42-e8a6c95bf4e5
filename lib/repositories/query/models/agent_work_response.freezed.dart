// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'agent_work_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AgentWorkResponse _$AgentWorkResponseFromJson(Map<String, dynamic> json) {
  return _AgentWorkResponse.fromJson(json);
}

/// @nodoc
mixin _$AgentWorkResponse {
  Attributes? get attributes => throw _privateConstructorUsedError;
  @JsonKey(name: 'Id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'Status')
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'LastModifiedDate')
  String? get lastModifiedDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'WorkItemId')
  String? get workItemId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AgentWorkResponseCopyWith<AgentWorkResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AgentWorkResponseCopyWith<$Res> {
  factory $AgentWorkResponseCopyWith(
          AgentWorkResponse value, $Res Function(AgentWorkResponse) then) =
      _$AgentWorkResponseCopyWithImpl<$Res, AgentWorkResponse>;
  @useResult
  $Res call(
      {Attributes? attributes,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Status') String? status,
      @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
      @JsonKey(name: 'WorkItemId') String? workItemId});

  $AttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class _$AgentWorkResponseCopyWithImpl<$Res, $Val extends AgentWorkResponse>
    implements $AgentWorkResponseCopyWith<$Res> {
  _$AgentWorkResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = freezed,
    Object? id = freezed,
    Object? status = freezed,
    Object? lastModifiedDate = freezed,
    Object? workItemId = freezed,
  }) {
    return _then(_value.copyWith(
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _value.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      workItemId: freezed == workItemId
          ? _value.workItemId
          : workItemId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AttributesCopyWith<$Res>? get attributes {
    if (_value.attributes == null) {
      return null;
    }

    return $AttributesCopyWith<$Res>(_value.attributes!, (value) {
      return _then(_value.copyWith(attributes: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AgentWorkResponseImplCopyWith<$Res>
    implements $AgentWorkResponseCopyWith<$Res> {
  factory _$$AgentWorkResponseImplCopyWith(_$AgentWorkResponseImpl value,
          $Res Function(_$AgentWorkResponseImpl) then) =
      __$$AgentWorkResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Attributes? attributes,
      @JsonKey(name: 'Id') String? id,
      @JsonKey(name: 'Status') String? status,
      @JsonKey(name: 'LastModifiedDate') String? lastModifiedDate,
      @JsonKey(name: 'WorkItemId') String? workItemId});

  @override
  $AttributesCopyWith<$Res>? get attributes;
}

/// @nodoc
class __$$AgentWorkResponseImplCopyWithImpl<$Res>
    extends _$AgentWorkResponseCopyWithImpl<$Res, _$AgentWorkResponseImpl>
    implements _$$AgentWorkResponseImplCopyWith<$Res> {
  __$$AgentWorkResponseImplCopyWithImpl(_$AgentWorkResponseImpl _value,
      $Res Function(_$AgentWorkResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attributes = freezed,
    Object? id = freezed,
    Object? status = freezed,
    Object? lastModifiedDate = freezed,
    Object? workItemId = freezed,
  }) {
    return _then(_$AgentWorkResponseImpl(
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Attributes?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _value.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      workItemId: freezed == workItemId
          ? _value.workItemId
          : workItemId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AgentWorkResponseImpl implements _AgentWorkResponse {
  const _$AgentWorkResponseImpl(
      {this.attributes,
      @JsonKey(name: 'Id') this.id,
      @JsonKey(name: 'Status') this.status,
      @JsonKey(name: 'LastModifiedDate') this.lastModifiedDate,
      @JsonKey(name: 'WorkItemId') this.workItemId});

  factory _$AgentWorkResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AgentWorkResponseImplFromJson(json);

  @override
  final Attributes? attributes;
  @override
  @JsonKey(name: 'Id')
  final String? id;
  @override
  @JsonKey(name: 'Status')
  final String? status;
  @override
  @JsonKey(name: 'LastModifiedDate')
  final String? lastModifiedDate;
  @override
  @JsonKey(name: 'WorkItemId')
  final String? workItemId;

  @override
  String toString() {
    return 'AgentWorkResponse(attributes: $attributes, id: $id, status: $status, lastModifiedDate: $lastModifiedDate, workItemId: $workItemId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AgentWorkResponseImpl &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.workItemId, workItemId) ||
                other.workItemId == workItemId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, attributes, id, status, lastModifiedDate, workItemId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AgentWorkResponseImplCopyWith<_$AgentWorkResponseImpl> get copyWith =>
      __$$AgentWorkResponseImplCopyWithImpl<_$AgentWorkResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AgentWorkResponseImplToJson(
      this,
    );
  }
}

abstract class _AgentWorkResponse implements AgentWorkResponse {
  const factory _AgentWorkResponse(
          {final Attributes? attributes,
          @JsonKey(name: 'Id') final String? id,
          @JsonKey(name: 'Status') final String? status,
          @JsonKey(name: 'LastModifiedDate') final String? lastModifiedDate,
          @JsonKey(name: 'WorkItemId') final String? workItemId}) =
      _$AgentWorkResponseImpl;

  factory _AgentWorkResponse.fromJson(Map<String, dynamic> json) =
      _$AgentWorkResponseImpl.fromJson;

  @override
  Attributes? get attributes;
  @override
  @JsonKey(name: 'Id')
  String? get id;
  @override
  @JsonKey(name: 'Status')
  String? get status;
  @override
  @JsonKey(name: 'LastModifiedDate')
  String? get lastModifiedDate;
  @override
  @JsonKey(name: 'WorkItemId')
  String? get workItemId;
  @override
  @JsonKey(ignore: true)
  _$$AgentWorkResponseImplCopyWith<_$AgentWorkResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
