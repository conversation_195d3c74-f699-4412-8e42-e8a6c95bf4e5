import 'dart:io';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/api/converters/file_to_path_converter.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'pending_outbound_message.freezed.dart';
part 'pending_outbound_message.g.dart';

@freezed
@collectionOnFreezed
class PendingOutboundMessage with _$PendingOutboundMessage {
  Id get localDbId => Isar.autoIncrement;

  const PendingOutboundMessage._();

  const factory PendingOutboundMessage({
    String? contactId,
    // String? workTargetId,
    required String messageId,
    String? messageContent,
    // String? messagingEndUserId,
    @Default(<String>[]) @FileToPathConverter() List<String> filePathsToAttach,
  }) = _PendingOutboundMessage;

  @ignore
  List<File> get filesToAttach =>
      const FileToPathConverter().fromJson(filePathsToAttach);

  @ignore
  MessageEntryType get messageEntryType => filePathsToAttach.isNotEmpty
      ? MessageEntryType.media
      : MessageEntryType.text;

  factory PendingOutboundMessage.fromJson(Map<String, dynamic> json) =>
      _$PendingOutboundMessageFromJson(json);

  static PendingOutboundMessage fromQueueSendMessage(QueueSendMessage message) => PendingOutboundMessage(
    messageId: message.messageId,
    messageContent: message.messageContent,
    filePathsToAttach: message.filePathsToAttach,
  );
}
