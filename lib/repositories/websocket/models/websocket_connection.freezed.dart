// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'websocket_connection.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WebsocketConnection {
  WebsocketConnectionState get connectionState =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WebsocketConnectionCopyWith<WebsocketConnection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebsocketConnectionCopyWith<$Res> {
  factory $WebsocketConnectionCopyWith(
          WebsocketConnection value, $Res Function(WebsocketConnection) then) =
      _$WebsocketConnectionCopyWithImpl<$Res, WebsocketConnection>;
  @useResult
  $Res call({WebsocketConnectionState connectionState});
}

/// @nodoc
class _$WebsocketConnectionCopyWithImpl<$Res, $Val extends WebsocketConnection>
    implements $WebsocketConnectionCopyWith<$Res> {
  _$WebsocketConnectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? connectionState = null,
  }) {
    return _then(_value.copyWith(
      connectionState: null == connectionState
          ? _value.connectionState
          : connectionState // ignore: cast_nullable_to_non_nullable
              as WebsocketConnectionState,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebsocketConnectionImplCopyWith<$Res>
    implements $WebsocketConnectionCopyWith<$Res> {
  factory _$$WebsocketConnectionImplCopyWith(_$WebsocketConnectionImpl value,
          $Res Function(_$WebsocketConnectionImpl) then) =
      __$$WebsocketConnectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({WebsocketConnectionState connectionState});
}

/// @nodoc
class __$$WebsocketConnectionImplCopyWithImpl<$Res>
    extends _$WebsocketConnectionCopyWithImpl<$Res, _$WebsocketConnectionImpl>
    implements _$$WebsocketConnectionImplCopyWith<$Res> {
  __$$WebsocketConnectionImplCopyWithImpl(_$WebsocketConnectionImpl _value,
      $Res Function(_$WebsocketConnectionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? connectionState = null,
  }) {
    return _then(_$WebsocketConnectionImpl(
      connectionState: null == connectionState
          ? _value.connectionState
          : connectionState // ignore: cast_nullable_to_non_nullable
              as WebsocketConnectionState,
    ));
  }
}

/// @nodoc

class _$WebsocketConnectionImpl implements _WebsocketConnection {
  const _$WebsocketConnectionImpl(
      {this.connectionState = WebsocketConnectionState.disconnected});

  @override
  @JsonKey()
  final WebsocketConnectionState connectionState;

  @override
  String toString() {
    return 'WebsocketConnection(connectionState: $connectionState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebsocketConnectionImpl &&
            (identical(other.connectionState, connectionState) ||
                other.connectionState == connectionState));
  }

  @override
  int get hashCode => Object.hash(runtimeType, connectionState);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WebsocketConnectionImplCopyWith<_$WebsocketConnectionImpl> get copyWith =>
      __$$WebsocketConnectionImplCopyWithImpl<_$WebsocketConnectionImpl>(
          this, _$identity);
}

abstract class _WebsocketConnection implements WebsocketConnection {
  const factory _WebsocketConnection(
          {final WebsocketConnectionState connectionState}) =
      _$WebsocketConnectionImpl;

  @override
  WebsocketConnectionState get connectionState;
  @override
  @JsonKey(ignore: true)
  _$$WebsocketConnectionImplCopyWith<_$WebsocketConnectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
