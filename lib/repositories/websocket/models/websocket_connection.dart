import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:web_socket_client/src/connection_state.dart';

part 'websocket_connection.freezed.dart';

enum WebsocketConnectionState { connecting, connected, reconnecting, reconnected, disconnecting, disconnected;

  bool get isConnected => this == WebsocketConnectionState.connected || this == WebsocketConnectionState.reconnected;

  bool get isConnecting => this == WebsocketConnectionState.connecting || this == WebsocketConnectionState.reconnecting;

  static fromConnection(ConnectionState event) {
    if (event is Connecting) {
      return WebsocketConnectionState.connecting;
    } else if (event is Connected) {
      return WebsocketConnectionState.connected;
    } else if (event is Reconnecting) {
      return WebsocketConnectionState.reconnecting;
    } else if (event is Reconnected) {
      return WebsocketConnectionState.reconnected;
    } else if (event is Disconnecting) {
      return WebsocketConnectionState.disconnecting;
    } else if (event is Disconnected) {
      return WebsocketConnectionState.disconnected;
    }
  }
}

@freezed
class WebsocketConnection with _$WebsocketConnection {
  const factory WebsocketConnection({
    @Default(WebsocketConnectionState.disconnected) WebsocketConnectionState connectionState}) = _WebsocketConnection;

  static WebsocketConnection fromConnection(ConnectionState event) => WebsocketConnection(
        connectionState: WebsocketConnectionState.fromConnection(event));
}
