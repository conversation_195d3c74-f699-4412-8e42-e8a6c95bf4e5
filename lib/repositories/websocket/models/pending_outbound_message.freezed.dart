// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pending_outbound_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PendingOutboundMessage _$PendingOutboundMessageFromJson(
    Map<String, dynamic> json) {
  return _PendingOutboundMessage.fromJson(json);
}

/// @nodoc
mixin _$PendingOutboundMessage {
  String? get contactId =>
      throw _privateConstructorUsedError; // String? workTargetId,
  String get messageId => throw _privateConstructorUsedError;
  String? get messageContent =>
      throw _privateConstructorUsedError; // String? messagingEndUserId,
  @FileToPathConverter()
  List<String> get filePathsToAttach => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PendingOutboundMessageCopyWith<PendingOutboundMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PendingOutboundMessageCopyWith<$Res> {
  factory $PendingOutboundMessageCopyWith(PendingOutboundMessage value,
          $Res Function(PendingOutboundMessage) then) =
      _$PendingOutboundMessageCopyWithImpl<$Res, PendingOutboundMessage>;
  @useResult
  $Res call(
      {String? contactId,
      String messageId,
      String? messageContent,
      @FileToPathConverter() List<String> filePathsToAttach});
}

/// @nodoc
class _$PendingOutboundMessageCopyWithImpl<$Res,
        $Val extends PendingOutboundMessage>
    implements $PendingOutboundMessageCopyWith<$Res> {
  _$PendingOutboundMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = freezed,
    Object? messageId = null,
    Object? messageContent = freezed,
    Object? filePathsToAttach = null,
  }) {
    return _then(_value.copyWith(
      contactId: freezed == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as String?,
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messageContent: freezed == messageContent
          ? _value.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      filePathsToAttach: null == filePathsToAttach
          ? _value.filePathsToAttach
          : filePathsToAttach // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PendingOutboundMessageImplCopyWith<$Res>
    implements $PendingOutboundMessageCopyWith<$Res> {
  factory _$$PendingOutboundMessageImplCopyWith(
          _$PendingOutboundMessageImpl value,
          $Res Function(_$PendingOutboundMessageImpl) then) =
      __$$PendingOutboundMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? contactId,
      String messageId,
      String? messageContent,
      @FileToPathConverter() List<String> filePathsToAttach});
}

/// @nodoc
class __$$PendingOutboundMessageImplCopyWithImpl<$Res>
    extends _$PendingOutboundMessageCopyWithImpl<$Res,
        _$PendingOutboundMessageImpl>
    implements _$$PendingOutboundMessageImplCopyWith<$Res> {
  __$$PendingOutboundMessageImplCopyWithImpl(
      _$PendingOutboundMessageImpl _value,
      $Res Function(_$PendingOutboundMessageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactId = freezed,
    Object? messageId = null,
    Object? messageContent = freezed,
    Object? filePathsToAttach = null,
  }) {
    return _then(_$PendingOutboundMessageImpl(
      contactId: freezed == contactId
          ? _value.contactId
          : contactId // ignore: cast_nullable_to_non_nullable
              as String?,
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      messageContent: freezed == messageContent
          ? _value.messageContent
          : messageContent // ignore: cast_nullable_to_non_nullable
              as String?,
      filePathsToAttach: null == filePathsToAttach
          ? _value._filePathsToAttach
          : filePathsToAttach // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PendingOutboundMessageImpl extends _PendingOutboundMessage {
  const _$PendingOutboundMessageImpl(
      {this.contactId,
      required this.messageId,
      this.messageContent,
      @FileToPathConverter()
      final List<String> filePathsToAttach = const <String>[]})
      : _filePathsToAttach = filePathsToAttach,
        super._();

  factory _$PendingOutboundMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$PendingOutboundMessageImplFromJson(json);

  @override
  final String? contactId;
// String? workTargetId,
  @override
  final String messageId;
  @override
  final String? messageContent;
// String? messagingEndUserId,
  final List<String> _filePathsToAttach;
// String? messagingEndUserId,
  @override
  @JsonKey()
  @FileToPathConverter()
  List<String> get filePathsToAttach {
    if (_filePathsToAttach is EqualUnmodifiableListView)
      return _filePathsToAttach;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filePathsToAttach);
  }

  @override
  String toString() {
    return 'PendingOutboundMessage(contactId: $contactId, messageId: $messageId, messageContent: $messageContent, filePathsToAttach: $filePathsToAttach)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PendingOutboundMessageImpl &&
            (identical(other.contactId, contactId) ||
                other.contactId == contactId) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.messageContent, messageContent) ||
                other.messageContent == messageContent) &&
            const DeepCollectionEquality()
                .equals(other._filePathsToAttach, _filePathsToAttach));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, contactId, messageId,
      messageContent, const DeepCollectionEquality().hash(_filePathsToAttach));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PendingOutboundMessageImplCopyWith<_$PendingOutboundMessageImpl>
      get copyWith => __$$PendingOutboundMessageImplCopyWithImpl<
          _$PendingOutboundMessageImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PendingOutboundMessageImplToJson(
      this,
    );
  }
}

abstract class _PendingOutboundMessage extends PendingOutboundMessage {
  const factory _PendingOutboundMessage(
          {final String? contactId,
          required final String messageId,
          final String? messageContent,
          @FileToPathConverter() final List<String> filePathsToAttach}) =
      _$PendingOutboundMessageImpl;
  const _PendingOutboundMessage._() : super._();

  factory _PendingOutboundMessage.fromJson(Map<String, dynamic> json) =
      _$PendingOutboundMessageImpl.fromJson;

  @override
  String? get contactId;
  @override // String? workTargetId,
  String get messageId;
  @override
  String? get messageContent;
  @override // String? messagingEndUserId,
  @FileToPathConverter()
  List<String> get filePathsToAttach;
  @override
  @JsonKey(ignore: true)
  _$$PendingOutboundMessageImplCopyWith<_$PendingOutboundMessageImpl>
      get copyWith => throw _privateConstructorUsedError;
}
