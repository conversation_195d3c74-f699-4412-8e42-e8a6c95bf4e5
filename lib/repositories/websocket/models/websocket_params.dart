import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:web_socket_client/web_socket_client.dart';

part 'websocket_params.freezed.dart';

@freezed
class WebsocketParams with _$WebsocketParams {
  const factory WebsocketParams({
    Uri? uri,
    Map<String, dynamic>? headers,
    Iterable<String>? protocols,
    Duration? pingInterval,
    Backoff? backoff,
    Duration? timeout,
    String? binaryType,
  }) = _WebsocketParams;
}
