// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'websocket_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WebsocketParams {
  Uri? get uri => throw _privateConstructorUsedError;
  Map<String, dynamic>? get headers => throw _privateConstructorUsedError;
  Iterable<String>? get protocols => throw _privateConstructorUsedError;
  Duration? get pingInterval => throw _privateConstructorUsedError;
  Backoff? get backoff => throw _privateConstructorUsedError;
  Duration? get timeout => throw _privateConstructorUsedError;
  String? get binaryType => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WebsocketParamsCopyWith<WebsocketParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WebsocketParamsCopyWith<$Res> {
  factory $WebsocketParamsCopyWith(
          WebsocketParams value, $Res Function(WebsocketParams) then) =
      _$WebsocketParamsCopyWithImpl<$Res, WebsocketParams>;
  @useResult
  $Res call(
      {Uri? uri,
      Map<String, dynamic>? headers,
      Iterable<String>? protocols,
      Duration? pingInterval,
      Backoff? backoff,
      Duration? timeout,
      String? binaryType});
}

/// @nodoc
class _$WebsocketParamsCopyWithImpl<$Res, $Val extends WebsocketParams>
    implements $WebsocketParamsCopyWith<$Res> {
  _$WebsocketParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uri = freezed,
    Object? headers = freezed,
    Object? protocols = freezed,
    Object? pingInterval = freezed,
    Object? backoff = freezed,
    Object? timeout = freezed,
    Object? binaryType = freezed,
  }) {
    return _then(_value.copyWith(
      uri: freezed == uri
          ? _value.uri
          : uri // ignore: cast_nullable_to_non_nullable
              as Uri?,
      headers: freezed == headers
          ? _value.headers
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      protocols: freezed == protocols
          ? _value.protocols
          : protocols // ignore: cast_nullable_to_non_nullable
              as Iterable<String>?,
      pingInterval: freezed == pingInterval
          ? _value.pingInterval
          : pingInterval // ignore: cast_nullable_to_non_nullable
              as Duration?,
      backoff: freezed == backoff
          ? _value.backoff
          : backoff // ignore: cast_nullable_to_non_nullable
              as Backoff?,
      timeout: freezed == timeout
          ? _value.timeout
          : timeout // ignore: cast_nullable_to_non_nullable
              as Duration?,
      binaryType: freezed == binaryType
          ? _value.binaryType
          : binaryType // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WebsocketParamsImplCopyWith<$Res>
    implements $WebsocketParamsCopyWith<$Res> {
  factory _$$WebsocketParamsImplCopyWith(_$WebsocketParamsImpl value,
          $Res Function(_$WebsocketParamsImpl) then) =
      __$$WebsocketParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Uri? uri,
      Map<String, dynamic>? headers,
      Iterable<String>? protocols,
      Duration? pingInterval,
      Backoff? backoff,
      Duration? timeout,
      String? binaryType});
}

/// @nodoc
class __$$WebsocketParamsImplCopyWithImpl<$Res>
    extends _$WebsocketParamsCopyWithImpl<$Res, _$WebsocketParamsImpl>
    implements _$$WebsocketParamsImplCopyWith<$Res> {
  __$$WebsocketParamsImplCopyWithImpl(
      _$WebsocketParamsImpl _value, $Res Function(_$WebsocketParamsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uri = freezed,
    Object? headers = freezed,
    Object? protocols = freezed,
    Object? pingInterval = freezed,
    Object? backoff = freezed,
    Object? timeout = freezed,
    Object? binaryType = freezed,
  }) {
    return _then(_$WebsocketParamsImpl(
      uri: freezed == uri
          ? _value.uri
          : uri // ignore: cast_nullable_to_non_nullable
              as Uri?,
      headers: freezed == headers
          ? _value._headers
          : headers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      protocols: freezed == protocols
          ? _value.protocols
          : protocols // ignore: cast_nullable_to_non_nullable
              as Iterable<String>?,
      pingInterval: freezed == pingInterval
          ? _value.pingInterval
          : pingInterval // ignore: cast_nullable_to_non_nullable
              as Duration?,
      backoff: freezed == backoff
          ? _value.backoff
          : backoff // ignore: cast_nullable_to_non_nullable
              as Backoff?,
      timeout: freezed == timeout
          ? _value.timeout
          : timeout // ignore: cast_nullable_to_non_nullable
              as Duration?,
      binaryType: freezed == binaryType
          ? _value.binaryType
          : binaryType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$WebsocketParamsImpl implements _WebsocketParams {
  const _$WebsocketParamsImpl(
      {this.uri,
      final Map<String, dynamic>? headers,
      this.protocols,
      this.pingInterval,
      this.backoff,
      this.timeout,
      this.binaryType})
      : _headers = headers;

  @override
  final Uri? uri;
  final Map<String, dynamic>? _headers;
  @override
  Map<String, dynamic>? get headers {
    final value = _headers;
    if (value == null) return null;
    if (_headers is EqualUnmodifiableMapView) return _headers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final Iterable<String>? protocols;
  @override
  final Duration? pingInterval;
  @override
  final Backoff? backoff;
  @override
  final Duration? timeout;
  @override
  final String? binaryType;

  @override
  String toString() {
    return 'WebsocketParams(uri: $uri, headers: $headers, protocols: $protocols, pingInterval: $pingInterval, backoff: $backoff, timeout: $timeout, binaryType: $binaryType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WebsocketParamsImpl &&
            (identical(other.uri, uri) || other.uri == uri) &&
            const DeepCollectionEquality().equals(other._headers, _headers) &&
            const DeepCollectionEquality().equals(other.protocols, protocols) &&
            (identical(other.pingInterval, pingInterval) ||
                other.pingInterval == pingInterval) &&
            (identical(other.backoff, backoff) || other.backoff == backoff) &&
            (identical(other.timeout, timeout) || other.timeout == timeout) &&
            (identical(other.binaryType, binaryType) ||
                other.binaryType == binaryType));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      uri,
      const DeepCollectionEquality().hash(_headers),
      const DeepCollectionEquality().hash(protocols),
      pingInterval,
      backoff,
      timeout,
      binaryType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WebsocketParamsImplCopyWith<_$WebsocketParamsImpl> get copyWith =>
      __$$WebsocketParamsImplCopyWithImpl<_$WebsocketParamsImpl>(
          this, _$identity);
}

abstract class _WebsocketParams implements WebsocketParams {
  const factory _WebsocketParams(
      {final Uri? uri,
      final Map<String, dynamic>? headers,
      final Iterable<String>? protocols,
      final Duration? pingInterval,
      final Backoff? backoff,
      final Duration? timeout,
      final String? binaryType}) = _$WebsocketParamsImpl;

  @override
  Uri? get uri;
  @override
  Map<String, dynamic>? get headers;
  @override
  Iterable<String>? get protocols;
  @override
  Duration? get pingInterval;
  @override
  Backoff? get backoff;
  @override
  Duration? get timeout;
  @override
  String? get binaryType;
  @override
  @JsonKey(ignore: true)
  _$$WebsocketParamsImplCopyWith<_$WebsocketParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
