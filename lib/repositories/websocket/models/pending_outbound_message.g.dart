// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pending_outbound_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PendingOutboundMessageImpl _$$PendingOutboundMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$PendingOutboundMessageImpl(
      contactId: json['contactId'] as String?,
      messageId: json['messageId'] as String,
      messageContent: json['messageContent'] as String?,
      filePathsToAttach: (json['filePathsToAttach'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const <String>[],
    );

Map<String, dynamic> _$$PendingOutboundMessageImplToJson(
        _$PendingOutboundMessageImpl instance) =>
    <String, dynamic>{
      'contactId': instance.contactId,
      'messageId': instance.messageId,
      'messageContent': instance.messageContent,
      'filePathsToAttach': instance.filePathsToAttach,
    };
