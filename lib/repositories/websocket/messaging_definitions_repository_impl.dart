import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/api/dtos/channel_messaging_definitions_response.dart';
import 'package:x1440/api/dtos/messaging_end_user_filters_response.dart';
import 'package:x1440/api/shim_service_api.dart';
import 'package:x1440/models/sf_id.dart';

import 'messaging_definitions_repository.dart';

class MessagingDefinitionsRepositoryImpl
    implements MessagingDefinitionsRepository {
  final ShimServiceApi api;

  MessagingDefinitionsRepositoryImpl(this.api);

  @override
  Future<Result<ChannelMessagingDefinitionsResponse, ApiError>>
      getChannelMessagingDefinitions(SfId messagingChannelId) async {
    try {
      final ChannelMessagingDefinitionsResponse response =
          await api.getChannelMessagingDefinitions(messagingChannelId.toString());
      return Success(response);
    } catch (error) {
      return Error(ApiError.createError(error as Exception));
    }
  }

  @override
  Future<Result<MessagingEndUserFiltersResponse, ApiError>>
      getMessagingEndUserFilters(SfId messagingEndUserId) async {
    try {
      final MessagingEndUserFiltersResponse response =
          await api.getMessagingEndUserFilters(messagingEndUserId.toString());
      return Success(response);
    } catch (error) {
      return Error(ApiError.createError(error as Exception));
    }
  }
}
