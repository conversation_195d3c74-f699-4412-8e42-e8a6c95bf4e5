import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/ui/blocs/contacts/contacts_bloc.dart';
import 'package:x1440/ui/blocs/contacts/contacts_state.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/ui/modals/base_modal_sheet.dart';

Future<bool?> markDoneConversationModalBottomSheet(
        {required BuildContext context,
        required LakConversation conversation,
        bool popOnConfirm = false}) async =>
    show1440ModalBottomSheet<bool?>(
        useRootNavigator: true,
        context: context,
        builder: (_) => BlocProvider.value(
              value: GetIt.I<ContactsBloc>(),
              child:
                  BlocBuilder<ContactsBloc, ContactsState>(builder: (_, state) {
                var contact = state.getContactByMeu(conversation.sfId);
                return ConfirmModalView(
                  title: S.of(context).mark_conversation_as_done_title(
                      contact?.name ?? conversation.username ?? ''),
                  confirmText: S.of(context).modal_end_cta,
                  cancelText: S.of(context).modal_cancel_cta,
                );
              }),
            )).then((value) {
      if (value == true) {
        if (popOnConfirm) {
          Navigator.of(context).pop();
        }
        GetIt.I<ConversationsBloc>().add(EndConversationEvent(conversation.sfId));
      }
      return value;
    });
