import 'package:flutter/material.dart';
import 'package:x1440/ui/themes/themeConstants.dart';
import 'package:x1440/utils/extensions/buildContext_extension.dart';

Future<T?> show1440ModalBottomSheet<T>(
    {required BuildContext context,
    required Widget Function(BuildContext) builder,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
    BoxConstraints? constraints,
    Color? barrierColor,
    Color? backgroundColor,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool showDismissBar = false}) {
  isScrollControlled = isScrollControlled ||
      MediaQuery.of(context).size.height <
          600; // TODO: standardize this app-wide
  return showModalBottomSheet(
    context: context,
    isScrollControlled: isScrollControlled,
    useRootNavigator: useRootNavigator,
    builder: (context) => BaseModalView(
      isScrollControlled: isScrollControlled,
      showDismissBar: showDismissBar,
      backgroundColor: backgroundColor,
      child: builder(context),
    ),
  );
}

class BaseModalView extends StatelessWidget {
  final Widget child;
  final bool showDismissBar;
  final bool isScrollControlled;
  final Color? backgroundColor;

  const BaseModalView({
    super.key,
    required this.child,
    this.showDismissBar = false,
    this.isScrollControlled = false,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    Widget wrappedChild = Container(
      // margin: EdgeInsets.only(bottom: 3.uiUnit),
      padding: EdgeInsets.only(
        // top: isScrollControlled ? 4.uiUnit : 1.uiUnit,
        top: 1.uiUnit,

        /// setting this instead of the SafeArea to keep the color consistent ... may want to revisit if modal bottom spacing is off what we want
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? context.theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(ThemeConstants.tightBorderRadius),
        ),
      ),
      child: child,
    );

    return Material(
      type: MaterialType.transparency,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (isScrollControlled) SizedBox(height: 8.uiUnit),
          if (showDismissBar)
            Container(
              height: 1.uiUnit,
              width: 5.uiUnit,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
              ),
            ),
          if (isScrollControlled)
            Flexible(
              child: wrappedChild,
            )
          else
            wrappedChild,
        ],
      ),
    );
  }
}

class ConfirmModalView extends StatelessWidget {
  final String title;
  final String? description;
  final String confirmText;
  final String? cancelText;

  const ConfirmModalView(
      {required this.title,
      required this.confirmText,
      this.cancelText,
      this.description,
      super.key});

  @override
  Widget build(BuildContext context) {
    final double marginHeight = MediaQuery.of(context).size.height > 600
        ? 2.uiUnit
        : 1.uiUnit; // TODO: standardize this app-wide
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 1.uiUnit),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          SizedBox(height: marginHeight),
          Center(
              child: Text(title,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge)),
          if (description != null)
            Container(
                padding: EdgeInsets.only(top: marginHeight),
                child: Text(description!,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium)),
          SizedBox(height: marginHeight),
          const Divider(),
          SizedBox(height: marginHeight),
          ModalConfirmCancelBar(
            cancelText: cancelText,
            confirmText: confirmText,
          ),
          SizedBox(
            height: marginHeight,
          )
        ]),
      ),
    );
  }
}

class ModalConfirmCancelBar<T> extends StatelessWidget {
  final String? cancelText;
  final String confirmText;
  final bool confirmIsActive;
  final T? confirmPopValue;

  const ModalConfirmCancelBar({
    super.key,
    this.cancelText,
    required this.confirmText,
    this.confirmIsActive = true,
    this.confirmPopValue,
  });

  @override
  Widget build(BuildContext context) =>
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        if (cancelText != null) ...[
          // SizedBox(width: 1.uiUnit),
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText!),
            ),
          ),
        ],
        if (cancelText != null) SizedBox(width: 1.uiUnit),
        Expanded(
          child: ElevatedButton(
            onPressed: confirmIsActive == false
                ? null
                : () => Navigator.of(context).pop(confirmPopValue ?? true),
            child: Text(confirmText),
          ),
        ),
        // SizedBox(width: 1.uiUnit),
      ]);
}
