import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:x1440/frameworks/settings/settings_manager.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/ui/themes/themeConstants.dart';
import 'package:x1440/use_cases/models/contact.dart';

import '../../widgets/user_avatars.dart';

class NewContactRowButton extends StatelessWidget {
  const NewContactRowButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) => NewMessageContactListRow(
        onTap: () {
          context.pop(); // pop the new message modal
          context.push('/newContact');
        },
        icon: const Icon(
          Icons.add,
          size: 26,
          color: Color(0xFF706E6B),
        ),
        child: Text(
          S.of(context).new_contact_cta,
          style: Theme.of(context)
              .textTheme
              .titleMedium
              ?.copyWith(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      );
}

class ContactRow extends StatelessWidget {
  final Contact contact;
  const ContactRow({
    required this.contact,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (contact.id == null || contact.id!.toString().isEmpty) return Container();
    var contactName = contact.name ?? '';
    if (GetIt.I<SettingsManager>().state.settings.showDevOptions && contact.mobilePhone?.isNotEmpty == true) {
      contactName += ' - ${contact.mobilePhone}';
    }
    return Column(
      children: [
        NewMessageContactListRow(
            onTap: () => context.pop(contact),
            icon: UserAvatarImage(
              contactPhotoUrl:
                  contact.photoUrl == null ? null : "${contact.photoUrl}",
              username: contact.name,
            ),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    contactName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w400,
                          fontSize: 16,
                        ),
                  ),
                  if (contact.title?.isNotEmpty == true)
                    Text(contact.title ?? '',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              color: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.color
                                  ?.withOpacity(0.6),
                            )),
                ])),
        const Divider(),
      ],
    );
  }
}

class NewMessageContactListRow extends StatelessWidget {
  final Widget _icon;
  final Widget _child;
  final void Function()? onTap;

  const NewMessageContactListRow({
    required Widget icon,
    required Widget child,
    this.onTap,
    super.key,
  })  : _icon = icon,
        _child = child;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque,
        child: SizedBox(
          height: 60,
          child: Row(children: [
            SizedBox(width: 6.uiUnit, child: _icon),
            SizedBox(width: 2.uiUnit),
            Expanded(child: _child)
          ]),
        ),
      );
}

class ContactsListView extends StatelessWidget {
  final List<Contact> contacts;
  const ContactsListView({super.key, required this.contacts});

  @override
  Widget build(BuildContext context) => ListView.builder(
        itemCount: contacts.length + 1,
        itemBuilder: (BuildContext context, int index) {
          if (index > contacts.length - 1) return const NewContactRowButton();
          return ContactRow(contact: contacts[index]);
        },
      );
}
