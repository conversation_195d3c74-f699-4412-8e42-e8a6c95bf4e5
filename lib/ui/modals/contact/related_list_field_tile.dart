import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:x1440/ui/blocs/contact_details/contact_details_bloc.dart';
import 'package:x1440/ui/blocs/contact_details/contact_details_state.dart';

class RelatedListTile extends StatelessWidget {
  final String text;

  const RelatedListTile({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) => BlocBuilder<ContactDetailsBloc, ContactDetailsState>(
  builder: (context, state) => ListTile(
      title: Text(
        text,
        overflow: TextOverflow.ellipsis,
        style: Theme.of(context)
            .textTheme
            .bodySmall!
            .copyWith(fontWeight: FontWeight.bold),
      ),
      trailing: !state.displayContactSFDeeplink
          ? null
          : const Icon(
              Icons.chevron_right,
            ),
      onTap: () async {
        // TODO: deep link!
        // contactViewmodel.launchRelatedListDeeplink(text);
      },
    ));
}
