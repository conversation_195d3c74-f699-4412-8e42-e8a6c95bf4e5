// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'image_size_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ImageSizeState _$ImageSizeStateFromJson(Map<String, dynamic> json) {
  return _ImageSizeState.fromJson(json);
}

/// @nodoc
mixin _$ImageSizeState {
  Map<String, ImageSize> get imageSizes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ImageSizeStateCopyWith<ImageSizeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ImageSizeStateCopyWith<$Res> {
  factory $ImageSizeStateCopyWith(
          ImageSizeState value, $Res Function(ImageSizeState) then) =
      _$ImageSizeStateCopyWithImpl<$Res, ImageSizeState>;
  @useResult
  $Res call({Map<String, ImageSize> imageSizes});
}

/// @nodoc
class _$ImageSizeStateCopyWithImpl<$Res, $Val extends ImageSizeState>
    implements $ImageSizeStateCopyWith<$Res> {
  _$ImageSizeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageSizes = null,
  }) {
    return _then(_value.copyWith(
      imageSizes: null == imageSizes
          ? _value.imageSizes
          : imageSizes // ignore: cast_nullable_to_non_nullable
              as Map<String, ImageSize>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ImageSizeStateImplCopyWith<$Res>
    implements $ImageSizeStateCopyWith<$Res> {
  factory _$$ImageSizeStateImplCopyWith(_$ImageSizeStateImpl value,
          $Res Function(_$ImageSizeStateImpl) then) =
      __$$ImageSizeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<String, ImageSize> imageSizes});
}

/// @nodoc
class __$$ImageSizeStateImplCopyWithImpl<$Res>
    extends _$ImageSizeStateCopyWithImpl<$Res, _$ImageSizeStateImpl>
    implements _$$ImageSizeStateImplCopyWith<$Res> {
  __$$ImageSizeStateImplCopyWithImpl(
      _$ImageSizeStateImpl _value, $Res Function(_$ImageSizeStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageSizes = null,
  }) {
    return _then(_$ImageSizeStateImpl(
      imageSizes: null == imageSizes
          ? _value._imageSizes
          : imageSizes // ignore: cast_nullable_to_non_nullable
              as Map<String, ImageSize>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ImageSizeStateImpl implements _ImageSizeState {
  const _$ImageSizeStateImpl(
      {final Map<String, ImageSize> imageSizes = const <String, ImageSize>{}})
      : _imageSizes = imageSizes;

  factory _$ImageSizeStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ImageSizeStateImplFromJson(json);

  final Map<String, ImageSize> _imageSizes;
  @override
  @JsonKey()
  Map<String, ImageSize> get imageSizes {
    if (_imageSizes is EqualUnmodifiableMapView) return _imageSizes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_imageSizes);
  }

  @override
  String toString() {
    return 'ImageSizeState(imageSizes: $imageSizes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ImageSizeStateImpl &&
            const DeepCollectionEquality()
                .equals(other._imageSizes, _imageSizes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_imageSizes));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ImageSizeStateImplCopyWith<_$ImageSizeStateImpl> get copyWith =>
      __$$ImageSizeStateImplCopyWithImpl<_$ImageSizeStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ImageSizeStateImplToJson(
      this,
    );
  }
}

abstract class _ImageSizeState implements ImageSizeState {
  const factory _ImageSizeState({final Map<String, ImageSize> imageSizes}) =
      _$ImageSizeStateImpl;

  factory _ImageSizeState.fromJson(Map<String, dynamic> json) =
      _$ImageSizeStateImpl.fromJson;

  @override
  Map<String, ImageSize> get imageSizes;
  @override
  @JsonKey(ignore: true)
  _$$ImageSizeStateImplCopyWith<_$ImageSizeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
