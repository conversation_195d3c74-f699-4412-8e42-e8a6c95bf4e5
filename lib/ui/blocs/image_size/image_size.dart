import 'package:freezed_annotation/freezed_annotation.dart';

part 'image_size.freezed.dart';
part 'image_size.g.dart';
@freezed
class ImageSize with _$ImageSize {

  const ImageSize._();
  const factory ImageSize({
    double? height,
    double? width,
  }) = _ImageSize;

  factory ImageSize.fromJson(Map<String, dynamic> json) => _$ImageSizeFromJson(json);

  double? get aspectRatio => width != null && height != null ? width! / height! : null;
}