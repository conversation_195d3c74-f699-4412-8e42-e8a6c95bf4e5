// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_size_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ImageSizeStateImpl _$$ImageSizeStateImplFromJson(Map<String, dynamic> json) =>
    _$ImageSizeStateImpl(
      imageSizes: (json['imageSizes'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry(k, ImageSize.fromJson(e as Map<String, dynamic>)),
          ) ??
          const <String, ImageSize>{},
    );

Map<String, dynamic> _$$ImageSizeStateImplToJson(
        _$ImageSizeStateImpl instance) =>
    <String, dynamic>{
      'imageSizes': instance.imageSizes.map((k, e) => MapEntry(k, e.toJson())),
    };
