// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'image_size.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ImageSize _$ImageSizeFromJson(Map<String, dynamic> json) {
  return _ImageSize.fromJson(json);
}

/// @nodoc
mixin _$ImageSize {
  double? get height => throw _privateConstructorUsedError;
  double? get width => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ImageSizeCopyWith<ImageSize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ImageSizeCopyWith<$Res> {
  factory $ImageSizeCopyWith(ImageSize value, $Res Function(ImageSize) then) =
      _$ImageSizeCopyWithImpl<$Res, ImageSize>;
  @useResult
  $Res call({double? height, double? width});
}

/// @nodoc
class _$ImageSizeCopyWithImpl<$Res, $Val extends ImageSize>
    implements $ImageSizeCopyWith<$Res> {
  _$ImageSizeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = freezed,
    Object? width = freezed,
  }) {
    return _then(_value.copyWith(
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ImageSizeImplCopyWith<$Res>
    implements $ImageSizeCopyWith<$Res> {
  factory _$$ImageSizeImplCopyWith(
          _$ImageSizeImpl value, $Res Function(_$ImageSizeImpl) then) =
      __$$ImageSizeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double? height, double? width});
}

/// @nodoc
class __$$ImageSizeImplCopyWithImpl<$Res>
    extends _$ImageSizeCopyWithImpl<$Res, _$ImageSizeImpl>
    implements _$$ImageSizeImplCopyWith<$Res> {
  __$$ImageSizeImplCopyWithImpl(
      _$ImageSizeImpl _value, $Res Function(_$ImageSizeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? height = freezed,
    Object? width = freezed,
  }) {
    return _then(_$ImageSizeImpl(
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ImageSizeImpl extends _ImageSize {
  const _$ImageSizeImpl({this.height, this.width}) : super._();

  factory _$ImageSizeImpl.fromJson(Map<String, dynamic> json) =>
      _$$ImageSizeImplFromJson(json);

  @override
  final double? height;
  @override
  final double? width;

  @override
  String toString() {
    return 'ImageSize(height: $height, width: $width)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ImageSizeImpl &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.width, width) || other.width == width));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, height, width);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ImageSizeImplCopyWith<_$ImageSizeImpl> get copyWith =>
      __$$ImageSizeImplCopyWithImpl<_$ImageSizeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ImageSizeImplToJson(
      this,
    );
  }
}

abstract class _ImageSize extends ImageSize {
  const factory _ImageSize({final double? height, final double? width}) =
      _$ImageSizeImpl;
  const _ImageSize._() : super._();

  factory _ImageSize.fromJson(Map<String, dynamic> json) =
      _$ImageSizeImpl.fromJson;

  @override
  double? get height;
  @override
  double? get width;
  @override
  @JsonKey(ignore: true)
  _$$ImageSizeImplCopyWith<_$ImageSizeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
