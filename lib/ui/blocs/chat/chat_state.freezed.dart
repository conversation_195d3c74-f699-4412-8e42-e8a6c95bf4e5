// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ChatState {
  @Deprecated('legacy support')
  Conversation? get conversation => throw _privateConstructorUsedError;
  MapEntry<SfId?, Contact>? get savedContact =>
      throw _privateConstructorUsedError;
  bool? get canTransfer => throw _privateConstructorUsedError;

  /// this is to support our legacy LakConversation that doesn't prompt the bloc updates correctly ... so setting 'toggle' value will force the ChatScreen to update // TODO: remove toggle
  bool get toggle => throw _privateConstructorUsedError;
  bool get isFetchingMessagingEndUserStatus =>
      throw _privateConstructorUsedError;
  bool get isFetchingMessagingDefinitions => throw _privateConstructorUsedError;
  MessagingEndUserStatusResponse? get messagingEndUserStatus =>
      throw _privateConstructorUsedError;
  String get messageText => throw _privateConstructorUsedError;
  List<File> get attachedFiles => throw _privateConstructorUsedError;
  MessagingDefinitionStatus get messagingDefinitionStatus =>
      throw _privateConstructorUsedError;
  ChatFieldStatus get chatFieldStatus => throw _privateConstructorUsedError;
  SfId? get messagingEndUserId => throw _privateConstructorUsedError;
  MessagingChannelType? get messagingType => throw _privateConstructorUsedError;
  UiEvent<Nothing>? get navigateToDefinitions =>
      throw _privateConstructorUsedError;
  UiEvent<MessagingException>? get messageDefinitionFailure =>
      throw _privateConstructorUsedError;
  UiEvent<SfId>? get goToConversation => throw _privateConstructorUsedError;
  QuickAction? get mainQuickAction => throw _privateConstructorUsedError;
  List<QuickAction>? get overflowQuickActions =>
      throw _privateConstructorUsedError;
  UiEvent<QuickActionException>? get quickActionException =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ChatStateCopyWith<ChatState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatStateCopyWith<$Res> {
  factory $ChatStateCopyWith(ChatState value, $Res Function(ChatState) then) =
      _$ChatStateCopyWithImpl<$Res, ChatState>;
  @useResult
  $Res call(
      {@Deprecated('legacy support') Conversation? conversation,
      MapEntry<SfId?, Contact>? savedContact,
      bool? canTransfer,
      bool toggle,
      bool isFetchingMessagingEndUserStatus,
      bool isFetchingMessagingDefinitions,
      MessagingEndUserStatusResponse? messagingEndUserStatus,
      String messageText,
      List<File> attachedFiles,
      MessagingDefinitionStatus messagingDefinitionStatus,
      ChatFieldStatus chatFieldStatus,
      SfId? messagingEndUserId,
      MessagingChannelType? messagingType,
      UiEvent<Nothing>? navigateToDefinitions,
      UiEvent<MessagingException>? messageDefinitionFailure,
      UiEvent<SfId>? goToConversation,
      QuickAction? mainQuickAction,
      List<QuickAction>? overflowQuickActions,
      UiEvent<QuickActionException>? quickActionException});

  $MessagingEndUserStatusResponseCopyWith<$Res>? get messagingEndUserStatus;
  $MessagingDefinitionStatusCopyWith<$Res> get messagingDefinitionStatus;
  $SfIdCopyWith<$Res>? get messagingEndUserId;
  $QuickActionCopyWith<$Res>? get mainQuickAction;
}

/// @nodoc
class _$ChatStateCopyWithImpl<$Res, $Val extends ChatState>
    implements $ChatStateCopyWith<$Res> {
  _$ChatStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversation = freezed,
    Object? savedContact = freezed,
    Object? canTransfer = freezed,
    Object? toggle = null,
    Object? isFetchingMessagingEndUserStatus = null,
    Object? isFetchingMessagingDefinitions = null,
    Object? messagingEndUserStatus = freezed,
    Object? messageText = null,
    Object? attachedFiles = null,
    Object? messagingDefinitionStatus = null,
    Object? chatFieldStatus = null,
    Object? messagingEndUserId = freezed,
    Object? messagingType = freezed,
    Object? navigateToDefinitions = freezed,
    Object? messageDefinitionFailure = freezed,
    Object? goToConversation = freezed,
    Object? mainQuickAction = freezed,
    Object? overflowQuickActions = freezed,
    Object? quickActionException = freezed,
  }) {
    return _then(_value.copyWith(
      conversation: freezed == conversation
          ? _value.conversation
          : conversation // ignore: cast_nullable_to_non_nullable
              as Conversation?,
      savedContact: freezed == savedContact
          ? _value.savedContact
          : savedContact // ignore: cast_nullable_to_non_nullable
              as MapEntry<SfId?, Contact>?,
      canTransfer: freezed == canTransfer
          ? _value.canTransfer
          : canTransfer // ignore: cast_nullable_to_non_nullable
              as bool?,
      toggle: null == toggle
          ? _value.toggle
          : toggle // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingMessagingEndUserStatus: null == isFetchingMessagingEndUserStatus
          ? _value.isFetchingMessagingEndUserStatus
          : isFetchingMessagingEndUserStatus // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingMessagingDefinitions: null == isFetchingMessagingDefinitions
          ? _value.isFetchingMessagingDefinitions
          : isFetchingMessagingDefinitions // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingEndUserStatus: freezed == messagingEndUserStatus
          ? _value.messagingEndUserStatus
          : messagingEndUserStatus // ignore: cast_nullable_to_non_nullable
              as MessagingEndUserStatusResponse?,
      messageText: null == messageText
          ? _value.messageText
          : messageText // ignore: cast_nullable_to_non_nullable
              as String,
      attachedFiles: null == attachedFiles
          ? _value.attachedFiles
          : attachedFiles // ignore: cast_nullable_to_non_nullable
              as List<File>,
      messagingDefinitionStatus: null == messagingDefinitionStatus
          ? _value.messagingDefinitionStatus
          : messagingDefinitionStatus // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionStatus,
      chatFieldStatus: null == chatFieldStatus
          ? _value.chatFieldStatus
          : chatFieldStatus // ignore: cast_nullable_to_non_nullable
              as ChatFieldStatus,
      messagingEndUserId: freezed == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingType: freezed == messagingType
          ? _value.messagingType
          : messagingType // ignore: cast_nullable_to_non_nullable
              as MessagingChannelType?,
      navigateToDefinitions: freezed == navigateToDefinitions
          ? _value.navigateToDefinitions
          : navigateToDefinitions // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      messageDefinitionFailure: freezed == messageDefinitionFailure
          ? _value.messageDefinitionFailure
          : messageDefinitionFailure // ignore: cast_nullable_to_non_nullable
              as UiEvent<MessagingException>?,
      goToConversation: freezed == goToConversation
          ? _value.goToConversation
          : goToConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      mainQuickAction: freezed == mainQuickAction
          ? _value.mainQuickAction
          : mainQuickAction // ignore: cast_nullable_to_non_nullable
              as QuickAction?,
      overflowQuickActions: freezed == overflowQuickActions
          ? _value.overflowQuickActions
          : overflowQuickActions // ignore: cast_nullable_to_non_nullable
              as List<QuickAction>?,
      quickActionException: freezed == quickActionException
          ? _value.quickActionException
          : quickActionException // ignore: cast_nullable_to_non_nullable
              as UiEvent<QuickActionException>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserStatusResponseCopyWith<$Res>? get messagingEndUserStatus {
    if (_value.messagingEndUserStatus == null) {
      return null;
    }

    return $MessagingEndUserStatusResponseCopyWith<$Res>(
        _value.messagingEndUserStatus!, (value) {
      return _then(_value.copyWith(messagingEndUserStatus: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionStatusCopyWith<$Res> get messagingDefinitionStatus {
    return $MessagingDefinitionStatusCopyWith<$Res>(
        _value.messagingDefinitionStatus, (value) {
      return _then(_value.copyWith(messagingDefinitionStatus: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUserId {
    if (_value.messagingEndUserId == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.messagingEndUserId!, (value) {
      return _then(_value.copyWith(messagingEndUserId: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $QuickActionCopyWith<$Res>? get mainQuickAction {
    if (_value.mainQuickAction == null) {
      return null;
    }

    return $QuickActionCopyWith<$Res>(_value.mainQuickAction!, (value) {
      return _then(_value.copyWith(mainQuickAction: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ChatStateImplCopyWith<$Res>
    implements $ChatStateCopyWith<$Res> {
  factory _$$ChatStateImplCopyWith(
          _$ChatStateImpl value, $Res Function(_$ChatStateImpl) then) =
      __$$ChatStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@Deprecated('legacy support') Conversation? conversation,
      MapEntry<SfId?, Contact>? savedContact,
      bool? canTransfer,
      bool toggle,
      bool isFetchingMessagingEndUserStatus,
      bool isFetchingMessagingDefinitions,
      MessagingEndUserStatusResponse? messagingEndUserStatus,
      String messageText,
      List<File> attachedFiles,
      MessagingDefinitionStatus messagingDefinitionStatus,
      ChatFieldStatus chatFieldStatus,
      SfId? messagingEndUserId,
      MessagingChannelType? messagingType,
      UiEvent<Nothing>? navigateToDefinitions,
      UiEvent<MessagingException>? messageDefinitionFailure,
      UiEvent<SfId>? goToConversation,
      QuickAction? mainQuickAction,
      List<QuickAction>? overflowQuickActions,
      UiEvent<QuickActionException>? quickActionException});

  @override
  $MessagingEndUserStatusResponseCopyWith<$Res>? get messagingEndUserStatus;
  @override
  $MessagingDefinitionStatusCopyWith<$Res> get messagingDefinitionStatus;
  @override
  $SfIdCopyWith<$Res>? get messagingEndUserId;
  @override
  $QuickActionCopyWith<$Res>? get mainQuickAction;
}

/// @nodoc
class __$$ChatStateImplCopyWithImpl<$Res>
    extends _$ChatStateCopyWithImpl<$Res, _$ChatStateImpl>
    implements _$$ChatStateImplCopyWith<$Res> {
  __$$ChatStateImplCopyWithImpl(
      _$ChatStateImpl _value, $Res Function(_$ChatStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversation = freezed,
    Object? savedContact = freezed,
    Object? canTransfer = freezed,
    Object? toggle = null,
    Object? isFetchingMessagingEndUserStatus = null,
    Object? isFetchingMessagingDefinitions = null,
    Object? messagingEndUserStatus = freezed,
    Object? messageText = null,
    Object? attachedFiles = null,
    Object? messagingDefinitionStatus = null,
    Object? chatFieldStatus = null,
    Object? messagingEndUserId = freezed,
    Object? messagingType = freezed,
    Object? navigateToDefinitions = freezed,
    Object? messageDefinitionFailure = freezed,
    Object? goToConversation = freezed,
    Object? mainQuickAction = freezed,
    Object? overflowQuickActions = freezed,
    Object? quickActionException = freezed,
  }) {
    return _then(_$ChatStateImpl(
      conversation: freezed == conversation
          ? _value.conversation
          : conversation // ignore: cast_nullable_to_non_nullable
              as Conversation?,
      savedContact: freezed == savedContact
          ? _value.savedContact
          : savedContact // ignore: cast_nullable_to_non_nullable
              as MapEntry<SfId?, Contact>?,
      canTransfer: freezed == canTransfer
          ? _value.canTransfer
          : canTransfer // ignore: cast_nullable_to_non_nullable
              as bool?,
      toggle: null == toggle
          ? _value.toggle
          : toggle // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingMessagingEndUserStatus: null == isFetchingMessagingEndUserStatus
          ? _value.isFetchingMessagingEndUserStatus
          : isFetchingMessagingEndUserStatus // ignore: cast_nullable_to_non_nullable
              as bool,
      isFetchingMessagingDefinitions: null == isFetchingMessagingDefinitions
          ? _value.isFetchingMessagingDefinitions
          : isFetchingMessagingDefinitions // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingEndUserStatus: freezed == messagingEndUserStatus
          ? _value.messagingEndUserStatus
          : messagingEndUserStatus // ignore: cast_nullable_to_non_nullable
              as MessagingEndUserStatusResponse?,
      messageText: null == messageText
          ? _value.messageText
          : messageText // ignore: cast_nullable_to_non_nullable
              as String,
      attachedFiles: null == attachedFiles
          ? _value._attachedFiles
          : attachedFiles // ignore: cast_nullable_to_non_nullable
              as List<File>,
      messagingDefinitionStatus: null == messagingDefinitionStatus
          ? _value.messagingDefinitionStatus
          : messagingDefinitionStatus // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionStatus,
      chatFieldStatus: null == chatFieldStatus
          ? _value.chatFieldStatus
          : chatFieldStatus // ignore: cast_nullable_to_non_nullable
              as ChatFieldStatus,
      messagingEndUserId: freezed == messagingEndUserId
          ? _value.messagingEndUserId
          : messagingEndUserId // ignore: cast_nullable_to_non_nullable
              as SfId?,
      messagingType: freezed == messagingType
          ? _value.messagingType
          : messagingType // ignore: cast_nullable_to_non_nullable
              as MessagingChannelType?,
      navigateToDefinitions: freezed == navigateToDefinitions
          ? _value.navigateToDefinitions
          : navigateToDefinitions // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      messageDefinitionFailure: freezed == messageDefinitionFailure
          ? _value.messageDefinitionFailure
          : messageDefinitionFailure // ignore: cast_nullable_to_non_nullable
              as UiEvent<MessagingException>?,
      goToConversation: freezed == goToConversation
          ? _value.goToConversation
          : goToConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      mainQuickAction: freezed == mainQuickAction
          ? _value.mainQuickAction
          : mainQuickAction // ignore: cast_nullable_to_non_nullable
              as QuickAction?,
      overflowQuickActions: freezed == overflowQuickActions
          ? _value._overflowQuickActions
          : overflowQuickActions // ignore: cast_nullable_to_non_nullable
              as List<QuickAction>?,
      quickActionException: freezed == quickActionException
          ? _value.quickActionException
          : quickActionException // ignore: cast_nullable_to_non_nullable
              as UiEvent<QuickActionException>?,
    ));
  }
}

/// @nodoc

class _$ChatStateImpl extends _ChatState {
  const _$ChatStateImpl(
      {@Deprecated('legacy support') this.conversation,
      this.savedContact,
      this.canTransfer = false,
      this.toggle = false,
      this.isFetchingMessagingEndUserStatus = false,
      this.isFetchingMessagingDefinitions = false,
      this.messagingEndUserStatus,
      this.messageText = '',
      final List<File> attachedFiles = const [],
      this.messagingDefinitionStatus =
          const MessagingDefinitionStatus(mustUseDefinition: true),
      this.chatFieldStatus = ChatFieldStatus.loading,
      this.messagingEndUserId,
      this.messagingType,
      this.navigateToDefinitions,
      this.messageDefinitionFailure,
      this.goToConversation,
      this.mainQuickAction,
      final List<QuickAction>? overflowQuickActions,
      this.quickActionException})
      : _attachedFiles = attachedFiles,
        _overflowQuickActions = overflowQuickActions,
        super._();

  @override
  @Deprecated('legacy support')
  final Conversation? conversation;
  @override
  final MapEntry<SfId?, Contact>? savedContact;
  @override
  @JsonKey()
  final bool? canTransfer;

  /// this is to support our legacy LakConversation that doesn't prompt the bloc updates correctly ... so setting 'toggle' value will force the ChatScreen to update // TODO: remove toggle
  @override
  @JsonKey()
  final bool toggle;
  @override
  @JsonKey()
  final bool isFetchingMessagingEndUserStatus;
  @override
  @JsonKey()
  final bool isFetchingMessagingDefinitions;
  @override
  final MessagingEndUserStatusResponse? messagingEndUserStatus;
  @override
  @JsonKey()
  final String messageText;
  final List<File> _attachedFiles;
  @override
  @JsonKey()
  List<File> get attachedFiles {
    if (_attachedFiles is EqualUnmodifiableListView) return _attachedFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachedFiles);
  }

  @override
  @JsonKey()
  final MessagingDefinitionStatus messagingDefinitionStatus;
  @override
  @JsonKey()
  final ChatFieldStatus chatFieldStatus;
  @override
  final SfId? messagingEndUserId;
  @override
  final MessagingChannelType? messagingType;
  @override
  final UiEvent<Nothing>? navigateToDefinitions;
  @override
  final UiEvent<MessagingException>? messageDefinitionFailure;
  @override
  final UiEvent<SfId>? goToConversation;
  @override
  final QuickAction? mainQuickAction;
  final List<QuickAction>? _overflowQuickActions;
  @override
  List<QuickAction>? get overflowQuickActions {
    final value = _overflowQuickActions;
    if (value == null) return null;
    if (_overflowQuickActions is EqualUnmodifiableListView)
      return _overflowQuickActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final UiEvent<QuickActionException>? quickActionException;

  @override
  String toString() {
    return 'ChatState(conversation: $conversation, savedContact: $savedContact, canTransfer: $canTransfer, toggle: $toggle, isFetchingMessagingEndUserStatus: $isFetchingMessagingEndUserStatus, isFetchingMessagingDefinitions: $isFetchingMessagingDefinitions, messagingEndUserStatus: $messagingEndUserStatus, messageText: $messageText, attachedFiles: $attachedFiles, messagingDefinitionStatus: $messagingDefinitionStatus, chatFieldStatus: $chatFieldStatus, messagingEndUserId: $messagingEndUserId, messagingType: $messagingType, navigateToDefinitions: $navigateToDefinitions, messageDefinitionFailure: $messageDefinitionFailure, goToConversation: $goToConversation, mainQuickAction: $mainQuickAction, overflowQuickActions: $overflowQuickActions, quickActionException: $quickActionException)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatStateImpl &&
            (identical(other.conversation, conversation) ||
                other.conversation == conversation) &&
            (identical(other.savedContact, savedContact) ||
                other.savedContact == savedContact) &&
            (identical(other.canTransfer, canTransfer) ||
                other.canTransfer == canTransfer) &&
            (identical(other.toggle, toggle) || other.toggle == toggle) &&
            (identical(other.isFetchingMessagingEndUserStatus,
                    isFetchingMessagingEndUserStatus) ||
                other.isFetchingMessagingEndUserStatus ==
                    isFetchingMessagingEndUserStatus) &&
            (identical(other.isFetchingMessagingDefinitions,
                    isFetchingMessagingDefinitions) ||
                other.isFetchingMessagingDefinitions ==
                    isFetchingMessagingDefinitions) &&
            (identical(other.messagingEndUserStatus, messagingEndUserStatus) ||
                other.messagingEndUserStatus == messagingEndUserStatus) &&
            (identical(other.messageText, messageText) ||
                other.messageText == messageText) &&
            const DeepCollectionEquality()
                .equals(other._attachedFiles, _attachedFiles) &&
            (identical(other.messagingDefinitionStatus, messagingDefinitionStatus) ||
                other.messagingDefinitionStatus == messagingDefinitionStatus) &&
            (identical(other.chatFieldStatus, chatFieldStatus) ||
                other.chatFieldStatus == chatFieldStatus) &&
            (identical(other.messagingEndUserId, messagingEndUserId) ||
                other.messagingEndUserId == messagingEndUserId) &&
            (identical(other.messagingType, messagingType) ||
                other.messagingType == messagingType) &&
            (identical(other.navigateToDefinitions, navigateToDefinitions) ||
                other.navigateToDefinitions == navigateToDefinitions) &&
            (identical(other.messageDefinitionFailure, messageDefinitionFailure) ||
                other.messageDefinitionFailure == messageDefinitionFailure) &&
            (identical(other.goToConversation, goToConversation) ||
                other.goToConversation == goToConversation) &&
            (identical(other.mainQuickAction, mainQuickAction) ||
                other.mainQuickAction == mainQuickAction) &&
            const DeepCollectionEquality()
                .equals(other._overflowQuickActions, _overflowQuickActions) &&
            (identical(other.quickActionException, quickActionException) ||
                other.quickActionException == quickActionException));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        conversation,
        savedContact,
        canTransfer,
        toggle,
        isFetchingMessagingEndUserStatus,
        isFetchingMessagingDefinitions,
        messagingEndUserStatus,
        messageText,
        const DeepCollectionEquality().hash(_attachedFiles),
        messagingDefinitionStatus,
        chatFieldStatus,
        messagingEndUserId,
        messagingType,
        navigateToDefinitions,
        messageDefinitionFailure,
        goToConversation,
        mainQuickAction,
        const DeepCollectionEquality().hash(_overflowQuickActions),
        quickActionException
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatStateImplCopyWith<_$ChatStateImpl> get copyWith =>
      __$$ChatStateImplCopyWithImpl<_$ChatStateImpl>(this, _$identity);
}

abstract class _ChatState extends ChatState {
  const factory _ChatState(
          {@Deprecated('legacy support') final Conversation? conversation,
          final MapEntry<SfId?, Contact>? savedContact,
          final bool? canTransfer,
          final bool toggle,
          final bool isFetchingMessagingEndUserStatus,
          final bool isFetchingMessagingDefinitions,
          final MessagingEndUserStatusResponse? messagingEndUserStatus,
          final String messageText,
          final List<File> attachedFiles,
          final MessagingDefinitionStatus messagingDefinitionStatus,
          final ChatFieldStatus chatFieldStatus,
          final SfId? messagingEndUserId,
          final MessagingChannelType? messagingType,
          final UiEvent<Nothing>? navigateToDefinitions,
          final UiEvent<MessagingException>? messageDefinitionFailure,
          final UiEvent<SfId>? goToConversation,
          final QuickAction? mainQuickAction,
          final List<QuickAction>? overflowQuickActions,
          final UiEvent<QuickActionException>? quickActionException}) =
      _$ChatStateImpl;
  const _ChatState._() : super._();

  @override
  @Deprecated('legacy support')
  Conversation? get conversation;
  @override
  MapEntry<SfId?, Contact>? get savedContact;
  @override
  bool? get canTransfer;
  @override

  /// this is to support our legacy LakConversation that doesn't prompt the bloc updates correctly ... so setting 'toggle' value will force the ChatScreen to update // TODO: remove toggle
  bool get toggle;
  @override
  bool get isFetchingMessagingEndUserStatus;
  @override
  bool get isFetchingMessagingDefinitions;
  @override
  MessagingEndUserStatusResponse? get messagingEndUserStatus;
  @override
  String get messageText;
  @override
  List<File> get attachedFiles;
  @override
  MessagingDefinitionStatus get messagingDefinitionStatus;
  @override
  ChatFieldStatus get chatFieldStatus;
  @override
  SfId? get messagingEndUserId;
  @override
  MessagingChannelType? get messagingType;
  @override
  UiEvent<Nothing>? get navigateToDefinitions;
  @override
  UiEvent<MessagingException>? get messageDefinitionFailure;
  @override
  UiEvent<SfId>? get goToConversation;
  @override
  QuickAction? get mainQuickAction;
  @override
  List<QuickAction>? get overflowQuickActions;
  @override
  UiEvent<QuickActionException>? get quickActionException;
  @override
  @JsonKey(ignore: true)
  _$$ChatStateImplCopyWith<_$ChatStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
