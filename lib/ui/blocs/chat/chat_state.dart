import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:universal_io/io.dart';
import 'package:x1440/api/dtos/messaging_definition.dart';
import 'package:x1440/api/salesforce/dtos/quick_actions/quick_action.dart';
import 'package:x1440/models/conversation_model.dart';
import 'package:x1440/api/dtos/messaging_end_user_status_response.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/contacts/contacts_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_exceptions.dart';
import 'package:x1440/use_cases/messaging/models/messaging_definition_status.dart';
import 'package:x1440/use_cases/models/contact.dart';
import 'package:x1440/use_cases/quick_actions/quick_actions_url_handler.dart';
// TODO: make immutable/freezed ... current implementation of Conversation is not, needs refactoring
part 'chat_state.freezed.dart';

enum ChatFieldStatus {
  loading,
  active,
  showTemplates,
  /// missing statuses handled by 'state.messageDefinitionFailure' // TODO: reconcile the two? better handle?
  showTemplatesRequiredButUnavailableWarning,
  inactiveExistingSession,
  inactiveNewSession;
}

@freezed
class ChatState with _$ChatState {
  const ChatState._();

  const factory ChatState({
    @Deprecated('legacy support') Conversation? conversation,
    SavedContactByMeu? savedContact,
    @Default(false) bool? canTransfer,
    /// this is to support our legacy LakConversation that doesn't prompt the bloc updates correctly ... so setting 'toggle' value will force the ChatScreen to update // TODO: remove toggle
    @Default(false) bool toggle,
    @Default(false) bool isFetchingMessagingEndUserStatus,
    @Default(false) bool isFetchingMessagingDefinitions,
    MessagingEndUserStatusResponse? messagingEndUserStatus,
    @Default('') String messageText,
    @Default([]) List<File> attachedFiles,
    @Default(MessagingDefinitionStatus(mustUseDefinition: true))
    MessagingDefinitionStatus messagingDefinitionStatus,
    @Default(ChatFieldStatus.loading) ChatFieldStatus chatFieldStatus,
    SfId? messagingEndUserId,
    MessagingChannelType? messagingType,
    UiEvent<Nothing>? navigateToDefinitions,
    UiEvent<MessagingException>? messageDefinitionFailure,
    UiEvent<SfId>? goToConversation,
    QuickAction? mainQuickAction,
    List<QuickAction>? overflowQuickActions,
    UiEvent<QuickActionException>? quickActionException,
  }) = _ChatState;


  bool get canSend =>
      chatFieldStatus == ChatFieldStatus.active && (messageText.isNotEmpty || attachedFiles.isNotEmpty);

  bool get canEndConversation =>
      chatFieldStatus != ChatFieldStatus.loading
          && conversation?.canEndConversation == true;
}
