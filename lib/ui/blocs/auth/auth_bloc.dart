import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/frameworks/conversations_service.dart';
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';
import 'package:x1440/utils/loggy_utils.dart';

import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc(super.initialState);
}

class AuthBlocImpl extends AuthBloc with BlocLoggy {
  final AuthUseCase _authUseCase;
  final SessionUseCase _sessionUseCase;
  final ConversationsUseCase _conversationsUseCase;
  final MessagingUseCase _messagingUseCase;
  final AppLifeCycleRepository _appLifeCycleRepository;
  final RemoteLogger _logger;

  AuthBlocImpl(
      this._authUseCase,
      this._sessionUseCase,
      this._conversationsUseCase,
      this._messagingUseCase,
      this._appLifeCycleRepository,
      this._logger
      )
      : super(const AuthState()) {
    on<AttemptAutoLoginEvent>(_onAttemptAutoLoginEvent);
    on<LogoutEvent>(_onLogoutEvent);
    on<SessionExpiredEvent>(_onSessionExpiredEvent);
    on<KeepSessionAliveEvent>(_onKeepSessionAliveEvent);
    on<SalesforceOAuthEvent>(_onSalesforceOAuthEvent);
    on<SalesforceOAuthErrorEvent>(_onSalesforceOAuthErrorEvent);
    on<SalesforceOAuthResponseEvent>(_onSalesforceOAuthResponseEvent);
  }

  Future<void> _onAttemptAutoLoginEvent(
      AttemptAutoLoginEvent event, Emitter<AuthState> emit) async {
    if (kDebugMode) {
      print(
          'AUTHBLOC-attempting auto login with: ${state.status}; isForegrounded: ${_appLifeCycleRepository.isForegrounded}; state: ${_appLifeCycleRepository.appLifecycleState}');
    }

    /// this needs to allow the situation where we've lost connection with the websocket -- the credentials will still show 'isLoggedIn'
    // state.status != AuthStatus.isLoggingIn

    final bool shouldAttempt = _appLifeCycleRepository.isForegrounded &&
        (event.force || state.status == AuthStatus.loggedOut);
    if (!shouldAttempt) {
      return;
    }
    emit(state.copyWith(status: AuthStatus.isLoggingIn));
    _messagingUseCase.disconnectWebsocket();
    if (await _authUseCase.attemptAutoLogin()) {
      loggy.info('AuthBloc - AttemptAutoLoginEvent - Success');
      emit(state.copyWith(status: AuthStatus.loggedIn));
    } else {
      loggy.info('AuthBloc - AttemptAutoLoginEvent - Failed');
      emit(state.copyWith(status: AuthStatus.loggedOut));
      add(LogoutEvent());
    }
  }

  FutureOr<void> _onSalesforceOAuthEvent(
      SalesforceOAuthEvent event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.isLoggingIn));
    final result = await _authUseCase.loginToSalesforce();

    loggy.info('AuthBloc - LoginEvent - Success: ${(result)}');
    if (result is Success) {
      final success = result as Success;
      add(SalesforceOAuthResponseEvent(response: success.data));
    } else {
      final error = result as Error;
      add(SalesforceOAuthErrorEvent(error: error.error.toString()));
    }
  }

  FutureOr<void> _onSalesforceOAuthErrorEvent(
      SalesforceOAuthErrorEvent event, Emitter<AuthState> emit) {
    loggy.info('SalesforceOAuthErrorEvent - Error: ${event.error}');

    emit(state.copyWith(
        status: AuthStatus.loggedOut, logOut: UiEvent(Nothing())));
  }

  FutureOr<void> _onSalesforceOAuthResponseEvent(
      SalesforceOAuthResponseEvent event, Emitter<AuthState> emit) async {
    loggy.info('SalesforceOAuthResponseEvent - Response: ${event.response}');

    await _authUseCase.handleOAuthResponse(event.response);
    await _authUseCase.shimLoginAndStartSession();
    if (await _authUseCase.isLoggedIn) {
      emit(state.copyWith(
          status: AuthStatus.loggedIn, logIn: UiEvent(Nothing())));
    }
  }

  Future<void> _onLogoutEvent(
      LogoutEvent event, Emitter<AuthState> emit) async {
    loggy.info('Logout Event Received');
    emit(state.copyWith(status: AuthStatus.isLoggingOut));
    _authUseCase.setIsLoggingOut();
    await GetIt.I<ConversationsService>().handlePresenceOffline();


    /// make sure we're not on a page that relies on any view models that are getting disposed in the logout
    try {
      /// this one uses credentials that the authUseCase.logout clears

      await _sessionUseCase.endSession().timeout(const Duration(seconds: 20));
      await _authUseCase.logout().timeout(const Duration(seconds: 20));

      _conversationsUseCase.clear();
      // TODO: better handle state resetting ... GetIt Scope?
      // GetIt.I<ConversationsBloc>().add(ClearConversationsEvent());
    } catch (e) {
      _logger.error('Error logging out: $e');
    }
    _authUseCase.setIsLoggingOut(false);
    emit(state.copyWith(
        status: AuthStatus.loggedOut,
        appError: event.appError,
        logOut: UiEvent(Nothing())));
  }

  Future<void> _onSessionExpiredEvent(
      SessionExpiredEvent event, Emitter<AuthState> emit) async {
    final bool isLoggedIn = await _authUseCase.isLoggedIn;
    loggy.info(
        'Session Expired Event Received with isLoggedIn: $isLoggedIn; isLoggingOut: ${_authUseCase.isLoggingOut}');
    if (isLoggedIn && !_authUseCase.isLoggingOut) {
      loggy.info('_onSessionExpiredEvent - logging out');
      add(LogoutEvent());
    }
  }

  Future<void> _onKeepSessionAliveEvent(
      KeepSessionAliveEvent event, Emitter<AuthState> emit) async {
    final success = await _sessionUseCase.keepSessionAlive();
    if (!success) {
      add(AttemptAutoLoginEvent());
      return;
    }
    _authUseCase.setIsLoggingOut(false);
    emit(state.copyWith(status: AuthStatus.loggedIn));
  }
}
