// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthState {
  AuthStatus get status => throw _privateConstructorUsedError;
  AppError? get appError => throw _privateConstructorUsedError;
  UiEvent<SalesforceConfig>? get oauth => throw _privateConstructorUsedError;
  UiEvent<Nothing>? get logIn => throw _privateConstructorUsedError;
  UiEvent<Nothing>? get logOut => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AuthStateCopyWith<AuthState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
  @useResult
  $Res call(
      {AuthStatus status,
      AppError? appError,
      UiEvent<SalesforceConfig>? oauth,
      UiEvent<Nothing>? logIn,
      UiEvent<Nothing>? logOut});

  $AppErrorCopyWith<$Res>? get appError;
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? appError = freezed,
    Object? oauth = freezed,
    Object? logIn = freezed,
    Object? logOut = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AuthStatus,
      appError: freezed == appError
          ? _value.appError
          : appError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      oauth: freezed == oauth
          ? _value.oauth
          : oauth // ignore: cast_nullable_to_non_nullable
              as UiEvent<SalesforceConfig>?,
      logIn: freezed == logIn
          ? _value.logIn
          : logIn // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      logOut: freezed == logOut
          ? _value.logOut
          : logOut // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get appError {
    if (_value.appError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.appError!, (value) {
      return _then(_value.copyWith(appError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AuthStateImplCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$$AuthStateImplCopyWith(
          _$AuthStateImpl value, $Res Function(_$AuthStateImpl) then) =
      __$$AuthStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AuthStatus status,
      AppError? appError,
      UiEvent<SalesforceConfig>? oauth,
      UiEvent<Nothing>? logIn,
      UiEvent<Nothing>? logOut});

  @override
  $AppErrorCopyWith<$Res>? get appError;
}

/// @nodoc
class __$$AuthStateImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$AuthStateImpl>
    implements _$$AuthStateImplCopyWith<$Res> {
  __$$AuthStateImplCopyWithImpl(
      _$AuthStateImpl _value, $Res Function(_$AuthStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? appError = freezed,
    Object? oauth = freezed,
    Object? logIn = freezed,
    Object? logOut = freezed,
  }) {
    return _then(_$AuthStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AuthStatus,
      appError: freezed == appError
          ? _value.appError
          : appError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      oauth: freezed == oauth
          ? _value.oauth
          : oauth // ignore: cast_nullable_to_non_nullable
              as UiEvent<SalesforceConfig>?,
      logIn: freezed == logIn
          ? _value.logIn
          : logIn // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      logOut: freezed == logOut
          ? _value.logOut
          : logOut // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }
}

/// @nodoc

class _$AuthStateImpl extends _AuthState {
  const _$AuthStateImpl(
      {this.status = AuthStatus.loggedOut,
      this.appError,
      this.oauth,
      this.logIn,
      this.logOut})
      : super._();

  @override
  @JsonKey()
  final AuthStatus status;
  @override
  final AppError? appError;
  @override
  final UiEvent<SalesforceConfig>? oauth;
  @override
  final UiEvent<Nothing>? logIn;
  @override
  final UiEvent<Nothing>? logOut;

  @override
  String toString() {
    return 'AuthState(status: $status, appError: $appError, oauth: $oauth, logIn: $logIn, logOut: $logOut)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.appError, appError) ||
                other.appError == appError) &&
            (identical(other.oauth, oauth) || other.oauth == oauth) &&
            (identical(other.logIn, logIn) || other.logIn == logIn) &&
            (identical(other.logOut, logOut) || other.logOut == logOut));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, status, appError, oauth, logIn, logOut);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthStateImplCopyWith<_$AuthStateImpl> get copyWith =>
      __$$AuthStateImplCopyWithImpl<_$AuthStateImpl>(this, _$identity);
}

abstract class _AuthState extends AuthState {
  const factory _AuthState(
      {final AuthStatus status,
      final AppError? appError,
      final UiEvent<SalesforceConfig>? oauth,
      final UiEvent<Nothing>? logIn,
      final UiEvent<Nothing>? logOut}) = _$AuthStateImpl;
  const _AuthState._() : super._();

  @override
  AuthStatus get status;
  @override
  AppError? get appError;
  @override
  UiEvent<SalesforceConfig>? get oauth;
  @override
  UiEvent<Nothing>? get logIn;
  @override
  UiEvent<Nothing>? get logOut;
  @override
  @JsonKey(ignore: true)
  _$$AuthStateImplCopyWith<_$AuthStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
