import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';
import 'package:x1440/models/app_error_model.dart';
import 'package:x1440/ui/blocs/ui_event.dart';

part 'auth_state.freezed.dart';

enum AuthStatus { loggedOut, isLoggingIn, sfLoggedIn, loggedIn, isLoggingOut }

@freezed
class AuthState with _$AuthState {
  const AuthState._();

  bool get isLoggedIn => status == AuthStatus.loggedIn;
  bool get isLoggingIn => status == AuthStatus.isLoggingIn;
  bool get isLoggingOut => status == AuthStatus.isLoggingOut;

  const factory AuthState(
      {@Default(AuthStatus.loggedOut) AuthStatus status,
      AppError? appError,
      UiEvent<SalesforceConfig>? oauth,
      UiEvent<Nothing>? logIn,
      UiEvent<Nothing>? logOut}) = _AuthState;
}
