// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TransferState {
  List<TransferOption> get transferOptions =>
      throw _privateConstructorUsedError;
  List<TransferDestination> get destinations =>
      throw _privateConstructorUsedError;
  List<TransferDestination> get filteredDestinations =>
      throw _privateConstructorUsedError;
  dynamic get isLoadingDestinations => throw _privateConstructorUsedError;
  dynamic get isTransferring => throw _privateConstructorUsedError;
  TransferDestination? get selectedDestination =>
      throw _privateConstructorUsedError;
  LakConversation? get conversation => throw _privateConstructorUsedError;
  String? get searchTerm => throw _privateConstructorUsedError;
  TransferDestinationType? get transferDestinationType =>
      throw _privateConstructorUsedError;
  UiEvent<TransferStatus>? get transferStatus =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $TransferStateCopyWith<TransferState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransferStateCopyWith<$Res> {
  factory $TransferStateCopyWith(
          TransferState value, $Res Function(TransferState) then) =
      _$TransferStateCopyWithImpl<$Res, TransferState>;
  @useResult
  $Res call(
      {List<TransferOption> transferOptions,
      List<TransferDestination> destinations,
      List<TransferDestination> filteredDestinations,
      dynamic isLoadingDestinations,
      dynamic isTransferring,
      TransferDestination? selectedDestination,
      LakConversation? conversation,
      String? searchTerm,
      TransferDestinationType? transferDestinationType,
      UiEvent<TransferStatus>? transferStatus});

  $TransferDestinationCopyWith<$Res>? get selectedDestination;
}

/// @nodoc
class _$TransferStateCopyWithImpl<$Res, $Val extends TransferState>
    implements $TransferStateCopyWith<$Res> {
  _$TransferStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transferOptions = null,
    Object? destinations = null,
    Object? filteredDestinations = null,
    Object? isLoadingDestinations = freezed,
    Object? isTransferring = freezed,
    Object? selectedDestination = freezed,
    Object? conversation = freezed,
    Object? searchTerm = freezed,
    Object? transferDestinationType = freezed,
    Object? transferStatus = freezed,
  }) {
    return _then(_value.copyWith(
      transferOptions: null == transferOptions
          ? _value.transferOptions
          : transferOptions // ignore: cast_nullable_to_non_nullable
              as List<TransferOption>,
      destinations: null == destinations
          ? _value.destinations
          : destinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
      filteredDestinations: null == filteredDestinations
          ? _value.filteredDestinations
          : filteredDestinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
      isLoadingDestinations: freezed == isLoadingDestinations
          ? _value.isLoadingDestinations
          : isLoadingDestinations // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isTransferring: freezed == isTransferring
          ? _value.isTransferring
          : isTransferring // ignore: cast_nullable_to_non_nullable
              as dynamic,
      selectedDestination: freezed == selectedDestination
          ? _value.selectedDestination
          : selectedDestination // ignore: cast_nullable_to_non_nullable
              as TransferDestination?,
      conversation: freezed == conversation
          ? _value.conversation
          : conversation // ignore: cast_nullable_to_non_nullable
              as LakConversation?,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
      transferDestinationType: freezed == transferDestinationType
          ? _value.transferDestinationType
          : transferDestinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType?,
      transferStatus: freezed == transferStatus
          ? _value.transferStatus
          : transferStatus // ignore: cast_nullable_to_non_nullable
              as UiEvent<TransferStatus>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TransferDestinationCopyWith<$Res>? get selectedDestination {
    if (_value.selectedDestination == null) {
      return null;
    }

    return $TransferDestinationCopyWith<$Res>(_value.selectedDestination!,
        (value) {
      return _then(_value.copyWith(selectedDestination: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TransferStateImplCopyWith<$Res>
    implements $TransferStateCopyWith<$Res> {
  factory _$$TransferStateImplCopyWith(
          _$TransferStateImpl value, $Res Function(_$TransferStateImpl) then) =
      __$$TransferStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<TransferOption> transferOptions,
      List<TransferDestination> destinations,
      List<TransferDestination> filteredDestinations,
      dynamic isLoadingDestinations,
      dynamic isTransferring,
      TransferDestination? selectedDestination,
      LakConversation? conversation,
      String? searchTerm,
      TransferDestinationType? transferDestinationType,
      UiEvent<TransferStatus>? transferStatus});

  @override
  $TransferDestinationCopyWith<$Res>? get selectedDestination;
}

/// @nodoc
class __$$TransferStateImplCopyWithImpl<$Res>
    extends _$TransferStateCopyWithImpl<$Res, _$TransferStateImpl>
    implements _$$TransferStateImplCopyWith<$Res> {
  __$$TransferStateImplCopyWithImpl(
      _$TransferStateImpl _value, $Res Function(_$TransferStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transferOptions = null,
    Object? destinations = null,
    Object? filteredDestinations = null,
    Object? isLoadingDestinations = freezed,
    Object? isTransferring = freezed,
    Object? selectedDestination = freezed,
    Object? conversation = freezed,
    Object? searchTerm = freezed,
    Object? transferDestinationType = freezed,
    Object? transferStatus = freezed,
  }) {
    return _then(_$TransferStateImpl(
      transferOptions: null == transferOptions
          ? _value._transferOptions
          : transferOptions // ignore: cast_nullable_to_non_nullable
              as List<TransferOption>,
      destinations: null == destinations
          ? _value._destinations
          : destinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
      filteredDestinations: null == filteredDestinations
          ? _value._filteredDestinations
          : filteredDestinations // ignore: cast_nullable_to_non_nullable
              as List<TransferDestination>,
      isLoadingDestinations: freezed == isLoadingDestinations
          ? _value.isLoadingDestinations!
          : isLoadingDestinations,
      isTransferring:
          freezed == isTransferring ? _value.isTransferring! : isTransferring,
      selectedDestination: freezed == selectedDestination
          ? _value.selectedDestination
          : selectedDestination // ignore: cast_nullable_to_non_nullable
              as TransferDestination?,
      conversation: freezed == conversation
          ? _value.conversation
          : conversation // ignore: cast_nullable_to_non_nullable
              as LakConversation?,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
      transferDestinationType: freezed == transferDestinationType
          ? _value.transferDestinationType
          : transferDestinationType // ignore: cast_nullable_to_non_nullable
              as TransferDestinationType?,
      transferStatus: freezed == transferStatus
          ? _value.transferStatus
          : transferStatus // ignore: cast_nullable_to_non_nullable
              as UiEvent<TransferStatus>?,
    ));
  }
}

/// @nodoc

class _$TransferStateImpl implements _TransferState {
  const _$TransferStateImpl(
      {final List<TransferOption> transferOptions = const <TransferOption>[],
      final List<TransferDestination> destinations =
          const <TransferDestination>[],
      final List<TransferDestination> filteredDestinations =
          const <TransferDestination>[],
      this.isLoadingDestinations = true,
      this.isTransferring = false,
      this.selectedDestination = null,
      this.conversation = null,
      this.searchTerm = null,
      this.transferDestinationType = null,
      this.transferStatus})
      : _transferOptions = transferOptions,
        _destinations = destinations,
        _filteredDestinations = filteredDestinations;

  final List<TransferOption> _transferOptions;
  @override
  @JsonKey()
  List<TransferOption> get transferOptions {
    if (_transferOptions is EqualUnmodifiableListView) return _transferOptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transferOptions);
  }

  final List<TransferDestination> _destinations;
  @override
  @JsonKey()
  List<TransferDestination> get destinations {
    if (_destinations is EqualUnmodifiableListView) return _destinations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_destinations);
  }

  final List<TransferDestination> _filteredDestinations;
  @override
  @JsonKey()
  List<TransferDestination> get filteredDestinations {
    if (_filteredDestinations is EqualUnmodifiableListView)
      return _filteredDestinations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredDestinations);
  }

  @override
  @JsonKey()
  final dynamic isLoadingDestinations;
  @override
  @JsonKey()
  final dynamic isTransferring;
  @override
  @JsonKey()
  final TransferDestination? selectedDestination;
  @override
  @JsonKey()
  final LakConversation? conversation;
  @override
  @JsonKey()
  final String? searchTerm;
  @override
  @JsonKey()
  final TransferDestinationType? transferDestinationType;
  @override
  final UiEvent<TransferStatus>? transferStatus;

  @override
  String toString() {
    return 'TransferState(transferOptions: $transferOptions, destinations: $destinations, filteredDestinations: $filteredDestinations, isLoadingDestinations: $isLoadingDestinations, isTransferring: $isTransferring, selectedDestination: $selectedDestination, conversation: $conversation, searchTerm: $searchTerm, transferDestinationType: $transferDestinationType, transferStatus: $transferStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferStateImpl &&
            const DeepCollectionEquality()
                .equals(other._transferOptions, _transferOptions) &&
            const DeepCollectionEquality()
                .equals(other._destinations, _destinations) &&
            const DeepCollectionEquality()
                .equals(other._filteredDestinations, _filteredDestinations) &&
            const DeepCollectionEquality()
                .equals(other.isLoadingDestinations, isLoadingDestinations) &&
            const DeepCollectionEquality()
                .equals(other.isTransferring, isTransferring) &&
            (identical(other.selectedDestination, selectedDestination) ||
                other.selectedDestination == selectedDestination) &&
            (identical(other.conversation, conversation) ||
                other.conversation == conversation) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm) &&
            (identical(
                    other.transferDestinationType, transferDestinationType) ||
                other.transferDestinationType == transferDestinationType) &&
            (identical(other.transferStatus, transferStatus) ||
                other.transferStatus == transferStatus));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_transferOptions),
      const DeepCollectionEquality().hash(_destinations),
      const DeepCollectionEquality().hash(_filteredDestinations),
      const DeepCollectionEquality().hash(isLoadingDestinations),
      const DeepCollectionEquality().hash(isTransferring),
      selectedDestination,
      conversation,
      searchTerm,
      transferDestinationType,
      transferStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferStateImplCopyWith<_$TransferStateImpl> get copyWith =>
      __$$TransferStateImplCopyWithImpl<_$TransferStateImpl>(this, _$identity);
}

abstract class _TransferState implements TransferState {
  const factory _TransferState(
      {final List<TransferOption> transferOptions,
      final List<TransferDestination> destinations,
      final List<TransferDestination> filteredDestinations,
      final dynamic isLoadingDestinations,
      final dynamic isTransferring,
      final TransferDestination? selectedDestination,
      final LakConversation? conversation,
      final String? searchTerm,
      final TransferDestinationType? transferDestinationType,
      final UiEvent<TransferStatus>? transferStatus}) = _$TransferStateImpl;

  @override
  List<TransferOption> get transferOptions;
  @override
  List<TransferDestination> get destinations;
  @override
  List<TransferDestination> get filteredDestinations;
  @override
  dynamic get isLoadingDestinations;
  @override
  dynamic get isTransferring;
  @override
  TransferDestination? get selectedDestination;
  @override
  LakConversation? get conversation;
  @override
  String? get searchTerm;
  @override
  TransferDestinationType? get transferDestinationType;
  @override
  UiEvent<TransferStatus>? get transferStatus;
  @override
  @JsonKey(ignore: true)
  _$$TransferStateImplCopyWith<_$TransferStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
