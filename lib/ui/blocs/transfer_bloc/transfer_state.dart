import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/transfer_destination.dart';
import 'package:x1440/api/dtos/transfer_messaging_session_body.dart';
import 'package:x1440/api/dtos/transfer_option.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';

part 'transfer_state.freezed.dart';

@freezed
class TransferState with _$TransferState {
  const factory TransferState(
      {@Default(<TransferOption>[]) List<TransferOption> transferOptions,
      @Default(<TransferDestination>[]) List<TransferDestination> destinations,
      @Default(<TransferDestination>[])
      List<TransferDestination> filteredDestinations,
      @Default(true) isLoadingDestinations,
      @Default(false) isTransferring,
      @Default(null) TransferDestination? selectedDestination,
      @Default(null) LakConversation? conversation,
      @Default(null) String? searchTerm,
      @Default(null) TransferDestinationType? transferDestinationType,
      final UiEvent<TransferStatus>? transferStatus}) = _TransferState;
}
