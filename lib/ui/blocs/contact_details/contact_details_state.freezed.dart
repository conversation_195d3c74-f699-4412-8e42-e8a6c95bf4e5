// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ContactDetailsState {
  ContactOrMeuAllDetails? get contactDetails =>
      throw _privateConstructorUsedError;
  Contact? get contact => throw _privateConstructorUsedError;
  String? get conversationUserName => throw _privateConstructorUsedError;
  MessagingEndUser? get messagingEndUser => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ContactDetailsStateCopyWith<ContactDetailsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactDetailsStateCopyWith<$Res> {
  factory $ContactDetailsStateCopyWith(
          ContactDetailsState value, $Res Function(ContactDetailsState) then) =
      _$ContactDetailsStateCopyWithImpl<$Res, ContactDetailsState>;
  @useResult
  $Res call(
      {ContactOrMeuAllDetails? contactDetails,
      Contact? contact,
      String? conversationUserName,
      MessagingEndUser? messagingEndUser,
      bool isLoading});

  $ContactCopyWith<$Res>? get contact;
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser;
}

/// @nodoc
class _$ContactDetailsStateCopyWithImpl<$Res, $Val extends ContactDetailsState>
    implements $ContactDetailsStateCopyWith<$Res> {
  _$ContactDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactDetails = freezed,
    Object? contact = freezed,
    Object? conversationUserName = freezed,
    Object? messagingEndUser = freezed,
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      contactDetails: freezed == contactDetails
          ? _value.contactDetails
          : contactDetails // ignore: cast_nullable_to_non_nullable
              as ContactOrMeuAllDetails?,
      contact: freezed == contact
          ? _value.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      conversationUserName: freezed == conversationUserName
          ? _value.conversationUserName
          : conversationUserName // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingEndUser: freezed == messagingEndUser
          ? _value.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as MessagingEndUser?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get contact {
    if (_value.contact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_value.contact!, (value) {
      return _then(_value.copyWith(contact: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser {
    if (_value.messagingEndUser == null) {
      return null;
    }

    return $MessagingEndUserCopyWith<$Res>(_value.messagingEndUser!, (value) {
      return _then(_value.copyWith(messagingEndUser: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ContactDetailsStateImplCopyWith<$Res>
    implements $ContactDetailsStateCopyWith<$Res> {
  factory _$$ContactDetailsStateImplCopyWith(_$ContactDetailsStateImpl value,
          $Res Function(_$ContactDetailsStateImpl) then) =
      __$$ContactDetailsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ContactOrMeuAllDetails? contactDetails,
      Contact? contact,
      String? conversationUserName,
      MessagingEndUser? messagingEndUser,
      bool isLoading});

  @override
  $ContactCopyWith<$Res>? get contact;
  @override
  $MessagingEndUserCopyWith<$Res>? get messagingEndUser;
}

/// @nodoc
class __$$ContactDetailsStateImplCopyWithImpl<$Res>
    extends _$ContactDetailsStateCopyWithImpl<$Res, _$ContactDetailsStateImpl>
    implements _$$ContactDetailsStateImplCopyWith<$Res> {
  __$$ContactDetailsStateImplCopyWithImpl(_$ContactDetailsStateImpl _value,
      $Res Function(_$ContactDetailsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactDetails = freezed,
    Object? contact = freezed,
    Object? conversationUserName = freezed,
    Object? messagingEndUser = freezed,
    Object? isLoading = null,
  }) {
    return _then(_$ContactDetailsStateImpl(
      contactDetails: freezed == contactDetails
          ? _value.contactDetails
          : contactDetails // ignore: cast_nullable_to_non_nullable
              as ContactOrMeuAllDetails?,
      contact: freezed == contact
          ? _value.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      conversationUserName: freezed == conversationUserName
          ? _value.conversationUserName
          : conversationUserName // ignore: cast_nullable_to_non_nullable
              as String?,
      messagingEndUser: freezed == messagingEndUser
          ? _value.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as MessagingEndUser?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ContactDetailsStateImpl extends _ContactDetailsState {
  const _$ContactDetailsStateImpl(
      {this.contactDetails,
      this.contact,
      this.conversationUserName,
      this.messagingEndUser,
      this.isLoading = true})
      : super._();

  @override
  final ContactOrMeuAllDetails? contactDetails;
  @override
  final Contact? contact;
  @override
  final String? conversationUserName;
  @override
  final MessagingEndUser? messagingEndUser;
  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'ContactDetailsState(contactDetails: $contactDetails, contact: $contact, conversationUserName: $conversationUserName, messagingEndUser: $messagingEndUser, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactDetailsStateImpl &&
            (identical(other.contactDetails, contactDetails) ||
                other.contactDetails == contactDetails) &&
            (identical(other.contact, contact) || other.contact == contact) &&
            (identical(other.conversationUserName, conversationUserName) ||
                other.conversationUserName == conversationUserName) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contactDetails, contact,
      conversationUserName, messagingEndUser, isLoading);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactDetailsStateImplCopyWith<_$ContactDetailsStateImpl> get copyWith =>
      __$$ContactDetailsStateImplCopyWithImpl<_$ContactDetailsStateImpl>(
          this, _$identity);
}

abstract class _ContactDetailsState extends ContactDetailsState {
  const factory _ContactDetailsState(
      {final ContactOrMeuAllDetails? contactDetails,
      final Contact? contact,
      final String? conversationUserName,
      final MessagingEndUser? messagingEndUser,
      final bool isLoading}) = _$ContactDetailsStateImpl;
  const _ContactDetailsState._() : super._();

  @override
  ContactOrMeuAllDetails? get contactDetails;
  @override
  Contact? get contact;
  @override
  String? get conversationUserName;
  @override
  MessagingEndUser? get messagingEndUser;
  @override
  bool get isLoading;
  @override
  @JsonKey(ignore: true)
  _$$ContactDetailsStateImplCopyWith<_$ContactDetailsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
