import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/use_cases/models/contact.dart';
import 'package:x1440/use_cases/models/messaging_end_user.dart';

import 'contact_details_event.dart';

part 'contact_details_state.freezed.dart';

@freezed
class ContactDetailsState with _$ContactDetailsState {
  
  const ContactDetailsState._();

  const factory ContactDetailsState({
    ContactOrMeuAllDetails? contactDetails,
    Contact? contact,
    String? conversationUserName,
    MessagingEndUser? messagingEndUser,
    @Default(true) bool isLoading,
  }) = _ContactDetailsState;

  bool get displayContactSFDeeplink => contact?.id != null || messagingEndUser?.id != null;
}