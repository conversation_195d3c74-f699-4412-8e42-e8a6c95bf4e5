// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'org_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrgState {
  User? get user => throw _privateConstructorUsedError;
  String? get userPhotoUrl => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OrgStateCopyWith<OrgState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrgStateCopyWith<$Res> {
  factory $OrgStateCopyWith(OrgState value, $Res Function(OrgState) then) =
      _$OrgStateCopyWithImpl<$Res, OrgState>;
  @useResult
  $Res call({User? user, String? userPhotoUrl, bool isLoading});

  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$OrgStateCopyWithImpl<$Res, $Val extends OrgState>
    implements $OrgStateCopyWith<$Res> {
  _$OrgStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
    Object? userPhotoUrl = freezed,
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      userPhotoUrl: freezed == userPhotoUrl
          ? _value.userPhotoUrl
          : userPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrgStateImplCopyWith<$Res>
    implements $OrgStateCopyWith<$Res> {
  factory _$$OrgStateImplCopyWith(
          _$OrgStateImpl value, $Res Function(_$OrgStateImpl) then) =
      __$$OrgStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({User? user, String? userPhotoUrl, bool isLoading});

  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$OrgStateImplCopyWithImpl<$Res>
    extends _$OrgStateCopyWithImpl<$Res, _$OrgStateImpl>
    implements _$$OrgStateImplCopyWith<$Res> {
  __$$OrgStateImplCopyWithImpl(
      _$OrgStateImpl _value, $Res Function(_$OrgStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
    Object? userPhotoUrl = freezed,
    Object? isLoading = null,
  }) {
    return _then(_$OrgStateImpl(
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      userPhotoUrl: freezed == userPhotoUrl
          ? _value.userPhotoUrl
          : userPhotoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$OrgStateImpl extends _OrgState {
  _$OrgStateImpl({this.user, this.userPhotoUrl, this.isLoading = true})
      : super._();

  @override
  final User? user;
  @override
  final String? userPhotoUrl;
  @override
  @JsonKey()
  final bool isLoading;

  @override
  String toString() {
    return 'OrgState(user: $user, userPhotoUrl: $userPhotoUrl, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrgStateImpl &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.userPhotoUrl, userPhotoUrl) ||
                other.userPhotoUrl == userPhotoUrl) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user, userPhotoUrl, isLoading);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrgStateImplCopyWith<_$OrgStateImpl> get copyWith =>
      __$$OrgStateImplCopyWithImpl<_$OrgStateImpl>(this, _$identity);
}

abstract class _OrgState extends OrgState {
  factory _OrgState(
      {final User? user,
      final String? userPhotoUrl,
      final bool isLoading}) = _$OrgStateImpl;
  _OrgState._() : super._();

  @override
  User? get user;
  @override
  String? get userPhotoUrl;
  @override
  bool get isLoading;
  @override
  @JsonKey(ignore: true)
  _$$OrgStateImplCopyWith<_$OrgStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
