import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/app_error_model.dart';
import 'package:x1440/ui/blocs/ui_event.dart';

part 'app_error_state.freezed.dart';

@freezed
class AppErrorState with _$AppErrorState {
  factory AppErrorState({
    UiEvent<Nothing>? showError,
    AppError? activeError,
    @Default(<AppError>[]) List<AppError> handledErrors,
    @Default(<AppError>[]) List<AppError> reportedErrors,
  }) = _AppErrorState;
}
