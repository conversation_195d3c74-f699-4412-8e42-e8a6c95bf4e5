// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppErrorState {
  UiEvent<Nothing>? get showError => throw _privateConstructorUsedError;
  AppError? get activeError => throw _privateConstructorUsedError;
  List<AppError> get handledErrors => throw _privateConstructorUsedError;
  List<AppError> get reportedErrors => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AppErrorStateCopyWith<AppErrorState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppErrorStateCopyWith<$Res> {
  factory $AppErrorStateCopyWith(
          AppErrorState value, $Res Function(AppErrorState) then) =
      _$AppErrorStateCopyWithImpl<$Res, AppErrorState>;
  @useResult
  $Res call(
      {UiEvent<Nothing>? showError,
      AppError? activeError,
      List<AppError> handledErrors,
      List<AppError> reportedErrors});

  $AppErrorCopyWith<$Res>? get activeError;
}

/// @nodoc
class _$AppErrorStateCopyWithImpl<$Res, $Val extends AppErrorState>
    implements $AppErrorStateCopyWith<$Res> {
  _$AppErrorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showError = freezed,
    Object? activeError = freezed,
    Object? handledErrors = null,
    Object? reportedErrors = null,
  }) {
    return _then(_value.copyWith(
      showError: freezed == showError
          ? _value.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      activeError: freezed == activeError
          ? _value.activeError
          : activeError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      handledErrors: null == handledErrors
          ? _value.handledErrors
          : handledErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
      reportedErrors: null == reportedErrors
          ? _value.reportedErrors
          : reportedErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get activeError {
    if (_value.activeError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.activeError!, (value) {
      return _then(_value.copyWith(activeError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppErrorStateImplCopyWith<$Res>
    implements $AppErrorStateCopyWith<$Res> {
  factory _$$AppErrorStateImplCopyWith(
          _$AppErrorStateImpl value, $Res Function(_$AppErrorStateImpl) then) =
      __$$AppErrorStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {UiEvent<Nothing>? showError,
      AppError? activeError,
      List<AppError> handledErrors,
      List<AppError> reportedErrors});

  @override
  $AppErrorCopyWith<$Res>? get activeError;
}

/// @nodoc
class __$$AppErrorStateImplCopyWithImpl<$Res>
    extends _$AppErrorStateCopyWithImpl<$Res, _$AppErrorStateImpl>
    implements _$$AppErrorStateImplCopyWith<$Res> {
  __$$AppErrorStateImplCopyWithImpl(
      _$AppErrorStateImpl _value, $Res Function(_$AppErrorStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showError = freezed,
    Object? activeError = freezed,
    Object? handledErrors = null,
    Object? reportedErrors = null,
  }) {
    return _then(_$AppErrorStateImpl(
      showError: freezed == showError
          ? _value.showError
          : showError // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
      activeError: freezed == activeError
          ? _value.activeError
          : activeError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      handledErrors: null == handledErrors
          ? _value._handledErrors
          : handledErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
      reportedErrors: null == reportedErrors
          ? _value._reportedErrors
          : reportedErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
    ));
  }
}

/// @nodoc

class _$AppErrorStateImpl implements _AppErrorState {
  _$AppErrorStateImpl(
      {this.showError,
      this.activeError,
      final List<AppError> handledErrors = const <AppError>[],
      final List<AppError> reportedErrors = const <AppError>[]})
      : _handledErrors = handledErrors,
        _reportedErrors = reportedErrors;

  @override
  final UiEvent<Nothing>? showError;
  @override
  final AppError? activeError;
  final List<AppError> _handledErrors;
  @override
  @JsonKey()
  List<AppError> get handledErrors {
    if (_handledErrors is EqualUnmodifiableListView) return _handledErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_handledErrors);
  }

  final List<AppError> _reportedErrors;
  @override
  @JsonKey()
  List<AppError> get reportedErrors {
    if (_reportedErrors is EqualUnmodifiableListView) return _reportedErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reportedErrors);
  }

  @override
  String toString() {
    return 'AppErrorState(showError: $showError, activeError: $activeError, handledErrors: $handledErrors, reportedErrors: $reportedErrors)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppErrorStateImpl &&
            (identical(other.showError, showError) ||
                other.showError == showError) &&
            (identical(other.activeError, activeError) ||
                other.activeError == activeError) &&
            const DeepCollectionEquality()
                .equals(other._handledErrors, _handledErrors) &&
            const DeepCollectionEquality()
                .equals(other._reportedErrors, _reportedErrors));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      showError,
      activeError,
      const DeepCollectionEquality().hash(_handledErrors),
      const DeepCollectionEquality().hash(_reportedErrors));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppErrorStateImplCopyWith<_$AppErrorStateImpl> get copyWith =>
      __$$AppErrorStateImplCopyWithImpl<_$AppErrorStateImpl>(this, _$identity);
}

abstract class _AppErrorState implements AppErrorState {
  factory _AppErrorState(
      {final UiEvent<Nothing>? showError,
      final AppError? activeError,
      final List<AppError> handledErrors,
      final List<AppError> reportedErrors}) = _$AppErrorStateImpl;

  @override
  UiEvent<Nothing>? get showError;
  @override
  AppError? get activeError;
  @override
  List<AppError> get handledErrors;
  @override
  List<AppError> get reportedErrors;
  @override
  @JsonKey(ignore: true)
  _$$AppErrorStateImplCopyWith<_$AppErrorStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
