import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'ai_suggestions_state.freezed.dart';
// part 'ai_suggestions_state.g.dart';

@freezed
class AiSuggestionsState with _$AiSuggestionsState {

  const AiSuggestionsState._();

  String? getSuggestion(SfId? convoId) => suggestions[convoId];

  const factory AiSuggestionsState({
    /// key: convoId (meuId), value: suggestion
    @Default({}) Map<SfId, String> suggestions,
  }) = _AiSuggestionsState;

  // factory AiSuggestionsState.fromJson(Map<String, dynamic> json) => _$AiSuggestionsStateFromJson(json);
}