import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';

part 'ai_suggestion.freezed.dart';
part 'ai_suggestion.g.dart';

@freezed
class AiSuggestion with _$AiSuggestion {
  const factory AiSuggestion({
    @ParseSfIdConverter() required SfId meuId,
    @Default(true) bool isLoading,
    String? suggestion,
  }) = _AiSuggestion;

  factory AiSuggestion.fromJson(Map<String, dynamic> json) => _$AiSuggestionFromJson(json);
}