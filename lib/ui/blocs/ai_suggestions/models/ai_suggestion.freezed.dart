// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_suggestion.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AiSuggestion _$AiSuggestionFromJson(Map<String, dynamic> json) {
  return _AiSuggestion.fromJson(json);
}

/// @nodoc
mixin _$AiSuggestion {
  @ParseSfIdConverter()
  SfId get meuId => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get suggestion => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AiSuggestionCopyWith<AiSuggestion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AiSuggestionCopyWith<$Res> {
  factory $AiSuggestionCopyWith(
          AiSuggestion value, $Res Function(AiSuggestion) then) =
      _$AiSuggestionCopyWithImpl<$Res, AiSuggestion>;
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId meuId, bool isLoading, String? suggestion});

  $SfIdCopyWith<$Res> get meuId;
}

/// @nodoc
class _$AiSuggestionCopyWithImpl<$Res, $Val extends AiSuggestion>
    implements $AiSuggestionCopyWith<$Res> {
  _$AiSuggestionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? meuId = null,
    Object? isLoading = null,
    Object? suggestion = freezed,
  }) {
    return _then(_value.copyWith(
      meuId: null == meuId
          ? _value.meuId
          : meuId // ignore: cast_nullable_to_non_nullable
              as SfId,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      suggestion: freezed == suggestion
          ? _value.suggestion
          : suggestion // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res> get meuId {
    return $SfIdCopyWith<$Res>(_value.meuId, (value) {
      return _then(_value.copyWith(meuId: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AiSuggestionImplCopyWith<$Res>
    implements $AiSuggestionCopyWith<$Res> {
  factory _$$AiSuggestionImplCopyWith(
          _$AiSuggestionImpl value, $Res Function(_$AiSuggestionImpl) then) =
      __$$AiSuggestionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@ParseSfIdConverter() SfId meuId, bool isLoading, String? suggestion});

  @override
  $SfIdCopyWith<$Res> get meuId;
}

/// @nodoc
class __$$AiSuggestionImplCopyWithImpl<$Res>
    extends _$AiSuggestionCopyWithImpl<$Res, _$AiSuggestionImpl>
    implements _$$AiSuggestionImplCopyWith<$Res> {
  __$$AiSuggestionImplCopyWithImpl(
      _$AiSuggestionImpl _value, $Res Function(_$AiSuggestionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? meuId = null,
    Object? isLoading = null,
    Object? suggestion = freezed,
  }) {
    return _then(_$AiSuggestionImpl(
      meuId: null == meuId
          ? _value.meuId
          : meuId // ignore: cast_nullable_to_non_nullable
              as SfId,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      suggestion: freezed == suggestion
          ? _value.suggestion
          : suggestion // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AiSuggestionImpl implements _AiSuggestion {
  const _$AiSuggestionImpl(
      {@ParseSfIdConverter() required this.meuId,
      this.isLoading = true,
      this.suggestion});

  factory _$AiSuggestionImpl.fromJson(Map<String, dynamic> json) =>
      _$$AiSuggestionImplFromJson(json);

  @override
  @ParseSfIdConverter()
  final SfId meuId;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? suggestion;

  @override
  String toString() {
    return 'AiSuggestion(meuId: $meuId, isLoading: $isLoading, suggestion: $suggestion)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AiSuggestionImpl &&
            (identical(other.meuId, meuId) || other.meuId == meuId) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.suggestion, suggestion) ||
                other.suggestion == suggestion));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, meuId, isLoading, suggestion);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AiSuggestionImplCopyWith<_$AiSuggestionImpl> get copyWith =>
      __$$AiSuggestionImplCopyWithImpl<_$AiSuggestionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AiSuggestionImplToJson(
      this,
    );
  }
}

abstract class _AiSuggestion implements AiSuggestion {
  const factory _AiSuggestion(
      {@ParseSfIdConverter() required final SfId meuId,
      final bool isLoading,
      final String? suggestion}) = _$AiSuggestionImpl;

  factory _AiSuggestion.fromJson(Map<String, dynamic> json) =
      _$AiSuggestionImpl.fromJson;

  @override
  @ParseSfIdConverter()
  SfId get meuId;
  @override
  bool get isLoading;
  @override
  String? get suggestion;
  @override
  @JsonKey(ignore: true)
  _$$AiSuggestionImplCopyWith<_$AiSuggestionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
