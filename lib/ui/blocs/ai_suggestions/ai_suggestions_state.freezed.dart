// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_suggestions_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AiSuggestionsState {
  /// key: convoId (meuId), value: suggestion
  Map<SfId, String> get suggestions => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AiSuggestionsStateCopyWith<AiSuggestionsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AiSuggestionsStateCopyWith<$Res> {
  factory $AiSuggestionsStateCopyWith(
          AiSuggestionsState value, $Res Function(AiSuggestionsState) then) =
      _$AiSuggestionsStateCopyWithImpl<$Res, AiSuggestionsState>;
  @useResult
  $Res call({Map<SfId, String> suggestions});
}

/// @nodoc
class _$AiSuggestionsStateCopyWithImpl<$Res, $Val extends AiSuggestionsState>
    implements $AiSuggestionsStateCopyWith<$Res> {
  _$AiSuggestionsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? suggestions = null,
  }) {
    return _then(_value.copyWith(
      suggestions: null == suggestions
          ? _value.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as Map<SfId, String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AiSuggestionsStateImplCopyWith<$Res>
    implements $AiSuggestionsStateCopyWith<$Res> {
  factory _$$AiSuggestionsStateImplCopyWith(_$AiSuggestionsStateImpl value,
          $Res Function(_$AiSuggestionsStateImpl) then) =
      __$$AiSuggestionsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<SfId, String> suggestions});
}

/// @nodoc
class __$$AiSuggestionsStateImplCopyWithImpl<$Res>
    extends _$AiSuggestionsStateCopyWithImpl<$Res, _$AiSuggestionsStateImpl>
    implements _$$AiSuggestionsStateImplCopyWith<$Res> {
  __$$AiSuggestionsStateImplCopyWithImpl(_$AiSuggestionsStateImpl _value,
      $Res Function(_$AiSuggestionsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? suggestions = null,
  }) {
    return _then(_$AiSuggestionsStateImpl(
      suggestions: null == suggestions
          ? _value._suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as Map<SfId, String>,
    ));
  }
}

/// @nodoc

class _$AiSuggestionsStateImpl extends _AiSuggestionsState {
  const _$AiSuggestionsStateImpl(
      {final Map<SfId, String> suggestions = const {}})
      : _suggestions = suggestions,
        super._();

  /// key: convoId (meuId), value: suggestion
  final Map<SfId, String> _suggestions;

  /// key: convoId (meuId), value: suggestion
  @override
  @JsonKey()
  Map<SfId, String> get suggestions {
    if (_suggestions is EqualUnmodifiableMapView) return _suggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_suggestions);
  }

  @override
  String toString() {
    return 'AiSuggestionsState(suggestions: $suggestions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AiSuggestionsStateImpl &&
            const DeepCollectionEquality()
                .equals(other._suggestions, _suggestions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_suggestions));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AiSuggestionsStateImplCopyWith<_$AiSuggestionsStateImpl> get copyWith =>
      __$$AiSuggestionsStateImplCopyWithImpl<_$AiSuggestionsStateImpl>(
          this, _$identity);
}

abstract class _AiSuggestionsState extends AiSuggestionsState {
  const factory _AiSuggestionsState({final Map<SfId, String> suggestions}) =
      _$AiSuggestionsStateImpl;
  const _AiSuggestionsState._() : super._();

  @override

  /// key: convoId (meuId), value: suggestion
  Map<SfId, String> get suggestions;
  @override
  @JsonKey(ignore: true)
  _$$AiSuggestionsStateImplCopyWith<_$AiSuggestionsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
