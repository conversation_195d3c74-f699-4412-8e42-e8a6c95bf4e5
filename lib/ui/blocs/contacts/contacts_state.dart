import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/use_cases/models/contact.dart';

part 'contacts_state.freezed.dart';

@freezed
class ContactsState with _$ContactsState {

  Contact? getContactByMeu(SfId? meuId) {
    if (meuId == null) {
      return null;
    }
    final contactId = meuContactAssociations[meuId];
    return contacts[contactId];
  }
  const ContactsState._();

  const factory ContactsState({
    @Default({}) Map<SfId, Contact> contacts,
    @Default({}) Map<SfId, SfId> meuContactAssociations,
  }) = _ContactsState;
}