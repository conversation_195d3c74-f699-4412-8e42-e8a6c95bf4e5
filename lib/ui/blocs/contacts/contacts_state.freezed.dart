// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contacts_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ContactsState {
  Map<SfId, Contact> get contacts => throw _privateConstructorUsedError;
  Map<SfId, SfId> get meuContactAssociations =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ContactsStateCopyWith<ContactsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactsStateCopyWith<$Res> {
  factory $ContactsStateCopyWith(
          ContactsState value, $Res Function(ContactsState) then) =
      _$ContactsStateCopyWithImpl<$Res, ContactsState>;
  @useResult
  $Res call(
      {Map<SfId, Contact> contacts, Map<SfId, SfId> meuContactAssociations});
}

/// @nodoc
class _$ContactsStateCopyWithImpl<$Res, $Val extends ContactsState>
    implements $ContactsStateCopyWith<$Res> {
  _$ContactsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
    Object? meuContactAssociations = null,
  }) {
    return _then(_value.copyWith(
      contacts: null == contacts
          ? _value.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Contact>,
      meuContactAssociations: null == meuContactAssociations
          ? _value.meuContactAssociations
          : meuContactAssociations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, SfId>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContactsStateImplCopyWith<$Res>
    implements $ContactsStateCopyWith<$Res> {
  factory _$$ContactsStateImplCopyWith(
          _$ContactsStateImpl value, $Res Function(_$ContactsStateImpl) then) =
      __$$ContactsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Map<SfId, Contact> contacts, Map<SfId, SfId> meuContactAssociations});
}

/// @nodoc
class __$$ContactsStateImplCopyWithImpl<$Res>
    extends _$ContactsStateCopyWithImpl<$Res, _$ContactsStateImpl>
    implements _$$ContactsStateImplCopyWith<$Res> {
  __$$ContactsStateImplCopyWithImpl(
      _$ContactsStateImpl _value, $Res Function(_$ContactsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
    Object? meuContactAssociations = null,
  }) {
    return _then(_$ContactsStateImpl(
      contacts: null == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Contact>,
      meuContactAssociations: null == meuContactAssociations
          ? _value._meuContactAssociations
          : meuContactAssociations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, SfId>,
    ));
  }
}

/// @nodoc

class _$ContactsStateImpl extends _ContactsState {
  const _$ContactsStateImpl(
      {final Map<SfId, Contact> contacts = const {},
      final Map<SfId, SfId> meuContactAssociations = const {}})
      : _contacts = contacts,
        _meuContactAssociations = meuContactAssociations,
        super._();

  final Map<SfId, Contact> _contacts;
  @override
  @JsonKey()
  Map<SfId, Contact> get contacts {
    if (_contacts is EqualUnmodifiableMapView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_contacts);
  }

  final Map<SfId, SfId> _meuContactAssociations;
  @override
  @JsonKey()
  Map<SfId, SfId> get meuContactAssociations {
    if (_meuContactAssociations is EqualUnmodifiableMapView)
      return _meuContactAssociations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_meuContactAssociations);
  }

  @override
  String toString() {
    return 'ContactsState(contacts: $contacts, meuContactAssociations: $meuContactAssociations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactsStateImpl &&
            const DeepCollectionEquality().equals(other._contacts, _contacts) &&
            const DeepCollectionEquality().equals(
                other._meuContactAssociations, _meuContactAssociations));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_contacts),
      const DeepCollectionEquality().hash(_meuContactAssociations));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactsStateImplCopyWith<_$ContactsStateImpl> get copyWith =>
      __$$ContactsStateImplCopyWithImpl<_$ContactsStateImpl>(this, _$identity);
}

abstract class _ContactsState extends ContactsState {
  const factory _ContactsState(
      {final Map<SfId, Contact> contacts,
      final Map<SfId, SfId> meuContactAssociations}) = _$ContactsStateImpl;
  const _ContactsState._() : super._();

  @override
  Map<SfId, Contact> get contacts;
  @override
  Map<SfId, SfId> get meuContactAssociations;
  @override
  @JsonKey(ignore: true)
  _$$ContactsStateImplCopyWith<_$ContactsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
