import "dart:async";
import "dart:io";
import "dart:typed_data";
import "dart:ui";

import "package:flutter/material.dart";
import "package:flutter_bloc/flutter_bloc.dart";
import "package:path_provider/path_provider.dart";
import "package:uuid/uuid.dart";
import "package:x1440/ui/blocs/ui_event.dart";

part 'image_viewer_event.dart';
part 'image_viewer_state.dart';

enum ImageViewerAction { share, save }

class ImageViewerBloc extends Bloc<ImageViewerEvent, ImageViewerState> {
  ImageViewerBloc() : super(ImageViewerState()) {
    on<ShareImageEvent>(_onShareImageEvent);
    on<SaveImageEvent>(_onSaveImageEvent);
    on<ImagePathEvent>(_onImagePathEvent);
    on<ImageSharedEvent>(_onImageSharedEvent);
    on<ImageSavedEvent>(_onImageSavedEvent);
    on<ToggleVisibilityEvent>(_onToggleVisibilityEvent);
  }

  // Handles the share image event
  FutureOr<void> _onShareImageEvent(
      ShareImageEvent event, Emitter<ImageViewerState> emit) {
    emit(state.copyWith(imageShared: true));
    _getImagePath(event.imageProvider, ImageViewerAction.share);
  }

  // Handles the request to save an image in the camera roll
  FutureOr<void> _onSaveImageEvent(
      SaveImageEvent event, Emitter<ImageViewerState> emit) {
    // Only save one time to avoid camera roll duplications
    if (!state.imageSaved) {
      _getImagePath(event.imageProvider, ImageViewerAction.save);
    }
  }

  // Handles the request to share an image
  FutureOr<void> _onImagePathEvent(
      ImagePathEvent event, Emitter<ImageViewerState> emit) {
    if (event.action == ImageViewerAction.share) {
      emit(state.copyWith(
          imagePath: event.imagePath,
          imageName: event.imageName,
          shareImage: UiEvent(Nothing())));
    } else {
      if (!state.imageSaved) {
        emit(state.copyWith(
            imagePath: event.imagePath,
            imageName: event.imageName,
            saveImage: UiEvent(Nothing())));
      }
    }
  }

  // Retrieves the image path given a provided imageProvider
  Future<void> _getImagePath(
      ImageProvider imageProvider, ImageViewerAction action) async {
    ImageStream imageStream = imageProvider.resolve(const ImageConfiguration());

    // Create the listener as a variable so we can remove it later
    ImageStreamListener? listener;
    listener = ImageStreamListener((info, _) async {
      // Remove the listener as soon as we get the image
      imageStream.removeListener(listener!);

      info.image
          .toByteData(format: ImageByteFormat.png)
          .then((ByteData? byteData) async {
        final directory = await getTemporaryDirectory();
        String imageName = const Uuid().v4();
        final file = File('${directory.path}/$imageName.png');
        await file.writeAsBytes(byteData!.buffer.asUint8List());
        add(ImagePathEvent(
            imageName: imageName, imagePath: file.path, action: action));
      });
    });

    imageStream.addListener(listener);
  }

  // Updates the state to reflect that the image has been shared
  // Note we do not do this with Save as we can only save once
  FutureOr<void> _onImageSharedEvent(
      ImageSharedEvent event, Emitter<ImageViewerState> emit) {
    emit(state.copyWith(imageShared: false));
  }

  // As we can only save once, we update the state to reflect that the image has been saved and cannot be saved again
  FutureOr<void> _onImageSavedEvent(
      ImageSavedEvent event, Emitter<ImageViewerState> emit) async {
    // final directory = await getTemporaryDirectory();
    // final file = File('${directory.path}/${state.imageName}');
    // await file.delete();
    emit(state.copyWith(imageSaved: true));
  }

  FutureOr<void> _onToggleVisibilityEvent(
      ToggleVisibilityEvent event, Emitter<ImageViewerState> emit) {
    emit(state.copyWith(visible: !state.visible));
  }
}
