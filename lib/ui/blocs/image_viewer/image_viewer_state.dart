part of 'image_viewer_bloc.dart';

class ImageViewerState {
  final bool imageShared;
  final bool imageSaved;
  final bool visible;
  final String? imagePath;
  final String? imageName;

  final UiEvent<Nothing>? shareImage;
  final UiEvent<Nothing>? saveImage;

  ImageViewerState([
    this.imageShared = false,
    this.imageSaved = false,
    this.visible = true,
    this.imagePath,
    this.imageName,
    this.shareImage,
    this.saveImage,
  ]);

  ImageViewerState copyWith(
      {bool? imageShared,
      bool? imageSaved,
      bool? visible,
      String? imagePath,
      String? imageName,
      UiEvent<Nothing>? shareImage,
      UiEvent<Nothing>? saveImage}) {
    return ImageViewerState(
        imageShared ?? this.imageShared,
        imageSaved ?? this.imageSaved,
        visible ?? this.visible,
        imagePath ?? this.imagePath,
        imageName ?? this.imageName,
        shareImage ?? this.shareImage,
        saveImage ?? this.saveImage);
  }
}
