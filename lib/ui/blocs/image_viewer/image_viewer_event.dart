part of 'image_viewer_bloc.dart';

abstract class ImageViewerEvent {}

class ShareImageEvent extends ImageViewerEvent {
  final ImageProvider imageProvider;
  ShareImageEvent({required this.imageProvider});
}

class ToggleVisibilityEvent extends ImageViewerEvent {}

class ImageSharedEvent extends ImageViewerEvent {}

class ImageSavedEvent extends ImageViewerEvent {}

class SaveImageEvent extends ImageViewerEvent {
  final ImageProvider imageProvider;
  SaveImageEvent({required this.imageProvider});
}

class ImagePathEvent extends ImageViewerEvent {
  final String imagePath;
  final String imageName;
  final ImageViewerAction action;
  ImagePathEvent(
      {required this.imageName, required this.action, required this.imagePath});
}
