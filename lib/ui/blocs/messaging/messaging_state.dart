import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';
import 'package:x1440/repositories/models/shim_websocket_message.dart';
import 'package:x1440/repositories/websocket/models/websocket_connection.dart';

part 'messaging_state.freezed.dart';

@freezed
class MessagingState with _$MessagingState {
  const factory MessagingState({
    @Default(<ShimWebsocketMessage>[]) List<ShimWebsocketMessage> websocketReceivedMessages,
    @Default(<QueueReceiveMessage>[]) List<QueueReceiveMessage> queueReceivedMessages,
    @Default(<QueueSendMessage>[]) List<QueueSendMessage> queueSendMessages,
    @Default(WebsocketConnection()) WebsocketConnection webSocketConnectionState,
    @Default(ConnectivityResult.none) ConnectivityResult connectivityState,
  }) = _MessagingState;
}
