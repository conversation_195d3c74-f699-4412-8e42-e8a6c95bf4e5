// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MessagingState {
  List<ShimWebsocketMessage> get websocketReceivedMessages =>
      throw _privateConstructorUsedError;
  List<QueueReceiveMessage> get queueReceivedMessages =>
      throw _privateConstructorUsedError;
  List<QueueSendMessage> get queueSendMessages =>
      throw _privateConstructorUsedError;
  WebsocketConnection get webSocketConnectionState =>
      throw _privateConstructorUsedError;
  ConnectivityResult get connectivityState =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MessagingStateCopyWith<MessagingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingStateCopyWith<$Res> {
  factory $MessagingStateCopyWith(
          MessagingState value, $Res Function(MessagingState) then) =
      _$MessagingStateCopyWithImpl<$Res, MessagingState>;
  @useResult
  $Res call(
      {List<ShimWebsocketMessage> websocketReceivedMessages,
      List<QueueReceiveMessage> queueReceivedMessages,
      List<QueueSendMessage> queueSendMessages,
      WebsocketConnection webSocketConnectionState,
      ConnectivityResult connectivityState});

  $WebsocketConnectionCopyWith<$Res> get webSocketConnectionState;
}

/// @nodoc
class _$MessagingStateCopyWithImpl<$Res, $Val extends MessagingState>
    implements $MessagingStateCopyWith<$Res> {
  _$MessagingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? websocketReceivedMessages = null,
    Object? queueReceivedMessages = null,
    Object? queueSendMessages = null,
    Object? webSocketConnectionState = null,
    Object? connectivityState = null,
  }) {
    return _then(_value.copyWith(
      websocketReceivedMessages: null == websocketReceivedMessages
          ? _value.websocketReceivedMessages
          : websocketReceivedMessages // ignore: cast_nullable_to_non_nullable
              as List<ShimWebsocketMessage>,
      queueReceivedMessages: null == queueReceivedMessages
          ? _value.queueReceivedMessages
          : queueReceivedMessages // ignore: cast_nullable_to_non_nullable
              as List<QueueReceiveMessage>,
      queueSendMessages: null == queueSendMessages
          ? _value.queueSendMessages
          : queueSendMessages // ignore: cast_nullable_to_non_nullable
              as List<QueueSendMessage>,
      webSocketConnectionState: null == webSocketConnectionState
          ? _value.webSocketConnectionState
          : webSocketConnectionState // ignore: cast_nullable_to_non_nullable
              as WebsocketConnection,
      connectivityState: null == connectivityState
          ? _value.connectivityState
          : connectivityState // ignore: cast_nullable_to_non_nullable
              as ConnectivityResult,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $WebsocketConnectionCopyWith<$Res> get webSocketConnectionState {
    return $WebsocketConnectionCopyWith<$Res>(_value.webSocketConnectionState,
        (value) {
      return _then(_value.copyWith(webSocketConnectionState: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingStateImplCopyWith<$Res>
    implements $MessagingStateCopyWith<$Res> {
  factory _$$MessagingStateImplCopyWith(_$MessagingStateImpl value,
          $Res Function(_$MessagingStateImpl) then) =
      __$$MessagingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ShimWebsocketMessage> websocketReceivedMessages,
      List<QueueReceiveMessage> queueReceivedMessages,
      List<QueueSendMessage> queueSendMessages,
      WebsocketConnection webSocketConnectionState,
      ConnectivityResult connectivityState});

  @override
  $WebsocketConnectionCopyWith<$Res> get webSocketConnectionState;
}

/// @nodoc
class __$$MessagingStateImplCopyWithImpl<$Res>
    extends _$MessagingStateCopyWithImpl<$Res, _$MessagingStateImpl>
    implements _$$MessagingStateImplCopyWith<$Res> {
  __$$MessagingStateImplCopyWithImpl(
      _$MessagingStateImpl _value, $Res Function(_$MessagingStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? websocketReceivedMessages = null,
    Object? queueReceivedMessages = null,
    Object? queueSendMessages = null,
    Object? webSocketConnectionState = null,
    Object? connectivityState = null,
  }) {
    return _then(_$MessagingStateImpl(
      websocketReceivedMessages: null == websocketReceivedMessages
          ? _value._websocketReceivedMessages
          : websocketReceivedMessages // ignore: cast_nullable_to_non_nullable
              as List<ShimWebsocketMessage>,
      queueReceivedMessages: null == queueReceivedMessages
          ? _value._queueReceivedMessages
          : queueReceivedMessages // ignore: cast_nullable_to_non_nullable
              as List<QueueReceiveMessage>,
      queueSendMessages: null == queueSendMessages
          ? _value._queueSendMessages
          : queueSendMessages // ignore: cast_nullable_to_non_nullable
              as List<QueueSendMessage>,
      webSocketConnectionState: null == webSocketConnectionState
          ? _value.webSocketConnectionState
          : webSocketConnectionState // ignore: cast_nullable_to_non_nullable
              as WebsocketConnection,
      connectivityState: null == connectivityState
          ? _value.connectivityState
          : connectivityState // ignore: cast_nullable_to_non_nullable
              as ConnectivityResult,
    ));
  }
}

/// @nodoc

class _$MessagingStateImpl implements _MessagingState {
  const _$MessagingStateImpl(
      {final List<ShimWebsocketMessage> websocketReceivedMessages =
          const <ShimWebsocketMessage>[],
      final List<QueueReceiveMessage> queueReceivedMessages =
          const <QueueReceiveMessage>[],
      final List<QueueSendMessage> queueSendMessages =
          const <QueueSendMessage>[],
      this.webSocketConnectionState = const WebsocketConnection(),
      this.connectivityState = ConnectivityResult.none})
      : _websocketReceivedMessages = websocketReceivedMessages,
        _queueReceivedMessages = queueReceivedMessages,
        _queueSendMessages = queueSendMessages;

  final List<ShimWebsocketMessage> _websocketReceivedMessages;
  @override
  @JsonKey()
  List<ShimWebsocketMessage> get websocketReceivedMessages {
    if (_websocketReceivedMessages is EqualUnmodifiableListView)
      return _websocketReceivedMessages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_websocketReceivedMessages);
  }

  final List<QueueReceiveMessage> _queueReceivedMessages;
  @override
  @JsonKey()
  List<QueueReceiveMessage> get queueReceivedMessages {
    if (_queueReceivedMessages is EqualUnmodifiableListView)
      return _queueReceivedMessages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_queueReceivedMessages);
  }

  final List<QueueSendMessage> _queueSendMessages;
  @override
  @JsonKey()
  List<QueueSendMessage> get queueSendMessages {
    if (_queueSendMessages is EqualUnmodifiableListView)
      return _queueSendMessages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_queueSendMessages);
  }

  @override
  @JsonKey()
  final WebsocketConnection webSocketConnectionState;
  @override
  @JsonKey()
  final ConnectivityResult connectivityState;

  @override
  String toString() {
    return 'MessagingState(websocketReceivedMessages: $websocketReceivedMessages, queueReceivedMessages: $queueReceivedMessages, queueSendMessages: $queueSendMessages, webSocketConnectionState: $webSocketConnectionState, connectivityState: $connectivityState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingStateImpl &&
            const DeepCollectionEquality().equals(
                other._websocketReceivedMessages, _websocketReceivedMessages) &&
            const DeepCollectionEquality()
                .equals(other._queueReceivedMessages, _queueReceivedMessages) &&
            const DeepCollectionEquality()
                .equals(other._queueSendMessages, _queueSendMessages) &&
            (identical(
                    other.webSocketConnectionState, webSocketConnectionState) ||
                other.webSocketConnectionState == webSocketConnectionState) &&
            (identical(other.connectivityState, connectivityState) ||
                other.connectivityState == connectivityState));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_websocketReceivedMessages),
      const DeepCollectionEquality().hash(_queueReceivedMessages),
      const DeepCollectionEquality().hash(_queueSendMessages),
      webSocketConnectionState,
      connectivityState);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingStateImplCopyWith<_$MessagingStateImpl> get copyWith =>
      __$$MessagingStateImplCopyWithImpl<_$MessagingStateImpl>(
          this, _$identity);
}

abstract class _MessagingState implements MessagingState {
  const factory _MessagingState(
      {final List<ShimWebsocketMessage> websocketReceivedMessages,
      final List<QueueReceiveMessage> queueReceivedMessages,
      final List<QueueSendMessage> queueSendMessages,
      final WebsocketConnection webSocketConnectionState,
      final ConnectivityResult connectivityState}) = _$MessagingStateImpl;

  @override
  List<ShimWebsocketMessage> get websocketReceivedMessages;
  @override
  List<QueueReceiveMessage> get queueReceivedMessages;
  @override
  List<QueueSendMessage> get queueSendMessages;
  @override
  WebsocketConnection get webSocketConnectionState;
  @override
  ConnectivityResult get connectivityState;
  @override
  @JsonKey(ignore: true)
  _$$MessagingStateImplCopyWith<_$MessagingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
