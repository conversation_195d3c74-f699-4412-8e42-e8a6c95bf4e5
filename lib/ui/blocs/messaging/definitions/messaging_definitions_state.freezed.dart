// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'messaging_definitions_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MessagingDefinitionsState {
  MessagingDefinitionStatus? get messagingDefinitionStatus =>
      throw _privateConstructorUsedError;
  MessagingChannelType? get messagingType => throw _privateConstructorUsedError;
  SfId? get messagingEndUser => throw _privateConstructorUsedError;
  List<MessagingDefinition>? get filteredDefinitions =>
      throw _privateConstructorUsedError;
  MessagingDefinition? get selectedMessagingDefinition =>
      throw _privateConstructorUsedError;
  UiEvent<Nothing>? get navigateToChat => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MessagingDefinitionsStateCopyWith<MessagingDefinitionsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessagingDefinitionsStateCopyWith<$Res> {
  factory $MessagingDefinitionsStateCopyWith(MessagingDefinitionsState value,
          $Res Function(MessagingDefinitionsState) then) =
      _$MessagingDefinitionsStateCopyWithImpl<$Res, MessagingDefinitionsState>;
  @useResult
  $Res call(
      {MessagingDefinitionStatus? messagingDefinitionStatus,
      MessagingChannelType? messagingType,
      SfId? messagingEndUser,
      List<MessagingDefinition>? filteredDefinitions,
      MessagingDefinition? selectedMessagingDefinition,
      UiEvent<Nothing>? navigateToChat});

  $MessagingDefinitionStatusCopyWith<$Res>? get messagingDefinitionStatus;
  $SfIdCopyWith<$Res>? get messagingEndUser;
  $MessagingDefinitionCopyWith<$Res>? get selectedMessagingDefinition;
}

/// @nodoc
class _$MessagingDefinitionsStateCopyWithImpl<$Res,
        $Val extends MessagingDefinitionsState>
    implements $MessagingDefinitionsStateCopyWith<$Res> {
  _$MessagingDefinitionsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingDefinitionStatus = freezed,
    Object? messagingType = freezed,
    Object? messagingEndUser = freezed,
    Object? filteredDefinitions = freezed,
    Object? selectedMessagingDefinition = freezed,
    Object? navigateToChat = freezed,
  }) {
    return _then(_value.copyWith(
      messagingDefinitionStatus: freezed == messagingDefinitionStatus
          ? _value.messagingDefinitionStatus
          : messagingDefinitionStatus // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionStatus?,
      messagingType: freezed == messagingType
          ? _value.messagingType
          : messagingType // ignore: cast_nullable_to_non_nullable
              as MessagingChannelType?,
      messagingEndUser: freezed == messagingEndUser
          ? _value.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as SfId?,
      filteredDefinitions: freezed == filteredDefinitions
          ? _value.filteredDefinitions
          : filteredDefinitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>?,
      selectedMessagingDefinition: freezed == selectedMessagingDefinition
          ? _value.selectedMessagingDefinition
          : selectedMessagingDefinition // ignore: cast_nullable_to_non_nullable
              as MessagingDefinition?,
      navigateToChat: freezed == navigateToChat
          ? _value.navigateToChat
          : navigateToChat // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionStatusCopyWith<$Res>? get messagingDefinitionStatus {
    if (_value.messagingDefinitionStatus == null) {
      return null;
    }

    return $MessagingDefinitionStatusCopyWith<$Res>(
        _value.messagingDefinitionStatus!, (value) {
      return _then(_value.copyWith(messagingDefinitionStatus: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SfIdCopyWith<$Res>? get messagingEndUser {
    if (_value.messagingEndUser == null) {
      return null;
    }

    return $SfIdCopyWith<$Res>(_value.messagingEndUser!, (value) {
      return _then(_value.copyWith(messagingEndUser: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingDefinitionCopyWith<$Res>? get selectedMessagingDefinition {
    if (_value.selectedMessagingDefinition == null) {
      return null;
    }

    return $MessagingDefinitionCopyWith<$Res>(
        _value.selectedMessagingDefinition!, (value) {
      return _then(_value.copyWith(selectedMessagingDefinition: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MessagingDefinitionsStateImplCopyWith<$Res>
    implements $MessagingDefinitionsStateCopyWith<$Res> {
  factory _$$MessagingDefinitionsStateImplCopyWith(
          _$MessagingDefinitionsStateImpl value,
          $Res Function(_$MessagingDefinitionsStateImpl) then) =
      __$$MessagingDefinitionsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MessagingDefinitionStatus? messagingDefinitionStatus,
      MessagingChannelType? messagingType,
      SfId? messagingEndUser,
      List<MessagingDefinition>? filteredDefinitions,
      MessagingDefinition? selectedMessagingDefinition,
      UiEvent<Nothing>? navigateToChat});

  @override
  $MessagingDefinitionStatusCopyWith<$Res>? get messagingDefinitionStatus;
  @override
  $SfIdCopyWith<$Res>? get messagingEndUser;
  @override
  $MessagingDefinitionCopyWith<$Res>? get selectedMessagingDefinition;
}

/// @nodoc
class __$$MessagingDefinitionsStateImplCopyWithImpl<$Res>
    extends _$MessagingDefinitionsStateCopyWithImpl<$Res,
        _$MessagingDefinitionsStateImpl>
    implements _$$MessagingDefinitionsStateImplCopyWith<$Res> {
  __$$MessagingDefinitionsStateImplCopyWithImpl(
      _$MessagingDefinitionsStateImpl _value,
      $Res Function(_$MessagingDefinitionsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messagingDefinitionStatus = freezed,
    Object? messagingType = freezed,
    Object? messagingEndUser = freezed,
    Object? filteredDefinitions = freezed,
    Object? selectedMessagingDefinition = freezed,
    Object? navigateToChat = freezed,
  }) {
    return _then(_$MessagingDefinitionsStateImpl(
      messagingDefinitionStatus: freezed == messagingDefinitionStatus
          ? _value.messagingDefinitionStatus
          : messagingDefinitionStatus // ignore: cast_nullable_to_non_nullable
              as MessagingDefinitionStatus?,
      messagingType: freezed == messagingType
          ? _value.messagingType
          : messagingType // ignore: cast_nullable_to_non_nullable
              as MessagingChannelType?,
      messagingEndUser: freezed == messagingEndUser
          ? _value.messagingEndUser
          : messagingEndUser // ignore: cast_nullable_to_non_nullable
              as SfId?,
      filteredDefinitions: freezed == filteredDefinitions
          ? _value._filteredDefinitions
          : filteredDefinitions // ignore: cast_nullable_to_non_nullable
              as List<MessagingDefinition>?,
      selectedMessagingDefinition: freezed == selectedMessagingDefinition
          ? _value.selectedMessagingDefinition
          : selectedMessagingDefinition // ignore: cast_nullable_to_non_nullable
              as MessagingDefinition?,
      navigateToChat: freezed == navigateToChat
          ? _value.navigateToChat
          : navigateToChat // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }
}

/// @nodoc

class _$MessagingDefinitionsStateImpl implements _MessagingDefinitionsState {
  const _$MessagingDefinitionsStateImpl(
      {this.messagingDefinitionStatus,
      this.messagingType,
      this.messagingEndUser,
      final List<MessagingDefinition>? filteredDefinitions,
      this.selectedMessagingDefinition,
      this.navigateToChat})
      : _filteredDefinitions = filteredDefinitions;

  @override
  final MessagingDefinitionStatus? messagingDefinitionStatus;
  @override
  final MessagingChannelType? messagingType;
  @override
  final SfId? messagingEndUser;
  final List<MessagingDefinition>? _filteredDefinitions;
  @override
  List<MessagingDefinition>? get filteredDefinitions {
    final value = _filteredDefinitions;
    if (value == null) return null;
    if (_filteredDefinitions is EqualUnmodifiableListView)
      return _filteredDefinitions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final MessagingDefinition? selectedMessagingDefinition;
  @override
  final UiEvent<Nothing>? navigateToChat;

  @override
  String toString() {
    return 'MessagingDefinitionsState(messagingDefinitionStatus: $messagingDefinitionStatus, messagingType: $messagingType, messagingEndUser: $messagingEndUser, filteredDefinitions: $filteredDefinitions, selectedMessagingDefinition: $selectedMessagingDefinition, navigateToChat: $navigateToChat)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MessagingDefinitionsStateImpl &&
            (identical(other.messagingDefinitionStatus,
                    messagingDefinitionStatus) ||
                other.messagingDefinitionStatus == messagingDefinitionStatus) &&
            (identical(other.messagingType, messagingType) ||
                other.messagingType == messagingType) &&
            (identical(other.messagingEndUser, messagingEndUser) ||
                other.messagingEndUser == messagingEndUser) &&
            const DeepCollectionEquality()
                .equals(other._filteredDefinitions, _filteredDefinitions) &&
            (identical(other.selectedMessagingDefinition,
                    selectedMessagingDefinition) ||
                other.selectedMessagingDefinition ==
                    selectedMessagingDefinition) &&
            (identical(other.navigateToChat, navigateToChat) ||
                other.navigateToChat == navigateToChat));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      messagingDefinitionStatus,
      messagingType,
      messagingEndUser,
      const DeepCollectionEquality().hash(_filteredDefinitions),
      selectedMessagingDefinition,
      navigateToChat);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MessagingDefinitionsStateImplCopyWith<_$MessagingDefinitionsStateImpl>
      get copyWith => __$$MessagingDefinitionsStateImplCopyWithImpl<
          _$MessagingDefinitionsStateImpl>(this, _$identity);
}

abstract class _MessagingDefinitionsState implements MessagingDefinitionsState {
  const factory _MessagingDefinitionsState(
          {final MessagingDefinitionStatus? messagingDefinitionStatus,
          final MessagingChannelType? messagingType,
          final SfId? messagingEndUser,
          final List<MessagingDefinition>? filteredDefinitions,
          final MessagingDefinition? selectedMessagingDefinition,
          final UiEvent<Nothing>? navigateToChat}) =
      _$MessagingDefinitionsStateImpl;

  @override
  MessagingDefinitionStatus? get messagingDefinitionStatus;
  @override
  MessagingChannelType? get messagingType;
  @override
  SfId? get messagingEndUser;
  @override
  List<MessagingDefinition>? get filteredDefinitions;
  @override
  MessagingDefinition? get selectedMessagingDefinition;
  @override
  UiEvent<Nothing>? get navigateToChat;
  @override
  @JsonKey(ignore: true)
  _$$MessagingDefinitionsStateImplCopyWith<_$MessagingDefinitionsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
