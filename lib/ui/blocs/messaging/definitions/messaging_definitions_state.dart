import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/messaging_definition.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/ui/blocs/ui_event.dart';

import 'package:x1440/use_cases/messaging/models/messaging_definition_status.dart';

part 'messaging_definitions_state.freezed.dart';

@freezed
class MessagingDefinitionsState with _$MessagingDefinitionsState {
  const factory MessagingDefinitionsState(
      {MessagingDefinitionStatus? messagingDefinitionStatus,
      MessagingChannelType? messagingType,
      SfId? messagingEndUser,
      List<MessagingDefinition>? filteredDefinitions,
      MessagingDefinition? selectedMessagingDefinition,
      final UiEvent<Nothing>? navigateToChat}) = _MessagingDefinitionsState;
}
