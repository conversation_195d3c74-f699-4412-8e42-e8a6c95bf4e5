import 'dart:async';

import 'package:x1440/api/dtos/messaging_definition.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/messaging/messaging_definitions_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'messaging_definitions_event.dart';
import 'messaging_definitions_state.dart';

class MessagingDefinitionsBloc
    extends Bloc<MessagingDefinitionsEvent, MessagingDefinitionsState> {
  final MessagingDefinitionsUseCase messagingDefinitionsUseCase;

  static const minimumSearchTermLength = 3;

  MessagingDefinitionsBloc(this.messagingDefinitionsUseCase)
      : super(const MessagingDefinitionsState()) {
    //on<GetMessagingDefinitions>(_onGetMessagingDefinitions);
    on<SelectMessagingDefinition>(_onSelectMessagingDefinition);
    on<SendMessagingDefinition>(_onSendMessagingDefinition);
    on<InitDefinitions>(_onInitDefinitions);
    on<SearchDefinitions>(_onSearchDefinitions);
  }

  FutureOr<void> _onSelectMessagingDefinition(SelectMessagingDefinition event,
      Emitter<MessagingDefinitionsState> emit) {
    if (event.messagingDefinition == state.selectedMessagingDefinition) {
      emit(state.copyWith(selectedMessagingDefinition: null));
    } else {
      emit(state.copyWith(
          selectedMessagingDefinition: event.messagingDefinition));
    }
  }

  FutureOr<void> _onSendMessagingDefinition(
      SendMessagingDefinition event, Emitter<MessagingDefinitionsState> emit) {
    if (state.selectedMessagingDefinition != null) {
      // The ChatBloc init takes care of preparing the conversation and therefore must send the message
      emit(state.copyWith(navigateToChat: UiEvent(Nothing())));
    }
  }

  // This should be called from the UI that has the Send Message CTA
  // It loads the definitions and sets the initial state
  FutureOr<void> _onInitDefinitions(
      InitDefinitions event, Emitter<MessagingDefinitionsState> emit) {
    emit(state.copyWith(
        selectedMessagingDefinition: null,
        filteredDefinitions: null,
        messagingDefinitionStatus: event.definitionsStatus,
        messagingEndUser: event.messagingEndUserId,
        messagingType: event.messagingType));
  }

  FutureOr<void> _onSearchDefinitions(
      SearchDefinitions event, Emitter<MessagingDefinitionsState> emit) {
    List<MessagingDefinition> filteredDefinitions = [];
    if (event.searchTerm.isEmpty) {
      emit(state.copyWith(filteredDefinitions: null));
    } else {
      if (event.searchTerm.length >= minimumSearchTermLength) {
        if (state.messagingDefinitionStatus != null &&
            state.messagingDefinitionStatus!.definitions != null) {
          filteredDefinitions =
              messagingDefinitionsUseCase.searchMessagingDefinitions(
                  state.messagingDefinitionStatus!.definitions!,
                  event.searchTerm);
          if (filteredDefinitions.isEmpty) {
            emit(state.copyWith(filteredDefinitions: []));
          } else {
            if (filteredDefinitions.isNotEmpty) {
              emit(state.copyWith(filteredDefinitions: filteredDefinitions));
            }
          }
        }
      }
    }
  }
}
