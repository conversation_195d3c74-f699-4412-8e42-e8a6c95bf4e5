// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ContactState {
  Contact? get contact => throw _privateConstructorUsedError;
  Contact? get savedContact => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isEditing => throw _privateConstructorUsedError;
  bool get showFirstNameErrorText => throw _privateConstructorUsedError;
  bool get showLastNameErrorText => throw _privateConstructorUsedError;
  bool get showPhoneNumberErrorText => throw _privateConstructorUsedError;
  bool get showEmailErrorText => throw _privateConstructorUsedError;
  MessagingChannelEntry? get messagingChannelEntry =>
      throw _privateConstructorUsedError;
  UiEvent<SfId>? get goToConversation => throw _privateConstructorUsedError;
  UiEvent<SfId>? get goToNewConversation => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ContactStateCopyWith<ContactState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactStateCopyWith<$Res> {
  factory $ContactStateCopyWith(
          ContactState value, $Res Function(ContactState) then) =
      _$ContactStateCopyWithImpl<$Res, ContactState>;
  @useResult
  $Res call(
      {Contact? contact,
      Contact? savedContact,
      bool isLoading,
      bool isEditing,
      bool showFirstNameErrorText,
      bool showLastNameErrorText,
      bool showPhoneNumberErrorText,
      bool showEmailErrorText,
      MessagingChannelEntry? messagingChannelEntry,
      UiEvent<SfId>? goToConversation,
      UiEvent<SfId>? goToNewConversation});

  $ContactCopyWith<$Res>? get contact;
  $ContactCopyWith<$Res>? get savedContact;
  $MessagingChannelEntryCopyWith<$Res>? get messagingChannelEntry;
}

/// @nodoc
class _$ContactStateCopyWithImpl<$Res, $Val extends ContactState>
    implements $ContactStateCopyWith<$Res> {
  _$ContactStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contact = freezed,
    Object? savedContact = freezed,
    Object? isLoading = null,
    Object? isEditing = null,
    Object? showFirstNameErrorText = null,
    Object? showLastNameErrorText = null,
    Object? showPhoneNumberErrorText = null,
    Object? showEmailErrorText = null,
    Object? messagingChannelEntry = freezed,
    Object? goToConversation = freezed,
    Object? goToNewConversation = freezed,
  }) {
    return _then(_value.copyWith(
      contact: freezed == contact
          ? _value.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      savedContact: freezed == savedContact
          ? _value.savedContact
          : savedContact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEditing: null == isEditing
          ? _value.isEditing
          : isEditing // ignore: cast_nullable_to_non_nullable
              as bool,
      showFirstNameErrorText: null == showFirstNameErrorText
          ? _value.showFirstNameErrorText
          : showFirstNameErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showLastNameErrorText: null == showLastNameErrorText
          ? _value.showLastNameErrorText
          : showLastNameErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showPhoneNumberErrorText: null == showPhoneNumberErrorText
          ? _value.showPhoneNumberErrorText
          : showPhoneNumberErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showEmailErrorText: null == showEmailErrorText
          ? _value.showEmailErrorText
          : showEmailErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingChannelEntry: freezed == messagingChannelEntry
          ? _value.messagingChannelEntry
          : messagingChannelEntry // ignore: cast_nullable_to_non_nullable
              as MessagingChannelEntry?,
      goToConversation: freezed == goToConversation
          ? _value.goToConversation
          : goToConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      goToNewConversation: freezed == goToNewConversation
          ? _value.goToNewConversation
          : goToNewConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get contact {
    if (_value.contact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_value.contact!, (value) {
      return _then(_value.copyWith(contact: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ContactCopyWith<$Res>? get savedContact {
    if (_value.savedContact == null) {
      return null;
    }

    return $ContactCopyWith<$Res>(_value.savedContact!, (value) {
      return _then(_value.copyWith(savedContact: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelEntryCopyWith<$Res>? get messagingChannelEntry {
    if (_value.messagingChannelEntry == null) {
      return null;
    }

    return $MessagingChannelEntryCopyWith<$Res>(_value.messagingChannelEntry!,
        (value) {
      return _then(_value.copyWith(messagingChannelEntry: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ContactStateImplCopyWith<$Res>
    implements $ContactStateCopyWith<$Res> {
  factory _$$ContactStateImplCopyWith(
          _$ContactStateImpl value, $Res Function(_$ContactStateImpl) then) =
      __$$ContactStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Contact? contact,
      Contact? savedContact,
      bool isLoading,
      bool isEditing,
      bool showFirstNameErrorText,
      bool showLastNameErrorText,
      bool showPhoneNumberErrorText,
      bool showEmailErrorText,
      MessagingChannelEntry? messagingChannelEntry,
      UiEvent<SfId>? goToConversation,
      UiEvent<SfId>? goToNewConversation});

  @override
  $ContactCopyWith<$Res>? get contact;
  @override
  $ContactCopyWith<$Res>? get savedContact;
  @override
  $MessagingChannelEntryCopyWith<$Res>? get messagingChannelEntry;
}

/// @nodoc
class __$$ContactStateImplCopyWithImpl<$Res>
    extends _$ContactStateCopyWithImpl<$Res, _$ContactStateImpl>
    implements _$$ContactStateImplCopyWith<$Res> {
  __$$ContactStateImplCopyWithImpl(
      _$ContactStateImpl _value, $Res Function(_$ContactStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contact = freezed,
    Object? savedContact = freezed,
    Object? isLoading = null,
    Object? isEditing = null,
    Object? showFirstNameErrorText = null,
    Object? showLastNameErrorText = null,
    Object? showPhoneNumberErrorText = null,
    Object? showEmailErrorText = null,
    Object? messagingChannelEntry = freezed,
    Object? goToConversation = freezed,
    Object? goToNewConversation = freezed,
  }) {
    return _then(_$ContactStateImpl(
      contact: freezed == contact
          ? _value.contact
          : contact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      savedContact: freezed == savedContact
          ? _value.savedContact
          : savedContact // ignore: cast_nullable_to_non_nullable
              as Contact?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEditing: null == isEditing
          ? _value.isEditing
          : isEditing // ignore: cast_nullable_to_non_nullable
              as bool,
      showFirstNameErrorText: null == showFirstNameErrorText
          ? _value.showFirstNameErrorText
          : showFirstNameErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showLastNameErrorText: null == showLastNameErrorText
          ? _value.showLastNameErrorText
          : showLastNameErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showPhoneNumberErrorText: null == showPhoneNumberErrorText
          ? _value.showPhoneNumberErrorText
          : showPhoneNumberErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      showEmailErrorText: null == showEmailErrorText
          ? _value.showEmailErrorText
          : showEmailErrorText // ignore: cast_nullable_to_non_nullable
              as bool,
      messagingChannelEntry: freezed == messagingChannelEntry
          ? _value.messagingChannelEntry
          : messagingChannelEntry // ignore: cast_nullable_to_non_nullable
              as MessagingChannelEntry?,
      goToConversation: freezed == goToConversation
          ? _value.goToConversation
          : goToConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      goToNewConversation: freezed == goToNewConversation
          ? _value.goToNewConversation
          : goToNewConversation // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
    ));
  }
}

/// @nodoc

class _$ContactStateImpl extends _ContactState {
  const _$ContactStateImpl(
      {this.contact,
      this.savedContact,
      this.isLoading = true,
      this.isEditing = false,
      this.showFirstNameErrorText = false,
      this.showLastNameErrorText = false,
      this.showPhoneNumberErrorText = false,
      this.showEmailErrorText = false,
      this.messagingChannelEntry,
      this.goToConversation,
      this.goToNewConversation})
      : super._();

  @override
  final Contact? contact;
  @override
  final Contact? savedContact;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isEditing;
  @override
  @JsonKey()
  final bool showFirstNameErrorText;
  @override
  @JsonKey()
  final bool showLastNameErrorText;
  @override
  @JsonKey()
  final bool showPhoneNumberErrorText;
  @override
  @JsonKey()
  final bool showEmailErrorText;
  @override
  final MessagingChannelEntry? messagingChannelEntry;
  @override
  final UiEvent<SfId>? goToConversation;
  @override
  final UiEvent<SfId>? goToNewConversation;

  @override
  String toString() {
    return 'ContactState(contact: $contact, savedContact: $savedContact, isLoading: $isLoading, isEditing: $isEditing, showFirstNameErrorText: $showFirstNameErrorText, showLastNameErrorText: $showLastNameErrorText, showPhoneNumberErrorText: $showPhoneNumberErrorText, showEmailErrorText: $showEmailErrorText, messagingChannelEntry: $messagingChannelEntry, goToConversation: $goToConversation, goToNewConversation: $goToNewConversation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactStateImpl &&
            (identical(other.contact, contact) || other.contact == contact) &&
            (identical(other.savedContact, savedContact) ||
                other.savedContact == savedContact) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isEditing, isEditing) ||
                other.isEditing == isEditing) &&
            (identical(other.showFirstNameErrorText, showFirstNameErrorText) ||
                other.showFirstNameErrorText == showFirstNameErrorText) &&
            (identical(other.showLastNameErrorText, showLastNameErrorText) ||
                other.showLastNameErrorText == showLastNameErrorText) &&
            (identical(
                    other.showPhoneNumberErrorText, showPhoneNumberErrorText) ||
                other.showPhoneNumberErrorText == showPhoneNumberErrorText) &&
            (identical(other.showEmailErrorText, showEmailErrorText) ||
                other.showEmailErrorText == showEmailErrorText) &&
            (identical(other.messagingChannelEntry, messagingChannelEntry) ||
                other.messagingChannelEntry == messagingChannelEntry) &&
            (identical(other.goToConversation, goToConversation) ||
                other.goToConversation == goToConversation) &&
            (identical(other.goToNewConversation, goToNewConversation) ||
                other.goToNewConversation == goToNewConversation));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      contact,
      savedContact,
      isLoading,
      isEditing,
      showFirstNameErrorText,
      showLastNameErrorText,
      showPhoneNumberErrorText,
      showEmailErrorText,
      messagingChannelEntry,
      goToConversation,
      goToNewConversation);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactStateImplCopyWith<_$ContactStateImpl> get copyWith =>
      __$$ContactStateImplCopyWithImpl<_$ContactStateImpl>(this, _$identity);
}

abstract class _ContactState extends ContactState {
  const factory _ContactState(
      {final Contact? contact,
      final Contact? savedContact,
      final bool isLoading,
      final bool isEditing,
      final bool showFirstNameErrorText,
      final bool showLastNameErrorText,
      final bool showPhoneNumberErrorText,
      final bool showEmailErrorText,
      final MessagingChannelEntry? messagingChannelEntry,
      final UiEvent<SfId>? goToConversation,
      final UiEvent<SfId>? goToNewConversation}) = _$ContactStateImpl;
  const _ContactState._() : super._();

  @override
  Contact? get contact;
  @override
  Contact? get savedContact;
  @override
  bool get isLoading;
  @override
  bool get isEditing;
  @override
  bool get showFirstNameErrorText;
  @override
  bool get showLastNameErrorText;
  @override
  bool get showPhoneNumberErrorText;
  @override
  bool get showEmailErrorText;
  @override
  MessagingChannelEntry? get messagingChannelEntry;
  @override
  UiEvent<SfId>? get goToConversation;
  @override
  UiEvent<SfId>? get goToNewConversation;
  @override
  @JsonKey(ignore: true)
  _$$ContactStateImplCopyWith<_$ContactStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
