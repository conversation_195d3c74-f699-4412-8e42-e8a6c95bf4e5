import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/messaging_channel_entry.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/models/contact.dart';
import 'package:x1440/utils/saved_regex.dart';

part 'contact_state.freezed.dart';

@freezed
class ContactState with _$ContactState {
  const ContactState._();

  bool get canBeSaved =>
      contact != null &&
      contact!.firstName?.isNotEmpty == true &&
      contact!.lastName?.isNotEmpty == true &&
      contact!.mobilePhone != null &&
      contact!.mobilePhone!.length > 3 &&
      emailIsValid;

  bool get emailIsValid => _validateEmail(contact?.email);
  bool get contactPhoneIsValid => _validatePhoneNumber(contact?.mobilePhone);
  bool get savedContactPhoneIsValid =>
      _validatePhoneNumber(savedContact?.mobilePhone);

  const factory ContactState(
      {Contact? contact,
      Contact? savedContact,
      @Default(true) bool isLoading,
      @Default(false) bool isEditing,
      @Default(false) bool showFirstNameErrorText,
      @Default(false) bool showLastNameErrorText,
      @Default(false) bool showPhoneNumberErrorText,
      @Default(false) bool showEmailErrorText,
      MessagingChannelEntry? messagingChannelEntry,
      final UiEvent<SfId>? goToConversation,
      final UiEvent<SfId>? goToNewConversation}) = _ContactState;

  bool _validatePhoneNumber(String? value) {
    if (value == null) return false;
    bool isValid = true
        // isSelectedCountryUS()
        ? SavedRegEx.usPhonePattern.hasMatch(value)
        : SavedRegEx.genericPhonePattern.hasMatch(value.replaceAll(' ', ''));

    return isValid;
  }

  bool _validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return true; // email is optional
    }

    // Regular expression pattern for email validation
    final emailRegExp = SavedRegEx.emailPattern;

    return emailRegExp.hasMatch(email);
  }
}
