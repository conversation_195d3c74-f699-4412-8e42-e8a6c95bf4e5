// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'channels_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ChannelsState {
  dynamic get isLoadingChannels => throw _privateConstructorUsedError;
  UiEvent<bool>? get isSelectedChannelChanged =>
      throw _privateConstructorUsedError;
  MessagingChannelsResponse? get messagingChannels =>
      throw _privateConstructorUsedError;
  MessagingChannelEntry? get selectedChannel =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ChannelsStateCopyWith<ChannelsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChannelsStateCopyWith<$Res> {
  factory $ChannelsStateCopyWith(
          ChannelsState value, $Res Function(ChannelsState) then) =
      _$ChannelsStateCopyWithImpl<$Res, ChannelsState>;
  @useResult
  $Res call(
      {dynamic isLoadingChannels,
      UiEvent<bool>? isSelectedChannelChanged,
      MessagingChannelsResponse? messagingChannels,
      MessagingChannelEntry? selectedChannel});

  $MessagingChannelsResponseCopyWith<$Res>? get messagingChannels;
  $MessagingChannelEntryCopyWith<$Res>? get selectedChannel;
}

/// @nodoc
class _$ChannelsStateCopyWithImpl<$Res, $Val extends ChannelsState>
    implements $ChannelsStateCopyWith<$Res> {
  _$ChannelsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingChannels = freezed,
    Object? isSelectedChannelChanged = freezed,
    Object? messagingChannels = freezed,
    Object? selectedChannel = freezed,
  }) {
    return _then(_value.copyWith(
      isLoadingChannels: freezed == isLoadingChannels
          ? _value.isLoadingChannels
          : isLoadingChannels // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSelectedChannelChanged: freezed == isSelectedChannelChanged
          ? _value.isSelectedChannelChanged
          : isSelectedChannelChanged // ignore: cast_nullable_to_non_nullable
              as UiEvent<bool>?,
      messagingChannels: freezed == messagingChannels
          ? _value.messagingChannels
          : messagingChannels // ignore: cast_nullable_to_non_nullable
              as MessagingChannelsResponse?,
      selectedChannel: freezed == selectedChannel
          ? _value.selectedChannel
          : selectedChannel // ignore: cast_nullable_to_non_nullable
              as MessagingChannelEntry?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelsResponseCopyWith<$Res>? get messagingChannels {
    if (_value.messagingChannels == null) {
      return null;
    }

    return $MessagingChannelsResponseCopyWith<$Res>(_value.messagingChannels!,
        (value) {
      return _then(_value.copyWith(messagingChannels: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MessagingChannelEntryCopyWith<$Res>? get selectedChannel {
    if (_value.selectedChannel == null) {
      return null;
    }

    return $MessagingChannelEntryCopyWith<$Res>(_value.selectedChannel!,
        (value) {
      return _then(_value.copyWith(selectedChannel: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ChannelsStateImplCopyWith<$Res>
    implements $ChannelsStateCopyWith<$Res> {
  factory _$$ChannelsStateImplCopyWith(
          _$ChannelsStateImpl value, $Res Function(_$ChannelsStateImpl) then) =
      __$$ChannelsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {dynamic isLoadingChannels,
      UiEvent<bool>? isSelectedChannelChanged,
      MessagingChannelsResponse? messagingChannels,
      MessagingChannelEntry? selectedChannel});

  @override
  $MessagingChannelsResponseCopyWith<$Res>? get messagingChannels;
  @override
  $MessagingChannelEntryCopyWith<$Res>? get selectedChannel;
}

/// @nodoc
class __$$ChannelsStateImplCopyWithImpl<$Res>
    extends _$ChannelsStateCopyWithImpl<$Res, _$ChannelsStateImpl>
    implements _$$ChannelsStateImplCopyWith<$Res> {
  __$$ChannelsStateImplCopyWithImpl(
      _$ChannelsStateImpl _value, $Res Function(_$ChannelsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoadingChannels = freezed,
    Object? isSelectedChannelChanged = freezed,
    Object? messagingChannels = freezed,
    Object? selectedChannel = freezed,
  }) {
    return _then(_$ChannelsStateImpl(
      isLoadingChannels: freezed == isLoadingChannels
          ? _value.isLoadingChannels!
          : isLoadingChannels,
      isSelectedChannelChanged: freezed == isSelectedChannelChanged
          ? _value.isSelectedChannelChanged
          : isSelectedChannelChanged // ignore: cast_nullable_to_non_nullable
              as UiEvent<bool>?,
      messagingChannels: freezed == messagingChannels
          ? _value.messagingChannels
          : messagingChannels // ignore: cast_nullable_to_non_nullable
              as MessagingChannelsResponse?,
      selectedChannel: freezed == selectedChannel
          ? _value.selectedChannel
          : selectedChannel // ignore: cast_nullable_to_non_nullable
              as MessagingChannelEntry?,
    ));
  }
}

/// @nodoc

class _$ChannelsStateImpl implements _ChannelsState {
  const _$ChannelsStateImpl(
      {this.isLoadingChannels = true,
      this.isSelectedChannelChanged,
      this.messagingChannels = null,
      this.selectedChannel = null});

  @override
  @JsonKey()
  final dynamic isLoadingChannels;
  @override
  final UiEvent<bool>? isSelectedChannelChanged;
  @override
  @JsonKey()
  final MessagingChannelsResponse? messagingChannels;
  @override
  @JsonKey()
  final MessagingChannelEntry? selectedChannel;

  @override
  String toString() {
    return 'ChannelsState(isLoadingChannels: $isLoadingChannels, isSelectedChannelChanged: $isSelectedChannelChanged, messagingChannels: $messagingChannels, selectedChannel: $selectedChannel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChannelsStateImpl &&
            const DeepCollectionEquality()
                .equals(other.isLoadingChannels, isLoadingChannels) &&
            (identical(
                    other.isSelectedChannelChanged, isSelectedChannelChanged) ||
                other.isSelectedChannelChanged == isSelectedChannelChanged) &&
            (identical(other.messagingChannels, messagingChannels) ||
                other.messagingChannels == messagingChannels) &&
            (identical(other.selectedChannel, selectedChannel) ||
                other.selectedChannel == selectedChannel));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(isLoadingChannels),
      isSelectedChannelChanged,
      messagingChannels,
      selectedChannel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChannelsStateImplCopyWith<_$ChannelsStateImpl> get copyWith =>
      __$$ChannelsStateImplCopyWithImpl<_$ChannelsStateImpl>(this, _$identity);
}

abstract class _ChannelsState implements ChannelsState {
  const factory _ChannelsState(
      {final dynamic isLoadingChannels,
      final UiEvent<bool>? isSelectedChannelChanged,
      final MessagingChannelsResponse? messagingChannels,
      final MessagingChannelEntry? selectedChannel}) = _$ChannelsStateImpl;

  @override
  dynamic get isLoadingChannels;
  @override
  UiEvent<bool>? get isSelectedChannelChanged;
  @override
  MessagingChannelsResponse? get messagingChannels;
  @override
  MessagingChannelEntry? get selectedChannel;
  @override
  @JsonKey(ignore: true)
  _$$ChannelsStateImplCopyWith<_$ChannelsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
