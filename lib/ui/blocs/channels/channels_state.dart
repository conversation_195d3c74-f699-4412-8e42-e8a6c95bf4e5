import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/dtos/messaging_channel_entry.dart';
import 'package:x1440/api/dtos/messaging_channels_response.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
part 'channels_state.freezed.dart';

@freezed
class ChannelsState with _$ChannelsState {
  const factory ChannelsState(
      {@Default(true) isLoadingChannels,
      UiEvent<bool>? isSelectedChannelChanged,
      @Default(null) MessagingChannelsResponse? messagingChannels,
      @Default(null) MessagingChannelEntry? selectedChannel}) = _ChannelsState;
}
