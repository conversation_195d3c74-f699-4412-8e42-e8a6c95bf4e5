// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'demo_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DemoUser _$DemoUserFromJson(Map<String, dynamic> json) {
  return _DemoUser.fromJson(json);
}

/// @nodoc
mixin _$DemoUser {
  String? get name => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get companyName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DemoUserCopyWith<DemoUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DemoUserCopyWith<$Res> {
  factory $DemoUserCopyWith(DemoUser value, $Res Function(DemoUser) then) =
      _$DemoUserCopyWithImpl<$Res, DemoUser>;
  @useResult
  $Res call(
      {String? name, String? email, String? phoneNumber, String? companyName});
}

/// @nodoc
class _$DemoUserCopyWithImpl<$Res, $Val extends DemoUser>
    implements $DemoUserCopyWith<$Res> {
  _$DemoUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? companyName = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DemoUserImplCopyWith<$Res>
    implements $DemoUserCopyWith<$Res> {
  factory _$$DemoUserImplCopyWith(
          _$DemoUserImpl value, $Res Function(_$DemoUserImpl) then) =
      __$$DemoUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? name, String? email, String? phoneNumber, String? companyName});
}

/// @nodoc
class __$$DemoUserImplCopyWithImpl<$Res>
    extends _$DemoUserCopyWithImpl<$Res, _$DemoUserImpl>
    implements _$$DemoUserImplCopyWith<$Res> {
  __$$DemoUserImplCopyWithImpl(
      _$DemoUserImpl _value, $Res Function(_$DemoUserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? companyName = freezed,
  }) {
    return _then(_$DemoUserImpl(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DemoUserImpl extends _DemoUser {
  const _$DemoUserImpl(
      {this.name, this.email, this.phoneNumber, this.companyName})
      : super._();

  factory _$DemoUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$DemoUserImplFromJson(json);

  @override
  final String? name;
  @override
  final String? email;
  @override
  final String? phoneNumber;
  @override
  final String? companyName;

  @override
  String toString() {
    return 'DemoUser(name: $name, email: $email, phoneNumber: $phoneNumber, companyName: $companyName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DemoUserImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, email, phoneNumber, companyName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DemoUserImplCopyWith<_$DemoUserImpl> get copyWith =>
      __$$DemoUserImplCopyWithImpl<_$DemoUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DemoUserImplToJson(
      this,
    );
  }
}

abstract class _DemoUser extends DemoUser {
  const factory _DemoUser(
      {final String? name,
      final String? email,
      final String? phoneNumber,
      final String? companyName}) = _$DemoUserImpl;
  const _DemoUser._() : super._();

  factory _DemoUser.fromJson(Map<String, dynamic> json) =
      _$DemoUserImpl.fromJson;

  @override
  String? get name;
  @override
  String? get email;
  @override
  String? get phoneNumber;
  @override
  String? get companyName;
  @override
  @JsonKey(ignore: true)
  _$$DemoUserImplCopyWith<_$DemoUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
