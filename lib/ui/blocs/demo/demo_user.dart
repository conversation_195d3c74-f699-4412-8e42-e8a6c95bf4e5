import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:isar/isar.dart';
import 'package:x1440/models/user_model.dart';
import 'package:x1440/utils/extensions/isar_on_freezed.dart';

part 'demo_user.freezed.dart';
part 'demo_user.g.dart';

@freezed
@collectionOnFreezed
class DemoUser with _$DemoUser {
  /// Isar ID for indexing — Setting this one to '0' for singleton item in collection; otherwise default use here is (can also use custom indices -- see isar docs):
  final Id id = 0;
  const DemoUser._();

  const factory DemoUser({
    String? name,
    String? email,
    String? phoneNumber,
    String? companyName,
  }) = _DemoUser;

  factory DemoUser.fromJson(Map<String, dynamic> json) => _$DemoUserFromJson(json);

  User toUser() {
    final firstName = name?.split(' ').first;
    final lastName = name?.split(' ').last;
    return User(
      firstName: firstName,
      lastName: lastName,
      email: email,
      phone: phoneNumber,
    );
  }
}