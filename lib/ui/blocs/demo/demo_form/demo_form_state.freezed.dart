// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'demo_form_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DemoFormState {
  String get name => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get companyName => throw _privateConstructorUsedError;
  bool get isValid => throw _privateConstructorUsedError;
  UiEvent<Nothing>? get submitEvent => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DemoFormStateCopyWith<DemoFormState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DemoFormStateCopyWith<$Res> {
  factory $DemoFormStateCopyWith(
          DemoFormState value, $Res Function(DemoFormState) then) =
      _$DemoFormStateCopyWithImpl<$Res, DemoFormState>;
  @useResult
  $Res call(
      {String name,
      String phoneNumber,
      String email,
      String companyName,
      bool isValid,
      UiEvent<Nothing>? submitEvent});
}

/// @nodoc
class _$DemoFormStateCopyWithImpl<$Res, $Val extends DemoFormState>
    implements $DemoFormStateCopyWith<$Res> {
  _$DemoFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? email = null,
    Object? companyName = null,
    Object? isValid = null,
    Object? submitEvent = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      companyName: null == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      submitEvent: freezed == submitEvent
          ? _value.submitEvent
          : submitEvent // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DemoFormStateImplCopyWith<$Res>
    implements $DemoFormStateCopyWith<$Res> {
  factory _$$DemoFormStateImplCopyWith(
          _$DemoFormStateImpl value, $Res Function(_$DemoFormStateImpl) then) =
      __$$DemoFormStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String phoneNumber,
      String email,
      String companyName,
      bool isValid,
      UiEvent<Nothing>? submitEvent});
}

/// @nodoc
class __$$DemoFormStateImplCopyWithImpl<$Res>
    extends _$DemoFormStateCopyWithImpl<$Res, _$DemoFormStateImpl>
    implements _$$DemoFormStateImplCopyWith<$Res> {
  __$$DemoFormStateImplCopyWithImpl(
      _$DemoFormStateImpl _value, $Res Function(_$DemoFormStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? email = null,
    Object? companyName = null,
    Object? isValid = null,
    Object? submitEvent = freezed,
  }) {
    return _then(_$DemoFormStateImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      companyName: null == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      submitEvent: freezed == submitEvent
          ? _value.submitEvent
          : submitEvent // ignore: cast_nullable_to_non_nullable
              as UiEvent<Nothing>?,
    ));
  }
}

/// @nodoc

class _$DemoFormStateImpl implements _DemoFormState {
  const _$DemoFormStateImpl(
      {this.name = '',
      this.phoneNumber = '',
      this.email = '',
      this.companyName = '',
      this.isValid = false,
      this.submitEvent});

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String phoneNumber;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final String companyName;
  @override
  @JsonKey()
  final bool isValid;
  @override
  final UiEvent<Nothing>? submitEvent;

  @override
  String toString() {
    return 'DemoFormState(name: $name, phoneNumber: $phoneNumber, email: $email, companyName: $companyName, isValid: $isValid, submitEvent: $submitEvent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DemoFormStateImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.submitEvent, submitEvent) ||
                other.submitEvent == submitEvent));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, name, phoneNumber, email, companyName, isValid, submitEvent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DemoFormStateImplCopyWith<_$DemoFormStateImpl> get copyWith =>
      __$$DemoFormStateImplCopyWithImpl<_$DemoFormStateImpl>(this, _$identity);
}

abstract class _DemoFormState implements DemoFormState {
  const factory _DemoFormState(
      {final String name,
      final String phoneNumber,
      final String email,
      final String companyName,
      final bool isValid,
      final UiEvent<Nothing>? submitEvent}) = _$DemoFormStateImpl;

  @override
  String get name;
  @override
  String get phoneNumber;
  @override
  String get email;
  @override
  String get companyName;
  @override
  bool get isValid;
  @override
  UiEvent<Nothing>? get submitEvent;
  @override
  @JsonKey(ignore: true)
  _$$DemoFormStateImplCopyWith<_$DemoFormStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
