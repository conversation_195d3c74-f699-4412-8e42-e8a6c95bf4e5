import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/ui/blocs/ui_event.dart';

part 'demo_form_state.freezed.dart';

@freezed
class DemoFormState with _$DemoFormState {
  const factory DemoFormState({
    @Default('') String name,
    @Default('') String phoneNumber,
    @Default('') String email,
    @Default('') String companyName,
    @Default(false) bool isValid,
    UiEvent<Nothing>? submitEvent,
  }) = _DemoFormState;
}
