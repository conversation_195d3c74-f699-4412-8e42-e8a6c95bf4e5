// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'demo_mode_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DemoModeState {
  bool get toggle => throw _privateConstructorUsedError;
  Map<SfId, String> get contactPhotoUrls => throw _privateConstructorUsedError;
  Map<SfId, Contact> get contacts => throw _privateConstructorUsedError;
  Map<SfId, SfId> get meuContactAssociations =>
      throw _privateConstructorUsedError;
  Set<SfId> get conversationsWithUnreadMessages =>
      throw _privateConstructorUsedError;
  DemoUser? get user => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DemoModeStateCopyWith<DemoModeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DemoModeStateCopyWith<$Res> {
  factory $DemoModeStateCopyWith(
          DemoModeState value, $Res Function(DemoModeState) then) =
      _$DemoModeStateCopyWithImpl<$Res, DemoModeState>;
  @useResult
  $Res call(
      {bool toggle,
      Map<SfId, String> contactPhotoUrls,
      Map<SfId, Contact> contacts,
      Map<SfId, SfId> meuContactAssociations,
      Set<SfId> conversationsWithUnreadMessages,
      DemoUser? user});

  $DemoUserCopyWith<$Res>? get user;
}

/// @nodoc
class _$DemoModeStateCopyWithImpl<$Res, $Val extends DemoModeState>
    implements $DemoModeStateCopyWith<$Res> {
  _$DemoModeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? toggle = null,
    Object? contactPhotoUrls = null,
    Object? contacts = null,
    Object? meuContactAssociations = null,
    Object? conversationsWithUnreadMessages = null,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      toggle: null == toggle
          ? _value.toggle
          : toggle // ignore: cast_nullable_to_non_nullable
              as bool,
      contactPhotoUrls: null == contactPhotoUrls
          ? _value.contactPhotoUrls
          : contactPhotoUrls // ignore: cast_nullable_to_non_nullable
              as Map<SfId, String>,
      contacts: null == contacts
          ? _value.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Contact>,
      meuContactAssociations: null == meuContactAssociations
          ? _value.meuContactAssociations
          : meuContactAssociations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, SfId>,
      conversationsWithUnreadMessages: null == conversationsWithUnreadMessages
          ? _value.conversationsWithUnreadMessages
          : conversationsWithUnreadMessages // ignore: cast_nullable_to_non_nullable
              as Set<SfId>,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as DemoUser?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $DemoUserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $DemoUserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DemoModeStateImplCopyWith<$Res>
    implements $DemoModeStateCopyWith<$Res> {
  factory _$$DemoModeStateImplCopyWith(
          _$DemoModeStateImpl value, $Res Function(_$DemoModeStateImpl) then) =
      __$$DemoModeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool toggle,
      Map<SfId, String> contactPhotoUrls,
      Map<SfId, Contact> contacts,
      Map<SfId, SfId> meuContactAssociations,
      Set<SfId> conversationsWithUnreadMessages,
      DemoUser? user});

  @override
  $DemoUserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$DemoModeStateImplCopyWithImpl<$Res>
    extends _$DemoModeStateCopyWithImpl<$Res, _$DemoModeStateImpl>
    implements _$$DemoModeStateImplCopyWith<$Res> {
  __$$DemoModeStateImplCopyWithImpl(
      _$DemoModeStateImpl _value, $Res Function(_$DemoModeStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? toggle = null,
    Object? contactPhotoUrls = null,
    Object? contacts = null,
    Object? meuContactAssociations = null,
    Object? conversationsWithUnreadMessages = null,
    Object? user = freezed,
  }) {
    return _then(_$DemoModeStateImpl(
      toggle: null == toggle
          ? _value.toggle
          : toggle // ignore: cast_nullable_to_non_nullable
              as bool,
      contactPhotoUrls: null == contactPhotoUrls
          ? _value._contactPhotoUrls
          : contactPhotoUrls // ignore: cast_nullable_to_non_nullable
              as Map<SfId, String>,
      contacts: null == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Contact>,
      meuContactAssociations: null == meuContactAssociations
          ? _value._meuContactAssociations
          : meuContactAssociations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, SfId>,
      conversationsWithUnreadMessages: null == conversationsWithUnreadMessages
          ? _value._conversationsWithUnreadMessages
          : conversationsWithUnreadMessages // ignore: cast_nullable_to_non_nullable
              as Set<SfId>,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as DemoUser?,
    ));
  }
}

/// @nodoc

class _$DemoModeStateImpl extends _DemoModeState {
  const _$DemoModeStateImpl(
      {this.toggle = true,
      final Map<SfId, String> contactPhotoUrls = const {},
      final Map<SfId, Contact> contacts = const {},
      final Map<SfId, SfId> meuContactAssociations = const {},
      final Set<SfId> conversationsWithUnreadMessages = const {},
      this.user})
      : _contactPhotoUrls = contactPhotoUrls,
        _contacts = contacts,
        _meuContactAssociations = meuContactAssociations,
        _conversationsWithUnreadMessages = conversationsWithUnreadMessages,
        super._();

  @override
  @JsonKey()
  final bool toggle;
  final Map<SfId, String> _contactPhotoUrls;
  @override
  @JsonKey()
  Map<SfId, String> get contactPhotoUrls {
    if (_contactPhotoUrls is EqualUnmodifiableMapView) return _contactPhotoUrls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_contactPhotoUrls);
  }

  final Map<SfId, Contact> _contacts;
  @override
  @JsonKey()
  Map<SfId, Contact> get contacts {
    if (_contacts is EqualUnmodifiableMapView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_contacts);
  }

  final Map<SfId, SfId> _meuContactAssociations;
  @override
  @JsonKey()
  Map<SfId, SfId> get meuContactAssociations {
    if (_meuContactAssociations is EqualUnmodifiableMapView)
      return _meuContactAssociations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_meuContactAssociations);
  }

  final Set<SfId> _conversationsWithUnreadMessages;
  @override
  @JsonKey()
  Set<SfId> get conversationsWithUnreadMessages {
    if (_conversationsWithUnreadMessages is EqualUnmodifiableSetView)
      return _conversationsWithUnreadMessages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_conversationsWithUnreadMessages);
  }

  @override
  final DemoUser? user;

  @override
  String toString() {
    return 'DemoModeState(toggle: $toggle, contactPhotoUrls: $contactPhotoUrls, contacts: $contacts, meuContactAssociations: $meuContactAssociations, conversationsWithUnreadMessages: $conversationsWithUnreadMessages, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DemoModeStateImpl &&
            (identical(other.toggle, toggle) || other.toggle == toggle) &&
            const DeepCollectionEquality()
                .equals(other._contactPhotoUrls, _contactPhotoUrls) &&
            const DeepCollectionEquality().equals(other._contacts, _contacts) &&
            const DeepCollectionEquality().equals(
                other._meuContactAssociations, _meuContactAssociations) &&
            const DeepCollectionEquality().equals(
                other._conversationsWithUnreadMessages,
                _conversationsWithUnreadMessages) &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      toggle,
      const DeepCollectionEquality().hash(_contactPhotoUrls),
      const DeepCollectionEquality().hash(_contacts),
      const DeepCollectionEquality().hash(_meuContactAssociations),
      const DeepCollectionEquality().hash(_conversationsWithUnreadMessages),
      user);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DemoModeStateImplCopyWith<_$DemoModeStateImpl> get copyWith =>
      __$$DemoModeStateImplCopyWithImpl<_$DemoModeStateImpl>(this, _$identity);
}

abstract class _DemoModeState extends DemoModeState {
  const factory _DemoModeState(
      {final bool toggle,
      final Map<SfId, String> contactPhotoUrls,
      final Map<SfId, Contact> contacts,
      final Map<SfId, SfId> meuContactAssociations,
      final Set<SfId> conversationsWithUnreadMessages,
      final DemoUser? user}) = _$DemoModeStateImpl;
  const _DemoModeState._() : super._();

  @override
  bool get toggle;
  @override
  Map<SfId, String> get contactPhotoUrls;
  @override
  Map<SfId, Contact> get contacts;
  @override
  Map<SfId, SfId> get meuContactAssociations;
  @override
  Set<SfId> get conversationsWithUnreadMessages;
  @override
  DemoUser? get user;
  @override
  @JsonKey(ignore: true)
  _$$DemoModeStateImplCopyWith<_$DemoModeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
