import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/ui/blocs/demo/demo_user.dart';
import 'package:x1440/use_cases/models/contact.dart';

part 'demo_mode_state.freezed.dart';

@freezed
class DemoModeState with _$DemoModeState {
  const DemoModeState._();

  Map<SfId, Contact> get savedContactsByMeuId => {
    for (var entry in contacts.entries.where(
            (entry) => meuContactAssociations[(entry.value.id)] != null))
      meuContactAssociations[entry.value.id]!: (entry).value
  };

  const factory DemoModeState({
    @Default(true) bool toggle,
    @Default({}) Map<SfId, String> contactPhotoUrls,
    @Default({}) Map<SfId, Contact> contacts,
    @Default({}) Map<SfId, SfId> meuContactAssociations,
    @Default({}) Set<SfId> conversationsWithUnreadMessages,
    DemoUser? user,
  }) = _DemoModeState;
}