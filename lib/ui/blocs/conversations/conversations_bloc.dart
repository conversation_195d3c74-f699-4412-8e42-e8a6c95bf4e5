import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/salesforce/dtos/quick_actions/quick_action.dart';
import 'package:x1440/frameworks/push_notification_manager.dart';
import 'package:x1440/models/conversation_model.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/ui/blocs/contacts/contacts_bloc.dart';
import 'package:x1440/ui/blocs/contacts/contacts_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/quick_actions/quick_actions_url_handler.dart';
import 'package:x1440/use_cases/quick_actions/quick_actions_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'conversations_state.dart';

class ConversationsBloc extends Bloc<ConversationsEvent, ConversationsState> {
  ConversationsBloc(super.initialState);
}

class ConversationsBlocImpl extends ConversationsBloc {
  final ConversationsUseCase _conversationsUseCase;
  final QuickActionsUseCase _quickActionsUseCase;
  final AuthUseCase _authUseCase;
  final RemoteLogger _remoteLogger;
  final List<SfId> _pendingGoToActions = [];

  ConversationsBlocImpl(this._conversationsUseCase, this._quickActionsUseCase,
      this._authUseCase, this._remoteLogger)
      : super(const ConversationsState()) {
    on<InitEvent>(_onInitEvent);
    on<RefreshConversationsEvent>(_onRefreshConversationsEvent);
    on<SetAllConversationsClosedEvent>(_onSetAllConversationsClosedEvent);
    on<GoToConversationEvent>(_onGoToConversationEvent);
    on<ClearConversationsEvent>(_onClearConversationsEvent);
    on<SetLakConversationsEvent>(_onSetLakConversationsEvent);
    on<SetConversationsSortingOptionEvent>(_onSetConversationsSortingOption);
    on<ToggleShowEndedEvent>(_onToggleShowEnded);
    on<FetchConversationsEvent>(_onFetchConversationsEvent);
    on<ShowEndAllConversationsConfirmationEvent>(
        _onShowEndAllConversationsConfirmationEvent);

    on<AcceptConversationEvent>(_onAcceptConversationEvent);
    on<DeclineConversationEvent>(_onDeclineConversationEvent);
    on<EndConversationEvent>(_onEndConversationEvent);
    on<GetDeviceTokenPermissionsEvent>(_onGetDeviceTokenPermissionsEvent);
  }

  void _onInitEvent(InitEvent event, Emitter<ConversationsState> emit) async {
    try {
      await _quickActionsUseCase.initialize();

      _pendingGoToActions.clear();

      /// this was doing a state.reset ... but we don't re-set the conversations from this method, so if reset gets called (on ConversationsScreen init), it will clear the conversations
      // emit(state.reset());
      emit(state.copyWith(
        mainQuickAction: null,
        overflowQuickActions: [],
        quickActionException: null,
      ));

      final credentials = await _authUseCase.getCredentials();
      final variables = {
        'organizationId': credentials.orgId,
        'userId': credentials.userId,
      };
      final mainAction = _quickActionsUseCase.quickActionsManager
          .resolveMainQuickAction(QuickActionsType.conversations, variables);
      final overflowActions = _quickActionsUseCase.quickActionsManager
          .resolveOverflowQuickActions(
              QuickActionsType.conversations, variables);
      emit(state.copyWith(
          mainQuickAction: mainAction, overflowQuickActions: overflowActions));
    } on QuickActionException catch (exception) {
      // Handle QuickActionException specifically
      _remoteLogger.error(exception.toString());
      emit(state.copyWith(quickActionException: UiEvent(exception)));
    } catch (e) {
      // Handle all other exceptions
      if (kDebugMode) {
        print(e);
      }
    }
  }

  void _onRefreshConversationsEvent(
      RefreshConversationsEvent event, Emitter<ConversationsState> emit) async {
    if (state.isLoading) return;
    emit(state.copyWith(isLoading: true));
    emit(state.copyWith(
        isLoading: false, conversations: _conversationsUseCase.conversations));

    _processPendingGoToActions(emit);
  }

  void _onSetAllConversationsClosedEvent(
      SetAllConversationsClosedEvent event, Emitter<ConversationsState> emit) {
    emit(state.copyWith(
        conversations: state.conversations.map((key, value) =>
            MapEntry(key, value.copyWith(status: ConversationStatus.closed)))));
  }

  void _onGoToConversationEvent(
      GoToConversationEvent event, Emitter<ConversationsState> emit) {
    if (state.isLoading) {
      _pendingGoToActions.add(event.messagingEndUserId);
    } else {
      emit(state.copyWith(
          goToMessagingEndUserId: UiEvent(event.messagingEndUserId)));
    }
  }

  void _onClearConversationsEvent(
      ClearConversationsEvent event, Emitter<ConversationsState> emit) async {
    // _conversationsUseCase.clearConversations();
    emit(state.copyWith(conversations: {}));
  }

  void _onSetLakConversationsEvent(
      SetLakConversationsEvent event, Emitter<ConversationsState> emit) {
    _conversationsUseCase.setLakConversations(event.conversations);
    emit(state.copyWith(conversations: _conversationsUseCase.conversations));
  }

  void _onSetConversationsSortingOption(
      SetConversationsSortingOptionEvent event,
      Emitter<ConversationsState> emit) {
    emit(state.copyWith(sortingOption: event.sortingOption));
  }

  void _onToggleShowEnded(
      ToggleShowEndedEvent event, Emitter<ConversationsState> emit) {
    emit(state.copyWith(showEnded: !state.showEnded));
  }

  void _onFetchConversationsEvent(
      FetchConversationsEvent event, Emitter<ConversationsState> emit) async {
    if (state.isLoading) return;

    emit(state.copyWith(isLoading: true));

    try {
      await _conversationsUseCase.fetchConversations();
    } catch (e) {
      _remoteLogger.error('CONVERSATION_BLOC onFetchConversationsEvent: $e');
      rethrow;
    }

    GetIt.I<ContactsBloc>().add(FetchContactsEvent());

    add(GetDeviceTokenPermissionsEvent());
    emit(state.copyWith(
        conversations: _conversationsUseCase.conversations, isLoading: false));

    _processPendingGoToActions(emit);
  }

  void _onShowEndAllConversationsConfirmationEvent(
      ShowEndAllConversationsConfirmationEvent event,
      Emitter<ConversationsState> emit) {
    emit(state.copyWith(
        showEndAllConversationsConfirmation:
            UiEvent(event.postEndConversationsEvent)));
  }

  void _onAcceptConversationEvent(
      AcceptConversationEvent event, Emitter<ConversationsState> emit) async {
    final success =
        await _conversationsUseCase.acceptConversation(event.conversationId);

    /// *****************************************************
    /// DEBUG ONLY // TODO: remove
    /// *****************************************************
    final (messagingSession, agentWork) = _conversationsUseCase
        .getLatestSessionAndWorkForMessagingEndUser(event.conversationId);
    if (agentWork == null) {
      Utils.showToast(
          'Attepted to Accept\nNo AgentWork found for conversation ${event.conversationId}${success != true ? '\nAccept Failed' : ''}',
          type: ToastType.warning);
    }

    /// *****************************************************
    /// END DEBUG ONLY
    /// *****************************************************
    // TODO: show error from here (not conversationsUseCase -- but that one is also called from ConversationsViewModel for accepting work from outbound/push notification)
    emit(state.copyWith(
        conversations: _conversationsUseCase.conversations,
        goToMessagingEndUserId: (success && event.navigateToChatOnSuccess)
            ? UiEvent(event.conversationId)
            : null));
  }

  void _onDeclineConversationEvent(
      DeclineConversationEvent event, Emitter<ConversationsState> emit) async {
    await _conversationsUseCase.declineConversation(event.conversationId);
    emit(state.copyWith(conversations: _conversationsUseCase.conversations));
  }

  void _onEndConversationEvent(
      EndConversationEvent event, Emitter<ConversationsState> emit) async {
    _remoteLogger.info('EndConversationEvent: ${event.conversationId}');
    final updatedConvos =
        await _conversationsUseCase.endConversation(event.conversationId);
    emit(state.copyWith(conversations: updatedConvos));
    _onRefreshConversationsEvent(RefreshConversationsEvent(), emit);
  }

  void _processPendingGoToActions(Emitter<ConversationsState> emit) {
    if (_pendingGoToActions.isNotEmpty) {
      final messagingEndUserId = _pendingGoToActions.removeAt(0);
      emit(state.copyWith(goToMessagingEndUserId: UiEvent(messagingEndUserId)));
    }
  }

  FutureOr<void> _onGetDeviceTokenPermissionsEvent(
      GetDeviceTokenPermissionsEvent event,
      Emitter<ConversationsState> emit) async {
    final pushManager = GetIt.instance<PushNotificationManager>();
    final granted = await pushManager.isPermissionGranted();

    if (granted) {
      final granted = await pushManager.refreshSessionDeviceToken();
      emit(state.copyWith(pushPermissionsGranted: granted));
    } else {
      emit(state.copyWith(pushPermissionsGranted: false));
    }
  }
}
