import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:x1440/api/salesforce/dtos/quick_actions/quick_action.dart';
import 'package:x1440/models/conversation_model.dart';
import 'package:x1440/models/enums/conversation_sorting_option.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/ui/blocs/ui_event.dart';
import 'package:x1440/use_cases/quick_actions/quick_actions_url_handler.dart';

import 'conversations_event.dart';

part 'conversations_state.freezed.dart';

@freezed
class ConversationsState with _$ConversationsState {
  const ConversationsState._();

  // QuickAction? get mainQuickAction => quickActions.firstOrNull;
  // List<QuickAction>? get quickActionsMenu =>
  //     quickActions.length > 1 ? quickActions.sublist(1) : null;

  @Deprecated('NEEDS REFACTORING - COPYPASTA')
  Map<SfId, Conversation> get activeConversationsToDisplay {
    Map<SfId, Conversation> conversationsToDisplay = Map.from(conversations);

    if (!showEnded) {
      conversationsToDisplay.removeWhere((key, value) {
        return value.isOpened != true && value.isOutbound != true;
      });
    }

    return conversationsToDisplay;
  }

  @Deprecated('NEEDS REFACTORING - COPYPASTA')
  List<SfId> get orderedConversationsIds {
    List<SfId> conversationsIds = activeConversationsToDisplay.values
        .map((value) => value.lakConversation?.sfId ?? 'no_id'.toSfId())
        .toList();

    DateTime safeParseDateTime(String? input) {
      if (input == null) return DateTime.parse('1970-01-01');

      // First try to parse input as a date string
      try {
        return DateTime.parse(input);
      } catch (e) {
        // Ignore error
      }

      // Then try to parse input as milliseconds since epoch
      try {
        return DateTime.fromMillisecondsSinceEpoch(int.parse(input));
      } catch (e) {
        // Ignore error
      }

      // Fallback to default value
      return DateTime.parse('1970-01-01');
    }

    switch (sortingOption) {
      case ConversationSortingOption.chronological:
        return List.from(conversationsIds)
          ..sort((a, b) {
            var aTime = safeParseDateTime(
                conversations[a]?.lakConversation?.lastMessageTime);
            var bTime = safeParseDateTime(
                conversations[b]?.lakConversation?.lastMessageTime);
            return bTime.compareTo(aTime); // descending
          });

      case ConversationSortingOption.byChannel:
        return List.from(conversationsIds)
          ..sort((a, b) {
            var comparison = (conversations[a]?.lakConversation?.channel ?? '')
                .compareTo(conversations[b]?.lakConversation?.channel ?? '');
            if (comparison != 0) return comparison;

            var aTime = safeParseDateTime(
                conversations[a]?.lakConversation?.lastMessageTime);
            var bTime = safeParseDateTime(
                conversations[b]?.lakConversation?.lastMessageTime);
            return bTime.compareTo(aTime); // descending
          });

    /// NOTE: proper sorting of these requires the Contact name. This is happening in the ConversationsList, where we have the ContactsBloc available too. // TODO: refactor
      case ConversationSortingOption.alphabetical:
        return List.from(conversationsIds)
          ..sort((a, b) => (conversations[a]?.lakConversation?.username ?? '')
              .compareTo(conversations[b]?.lakConversation?.username ?? ''));
      default:
        return conversationsIds;
    }
  }

  ConversationsState reset() => const ConversationsState(
        conversations: {},
        goToMessagingEndUserId: null,
        showEnded: false,
        sortingOption: null,
        isLoading: false,
        showEndAllConversationsConfirmation: null,
        mainQuickAction: null,
        overflowQuickActions: null,
        quickActionException: null,
        pushPermissionsGranted: true,
      );

  const factory ConversationsState({
    @Default({}) Map<SfId, Conversation> conversations,
    UiEvent<SfId>? goToMessagingEndUserId,
    @Default(false) bool showEnded,
    ConversationSortingOption? sortingOption,
    @Default(false) isLoading,
    UiEvent<PostEndConversationsEvent>? showEndAllConversationsConfirmation,
    QuickAction? mainQuickAction,
    List<QuickAction>? overflowQuickActions,
    UiEvent<QuickActionException>? quickActionException,
    @Default(true) bool pushPermissionsGranted,
  }) = _ConversationsState;
}
