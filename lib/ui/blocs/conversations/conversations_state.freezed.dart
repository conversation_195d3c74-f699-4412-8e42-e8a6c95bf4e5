// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'conversations_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConversationsState {
  Map<SfId, Conversation> get conversations =>
      throw _privateConstructorUsedError;
  UiEvent<SfId>? get goToMessagingEndUserId =>
      throw _privateConstructorUsedError;
  bool get showEnded => throw _privateConstructorUsedError;
  ConversationSortingOption? get sortingOption =>
      throw _privateConstructorUsedError;
  dynamic get isLoading => throw _privateConstructorUsedError;
  UiEvent<PostEndConversationsEvent>? get showEndAllConversationsConfirmation =>
      throw _privateConstructorUsedError;
  QuickAction? get mainQuickAction => throw _privateConstructorUsedError;
  List<QuickAction>? get overflowQuickActions =>
      throw _privateConstructorUsedError;
  UiEvent<QuickActionException>? get quickActionException =>
      throw _privateConstructorUsedError;
  bool get pushPermissionsGranted => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ConversationsStateCopyWith<ConversationsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConversationsStateCopyWith<$Res> {
  factory $ConversationsStateCopyWith(
          ConversationsState value, $Res Function(ConversationsState) then) =
      _$ConversationsStateCopyWithImpl<$Res, ConversationsState>;
  @useResult
  $Res call(
      {Map<SfId, Conversation> conversations,
      UiEvent<SfId>? goToMessagingEndUserId,
      bool showEnded,
      ConversationSortingOption? sortingOption,
      dynamic isLoading,
      UiEvent<PostEndConversationsEvent>? showEndAllConversationsConfirmation,
      QuickAction? mainQuickAction,
      List<QuickAction>? overflowQuickActions,
      UiEvent<QuickActionException>? quickActionException,
      bool pushPermissionsGranted});

  $QuickActionCopyWith<$Res>? get mainQuickAction;
}

/// @nodoc
class _$ConversationsStateCopyWithImpl<$Res, $Val extends ConversationsState>
    implements $ConversationsStateCopyWith<$Res> {
  _$ConversationsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversations = null,
    Object? goToMessagingEndUserId = freezed,
    Object? showEnded = null,
    Object? sortingOption = freezed,
    Object? isLoading = freezed,
    Object? showEndAllConversationsConfirmation = freezed,
    Object? mainQuickAction = freezed,
    Object? overflowQuickActions = freezed,
    Object? quickActionException = freezed,
    Object? pushPermissionsGranted = null,
  }) {
    return _then(_value.copyWith(
      conversations: null == conversations
          ? _value.conversations
          : conversations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Conversation>,
      goToMessagingEndUserId: freezed == goToMessagingEndUserId
          ? _value.goToMessagingEndUserId
          : goToMessagingEndUserId // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      showEnded: null == showEnded
          ? _value.showEnded
          : showEnded // ignore: cast_nullable_to_non_nullable
              as bool,
      sortingOption: freezed == sortingOption
          ? _value.sortingOption
          : sortingOption // ignore: cast_nullable_to_non_nullable
              as ConversationSortingOption?,
      isLoading: freezed == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as dynamic,
      showEndAllConversationsConfirmation: freezed ==
              showEndAllConversationsConfirmation
          ? _value.showEndAllConversationsConfirmation
          : showEndAllConversationsConfirmation // ignore: cast_nullable_to_non_nullable
              as UiEvent<PostEndConversationsEvent>?,
      mainQuickAction: freezed == mainQuickAction
          ? _value.mainQuickAction
          : mainQuickAction // ignore: cast_nullable_to_non_nullable
              as QuickAction?,
      overflowQuickActions: freezed == overflowQuickActions
          ? _value.overflowQuickActions
          : overflowQuickActions // ignore: cast_nullable_to_non_nullable
              as List<QuickAction>?,
      quickActionException: freezed == quickActionException
          ? _value.quickActionException
          : quickActionException // ignore: cast_nullable_to_non_nullable
              as UiEvent<QuickActionException>?,
      pushPermissionsGranted: null == pushPermissionsGranted
          ? _value.pushPermissionsGranted
          : pushPermissionsGranted // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $QuickActionCopyWith<$Res>? get mainQuickAction {
    if (_value.mainQuickAction == null) {
      return null;
    }

    return $QuickActionCopyWith<$Res>(_value.mainQuickAction!, (value) {
      return _then(_value.copyWith(mainQuickAction: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ConversationsStateImplCopyWith<$Res>
    implements $ConversationsStateCopyWith<$Res> {
  factory _$$ConversationsStateImplCopyWith(_$ConversationsStateImpl value,
          $Res Function(_$ConversationsStateImpl) then) =
      __$$ConversationsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Map<SfId, Conversation> conversations,
      UiEvent<SfId>? goToMessagingEndUserId,
      bool showEnded,
      ConversationSortingOption? sortingOption,
      dynamic isLoading,
      UiEvent<PostEndConversationsEvent>? showEndAllConversationsConfirmation,
      QuickAction? mainQuickAction,
      List<QuickAction>? overflowQuickActions,
      UiEvent<QuickActionException>? quickActionException,
      bool pushPermissionsGranted});

  @override
  $QuickActionCopyWith<$Res>? get mainQuickAction;
}

/// @nodoc
class __$$ConversationsStateImplCopyWithImpl<$Res>
    extends _$ConversationsStateCopyWithImpl<$Res, _$ConversationsStateImpl>
    implements _$$ConversationsStateImplCopyWith<$Res> {
  __$$ConversationsStateImplCopyWithImpl(_$ConversationsStateImpl _value,
      $Res Function(_$ConversationsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? conversations = null,
    Object? goToMessagingEndUserId = freezed,
    Object? showEnded = null,
    Object? sortingOption = freezed,
    Object? isLoading = freezed,
    Object? showEndAllConversationsConfirmation = freezed,
    Object? mainQuickAction = freezed,
    Object? overflowQuickActions = freezed,
    Object? quickActionException = freezed,
    Object? pushPermissionsGranted = null,
  }) {
    return _then(_$ConversationsStateImpl(
      conversations: null == conversations
          ? _value._conversations
          : conversations // ignore: cast_nullable_to_non_nullable
              as Map<SfId, Conversation>,
      goToMessagingEndUserId: freezed == goToMessagingEndUserId
          ? _value.goToMessagingEndUserId
          : goToMessagingEndUserId // ignore: cast_nullable_to_non_nullable
              as UiEvent<SfId>?,
      showEnded: null == showEnded
          ? _value.showEnded
          : showEnded // ignore: cast_nullable_to_non_nullable
              as bool,
      sortingOption: freezed == sortingOption
          ? _value.sortingOption
          : sortingOption // ignore: cast_nullable_to_non_nullable
              as ConversationSortingOption?,
      isLoading: freezed == isLoading ? _value.isLoading! : isLoading,
      showEndAllConversationsConfirmation: freezed ==
              showEndAllConversationsConfirmation
          ? _value.showEndAllConversationsConfirmation
          : showEndAllConversationsConfirmation // ignore: cast_nullable_to_non_nullable
              as UiEvent<PostEndConversationsEvent>?,
      mainQuickAction: freezed == mainQuickAction
          ? _value.mainQuickAction
          : mainQuickAction // ignore: cast_nullable_to_non_nullable
              as QuickAction?,
      overflowQuickActions: freezed == overflowQuickActions
          ? _value._overflowQuickActions
          : overflowQuickActions // ignore: cast_nullable_to_non_nullable
              as List<QuickAction>?,
      quickActionException: freezed == quickActionException
          ? _value.quickActionException
          : quickActionException // ignore: cast_nullable_to_non_nullable
              as UiEvent<QuickActionException>?,
      pushPermissionsGranted: null == pushPermissionsGranted
          ? _value.pushPermissionsGranted
          : pushPermissionsGranted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ConversationsStateImpl extends _ConversationsState {
  const _$ConversationsStateImpl(
      {final Map<SfId, Conversation> conversations = const {},
      this.goToMessagingEndUserId,
      this.showEnded = false,
      this.sortingOption,
      this.isLoading = false,
      this.showEndAllConversationsConfirmation,
      this.mainQuickAction,
      final List<QuickAction>? overflowQuickActions,
      this.quickActionException,
      this.pushPermissionsGranted = true})
      : _conversations = conversations,
        _overflowQuickActions = overflowQuickActions,
        super._();

  final Map<SfId, Conversation> _conversations;
  @override
  @JsonKey()
  Map<SfId, Conversation> get conversations {
    if (_conversations is EqualUnmodifiableMapView) return _conversations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_conversations);
  }

  @override
  final UiEvent<SfId>? goToMessagingEndUserId;
  @override
  @JsonKey()
  final bool showEnded;
  @override
  final ConversationSortingOption? sortingOption;
  @override
  @JsonKey()
  final dynamic isLoading;
  @override
  final UiEvent<PostEndConversationsEvent>? showEndAllConversationsConfirmation;
  @override
  final QuickAction? mainQuickAction;
  final List<QuickAction>? _overflowQuickActions;
  @override
  List<QuickAction>? get overflowQuickActions {
    final value = _overflowQuickActions;
    if (value == null) return null;
    if (_overflowQuickActions is EqualUnmodifiableListView)
      return _overflowQuickActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final UiEvent<QuickActionException>? quickActionException;
  @override
  @JsonKey()
  final bool pushPermissionsGranted;

  @override
  String toString() {
    return 'ConversationsState(conversations: $conversations, goToMessagingEndUserId: $goToMessagingEndUserId, showEnded: $showEnded, sortingOption: $sortingOption, isLoading: $isLoading, showEndAllConversationsConfirmation: $showEndAllConversationsConfirmation, mainQuickAction: $mainQuickAction, overflowQuickActions: $overflowQuickActions, quickActionException: $quickActionException, pushPermissionsGranted: $pushPermissionsGranted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConversationsStateImpl &&
            const DeepCollectionEquality()
                .equals(other._conversations, _conversations) &&
            (identical(other.goToMessagingEndUserId, goToMessagingEndUserId) ||
                other.goToMessagingEndUserId == goToMessagingEndUserId) &&
            (identical(other.showEnded, showEnded) ||
                other.showEnded == showEnded) &&
            (identical(other.sortingOption, sortingOption) ||
                other.sortingOption == sortingOption) &&
            const DeepCollectionEquality().equals(other.isLoading, isLoading) &&
            (identical(other.showEndAllConversationsConfirmation,
                    showEndAllConversationsConfirmation) ||
                other.showEndAllConversationsConfirmation ==
                    showEndAllConversationsConfirmation) &&
            (identical(other.mainQuickAction, mainQuickAction) ||
                other.mainQuickAction == mainQuickAction) &&
            const DeepCollectionEquality()
                .equals(other._overflowQuickActions, _overflowQuickActions) &&
            (identical(other.quickActionException, quickActionException) ||
                other.quickActionException == quickActionException) &&
            (identical(other.pushPermissionsGranted, pushPermissionsGranted) ||
                other.pushPermissionsGranted == pushPermissionsGranted));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_conversations),
      goToMessagingEndUserId,
      showEnded,
      sortingOption,
      const DeepCollectionEquality().hash(isLoading),
      showEndAllConversationsConfirmation,
      mainQuickAction,
      const DeepCollectionEquality().hash(_overflowQuickActions),
      quickActionException,
      pushPermissionsGranted);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ConversationsStateImplCopyWith<_$ConversationsStateImpl> get copyWith =>
      __$$ConversationsStateImplCopyWithImpl<_$ConversationsStateImpl>(
          this, _$identity);
}

abstract class _ConversationsState extends ConversationsState {
  const factory _ConversationsState(
      {final Map<SfId, Conversation> conversations,
      final UiEvent<SfId>? goToMessagingEndUserId,
      final bool showEnded,
      final ConversationSortingOption? sortingOption,
      final dynamic isLoading,
      final UiEvent<PostEndConversationsEvent>?
          showEndAllConversationsConfirmation,
      final QuickAction? mainQuickAction,
      final List<QuickAction>? overflowQuickActions,
      final UiEvent<QuickActionException>? quickActionException,
      final bool pushPermissionsGranted}) = _$ConversationsStateImpl;
  const _ConversationsState._() : super._();

  @override
  Map<SfId, Conversation> get conversations;
  @override
  UiEvent<SfId>? get goToMessagingEndUserId;
  @override
  bool get showEnded;
  @override
  ConversationSortingOption? get sortingOption;
  @override
  dynamic get isLoading;
  @override
  UiEvent<PostEndConversationsEvent>? get showEndAllConversationsConfirmation;
  @override
  QuickAction? get mainQuickAction;
  @override
  List<QuickAction>? get overflowQuickActions;
  @override
  UiEvent<QuickActionException>? get quickActionException;
  @override
  bool get pushPermissionsGranted;
  @override
  @JsonKey(ignore: true)
  _$$ConversationsStateImplCopyWith<_$ConversationsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
