import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:open_file/open_file.dart';
import 'package:path/path.dart' as p;
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:x1440/generated/l10n.dart';
import 'package:x1440/ui/screens/chat/messages/file_message.dart';

import 'text_with_mid_ellipsis.dart';

class LocalMedia extends StatelessWidget {
  final File file;
  final double? _height;
  final bool showPreviewOnTap;

  const LocalMedia(
      {super.key,
      required this.file,
      this.showPreviewOnTap = true,
      double? height})
      : _height = height;

  void openFile(BuildContext context) async {
    try {
      await OpenFile.open(file.path);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(S.of(context).local_media_cannot_open_label)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    double? height = _height; // ?? 20.uiUnit;
    String fileExtension = p.extension(file.path);

    Widget child = () {
      if (fileExtension == '.png' ||
          fileExtension == '.jpg' ||
          fileExtension == '.jpeg') {
        return Image.file(
          file,
          fit: BoxFit.cover,
          height: height,
        );
      } else if (fileExtension == '.mp4' ||
          fileExtension.toLowerCase() == '.mov' ||
          fileExtension == '.avi') {
        return FutureBuilder<Uint8List?>(
          future: VideoThumbnail.thumbnailData(
            video: file.path,
            imageFormat: ImageFormat.JPEG,
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done) {
              if (snapshot.data != null) {
                return Image.memory(
                  snapshot.data!,
                  fit: BoxFit.cover,
                  height: height,
                );
              } else {
                return Icon(
                  Icons.error,
                  size: height != null ? height / 2 : null,
                  color: Colors.red,
                );
              }
            } else {
              return const CircularProgressIndicator();
            }
          },
        );
      } else {
        return FileMessage(
            file: file,
            isAgent: true,
            text: S
                .of(context)
                .chat_bubble_file_attachment_label(file.path.split('/').last));

        IconData fileIcon;
        if (fileExtension == '.pdf') {
          fileIcon = Icons.picture_as_pdf;
        } else {
          fileIcon = Icons.insert_drive_file;
        }

        return SizedBox(
          height: height,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                fileIcon,
                size: 100,
              ),
              TextWithMidEllipsis(file.path.split('/').last)
            ],
          ),
        );
      }
    }();

    return GestureDetector(
        onTap: !showPreviewOnTap ? null : () => openFile(context),
        child: Container(
            constraints:
                height == null ? null : BoxConstraints(maxWidth: height),
            height: height,
            child: child));
  }
}
