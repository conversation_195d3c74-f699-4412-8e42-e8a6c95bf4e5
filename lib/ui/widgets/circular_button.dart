import 'package:flutter/material.dart';
import 'package:x1440/ui/themes/themeConstants.dart';

class CircularButton extends StatelessWidget {
  final double? _size;
  final double? _iconSize;
  final void Function()? onTap;
  final Widget? _child;
  final IconData? _icon;
  final Color? _backgroundColor;
  final Color? _iconColor;
  final EdgeInsets? _padding;
  final EdgeInsets? _margin;
  final BoxBorder? _border;
  final BorderRadius? _borderRadius;

  const CircularButton(
      {super.key,
      this.onTap,
      double? size,
      double? iconSize,
      Color? backgroundColor,
      Color? iconColor,
      EdgeInsets? padding,
      EdgeInsets? margin,
      BoxBorder? border,
      BorderRadius? borderRadius,
      required Widget child})
      : _size = size,
        _iconSize = iconSize,
        _child = child,
        _icon = null,
        _backgroundColor = backgroundColor,
        _iconColor = iconColor,
        _padding = padding,
        _margin = margin,
        _border = border,
        _borderRadius = borderRadius;

  const CircularButton.asIcon({
    super.key,
    this.onTap,
    double? size,
    double? iconSize,
    required IconData iconData,
    Color? backgroundColor,
    Color? iconColor,
    EdgeInsets? padding,
    EdgeInsets? margin,
    BoxBorder? border,
    BorderRadius? borderRadius,
  })  : _size = size,
        _iconSize = iconSize,
        _icon = iconData,
        _child = null,
        _backgroundColor = backgroundColor,
        _iconColor = iconColor,
        _padding = padding,
        _margin = margin,
        _border = border,
        _borderRadius = borderRadius;

  @override
  Widget build(BuildContext context) {
    final double size = _size ?? 4.uiUnit;
    bool isDarkMode = Theme.of(context).brightness == Brightness.dark;

    Color backgroundColor = _backgroundColor ??
        (isDarkMode
            ? const Color(0xff4c4c4e)
            : const Color(0xffd9d9d9)); // TODO: try this as a theme element

    Color iconColor =
        _iconColor ?? (isDarkMode ? Colors.white : const Color(0xff706e6b));

    final Widget child = _child ??
        Icon(
          _icon!,
          size: _iconSize,
          color: iconColor,
        );

    return Opacity(
      opacity: onTap == null ? 0.5 : 1.0,
      child: Container(
        padding: _padding,
        margin: _margin ?? EdgeInsets.fromLTRB(0, 1.uiUnit, 1.uiUnit, 1.uiUnit),
        height: size,
        width: size,
        decoration: BoxDecoration(
            color: backgroundColor,
            shape: _border != null ? BoxShape.rectangle : BoxShape.circle,
            border: _border, // TODO: handle these options
            borderRadius: _borderRadius
            // borderRadius: _border == null ? null : BorderRadius.circular(size),
            ),
        child: InkWell(
          onTap: onTap,
          child: child,
        ),
      ),
    );
  }
}
