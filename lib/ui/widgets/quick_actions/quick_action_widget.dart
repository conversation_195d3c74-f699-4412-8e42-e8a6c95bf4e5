import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:x1440/api/salesforce/dtos/quick_actions/quick_action.dart';
import 'package:x1440/utils/Utils.dart';

class QuickActionWidget extends StatelessWidget {
  final QuickAction? quickAction;
  final bool showTitle;
  const QuickActionWidget(
      {super.key, required this.quickAction, this.showTitle = false});

  @override
  Widget build(BuildContext context) {
    if (quickAction == null) return Container();
    return Row(
      children: [
        Semantics(
          label: quickAction!.actionName,
          child: IconButton(
              padding: EdgeInsets.zero,
              visualDensity: VisualDensity.compact,
              splashRadius: 24,
              color: quickAction?.isResolvable == true
                  ? (Brightness.dark == Theme.of(context).brightness
                      ? Colors.white
                      : Colors.black)
                  : Colors.grey.shade400,
              onPressed: () => quickAction?.isResolvable == true
                  ? _openInAppBrowser(context)
                  : null,
              icon: Icon(quickAction!.iconData, weight: 700)),
        ),
        if (showTitle)
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: quickAction?.isResolvable == true
                ? Text(quickAction!.actionName!)
                : Text(quickAction!.actionName!,
                    style: TextStyle(color: Colors.grey.shade400)),
          ),
      ],
    );
  }

  void _openInAppBrowser(BuildContext context) async {
    if (quickAction?.actionUrl?.startsWith('http') == true) {
      context.push('/quickaction', extra: [
        quickAction!.actionName,
        quickAction!.actionUrl,
        quickAction!.actionDescription
      ]);
    } else if (quickAction?.actionUrl != null) {
      if (!await launchUrl(Uri.parse(quickAction!.actionUrl!))) {
        Utils.showToast('Failed to open URL', type: ToastType.error);
      }
    }
  }
}
