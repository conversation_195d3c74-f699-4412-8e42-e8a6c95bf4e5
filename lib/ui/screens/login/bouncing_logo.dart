import 'dart:math' as math;

import 'package:flutter/material.dart';

class BouncingLogo extends StatefulWidget {
  final String image;

  const BouncingLogo({super.key, required this.image});

  @override
  State<BouncingLogo> createState() => _BouncingLogoState();
}

class _BouncingLogoState extends State<BouncingLogo>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  Animation<double>? _animation;

  @override
  void initState() {
    super.initState();

    // Initialize the AnimationController with a random duration between 2 and 3 seconds
    _controller = AnimationController(
      duration: Duration(milliseconds: 2000 + math.Random().nextInt(1000)),
      vsync:
          this, // TickerProvider for the controller to be bound to this State object
    );

    // Define a Tween animation. It will animate the scale of the image between 1.2 and 0.8
    _animation = Tween<double>(begin: 1.2, end: 0.8).animate(
      // Apply a curve to the animation to make it change gradually
      CurvedAnimation(
        parent: _controller!,
        curve: Curves.easeInOut,
      ),
    );

    // Start the animation. It will repeat indefinitely and reverse after reaching the end state
    _controller!.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return AnimatedBuilder(
      animation: _animation!,
      builder: (BuildContext context, Widget? child) {
        return Transform.scale(
          scale: _animation!.value,
          child: Image(
            image: AssetImage(widget.image),
            width: screenWidth / 11,
          ),
        );
      },
    );
  }
}
