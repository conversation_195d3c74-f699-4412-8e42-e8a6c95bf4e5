import 'dart:io';

import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gal/gal.dart';
import 'package:share_plus/share_plus.dart';
import 'package:x1440/ui/blocs/image_viewer/image_viewer_bloc.dart';
import 'package:x1440/ui/themes/themeConstants.dart';

class ImageViewer extends StatelessWidget {
  final ImageProvider? imageProvider;

  const ImageViewer(this.imageProvider, {super.key});

  @override
  Widget build(BuildContext context) {
    Color iconColor = Brightness.dark == Theme.of(context).brightness
        ? Colors.white
        : Colors.black;
    return BlocProvider(
      create: (context) => ImageViewerBloc(),
      child: BlocConsumer<ImageViewerBloc, ImageViewerState>(
        listener: (context, state) {
          if (state.imagePath != null) {
            final bloc = context.read<ImageViewerBloc>();
            if (state.shareImage?.consume() != null) {
              Share.shareXFiles([XFile(state.imagePath!)])
                  .then((value) => bloc.add(ImageSharedEvent()));
            }
            if (state.saveImage?.consume() != null) {
              Gal.putImage(state.imagePath!);
              bloc.add(ImageSavedEvent());
            }
          }
        },
        builder: (context, state) {
          return Scaffold(
              appBar: state.visible
                  ? AppBar(
                      elevation: 0,
                      automaticallyImplyLeading: false,
                      leading: IconButton(
                        icon: const BackButtonIcon(),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    )
                  : null,
              body: Center(
                child: GestureDetector(
                  onTap: () {
                    final bloc = context.read<ImageViewerBloc>();
                    bloc.add(ToggleVisibilityEvent());
                  },
                  child: EasyImageView(
                    imageProvider: imageProvider!,
                    doubleTapZoomable: true,
                  ),
                ),
              ),
              bottomNavigationBar: state.visible
                  ? BottomAppBar(
                      color: AppBarTheme.of(context).backgroundColor,
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // IconButton(
                            //     icon: Icon(
                            //       Platform.isAndroid
                            //           ? Icons.share
                            //           : CupertinoIcons.share,
                            //       color: state.imageShared
                            //           ? ThemeConstants.colors.appBlue
                            //           : iconColor,
                            //     ),
                            //     onPressed: state.imageShared
                            //         ? null
                            //         : () async {
                            //             if (imageProvider != null) {
                            //               final bloc =
                            //                   context.read<ImageViewerBloc>();
                            //               bloc.add(ShareImageEvent(
                            //                   imageProvider: imageProvider!));
                            //             }
                            //           }),
                            IconButton(
                                icon: Icon(
                                  Platform.isAndroid
                                      ? Icons.download
                                      : CupertinoIcons.square_arrow_down,
                                  color: state.imageSaved
                                      ? ThemeConstants.colors.appBlue
                                      : iconColor //context.theme.primaryColor
                                  ,
                                ),
                                onPressed: state.imageSaved
                                    ? null
                                    : () async {
                                        if (imageProvider != null) {
                                          final bloc =
                                              context.read<ImageViewerBloc>();
                                          bloc.add(SaveImageEvent(
                                              imageProvider: imageProvider!));
                                        }
                                      })
                          ]),
                    )
                  : null);
        },
      ),
    );
  }
}
