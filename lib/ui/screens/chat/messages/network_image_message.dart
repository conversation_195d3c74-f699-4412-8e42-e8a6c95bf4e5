import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:loggy/loggy.dart';
import 'package:x1440/ui/blocs/image_size/image_size.dart';
import 'package:x1440/ui/blocs/image_size/image_size_bloc.dart';
import 'package:x1440/ui/blocs/image_size/image_size_event.dart';
import 'package:x1440/ui/blocs/image_size/image_size_state.dart';
import 'package:x1440/ui/themes/themeConstants.dart';

import '../../../widgets/rounded_image_wrapper.dart';

class NetworkImageBubble extends StatelessWidget with UiLoggy {
  final String imageUrl;
  final Map<String, String>? headers;
  final double maxWidth;

  NetworkImageBubble({
    super.key,
    required this.imageUrl,
    required this.maxWidth,
    this.headers,
  });

  @override
  Widget build(BuildContext context) =>
      Column(children: [_buildRoundedCachedNetworkImage(imageUrl, headers)]);

  Widget _buildRoundedCachedNetworkImage(
      String url, Map<String, String>? headers) {
    return RoundedWidgetWrapper(
      child: BlocBuilder<ImageSizeBloc, ImageSizeState>(
          bloc: GetIt.I<ImageSizeBloc>(),
          buildWhen: (previous, current) {
            return previous.imageSizes[url] != current.imageSizes[url];
          },
          builder: (context, state) {
            ImageSizeBloc imageSizeBloc = GetIt.I<ImageSizeBloc>();
            ImageSize? imageSize = state.imageSizes[url];

            double? imageWidth = imageSize?.width == null
                ? maxWidth
                : min(imageSize!.width!, maxWidth);
            double? imageHeight = imageSize?.aspectRatio == null
                ? null
                : imageWidth / imageSize!.aspectRatio!;

            return CachedNetworkImage(
              imageUrl: url,
              httpHeaders: headers,
              width: imageWidth,
              height: imageHeight,
              errorListener: (e) {
                if (kDebugMode) print('CachedNetworkImage errorListener: $e');
              },
              imageBuilder: (context, imageProvider) {
                Image image = Image(
                  image: imageProvider,
                  fit: BoxFit.cover,
                );

                // Create the listener only once and store the reference
                final ImageStreamListener imageStreamListener =
                    ImageStreamListener(
                  (ImageInfo imageInfo, bool synchronousCall) {
                    if (kDebugMode) print('ImageStreamListener: $imageInfo');
                    // Only update if the size has changed
                    if (imageSize?.width != imageInfo.image.width.toDouble() ||
                        imageSize?.height !=
                            imageInfo.image.height.toDouble()) {
                      imageSizeBloc.add(ImageSizeUpdateEvent(
                          ImageSize(
                            height: imageInfo.image.height.toDouble(),
                            width: imageInfo.image.width.toDouble(),
                          ),
                          url));
                    }
                  },
                  onError: (dynamic exception, StackTrace? stackTrace) {
                    if (kDebugMode) {
                      print('ImageStreamListener onError: $exception');
                    }
                  },
                );

                // Remove previous listener before adding new one
                final ImageStream imageStream =
                    image.image.resolve(const ImageConfiguration());
                imageStream.removeListener(imageStreamListener);
                imageStream.addListener(imageStreamListener);

                return GestureDetector(
                  onTap: () {
                    context.push('/imageViewer', extra: imageProvider);
                  },
                  child: image,
                );
              },
              placeholder: (context, url) => Center(
                child: Padding(
                  padding: EdgeInsets.all(2.uiUnit),
                  child: const CircularProgressIndicator(),
                ),
              ),
              errorWidget: (context, url, error) => const Center(
                child: Icon(Icons.error),
              ),
            );
          }),
    );
  }
}
