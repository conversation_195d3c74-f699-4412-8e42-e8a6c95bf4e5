import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:x1440/repositories/message_queue/message_queue_repository_impl.dart';
import 'package:x1440/repositories/message_queue/models/queue_send_message.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';

class MockLocalStorageRepository extends Mock implements LocalStorageRepository {}

class MockRemoteLogger extends Mock implements RemoteLogger {}

QueueReceiveMessage getTestQueueReceiveMessage() => QueueReceiveMessage(
  stringifiedPayload: jsonEncode({
        'id': '1',
        'body': 'Test message',
      }),
  timestamp: 1,
  notificationId: '1',
  sessionId: 'sessionId',
  messageCategory: 'Test',
);

const mockQueueSendWorkMessage = QueueSendMessage(
  messageId: '1',
  workTargetId: 'workTargetId',
  type: QueueSendMessageType.workMessage,
);
final mockQueueSendOutboundMessage = mockQueueSendWorkMessage.copyWith(
  workTargetId: '',
  type: QueueSendMessageType.outboundMessage,
);

void main() {
  late MessageQueueRepositoryImpl messageQueueRepository;
  late MockLocalStorageRepository mockLocalStorageRepository;
  final mockQueueReceiveMessage = getTestQueueReceiveMessage();

  setUp(() {
    registerFallbackValue(mockQueueReceiveMessage);
    registerFallbackValue(mockQueueSendWorkMessage);
    mockLocalStorageRepository = MockLocalStorageRepository();

    /// receive queue
    when(() => mockLocalStorageRepository.removeMessageFromReceiveQueueByNotificationId(any()))
        .thenAnswer((_) async {});
    when(() => mockLocalStorageRepository.getMessageReceiveQueueState())
        .thenAnswer((_) async => <QueueReceiveMessage>[]);
    when(() => mockLocalStorageRepository.addMessageToReceiveQueue(any()))
        .thenAnswer((_) async {});

    /// send queue
  when(() => mockLocalStorageRepository.removeMessageFromSendQueueByMessageId(any()))
        .thenAnswer((_) async {});
    when(() => mockLocalStorageRepository.getMessageSendQueueState())
        .thenAnswer((_) async => <QueueSendMessage>[]);
    when(() => mockLocalStorageRepository.addMessageToSendQueue(any()))
        .thenAnswer((_) async {});

    messageQueueRepository = MessageQueueRepositoryImpl(MockRemoteLogger(), mockLocalStorageRepository);
  });

    group('Message Receive Queue', () {
      test('addMessageToReceiveQueue adds message to queue', () async {
        await messageQueueRepository.addMessageToReceiveQueue(mockQueueReceiveMessage);
        verify(() => mockLocalStorageRepository.addMessageToReceiveQueue(mockQueueReceiveMessage)).called(1);
      });

      test('removeMessageIdsFromReceiveQueue removes message from queue', () async {
        when(() => mockLocalStorageRepository.getMessageReceiveQueueState())
            .thenAnswer((_) async => <QueueReceiveMessage>[mockQueueReceiveMessage]);

        await messageQueueRepository.removeMessageIdsFromReceiveQueue([mockQueueReceiveMessage.notificationId]);

        verify(() => mockLocalStorageRepository.removeMessageFromReceiveQueueByNotificationId(mockQueueReceiveMessage.notificationId)).called(1);
      });
    });

    group('Message Send Queue', () {
      test('addMessageToSendQueue adds work message to queue', () async {
        await messageQueueRepository.addMessageToSendQueue(mockQueueSendWorkMessage);
        verify(() => mockLocalStorageRepository.addMessageToSendQueue(mockQueueSendWorkMessage)).called(1);
      });

      test('addMessageToSendQueue adds outbound message to queue', () async {
        await messageQueueRepository.addMessageToSendQueue(mockQueueSendOutboundMessage);
        verify(() => mockLocalStorageRepository.addMessageToSendQueue(mockQueueSendOutboundMessage)).called(1);
      });

      test('removeMessageIdsFromSendQueue removes work message from queue', () async {
        when(() => mockLocalStorageRepository.getMessageSendQueueState())
            .thenAnswer((_) async => <QueueSendMessage>[mockQueueSendWorkMessage]);

        await messageQueueRepository.removeMessageIdsFromSendQueue([mockQueueSendWorkMessage.messageId]);

        verify(() => mockLocalStorageRepository.removeMessageFromSendQueueByMessageId(mockQueueSendWorkMessage.messageId)).called(1);
      });

      test('removeMessageIdsFromSendQueue removes outbound message from queue', () async {
        when(() => mockLocalStorageRepository.getMessageSendQueueState())
            .thenAnswer((_) async => <QueueSendMessage>[mockQueueSendOutboundMessage]);

        await messageQueueRepository.removeMessageIdsFromSendQueue([mockQueueSendOutboundMessage.messageId]);

        verify(() => mockLocalStorageRepository.removeMessageFromSendQueueByMessageId(mockQueueSendOutboundMessage.messageId)).called(1);
      });
    });
}