import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/close_work_body.dart';
import 'package:x1440/api/dtos/decline_work_body.dart';
import 'package:x1440/api/dtos/work_body.dart';
import 'package:x1440/api/dtos/work_response.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/create_messaging_end_user_body.dart';
import 'package:x1440/api/salesforce/dtos/record_ui/create_object_response.dart';
import 'package:x1440/api/salesforce/dtos/salesforce_soql_query_response.dart';
import 'package:x1440/api/salesforce/legacy_models/salesforce_types.dart';
import 'package:x1440/frameworks/auth_service.dart';
import 'package:x1440/frameworks/conversations_service.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/models/enums/conversation_sorting_option.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/models/messaging_session_model.dart';
import 'package:x1440/models/user_model.dart';
import 'package:x1440/repositories/conversation/conversation_repository.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/models/shim_websocket_message.dart';
import 'package:x1440/repositories/query/query_repository.dart';
import 'package:x1440/repositories/transfer/transfer_repository.dart';
import 'package:x1440/services/de_conversations_service.dart';
import 'package:x1440/services/interfaces/org_viewmodel_interface.dart';
import 'package:x1440/services/shim_service/shim_service.dart';
import 'package:x1440/ui/blocs/ai_suggestions/ai_suggestions_bloc.dart';
import 'package:x1440/ui/blocs/chat/chat_event.dart';
import 'package:x1440/ui/blocs/chat/chat_state.dart';
import 'package:x1440/ui/blocs/contacts/contacts_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/ui/blocs/conversations/conversations_state.dart';
import 'package:x1440/use_cases/contacts/contacts_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/ui/blocs/chat/chat_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/models/conversation_model.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/models/agent_work.dart';
import 'package:x1440/use_cases/models/contact.dart';
import 'package:x1440/use_cases/models/messaging_end_user.dart';
import 'package:x1440/use_cases/quick_actions/quick_actions_use_case.dart';
import 'package:x1440/utils/extensions/iterable_extension.dart';

import '../frameworks/messaging_service_test.dart';
import '../mocks/shim_service/notifications/presence/work_assigned.dart';
import '../repositories/query/query_repository_test.dart';
import '../use_cases/messaging/messaging_use_case_test.dart';

class MockOrgViewmodelInterface extends Mock implements OrgViewmodelInterface {}

class MockConversationsBloc extends Mock implements ConversationsBloc {}

class MockContactsUseCase extends Mock implements ContactsUseCase {}

class MockChatBloc extends Mock implements ChatBloc {}

class MockQueryRepository extends Mock implements QueryRepository {}

class MockTransferRepository extends Mock implements TransferRepository {}

class MockConversationRepository extends Mock
    implements ConversationRepository {}

class MockChatEvent extends Mock implements ChatEvent {}

class MockConversationsEvent extends Mock implements ConversationsEvent {}

class MockDeclineWorkBody extends Mock implements DeclineWorkBody {}

class MockCloseWorkBody extends Mock implements CloseWorkBody {}

class MockCreateMessagingEndUserBody extends Mock
    implements CreateMessagingEndUserBody {}

class MockNotificationManager extends Mock implements NotificationManager {}

class MockRoutingManager extends Mock implements RoutingManager {}

class MockRemoteLogger extends Mock implements RemoteLogger {}

class MockDeConversationsService extends Mock
    implements DeConversationsService {}

class MockAuthService extends Mock implements AuthService {}

class MockConversationsUseCase extends Mock implements ConversationsUseCase {}

class MockAiSuggestionsBloc extends Mock implements AiSuggestionsBloc {}

class MockContactsBloc extends Mock implements ContactsBloc {}

class MockAgentWork extends Mock implements AgentWork {}

class MockQuickActionsUseCase extends Mock implements QuickActionsUseCase {}

class MockShimService extends Mock implements ShimService {}

const String contactId1 = '***************';
const String contactId2 = '***************';

const contact1 = Contact(id: contactId1, mobilePhone: '+***********');

const String messagingEndUserId1 = '0PA000123456789';
const String messagingEndUserId2 = '0PA000987654321';
const String messagingEndUserId_closed1 = '0PA000123456782';
const String messagingEndUserId_created1 = '0PA98765432109';
const String messagingSessionId1 = '0Mw000123456789';
const String messagingSessionId2 = '0Mw000987654321';
const String messagingSessionId_closed1 = '0Mw000123456789';

const ConversationStatus openedStatus = ConversationStatus.opened;
const ConversationStatus closedStatus = ConversationStatus.closed;

final lakConversations = <LakConversation>{
  LakConversation(
      id: messagingEndUserId1, channelType: ChannelType.sfdcLiveagent),
  LakConversation(id: messagingEndUserId2, channelType: ChannelType.sfdcSms),
  LakConversation(
      id: messagingEndUserId_closed1,
      channelType: ChannelType.sfdcWhatsappEnhanced),
}.toList();

final conversations = <String, Conversation>{
  messagingEndUserId1:
      Conversation(status: openedStatus, lakConversation: lakConversations[0]),
  messagingEndUserId2:
      Conversation(status: openedStatus, lakConversation: lakConversations[1]),
  messagingEndUserId_closed1:
      Conversation(status: closedStatus, lakConversation: lakConversations[2]),
};
const workBody = WorkBody(
    requestId: '123', workId: '456', workTargetId: messagingSessionId_closed1);

void main() {
  late ConversationsService conversationsService;
  late MockOrgViewmodelInterface mockOrgViewmodelInterface;
  late MockConversationsBloc mockConversationsBloc;
  late MockChatBloc mockChatBloc;
  late MockQueryRepository mockQueryRepository;
  late MockTransferRepository mockTransferRepository;
  late MockConversationRepository mockConversationRepository;
  late ConversationsUseCase conversationsUseCase;
  late MockNotificationManager mockNotificationManager;
  late MockTypingIndicatorManager mockTypingIndicatorManager;
  late MockRemoteLogger mockRemoteLogger;
  late MockDeConversationsService mockDeConversationsService;
  late MockAiSuggestionsBloc mockAiSuggestionsBloc;
  late MockContactsBloc mockContactsBloc;
  late MockAgentWork mockAgentWork;
  late MockLocalStorageRepository mockLocalStorageRepository;
  late MockQuickActionsUseCase mockQuickActionsUseCase;
  late MockShimService mockShimService;

  const workResponse = WorkResponse();
  var mockMockHttpResponse = MockHttpResponse();

  final shimWebsocketMessage = ShimWebsocketMessage.fromJson(
      MockedPresenceWorkAssignedPayloads().webSocketiOS());
  final queueReceiveMessage =
      QueueReceiveMessage.fromShimWebsocketMessage(shimWebsocketMessage);

  // final declineQueueReceiveMessage = QueueReceiveMessage.fromPushNotification('Work', 'DECLINE_ACTION', {"sessionId": "testSessionId","timestamp": 0, "notificationId": "testNotificationId", "platformType": "omni", "messageType": "Presence/WorkAssigned", "message": "{\"isPreferredUserRequired\": true, \"channelName\": \"sfdc_livemessage\", \"workId\": \"0BzHn000003cpyc\", \"acwMaxTime\": 0, \"pushTimeout\": 0.0, \"isAutoAcceptEnabled\": false, \"isInterruptible\": false, \"routingType\": 1, \"capacityImpact\": 100.0, \"isTransfer\": false, \"isConference\": false, \"workTargetId\": \"0MwHn000000pKk3\", \"maxExtensions\": 1, \"isEngaged\": false, \"acwExtensionDuration\": 0, \"secondaryRoutingPriority\": -1, \"priority\": 2, \"psrDateAdded\": *************}"})!;

  final declineQueueReceiveMessage = QueueReceiveMessage(
      timestamp: *************,
      notificationId: '00000000000000144924',
      sessionId: 'testSessionId',
      messageCategory: 'Work',
      stringifiedPayload:
          MockedPresenceWorkAssignedPayloads().apns()['payload'],
      notificationAction: 'DECLINE_ACTION',
      notificationAvatarUrl:
          "https://re1693247397909.my.salesforce.com/services/images/photo/003Hn00002kZi2DIAS",
      notificationAvatarName: "ZB",
      notificationTitle: "Zzz Bbb",
      notificationBody: "Can you decline me? ");

  setUp(() {
    registerFallbackValue(workBody);
    registerFallbackValue(MockDeclineWorkBody());
    registerFallbackValue(MockChatEvent());
    registerFallbackValue(MockConversationsEvent());
    registerFallbackValue(MockCloseWorkBody());
    registerFallbackValue(MockCreateMessagingEndUserBody());
    mockQueryRepository = MockQueryRepository();
    mockTransferRepository = MockTransferRepository();
    mockConversationRepository = MockConversationRepository();
    mockNotificationManager = MockNotificationManager();
    mockTypingIndicatorManager = MockTypingIndicatorManager();
    mockRemoteLogger = MockRemoteLogger();
    mockDeConversationsService = MockDeConversationsService();
    mockLocalStorageRepository = MockLocalStorageRepository();
    mockAiSuggestionsBloc = MockAiSuggestionsBloc();
    mockContactsBloc = MockContactsBloc();
    mockAgentWork = MockAgentWork();
    mockQuickActionsUseCase = MockQuickActionsUseCase();
    mockShimService = MockShimService();

    conversationsUseCase = ConversationsUseCase(
      mockQueryRepository,
      mockConversationRepository,
      mockTransferRepository,
      mockLocalStorageRepository,
      mockNotificationManager,
      mockTypingIndicatorManager,
      mockRemoteLogger,
    );

    mockConversationsBloc = MockConversationsBloc();
    mockChatBloc = MockChatBloc();
    mockOrgViewmodelInterface = MockOrgViewmodelInterface();

    when(() => mockConversationRepository.acceptWork(any()))
        .thenAnswer((_) async => Success(workResponse));
    when(() => mockConversationRepository.declineWork(any()))
        .thenAnswer((_) async => Success(mockMockHttpResponse));
    when(() => mockConversationRepository.closeWork(any()))
        .thenAnswer((_) async => Success(mockMockHttpResponse));
    when(() => mockChatBloc.state).thenReturn(ChatState());
    when(() => mockDeConversationsService.createConversation(any()))
        .thenAnswer((_) async => conversations.values.first.lakConversation);
    when(() => mockDeConversationsService.fetchConversationsAndMessages())
        .thenAnswer((_) async => lakConversations);

    when(() => mockConversationRepository.createMessagingEndUser(any()))
        .thenAnswer((_) async => Success(const CreateSfObjectReponse(
            id: messagingEndUserId_created1, success: true, errors: [])));
    when(() => mockOrgViewmodelInterface._user).thenReturn(User(
      localSMSChannelId: 'smsChannelId',
      localWhatsAppChannelId: 'whatsAppChannelId',
    ));
    when(() => mockQueryRepository.queryMessagingEndUsers(
          contactId: any(named: 'contactId'),
          messagingChannelId: any(named: 'messagingChannelId'),
          messagingPlatformKey: any(named: 'messagingPlatformKey'),
          id: any(named: 'id'),
        )).thenAnswer((_) async => Success(SalesforceSoqlQueryResponse(
          done: true,
          totalSize: 1,
          records: [
            const MessagingEndUser(
                    id: messagingEndUserId1, contactId: contactId1)
                .toJson()
          ],
        )));

    when(() => mockShimService.workTargetIdsByWorkId)
        .thenReturn({workBody.workId: messagingSessionId_closed1});

    conversationsService = ConversationsService(
      conversationsUseCase,
    );
    GetIt.I.registerSingleton<ConversationsBloc>(mockConversationsBloc);
    GetIt.I.registerSingleton<ConversationsService>(conversationsService);
    GetIt.I.registerSingleton<ChatBloc>(mockChatBloc);
    GetIt.I.registerSingleton<OrgViewmodelInterface>(mockOrgViewmodelInterface);
    GetIt.I
        .registerSingleton<DeConversationsService>(mockDeConversationsService);
    GetIt.I.registerSingleton<ContactsBloc>(mockContactsBloc);
    GetIt.I.registerSingleton<ShimService>(mockShimService);

    conversationsUseCase.setConversations(conversations);

    // This is to support the Toast calls direct from Bloc // TODO: move the toasts into the UI layer?
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  tearDown(() {
    GetIt.I.reset();
  });

  group('ConversationsUseCase', () {
    test('setConversations sets conversations', () {
      expect(conversationsUseCase.conversations, conversations);
    });

    test('numActiveConversations returns number of active conversations', () {
      final numActiveConversations =
          conversationsUseCase.numActiveConversations;
      expect(numActiveConversations, 2);
    });

    test('updateConversationStatus updates conversation status', () {
      conversationsUseCase.setConversations(conversations);
      final updatedConversations = conversationsUseCase
          .updateConversationStatus(messagingEndUserId1, openedStatus);

      expect(updatedConversations[messagingEndUserId1]!.status, openedStatus);
    });

    test('updateMessagingSessionStatus updates messaging session status', () async {
      final updatedConversations = await conversationsUseCase
          .updateMessagingSessionStatus(messagingSessionId1, openedStatus);
      expect(updatedConversations[messagingEndUserId1]!.status, openedStatus);
    });

    test('setLakConversations sets conversations', () {
      final newLakConvo =
          LakConversation(id: 'lak123', channelType: ChannelType.sfdcSms);
      conversationsUseCase.setLakConversations([newLakConvo]);

      expect(
          conversationsUseCase
              .getConversation(newLakConvo.id)
              ?.lakConversation
              ?.id,
          newLakConvo.id);
    });

    test('onMessageQueueReceive adds message to stream', () {
      conversationsUseCase.onMessageQueueReceive([queueReceiveMessage]);

      conversationsUseCase.messageReceiveQueueLegacyStream.listen((event) {
        expect(event, queueReceiveMessage);
      });
    });

    // test('acceptWork success calls conversationRepository', () async {
    //   await conversationsUseCase.acceptWork(workBody.workId, workBody.workTargetId);
    //
    //   /// need to use 'any' b/c the requestId is generated in the useCase
    //   verify(() => mockConversationRepository.acceptWork(any())).called(1);
    // });

    test(
        'declineWork success calls conversationRepository & conversationsService (=> chatBloc)',
        () async {
      await conversationsUseCase.declineWork(
          workBody.workId, workBody.workTargetId);

      /// need to use 'any' b/c the requestId is generated in the useCase
      verify(() => mockConversationRepository.declineWork(any())).called(1);
    });

    test(
        'onMessageQueueReceive with DECLINE_ACTION calls conversationRepository & conversationsService (=> chatBloc)',
        () async {
          print('declineQueueReceiveMessage: $declineQueueReceiveMessage');
      conversationsUseCase.onMessageQueueReceive([declineQueueReceiveMessage]);

      // expect(conversationsUseCase.messageReceiveQueueLegacyStream, emits([declineQueueReceiveMessage]));

      /// need to use 'any' b/c the requestId is generated in the useCase
      verify(() => mockConversationRepository.declineWork(any())).called(1);
    });

    test('closeWork calls conversationRepository.closeWork', () async {
      await conversationsUseCase.closeWork(workBody.workId);

      /// need to use 'any' b/c the requestId is generated in the useCase
      verify(() => mockConversationRepository.closeWork(any())).called(1);
    });

    test('createMessagingEndUser calls convoRepo correctly', () async {
      final id = await conversationsUseCase.createMessagingEndUser(
          contactId: 'testContactId',
          messagingChannelId: 'testChannelId',
          messagingPlatformKey: 'testPlatformKey',
          messageType: 'testMessageType',
          name: messagingEndUserId_created1);
      expect(id, messagingEndUserId_created1);
    });

    // TODO: Fix later, missing new 3rd argument
    // test(
    //     'prepareOutboundConversation with null contact.mobilePhone returns conversation',
    //     () async {
    //   final conversation =
    //       await conversationsUseCase.prepareOutboundConversation(
    //           conversations.values.first, const Contact());
    //   expect(conversation, conversation);
    // });

    // test(
    //     'prepareOutboundConversation with a contact.mobilePhone returns conversation',
    //     () async {
    //   final convo = await conversationsUseCase.prepareOutboundConversationOld(
    //       conversations.values.first, contact1);

    //   expect(convo, conversations.values.first);
    // });

    // test(
    //     'prepareOutboundConversation with a contact.mobilePhone & no matched messagingEndUser calls createMessagingEndUser',
    //     () async {
    //   final convo = await conversationsUseCase.prepareOutboundConversationOld(
    //       conversations.values.first,
    //       const Contact(id: contactId2, mobilePhone: '+***********'));

    //   expect(convo?.id, messagingEndUserId_created1);
    //   verify(() => mockConversationRepository.createMessagingEndUser(any()))
    //       .called(1);
    // });
  });

  group('ConversationsService', () {
    test('addReceivedMessage adds message to conversations & chatBloc',
        () async {
      when(() => mockChatBloc.state).thenReturn(
          ChatState(conversation: conversations[messagingEndUserId1]));
      GetIt.I.registerSingleton<AiSuggestionsBloc>(mockAiSuggestionsBloc);
      conversationsUseCase.updateMessagingSessions([
        const MessagingSession(
            id: messagingSessionId1,
            messagingEndUser: MessagingEndUser(id: messagingEndUserId1)),
      ]);
      await conversationsService.addReceivedMessageToMessagingSession(
          LakMessage(
              id: '123',
              actorType: MessageActorType.EndUser,
              entryType: MessageEntryType.text),
          messagingSessionId_closed1);

      expect(
          conversationsUseCase.conversations[messagingEndUserId1]
              ?.lakConversation?.messages.length,
          1);
      verify(() => mockConversationsBloc.add(any())).called(1);
    });

    test(
        'updateMessagingEndUserStatus updates conversations and calls _updateConversations',
        () {
      conversationsService.updateMessagingEndUserStatus(
          messagingEndUserId1, openedStatus);

      expect(conversationsUseCase.conversations[messagingEndUserId1]?.status,
          openedStatus);
      verify(() => mockConversationsBloc.add(any())).called(1);
    });

    test(
        'updateMessagingSessionStatus updates conversations and calls _updateConversations',
        () {
      conversationsService.updateMessagingSessionStatus(
          messagingSessionId1, openedStatus);

      expect(conversationsUseCase.conversations[messagingEndUserId1]?.status,
          openedStatus);
      verify(() => mockConversationsBloc.add(any())).called(1);
    });

    test('updateFromConversationsViewmodel does nothing when viewmodel is null',
        () {
      // conversationsService.updateFromConversationsViewmodel(null);

      // verifyNever(() => mockConversationsBloc.add(any()));
    });
  });

  group('conversations bloc', () {
    setUp(() {});
    blocTest<ConversationsBloc, ConversationsState>('RefreshScreenEvent',
        build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
        act: (bloc) => bloc.add(RefreshConversationsEvent()),
        expect: () => [
              isA<ConversationsState>()
                  .having((state) => state.isLoading, 'isLoading', true),
              isA<ConversationsState>()
                  .having((state) => state.isLoading, 'isLoading', false)
                  .having((state) => state.conversations, 'conversations',
                      conversations),
            ]);
    blocTest<ConversationsBloc, ConversationsState>(
        'SetAllConversationsClosedEvent',
        build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
        act: (bloc) => bloc.add(SetAllConversationsClosedEvent()),
        expect: () => [
              isA<ConversationsState>().having(
                  (state) => state.conversations.values.firstWhereOrNull(
                      (convo) => convo.status != ConversationStatus.closed),
                  'isLoading',
                  null),
            ]);
    blocTest<ConversationsBloc, ConversationsState>('ClearConversationsEvent',
        build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
        act: (bloc) => bloc.add(ClearConversationsEvent()),
        expect: () => [
              isA<ConversationsState>()
                  .having((state) => state.conversations, 'isLoading', isEmpty),
            ]);
    blocTest<ConversationsBloc, ConversationsState>('GoToConversationEvent',
        build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
        act: (bloc) => bloc.add(GoToConversationEvent(messagingEndUserId1)),
        expect: () => [
              isA<ConversationsState>().having(
                  (state) => state.goToMessagingEndUserId?.consume(),
                  'goToMessagingEndUserId',
                  messagingEndUserId1),
            ]);
    blocTest<ConversationsBloc, ConversationsState>('SetLakConversationsEvent',
        build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
        act: (bloc) => bloc.add(SetLakConversationsEvent(lakConversations)),
        expect: () => [
              /// Note: setting LakConversations creates "Conversations" but these are not identical to the 'conversations' set above b/c the only have the lakConversation component
              isA<ConversationsState>().having(
                  (state) => state.conversations.length,
                  'conversations',
                  conversations.length),
            ]);
    blocTest<ConversationsBloc, ConversationsState>(
        'SetConversationsSortingOptionEvent',
        build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
        act: (bloc) => bloc
          ..add(SetConversationsSortingOptionEvent(
              ConversationSortingOption.alphabetical))
          ..add(SetConversationsSortingOptionEvent(
              ConversationSortingOption.chronological))
          ..add(SetConversationsSortingOptionEvent(
              ConversationSortingOption.byChannel)),
        expect: () => [
              isA<ConversationsState>().having((state) => state.sortingOption,
                  'sortingOption', ConversationSortingOption.alphabetical),
              isA<ConversationsState>().having((state) => state.sortingOption,
                  'sortingOption', ConversationSortingOption.chronological),
              isA<ConversationsState>().having((state) => state.sortingOption,
                  'sortingOption', ConversationSortingOption.byChannel),
            ]);
    blocTest<ConversationsBloc, ConversationsState>('ToggleShowEndedEvent',
        build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
        act: (bloc) => bloc
          ..add(ToggleShowEndedEvent())
          ..add(ToggleShowEndedEvent()),
        expect: () => [
              isA<ConversationsState>()
                  .having((state) => state.showEnded, 'showEnded', true),
              isA<ConversationsState>()
                  .having((state) => state.showEnded, 'showEnded', false),
            ]);
    blocTest<ConversationsBloc, ConversationsState>('FetchConversationsEvent',
        build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
        act: (bloc) => bloc.add(FetchConversationsEvent()),
        expect: () => [
              isA<ConversationsState>()
                  .having((state) => state.isLoading, 'isLoading', true),

              /// these conversations don't type match b/c of their statuses
              isA<ConversationsState>()
                  .having((state) => state.isLoading, 'isLoading', false)
                  .having((state) => state.conversations.length,
                      'conversations', conversations.length),
            ]);

    blocTest<ConversationsBloc, ConversationsState>(
      'emits [ConversationsState] with showEndAllConversationsConfirmation when ShowEndAllConversationsConfirmationEvent is added',
      build: () => ConversationsBloc(conversationsUseCase, mockQuickActionsUseCase),
      act: (bloc) => bloc.add(ShowEndAllConversationsConfirmationEvent(
          PostEndConversationsEvent.logout)),
      expect: () => [
        isA<ConversationsState>().having(
            (state) => state.showEndAllConversationsConfirmation?.consume(),
            'goToMessagingEndUserId',
            PostEndConversationsEvent.logout),
      ],
    );

    late MockConversationsUseCase mockConversationsUseCase;

    blocTest<ConversationsBloc, ConversationsState>(
      'calls acceptConversation and emits updated state when AcceptConversationEvent is added',
      setUp: () async {
        mockConversationsUseCase = MockConversationsUseCase();
        when(() => mockConversationsUseCase.conversations)
            .thenReturn(conversations);
        when(() => mockConversationsUseCase.acceptConversation(any()))
            .thenAnswer((_) async => true);
        when(() => mockConversationsUseCase
                .getLatestSessionAndWorkForMessagingEndUser(any()))
            .thenAnswer((_) => (null, mockAgentWork));
        when(() => mockConversationsUseCase.getAgentWorkForMessagingEndUserId(
            any())).thenAnswer((_) => (mockAgentWork, null));
      },
      build: () => ConversationsBloc(mockConversationsUseCase, mockQuickActionsUseCase),
      act: (bloc) {
        bloc.add(AcceptConversationEvent(messagingEndUserId1));
      },
      expect: () => [
        isA<ConversationsState>().having(
            (state) => state.conversations[messagingEndUserId1]?.status,
            'status',
            ConversationStatus.opened),
      ],
      verify: (_) {
        verify(() => mockConversationsUseCase
            .acceptConversation(messagingEndUserId1)).called(1);
      },
    );

    blocTest<ConversationsBloc, ConversationsState>(
      'calls declineConversation and emits updated state when DeclineConversationEvent is added',
      setUp: () async {
        mockConversationsUseCase = MockConversationsUseCase();
        when(() => mockConversationsUseCase.conversations)
            .thenReturn(conversations);
        when(() => mockConversationsUseCase.declineConversation(any()))
            .thenAnswer((_) async => true);
        when(() => mockConversationsUseCase
                .getLatestSessionAndWorkForMessagingEndUser(any()))
            .thenAnswer((_) => (null, mockAgentWork));
      },
      build: () => ConversationsBloc(mockConversationsUseCase, mockQuickActionsUseCase),
      act: (bloc) {
        bloc.add(DeclineConversationEvent(messagingEndUserId_closed1));
      },
      expect: () => [
        isA<ConversationsState>().having(
            (state) => state.conversations[messagingEndUserId_closed1]?.status,
            'status',
            ConversationStatus.closed),
      ],
    );

    blocTest<ConversationsBloc, ConversationsState>(
      'calls endConversation and emits updated state when EndConversationEvent is added',
      setUp: () async {
        mockConversationsUseCase = MockConversationsUseCase();
        when(() => mockConversationsUseCase.conversations)
            .thenReturn(conversations);
        when(() => mockConversationsUseCase.endConversation(any()))
            .thenAnswer((_) async => conversations);
      },
      build: () => ConversationsBloc(mockConversationsUseCase, mockQuickActionsUseCase),
      act: (bloc) {
        bloc.add(EndConversationEvent(messagingEndUserId_closed1));
      },
      expect: () => [
        isA<ConversationsState>().having(
            (state) => state.conversations[messagingEndUserId_closed1]?.status,
            'status',
            ConversationStatus.closed),
        isA<ConversationsState>()
            .having((state) => state.isLoading, 'isLoading', true),
        isA<ConversationsState>()
            .having((state) => state.isLoading, 'isLoading', false),
      ],
      verify: (_) {
        verify(() => mockConversationsUseCase
            .endConversation(messagingEndUserId_closed1)).called(1);
      },
    );
  });
}
